"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[371],{9045:function(o,t,n){n.r(t);var f=new URL(n(1404),n.b),r=new URL(n(4117),n.b),l=new URL(n(1348),n.b),e=new URL(n(7262),n.b),i=new URL(n(6694),n.b),s=new URL(n(1953),n.b),p=new URL(n(9568),n.b),c=new URL(n(1199),n.b),a=new URL(n(2501),n.b),d=new URL(n(2820),n.b),u=new URL(n(1668),n.b),y=new URL(n(8257),n.b),h='<h1 id="容器化部署-stablediffusion-35-large-文生图模型应用"><font style="color:#020817">容器化部署 StableDiffusion-3.5-large 文生图模型应用</font></h1> <h2 id="1-部署步骤"><font style="color:#020817">1 部署步骤</font></h2> <h3 id="11-访问天工开物控制台，点击新增部署。"><font style="color:#020817">1.1 访问</font><a href="https://tiangongkaiwu.top/#/console">天工开物控制台</a><font style="color:#020817">，点击新增部署。</font></h3> <p><img src="'+f+'" alt=""></p> <h3 id="12-基于自身需要进行配置，参考配置为单卡-4090-和-1-个节点（初次使用进行调试）。"><font style="color:#020817">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></h3> <p><img src="'+r+'" alt=""></p> <h3 id="13-选择相应预制镜像"><font style="color:#020817">1.3 选择相应预制镜像</font></h3> <p><img src="'+l+'" alt=""></p> <h3 id="14-点击部署服务，耐心等待节点拉取镜像并启动。"><font style="color:#020817">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</font></h3> <p><img src="'+e+'" alt=""></p> <h3 id="15-节点启动后，你所在任务详情页中看到的内容可能如下："><font style="color:#020817">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font></h3> <p><img src="'+i+'" alt=""></p> <h3 id="16-我们可以点击快速访问下方8188端口的链接，测试-comfyui-部署情况"><font style="color:#020817">1.6 我们可以点击快速访问下方“8188”端口的链接，测试 comfyui 部署情况</font></h3> <p><font style="color:#020817">系统会自动分配一个可公网访问的域名，点击 8188 端口的链接。接下来我们即可自由地通过使用工作流在 comfyui 中使用 Flux 模型进行图像生成。</font></p> <p><font style="color:#020817">我们进入 8188 端口的 web ui 界面，选择左侧的“工作流“菜单，找到名为”sd_text_encoder_exampl.json“的工作流文件，鼠标点击。</font></p> <p><img src="'+s+'" alt=""></p> <p><font style="color:#020817">随后，我们可以看到工作流已经被正常载入 ComfyUI 了。接下来，我们找到 prompt（提示词）的填写节点，输入我们想要生成的图像描述文本。</font></p> <p><img src="'+p+'" alt=""></p> <p><font style="color:#020817">参考的 prompt 如下（SD 模型对英文支持较好）：</font></p> <p><font style="color:#67676c">best quality, a cute anime girl, sunset light, warm atmosphere, soft features, dreamy mood, sitting on a swing, solo, looking at viewer, gentle smile, blush, pale skin, full body, long flowing hair, off-shoulder dress with blue and white edges, visible collarbone, barefoot, golden hour lighting, light particles, flower petals floating, orange sky, backlight glow, surrounded by vines and plants, roses and blue flowers around, soft shadows, natural environment, cinematic framing</font></p> <p><font style="color:#020817">稍等片刻，即可在后方的“保存图像”节点看到基于我们提示词生成的图片。右键点击图片，选择“Save Image”即可保存图片。</font></p> <p><img src="'+c+'" alt=""></p> <h3 id="17-通过-api-的形式来调用-comfyui-进行图像生成"><font style="color:#020817">1.7 通过 API 的形式来调用 comfyui 进行图像生成</font></h3> <p><font style="color:#020817">我们不推荐直接使用 comfyui 的默认 API 接口，因为多节点时需自行解决无状态问题。更推荐的做法是通过我们暴露的 3000 端口进行请求，其通过 </font><a href="https://github.com/SaladTechnologies/comfyui-api?tab=readme-ov-file"><font style="color:#2f8ef4">comfyui-api</font></a><font style="color:#020817"> 进行包装，支持默认的同步生图请求和 webhook 实现。</font></p> <p><font style="color:#020817">以下以 POSTMAN 为例，简要描述如何向 3000 端口发送图片生成的 API 请求：</font></p> <h4 id="171-保存页面工作流"><font style="color:#020817">1.7.1 保存页面工作流</font></h4> <p><font style="color:#020817">点击”导航栏——工作流——导出（API）“菜单，浏览器会自动下载一个 json 文件。</font></p> <p><img src="'+a+'" alt=""></p> <h4 id="172-打开-postman，新建一个-post-请求"><font style="color:#020817">1.7.2 打开 POSTMAN，新建一个 POST 请求</font></h4> <p><font style="color:#020817">新建一个 POST 请求，并命名为”prompt“，如下图所示：</font></p> <p><img src="'+d+'" alt=""></p> <h4 id="173-完善请求信息"><font style="color:#020817">1.7.3 完善请求信息</font></h4> <p><font style="color:#020817">需要完善的信息如下：</font></p> <ul> <li><strong><font style="color:#020817">请求的 URL</font></strong></li> </ul> <p><font style="color:#020817">在 3000 端口的回传链接后加上“/prompt”的路径，保证其格式类似于</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)"><a href="https://xxx/prompt">https://xxx/prompt</a></font><font style="color:#020817">。</font></p> <ul> <li><strong><font style="color:#020817">将请求体参数格式设置为 raw 和 json</font></strong></li> </ul> <p><font style="color:#020817">如图。</font></p> <ul> <li><strong><font style="color:#020817">设置参数内容基本格式</font></strong></li> </ul> <p><font style="color:#020817">如图。</font></p> <p><img src="'+u+'" alt=""></p> <h4 id="174-将我们下载好的工作流-json-文件粘贴为参数中prompt字段的值"><font style="color:#020817">1.7.4 将我们下载好的工作流 json 文件粘贴为参数中</font><font style="color:#020817;background-color:rgba(142,150,170,.14)">prompt</font><font style="color:#020817">字段的值</font></h4> <p><font style="color:#020817">如下图所示，我们将鼠标移动至 prompt 字段的冒号后，粘贴工作流的内容。</font></p> <p><img src="'+y+'" alt=""></p> <h4 id="175-发送请求"><font style="color:#020817">1.7.5 发送请求</font></h4> <p><font style="color:#020817">返回结果如下所示，</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">images</font><font style="color:#020817">字段包含一个字符数组，其中的元素即为生成图片的 base64 编码。</font></p> <p><strong><font style="color:#67676c"></font></strong></p> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/18 16:03</font></p> ';t["default"]=h},2820:function(o,t,n){o.exports=n.p+"img/comfyui10.c821ef82.png"},1668:function(o,t,n){o.exports=n.p+"img/comfyui11.d0c427b4.png"},8257:function(o){o.exports="data:image/png;base64,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"},1348:function(o,t,n){o.exports=n.p+"img/comfyui3.ef78b65d.png"},7262:function(o,t,n){o.exports=n.p+"img/comfyui4.7a6aa585.png"},6694:function(o,t,n){o.exports=n.p+"img/comfyui5.5f4e3b05.png"},1953:function(o,t,n){o.exports=n.p+"img/comfyui6.44d09aad.png"},9568:function(o,t,n){o.exports=n.p+"img/comfyui7.205d1665.png"},1199:function(o,t,n){o.exports=n.p+"img/comfyui8.794b9d4d.png"},2501:function(o,t,n){o.exports=n.p+"img/comfyui9.dd2126b4.png"},1404:function(o,t,n){o.exports=n.p+"img/universal1.9a1b3f4b.png"},4117:function(o,t,n){o.exports=n.p+"img/universal2.4306636e.png"}}]);
//# sourceMappingURL=docs19.e5d04027.js.map