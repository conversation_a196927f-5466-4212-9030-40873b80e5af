{"version": 3, "file": "js/24.1bbfac43.js", "mappings": "gJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYC,GAAG,CAAC,WAAa,SAASC,GAAQ,OAAON,EAAIO,UAAU,SAAS,EAAE,WAAa,SAASD,GAAQ,OAAON,EAAIQ,UAAU,SAAS,IAAI,CAACN,EAAG,IAAI,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,sfAAsf,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,6MAA6M,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,w3BAAw3B,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,wEAAwE,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,wEAAwE,KAAO,iBAAiBP,EAAG,MAAM,CAACQ,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAA2B,WAApBb,EAAIc,YAA0BC,WAAW,6BAA6BX,YAAY,gCAAgC,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACF,EAAIgB,GAAG,cAAcd,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,IAAMT,EAAIiB,aAAa,IAAM,qBAAqBf,EAAG,MAAM,CAACE,YAAY,YAAYC,GAAG,CAAC,WAAa,SAASC,GAAQ,OAAON,EAAIO,UAAU,UAAU,EAAE,WAAa,SAASD,GAAQ,OAAON,EAAIQ,UAAU,UAAU,IAAI,CAACN,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,mfAAmf,KAAO,iBAAiBP,EAAG,MAAM,CAACQ,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAA2B,YAApBb,EAAIc,YAA2BC,WAAW,8BAA8BX,YAAY,iCAAiC,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACF,EAAIgB,GAAG,eAAed,EAAG,IAAI,CAACE,YAAY,gBAAgB,CAACJ,EAAIgB,GAAG,iBAAiBd,EAAG,IAAI,CAACF,EAAIgB,GAAG,iBAAiBd,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,IAAMT,EAAIkB,cAAc,IAAM,qBAAqBhB,EAAG,MAAM,CAACE,YAAY,YAAYC,GAAG,CAAC,MAAQL,EAAImB,kBAAkB,WAAanB,EAAIoB,YAAY,WAAapB,EAAIqB,cAAc,CAACnB,EAAG,IAAI,CAACE,YAAY,0BAA0B,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,qKAAqK,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,kxBAAkxB,KAAO,iBAAiBP,EAAG,MAAM,CAACQ,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOb,EAAIsB,oBAAqBP,WAAW,wBAAwBX,YAAY,WAAW,CAACJ,EAAIgB,GAAG,mBAAoBhB,EAAIuB,UAAWrB,EAAG,MAAM,CAACE,YAAY,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAGA,EAAOkB,SAAWlB,EAAOmB,cAAqB,KAAYzB,EAAI0B,mBAAmBC,MAAM,KAAMC,UAAU,IAAI,CAAC1B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACF,EAAIgB,GAAG,WAAWd,EAAG,OAAO,CAACE,YAAY,YAAYC,GAAG,CAAC,MAAQL,EAAI0B,qBAAqB,CAAC1B,EAAIgB,GAAG,SAASd,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,yBAAyB,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,2PAA2P,KAAO,iBAAiBT,EAAIgB,GAAG,+BAA+Bd,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACE,YAAY,YAAY,CAACJ,EAAIgB,GAAG,WAAWd,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,SAAS,CAACQ,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOb,EAAI6B,SAASC,KAAMf,WAAW,kBAAkBN,MAAM,CAAC,SAAW,IAAIJ,GAAG,CAAC,OAAS,SAASC,GAAQ,IAAIyB,EAAgBC,MAAMC,UAAUC,OAAOC,KAAK7B,EAAOkB,OAAOY,SAAQ,SAASC,GAAG,OAAOA,EAAEC,QAAQ,IAAGC,KAAI,SAASF,GAAG,IAAIG,EAAM,WAAYH,EAAIA,EAAEI,OAASJ,EAAExB,MAAM,OAAO2B,CAAG,IAAIxC,EAAI0C,KAAK1C,EAAI6B,SAAU,OAAQvB,EAAOkB,OAAOmB,SAAWZ,EAAgBA,EAAc,GAAG,IAAI,CAAC7B,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,KAAK,CAACT,EAAIgB,GAAG,SAASd,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAIgB,GAAG,UAAUd,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAIgB,GAAG,UAAUd,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAIgB,GAAG,UAAUd,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,SAAS,CAACT,EAAIgB,GAAG,UAAUd,EAAG,SAAS,CAACO,MAAM,CAAC,MAAQ,OAAO,CAACT,EAAIgB,GAAG,aAAchB,EAAI6B,SAASC,MAAQ9B,EAAI4C,WAAY1C,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIgB,GAAG,aAAahB,EAAI6C,OAAO3C,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACE,YAAY,YAAY,CAACJ,EAAIgB,GAAG,WAAWd,EAAG,WAAW,CAACQ,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOb,EAAI6B,SAASiB,YAAa/B,WAAW,yBAAyBN,MAAM,CAAC,YAAc,MAAM,SAAW,IAAIsC,SAAS,CAAC,MAAS/C,EAAI6B,SAASiB,aAAczC,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAOkB,OAAOwB,WAAiBhD,EAAI0C,KAAK1C,EAAI6B,SAAU,cAAevB,EAAOkB,OAAOX,MAAM,MAAOb,EAAI6B,SAASiB,aAAe9C,EAAI4C,WAAY1C,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIgB,GAAG,aAAahB,EAAI6C,OAAO3C,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACE,YAAY,aAAa,CAACJ,EAAIgB,GAAG,WAAWd,EAAG,MAAM,CAACE,YAAY,iBAAiBC,GAAG,CAAC,MAAQL,EAAIiD,kBAAkB,SAAW,SAAS3C,GAAQA,EAAO4C,gBAAiB,EAAE,KAAO,SAAS5C,GAAgC,OAAxBA,EAAO4C,iBAAwBlD,EAAImD,WAAWxB,MAAM,KAAMC,UAAU,IAAI,CAAC1B,EAAG,QAAQ,CAACkD,IAAI,YAAYC,YAAY,CAAC,QAAU,QAAQ5C,MAAM,CAAC,KAAO,OAAO,OAAS,WAAWJ,GAAG,CAAC,OAASL,EAAIsD,gBAAkBtD,EAAI6B,SAAS0B,MAAuoCrD,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgBK,MAAM,CAAC,IAAMT,EAAI6B,SAAS2B,aAAa,IAAM,YAAYtD,EAAG,MAAM,CAACE,YAAY,eAAeC,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOmD,kBAAyBzD,EAAI0D,YAAY/B,MAAM,KAAMC,UAAU,IAAI,CAAC5B,EAAIgB,GAAG,SAA/5Cd,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,IAAI,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,oKAAoK,KAAO,aAAaP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,8rBAA8rB,KAAO,iBAAiBP,EAAG,IAAI,CAACF,EAAIgB,GAAG,0BAAkUd,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,SAAS,CAACE,YAAY,iBAAiBC,GAAG,CAAC,MAAQL,EAAI0B,qBAAqB,CAAC1B,EAAIgB,GAAG,QAAQd,EAAG,SAAS,CAACE,YAAY,iBAAiBC,GAAG,CAAC,MAAQL,EAAI2D,gBAAgB,CAAC3D,EAAIgB,GAAG,cAAchB,EAAI6C,KAAM7C,EAAI4D,iBAAkB1D,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACO,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAACP,EAAG,OAAO,CAACO,MAAM,CAAC,EAAI,sRAAsR,KAAO,iBAAiBP,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIgB,GAAG,UAAUd,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACJ,EAAIgB,GAAG,oBAAoBd,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,SAAS,CAACE,YAAY,kBAAkBC,GAAG,CAAC,MAAQL,EAAI6D,oBAAoB,CAAC7D,EAAIgB,GAAG,cAAchB,EAAI6C,MAC5tV,EACIiB,EAAkB,GCuKtB,GACAnD,KAAA,QACAoD,OACA,OACAjD,YAAA,KACAS,WAAA,EACAqB,YAAA,EACAtB,qBAAA,EACAsC,kBAAA,EACA3C,aAAA+C,EAAA,IACA9C,cAAA8C,EAAA,IACAnC,SAAA,CACAC,KAAA,GACAgB,YAAA,GACAmB,WAAA,GACAV,MAAA,KACAC,aAAA,MAGA,EACAU,QAAA,CACA3D,UAAAuB,GACA,KAAAhB,YAAAgB,CACA,EACAtB,UAAAsB,GACA,KAAAhB,cAAAgB,IACA,KAAAhB,YAAA,KAEA,EACAM,cACA,KAAAE,qBAAA,CACA,EACAD,cACA,KAAAC,qBAAA,CACA,EACAH,oBACA,KAAAI,WAAA,EACA,KAAAqB,YAAA,CACA,EACAlB,qBACA,KAAAH,WAAA,EAEA,KAAAM,SAAA,CACAC,KAAA,GACAgB,YAAA,GACAmB,WAAA,GACAV,MAAA,KACAC,aAAA,MAEA,KAAAZ,YAAA,CACA,EACAK,oBACA,KAAAkB,MAAAC,UAAAC,OACA,EACAf,aAAAgB,GACA,MAAAC,EAAAD,EAAA9C,OAAAgD,MAAA,GACAD,GACA,KAAAE,aAAAF,EAEA,EACApB,WAAAmB,GACA,MAAAC,EAAAD,EAAAI,aAAAF,MAAA,GACAD,GAAAA,EAAAzC,KAAA6C,MAAA,YACA,KAAAF,aAAAF,EAEA,EACAE,aAAAF,GACA,GAAAA,GAAAA,EAAAzC,KAAA6C,MAAA,YACA,KAAA9C,SAAA0B,MAAAgB,EAGA,MAAAK,EAAA,IAAAC,WACAD,EAAAE,OAAAC,IACA,KAAAlD,SAAA2B,aAAAuB,EAAAvD,OAAAwD,MAAA,EAEAJ,EAAAK,cAAAV,EACA,CACA,EACAb,cACA,KAAA7B,SAAA0B,MAAA,KACA,KAAA1B,SAAA2B,aAAA,IACA,EACAG,gBAEA,KAAAf,YAAA,EACA,KAAAf,SAAAC,MAAA,KAAAD,SAAAiB,aAKA,KAAAoC,gBACA,EACAA,iBAIA,QAAArD,SAAA0B,MAAA,CACA,MAAA4B,EAAA,IAAAC,SACAD,EAAAE,OAAA,YAAAxD,SAAAC,MACAqD,EAAAE,OAAA,mBAAAxD,SAAAiB,aACAqC,EAAAE,OAAA,kBAAAxD,SAAAoC,YACAkB,EAAAE,OAAA,aAAAxD,SAAA0B,MAIA,CAGA,KAAAhC,WAAA,EACA,KAAAqC,kBAAA,EAGA,KAAA/B,SAAA,CACAC,KAAA,GACAgB,YAAA,GACAmB,WAAA,GACAV,MAAA,KACAC,aAAA,KAEA,EACAK,oBACA,KAAAD,kBAAA,CACA,ICnSuQ,I,UCQnQ0B,GAAY,OACd,EACAvF,EACA+D,GACA,EACA,KACA,WACA,MAIF,EAAewB,EAAiB,O,oECnBhC,IAAIvF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoBC,GAAG,CAAC,WAAaL,EAAIuF,cAAc,WAAavF,EAAIwF,iBAAiB,CAACtF,EAAG,mBAAmB,CAACE,YAAY,mBAAmBK,MAAM,CAAC,KAAO,QAAQ,IAAM,QAAQT,EAAIyF,GAAIzF,EAAI0F,WAAW,SAASC,EAASC,GAAO,OAAO1F,EAAG,MAAM,CAACQ,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOb,EAAI6F,uBAAyBD,EAAO7E,WAAW,mCAAmC+E,IAAIH,EAASvF,YAAY,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAI+F,qBAAqBJ,EAAS,EAAE,WAAa,SAASrF,GAAQ,OAAON,EAAIgG,MAAMJ,EAAM,IAAI,CAAC5F,EAAIgB,GAAG,IAAIhB,EAAIiG,GAAGN,GAAU,MAAM,IAAG,IAAI,GAAGzF,EAAG,MAAM,CAACE,YAAY,YAAY8F,MAAM,CAAE,mBAAoBlG,EAAImG,UAAW9F,GAAG,CAAC,MAAQL,EAAIoG,aAAa,CAAClG,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,MAAM,CAACQ,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOb,EAAImG,SAAUpF,WAAW,aAAaX,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIqG,GAAG,GAAGnG,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,eAAeC,GAAG,CAAC,MAAQL,EAAIoG,kBAAkBlG,EAAG,MAAM,CAACkD,IAAI,oBAAoBhD,YAAY,iBAAiB,CAACJ,EAAIyF,GAAIzF,EAAIsG,UAAU,SAASC,EAAQX,GAAO,OAAO1F,EAAG,MAAM,CAAC4F,IAAIF,EAAMM,MAAM,CAAC,UAAWK,EAAQzE,OAAO,CAAmB,QAAjByE,EAAQzE,KAAgB5B,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAI6C,KAAK3C,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe2C,SAAS,CAAC,UAAY/C,EAAIiG,GAAGjG,EAAIwG,cAAcD,EAAQE,UAAUvG,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIgB,GAAGhB,EAAIiG,GAAGjG,EAAI0G,WAAWH,EAAQI,aAAa,IAAI3G,EAAI4G,QAAS1G,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQF,EAAG,MAAM,CAACE,YAAY,QAAQF,EAAG,MAAM,CAACE,YAAY,UAAUJ,EAAI6C,MAAM,GAAG3C,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACQ,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOb,EAAI6G,UAAW9F,WAAW,cAAcN,MAAM,CAAC,KAAO,OAAO,YAAc,aAAa,SAAWT,EAAI4G,SAAS7D,SAAS,CAAC,MAAS/C,EAAI6G,WAAYxG,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOwB,KAAKgF,QAAQ,QAAQ9G,EAAI+G,GAAGzG,EAAO0G,QAAQ,QAAQ,GAAG1G,EAAOwF,IAAI,SAAgB,KAAY9F,EAAIiH,YAAYtF,MAAM,KAAMC,UAAU,EAAE,MAAQ,SAAStB,GAAWA,EAAOkB,OAAOwB,YAAiBhD,EAAI6G,UAAUvG,EAAOkB,OAAOX,MAAK,KAAKX,EAAG,SAAS,CAACO,MAAM,CAAC,SAAWT,EAAI4G,UAAY5G,EAAI6G,UAAUK,QAAQ7G,GAAG,CAAC,MAAQL,EAAIiH,cAAc,CAAC/G,EAAG,IAAI,CAACE,YAAY,8BAC36E,EACI0D,EAAkB,CAAC,WAAY,IAAI9D,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIgB,GAAG,WACnK,GC6EA,G,QAAA,CACAL,KAAA,SAEAoD,OACA,OACAoC,UAAA,EACAU,UAAA,GACAP,SAAA,CACA,CACAxE,KAAA,MACA2E,KAAA,uBACAE,KAAA,IAAAQ,OAGAP,SAAA,EACAQ,gBAAA,GACA1B,UAAA,CACA,aACA,YACA,aAEAG,qBAAA,EACAwB,cAAA,KACAC,iBAAA,IACAC,UAAA,EAEA,EACAC,gBACA,KAAAC,eACA,EACAC,UAGA,GAFA,KAAAC,iBAEAC,SAAAC,eAAA,iBACA,MAAAC,EAAAF,SAAAG,cAAA,QACAD,EAAAE,GAAA,eACAF,EAAAG,IAAA,aACAH,EAAAI,KAAA,6EACAN,SAAAO,KAAAC,YAAAN,EACA,CACA,EAEA5D,QAAA,CACA8B,MAAAJ,GACA,KAAAC,qBAAAD,EACA,KAAAL,eACA,EACAoC,gBACA,IAAAU,EAAA,KACA,KAAAZ,gBACA,KAAAJ,cAAAiB,aAAA,KACAD,EAAAd,WACA,KAAA1B,sBACA,KAAAA,qBAAA,QAAAH,UAAA6C,OACAC,QAAAC,IAAA,UAAA5C,sBACA2C,QAAAC,IAAA,WAAAJ,EAAAd,UACA,GACA,KAAAD,iBACA,EACA/B,gBACA,KAAAgC,UAAA,CACA,EACA/B,iBACA,KAAA+B,UAAA,CACA,EACAE,gBACA,KAAAJ,gBACAqB,cAAA,KAAArB,eACA,KAAAA,cAAA,KAEA,EAEAtB,qBAAAJ,GACA,KAAAkB,UAAAlB,EACA,KAAAsB,aACA,EACAb,aACA,KAAAD,UAAA,KAAAA,SAEA,KAAAA,UACA,KAAAwC,WAAA,KACA,KAAAC,gBAAA,GAGA,EAEA,oBACA,SAAA/B,UAAAK,QAAA,KAAAN,QAAA,OAGA,KAAAN,SAAAuC,KAAA,CACA/G,KAAA,OACA2E,KAAA,KAAAI,UACAF,KAAA,IAAAQ,OAGA,MAAA2B,EAAA,KAAAjC,UACA,KAAAA,UAAA,GACA,KAAAD,SAAA,EAEA,KAAAQ,gBAAAyB,KAAA,CACAE,KAAA,OACAC,QAAAF,IAIA,MAAAG,EAAA,CACAC,MAAA,eACA5C,SAAA,EAAAyC,KAAA,SAAAC,QAAA,+HAAA5B,iBACA+B,QAAA,EACA/G,QAAA,CACAgH,iBAAA,IACAC,kBAAA,IAEAC,KAAA,QAIA,KAAAX,WAAA,KACA,KAAAC,gBAAA,IAGA,IAGA,MAAAW,QAAAC,MAAA,kDACAC,OAAA,OACAC,QAAA,CACAC,cAAA,6DACA,mCAEAC,KAAAC,KAAAC,UAAAb,KAIArE,EAAA2E,EAAAK,KAAAG,YACAC,EAAA,IAAAC,YACAC,EAAA,KAAA5D,SAAAuC,KAAA,CACA/G,KAAA,MACA2E,KAAA,GACAE,KAAA,IAAAQ,OACA,EACA,SACA,WAAAgD,EAAA,MAAAtJ,SAAA+D,EAAAwF,OACA,GAAAD,EAAA,MAGA,MAAAE,EAAAL,EAAAM,OAAAzJ,GACA0J,EAAAF,EAAAG,MAAA,MAAAtI,QAAAuI,GAAAA,EAAAvD,SAEA,UAAAuD,KAAAF,EACA,IACA,MAAAG,EAAAD,EAAAE,MAAA,GAAAzD,OACA,QAAAwD,GAAA,WAAAA,EAAA,SAEA,IAAA3G,EAAA8F,KAAAe,MAAAF,GACA,GAAA3G,EAAA8G,QAAA,CACA,SAAA9G,EAAA8G,QAAA,GAAAC,MAAAC,kBACA,SAEA,WAAAhH,EAAA8G,QAAA,GAAAC,MAAA9B,QACA,SAEA,KAAA1C,SAAA4D,GAAAzD,MAAA1C,EAAA8G,QAAA,GAAAC,MAAA9B,OACA,CACA,OAAAjE,GACA,CAEA,CACA,KAAAqC,gBAAAyB,KAAA,CACAE,KAAA,YACAC,QAAA,KAAA1C,SAAA4D,GAAAzD,MASA,OAAAuE,GAGA,KAAA1E,SAAAuC,KAAA,CACA/G,KAAA,MACA2E,KAAA,qBACAE,KAAA,IAAAQ,MAEA,SACA,KAAAP,SAAA,EAGA,KAAA+B,WAAA,KACA,KAAAC,gBAAA,GAEA,CACA,EAGA,kBAAArC,GAeA,aAbA,IAAA0E,SAAAC,GAAAC,WAAAD,EAAA,OAaA,YAAA3E,0BACA,EAEAqC,iBACA,MAAAwC,EAAA,KAAAjH,MAAAkH,kBACAD,EAAAE,UAAAF,EAAAG,YACA,EAEA7E,WAAA8E,GACA,WAAArE,KAAAqE,GAAAC,mBAAA,IAAAC,KAAA,UAAAC,OAAA,WACA,EAEAnF,cAAAC,GAEA,OAAAA,EACAmF,QAAA,cACAA,QAAA,6DACA,KCvTwQ,I,UCQpQtG,GAAY,OACd,EACAvF,EACA+D,GACA,EACA,KACA,WACA,MAIF,EAAewB,EAAiB,O,iFCnBhC,IAAIvF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAAEJ,EAAI6L,SAAU3L,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,SAAS,CAACE,YAAY,iBAAiB8F,MAAM,CAAE,OAAUlG,EAAI8L,gBAAiBzL,GAAG,CAAC,MAAQL,EAAI+L,gBAAgB,CAAC7L,EAAG,IAAI,CAACE,YAAY,cAAcF,EAAG,OAAO,CAACF,EAAIgB,GAAG,UAAUd,EAAG,SAAS,CAACE,YAAY,aAAa8F,MAAM,CAAE,OAAUlG,EAAIgM,YAAa3L,GAAG,CAAC,MAAQL,EAAIiM,YAAY,CAAC/L,EAAG,IAAI,CAACE,YAAY,cAAcF,EAAG,OAAO,CAACF,EAAIgB,GAAG,YAAYhB,EAAI6C,KAAM7C,EAAI6L,WAAa7L,EAAI8L,gBAAkB9L,EAAIgM,YAAa9L,EAAG,MAAM,CAACE,YAAY,UAAUC,GAAG,CAAC,MAAQL,EAAIkM,kBAAkBlM,EAAI6C,KAAK3C,EAAG,QAAQ,CAACkD,IAAI,UAAUhD,YAAY,UAAU8F,MAAM,CACrrB,kBAAmBlG,EAAI8L,gBAAkB9L,EAAI6L,SAC7C,kBAAmB7L,EAAI8L,iBAAmB9L,EAAI6L,WAC7C,CAAE7L,EAAI6L,SAAU3L,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,OAAO,CAACE,YAAY,iBAAiB,CAACJ,EAAIgB,GAAG,UAAUd,EAAG,SAAS,CAACE,YAAY,YAAYC,GAAG,CAAC,MAAQL,EAAImM,eAAe,CAACjM,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIgB,GAAG,WAAWhB,EAAI6C,KAAK3C,EAAG,MAAM,CAACE,YAAY,gBAAgBJ,EAAIyF,GAAIzF,EAAIoM,MAAM,SAASC,GAAU,OAAOnM,EAAG,MAAM,CAAC4F,IAAIuG,EAASC,MAAMlM,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACJ,EAAIgB,GAAGhB,EAAIiG,GAAGoG,EAASC,UAAUpM,EAAG,KAAK,CAACE,YAAY,aAAaJ,EAAIyF,GAAI4G,EAASE,OAAO,SAASC,GAAM,OAAOtM,EAAG,KAAK,CAAC4F,IAAI0G,EAAKC,KAAKrM,YAAY,aAAa,CAACF,EAAG,cAAc,CAACE,YAAY,YAAY8F,MAAM,CAAE,mBAAoBlG,EAAI0M,aAAaF,EAAKC,OAAQhM,MAAM,CAAC,GAAK+L,EAAKC,MAAMpM,GAAG,CAAC,MAAQL,EAAI2M,kBAAkB,CAAC3M,EAAIgB,GAAG,IAAIhB,EAAIiG,GAAGuG,EAAK7L,MAAM,QAAQ,EAAE,IAAG,IAAI,IAAG,KAAKT,EAAG,OAAO,CAACkD,IAAI,cAAchD,YAAY,eAAe8F,MAAM,CACj2B,qBAAsBlG,EAAI8L,iBAAmB9L,EAAIgM,aAAehM,EAAI6L,SACpE,gBAAiB7L,EAAI8L,iBAAmB9L,EAAIgM,YAAchM,EAAI6L,WAC7D,CAAC3L,EAAG,cAAc,CAACO,MAAM,CAAC,IAAMT,EAAI4M,WAAW,YAAY5M,EAAI6M,cAAc,YAAY7M,EAAI8M,eAAezM,GAAG,CAAC,iBAAiBL,EAAI+M,aAAa,GAAG7M,EAAG,QAAQ,CAACE,YAAY,MAAM8F,MAAM,CAC1L,cAAelG,EAAIgM,YAAchM,EAAI6L,SACrC,cAAe7L,EAAIgM,aAAehM,EAAI6L,WACrC,CAAE7L,EAAI6L,SAAU3L,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,OAAO,CAACE,YAAY,aAAa,CAACJ,EAAIgB,GAAG,UAAUd,EAAG,SAAS,CAACE,YAAY,YAAYC,GAAG,CAAC,MAAQL,EAAIgN,WAAW,CAAC9M,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIgB,GAAG,WAAWhB,EAAI6C,KAAO7C,EAAI6L,SAA+D7L,EAAI6C,KAAzD3C,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIgB,GAAG,UAAmBd,EAAG,KAAK,CAACE,YAAY,YAAYJ,EAAIyF,GAAIzF,EAAIiN,KAAK,SAAST,GAAM,OAAOtM,EAAG,KAAK,CAAC4F,IAAI0G,EAAKxE,GAAG5H,YAAY,WAAW8F,MAAM,CAAE,cAA8B,IAAfsG,EAAKU,MAAa,OAAUV,EAAKxE,KAAOhI,EAAImN,cAAe,CAACjN,EAAG,IAAI,CAACE,YAAY,WAAWK,MAAM,CAAC,KAAO,IAAM+L,EAAKxE,IAAI3H,GAAG,CAAC,MAAQ,SAASC,GAAgC,OAAxBA,EAAO4C,iBAAwBlD,EAAIoN,eAAeZ,EAAKxE,GAAG,IAAI,CAAChI,EAAIgB,GAAG,IAAIhB,EAAIiG,GAAGuG,EAAK/F,MAAM,QAAQ,IAAG,KAAKvG,EAAG,SAASA,EAAG,WAAW,EACruB,EACI4D,EAAkB,GCXlB/D,G,QAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,eAAe,CAAEJ,EAAI4G,QAAS1G,EAAG,MAAM,CAACE,YAAY,WAAW,CAACJ,EAAIgB,GAAG,cAAehB,EAAIgL,MAAO9K,EAAG,MAAM,CAACE,YAAY,SAAS,CAACJ,EAAIgB,GAAG,WAAWhB,EAAIiG,GAAGjG,EAAIgL,UAAU9K,EAAG,MAAM,CAACA,EAAG,MAAM,CAACkD,IAAI,aAAaL,SAAS,CAAC,UAAY/C,EAAIiG,GAAGjG,EAAIqN,oBAAoBnN,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAAEJ,EAAIsN,SAAUpN,EAAG,cAAc,CAACE,YAAY,YAAYK,MAAM,CAAC,GAAKT,EAAIsN,SAASb,OAAO,CAACvM,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIgB,GAAG,SAASd,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIgB,GAAGhB,EAAIiG,GAAGjG,EAAIsN,SAAS3M,WAAWT,EAAG,MAAM,CAACE,YAAY,oBAAqBJ,EAAIuN,SAAUrN,EAAG,cAAc,CAACE,YAAY,YAAYK,MAAM,CAAC,GAAKT,EAAIuN,SAASd,OAAO,CAACvM,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIgB,GAAG,SAASd,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIgB,GAAGhB,EAAIiG,GAAGjG,EAAIuN,SAAS5M,WAAWT,EAAG,MAAM,CAACE,YAAY,qBAAqB,QACx7B,GACI0D,EAAkB,GCgCtB,GACA0J,MAAA,CACAC,IAAAC,OACAJ,SAAAK,OACAJ,SAAAI,QAEA5J,OACA,OACAsJ,gBAAA,GACAzG,SAAA,EACAoE,MAAA,KAEA,EACA4C,MAAA,CACAH,IAAA,CACAI,WAAA,EACA,cAAAJ,GACA,KAAA7G,SAAA,EACA,KAAAoE,MAAA,KACA,IACA,MAAA8C,QAAA,YAAAL,QACA,KAAAJ,gBAAAS,EAAAC,QACA,KAAApF,WAAA,KACA,KAAAqF,iBACA,KAAAC,MAAA,oBAEA,OAAAlJ,GACA,KAAAiG,MAAAjG,EAAAwB,QACA,KAAA8G,gBAAA,iBACA,SACA,KAAAzG,SAAA,CACA,CACA,IAGA1C,QAAA,CACAgK,QAAAzH,GACA,IAAA0H,EAAA1H,EACA2H,cACAxC,QAAA,YACAA,QAAA,oBACAA,QAAA,cACAA,QAAA,UACAA,QAAA,UAKA,OAJAuC,IAAA,WAAAE,KAAAF,KAEAA,EAAA,mBAAAA,EAAA,IAAAG,KAAAC,SAAAC,SAAA,IAAAC,UAAA,OAEAN,CACA,EACAH,iBACA,QAAA7J,MAAAuK,WAAA,CACA,MAAAC,EAAA,KAAAxK,MAAAuK,WAAAE,iBAAA,UACAD,EAAAE,SAAAC,IACAA,EAAA9G,GAAA,KAAAkG,QAAAY,EAAAC,YAAA,IAGA,MAAAC,EAAA,KAAA7K,MAAAuK,WAAAE,iBAAA,YACAI,EAAAH,SAAAI,IACAA,EAAAC,UAAAC,IAAA,WAGA,MAAAC,EAAA,KAAAjL,MAAAuK,WAAAE,iBAAA,gBACAQ,EAAAP,SAAA/G,IACAA,EAAAuH,iBAAA,SAAAtK,IACAA,EAAA7B,iBACA,MAAA8E,EAAAF,EAAAwH,aAAA,QAAAb,UAAA,GACAc,EAAA3H,SAAAC,eAAAG,GACAuH,GACAA,EAAAC,eAAA,CAAAC,SAAA,UACA,GACA,GAEA,CACA,IC5G2P,I,UCQvPnK,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIF,EAAeA,EAAiB,Q,oBCkGhC,GACAoK,WAAA,CAAAC,YAAA,EAAAC,OAAA,IAAAC,MAAAA,EAAAA,GACA9L,OACA,OACAqI,KAAA,CACA,CAAAE,MAAA,SAAAC,MAAA,CACA,CAAA5L,KAAA,OAAA8L,KAAA,iBACA,CAAA9L,KAAA,OAAA8L,KAAA,qBACA,CAAA9L,KAAA,OAAA8L,KAAA,mBAEA,CAAAH,MAAA,OAAAC,MAAA,CACA,CAAA5L,KAAA,OAAA8L,KAAA,gBACA,CAAA9L,KAAA,UAAA8L,KAAA,uBACA,CAAA9L,KAAA,OAAA8L,KAAA,sBACA,CAAA9L,KAAA,cAAA8L,KAAA,yBACA,CAAA9L,KAAA,QAAA8L,KAAA,yBAEA,CAAAH,MAAA,OAAAC,MAAA,CACA,CAAA5L,KAAA,yBAAA8L,KAAA,2BACA,CAAA9L,KAAA,qBAAA8L,KAAA,qBACA,CAAA9L,KAAA,gCAAA8L,KAAA,2BACA,CAAA9L,KAAA,mBAAA8L,KAAA,qBACA,CAAA9L,KAAA,2BAAA8L,KAAA,kBACA,CAAA9L,KAAA,yBAAA8L,KAAA,oBACA,CAAA9L,KAAA,gBAAA8L,KAAA,iBACA,CAAA9L,KAAA,oCAAA8L,KAAA,6BACA,CAAA9L,KAAA,oCAAA8L,KAAA,6BACA,CAAA9L,KAAA,yCAAA8L,KAAA,qCAEA,CAAAH,MAAA,QAAAC,MAAA,CACA,CAAA5L,KAAA,WAAA8L,KAAA,wBACA,CAAA9L,KAAA,SAAA8L,KAAA,kCAEA,CAAAH,MAAA,OAAAC,MAAA,CACA,CAAA5L,KAAA,OAAA8L,KAAA,wBACA,CAAA9L,KAAA,OAAA8L,KAAA,0BAEA,CAAAH,MAAA,KAAAC,MAAA,CACA,CAAA5L,KAAA,YAAA8L,KAAA,4BAGAQ,IAAA,GACA6C,SAAA,GACA3C,YAAA,KACA4C,kBAAA,EAEAlE,UAAA,EACAmE,YAAA,EACAlE,gBAAA,EACAE,YAAA,EAEAiE,sBAAA,EAEA,EACAC,SAAA,CACAtD,aACA,YAAAuD,OAAAC,OAAA3C,KAAA,SACA,EACA4C,cACA,YAAAF,OAAA1D,IACA,GAEA6D,UACA,KAAAC,eACA,KAAAC,iBACA,EACA9I,UACA,KAAA+I,kBAGA,MAAAC,EAAAC,aAAAC,QAAA,6BACAF,IACA,KAAAT,sBAAAY,SAAAH,EAAA,KAGA,KAAA/H,WAAA,KACA,MAAAmI,EAAA,KAAA3M,MAAA2M,YACAA,GACAA,EAAAzB,iBAAA,cAAA0B,qBAGA,MAAAC,EAAA,KAAA7M,MAAA6M,QACAA,GACAA,EAAA3B,iBAAA,cAAA4B,qBAGA,KAAAC,8BAAA,IAGAC,OAAA9B,iBAAA,cAAA+B,cACA,KAAAZ,iBACA,EACAhJ,gBACA,MAAAsJ,EAAA,KAAA3M,MAAA2M,YACAA,GACAA,EAAAO,oBAAA,cAAAN,qBAGA,MAAAC,EAAA,KAAA7M,MAAA6M,QACAA,GACAA,EAAAK,oBAAA,cAAAJ,qBAGAE,OAAAE,oBAAA,cAAAD,aACA,EACAxD,MAAA,CACA,gBACA,KAAA6C,kBACA,KAAA9H,WAAA,KACAwC,YAAA,KACA,KAAA+F,8BAAA,GACA,MAEA,GAEAhN,QAAA,CACAgK,QAAAzH,GACA,IAAA0H,EAAA1H,EACA2H,cACAxC,QAAA,YACAA,QAAA,oBACAA,QAAA,cACAA,QAAA,UACAA,QAAA,UAKA,OAJAuC,IAAA,WAAAE,KAAAF,KAEAA,EAAA,eAAAA,EAAA,IAAAhH,KAAAmK,OAEAnD,CACA,EACAoC,eACA,KAAAT,SAAA,GACA,KAAA1D,KAAAyC,SAAAxC,IACAA,EAAAE,MAAAsC,SAAArC,IACA,KAAAsD,SAAAjH,KAAA,CACAlI,KAAA6L,EAAA7L,KACA8L,KAAAD,EAAAC,KACAJ,SAAAA,EAAAC,OACA,GACA,GAEA,EACAO,cACA,MAAA0E,EAAA,KAAAzB,SAAA0B,WAAAC,GAAAA,EAAAhF,OAAA,KAAA4D,cACA,OAAAkB,EAAA,OAAAzB,SAAAyB,EAAA,OACA,EACAzE,cACA,MAAAyE,EAAA,KAAAzB,SAAA0B,WAAAC,GAAAA,EAAAhF,OAAA,KAAA4D,cACA,WAAAkB,GAAAA,EAAA,KAAAzB,SAAAvH,OAAA,OAAAuH,SAAAyB,EAAA,OACA,EACAxE,WACA,KAAApE,WAAA,KACA,MAAAmI,EAAA,KAAA3M,MAAA2M,YACAY,EAAAZ,EAAAa,cAAA,gBACA,IAAAD,EAAA,OACA,MAAA/C,EAAA+C,EAAA9C,iBAAA,UACA,KAAA3B,IAAAjL,MAAA4P,KAAAjD,GAAApM,KAAAsP,IAEA,MAAAC,EAAAD,EAAA7J,GACAA,EAAA8J,GAAA,KAAA5D,QAAA2D,EAAA9C,aAKA,OAHA+C,IACAD,EAAA7J,GAAAA,GAEA,CACAA,GAAAA,EACAvB,KAAAoL,EAAA9C,YACA7B,MAAA2D,SAAAgB,EAAAE,QAAAtD,UAAA,IACA,IAEA,KAAA9F,UAAA,KAAAoI,oBAAA,GAEA,EACAN,kBACA,MAAAJ,EAAA,KAAAF,OAAA1D,KACA,UAAAJ,KAAA,KAAAD,KACA,UAAAI,KAAAH,EAAAE,MACA,GAAAC,EAAAC,OAAA4D,EAEA,YADA,KAAA2B,iBAAAxF,EAAA7L,KAKA,EACAyM,eAAApF,GACA,MAAAiK,EAAA,GACA,KAAAlC,kBAAA,EACA,KAAA5C,YAAAnF,EACA,MAAA8I,EAAA,KAAA3M,MAAA2M,YACAY,EAAAZ,EAAAa,cAAA,gBACAO,EAAAtK,SAAAC,eAAAG,GACA,GAAAkK,EAAA,CACA,MAAAC,EAAAD,EAAAC,UAAAT,EAAAS,UACArB,EAAAxF,UAAA6G,EAAAF,CACA,CACA9G,YAAA,KACA,KAAA4E,kBAAA,IACA,IACA,EACAgB,sBACA,QAAAhB,iBAAA,OACA,MAAAkC,EAAA,GACAnB,EAAA,KAAA3M,MAAA2M,YACAY,EAAAZ,EAAAa,cAAA,gBACA,IAAAD,EAAA,OACA,MAAA/C,EAAA+C,EAAA9C,iBAAA,UACA,IAAAwD,EAAA,KACA,MAAA9G,EAAAwF,EAAAxF,UACA,QAAA+G,EAAA1D,EAAApG,OAAA,EAAA8J,GAAA,EAAAA,IAAA,CACA,MAAAvD,EAAAH,EAAA0D,GACA,GAAAvD,EAAAqD,UAAAF,GAAA3G,EAAA,CACA8G,EAAAtD,EAAA9G,GACA,KACA,CACA,CACA,KAAAmF,YAAAiF,CACA,EACA1F,aAAAD,GACA,YAAA0D,OAAA1D,OAAAA,IACA,eAAA0D,OAAA1D,MAAA,gBAAA0D,OAAA1D,OAAA,KAAAL,KAAA,GAAAG,MAAA,GAAAE,OAAAA,CAEA,EAEA+D,kBACA,KAAAR,YAAAmB,OAAAmB,WACA,MAAAC,EAAA,KAAA1G,SACA,KAAAA,SAAA,KAAAmE,aAAA,IAGAuC,IAAA,KAAA1G,UACA,KAAAC,gBAAA,EACA,KAAAE,YAAA,IAGAuG,GAAA,KAAA1G,WACA,KAAAC,gBAAA,EACA,KAAAE,YAAA,EAEA,EAEAoF,eACAoB,aAAA,KAAAC,aACA,KAAAA,YAAAtH,YAAA,KACA,KAAAqF,iBAAA,GACA,IACA,EAEAzE,gBACA,KAAAD,gBAAA,KAAAA,eACA,KAAAA,gBAAA,KAAAE,aACA,KAAAA,YAAA,EAEA,EAEAC,YACA,KAAAD,YAAA,KAAAA,WACA,KAAAA,YAAA,KAAAF,iBACA,KAAAA,gBAAA,EAEA,EAEAK,eACA,KAAAL,gBAAA,CACA,EAEAkB,WACA,KAAAhB,YAAA,CACA,EAEAE,iBACA,KAAAJ,gBAAA,EACA,KAAAE,YAAA,CACA,EAEAW,kBACA,KAAAd,WACA,KAAAC,gBAAA,EAEA,EAEAmF,sBACA,MAAAD,EAAA,KAAA7M,MAAA6M,QACAA,IACA,KAAAf,sBAAAe,EAAA1F,UACAqF,aAAA+B,QAAA,4BAAA1B,EAAA1F,UAAAkD,YAEA,EAEA0C,+BACA,MAAAF,EAAA,KAAA7M,MAAA6M,QACAA,GAAA,KAAAf,uBAAA,GACA0C,uBAAA,KACA3B,EAAA1F,UAAA,KAAA2E,qBAAA,GAGA,IC5ZwP,ICQpP,GAAY,OACd,EACAlQ,EACA+D,GACA,EACA,KACA,WACA,MAIF,EAAe,EAAiB,O,sBCnBhC,IAAIvB,EAAM,CACT,qBAAsB,CACrB,IACA,KAED,yBAA0B,CACzB,KACA,KAED,uBAAwB,CACvB,IACA,KAED,gBAAiB,CAChB,IACA,IAED,kBAAmB,CAClB,KACA,KAED,qBAAsB,CACrB,KACA,KAED,oBAAqB,CACpB,KACA,KAED,mBAAoB,CACnB,KACA,KAED,uBAAwB,CACvB,KACA,KAED,cAAe,CACd,KACA,KAED,yBAA0B,CACzB,KACA,KAED,mBAAoB,CACnB,KACA,KAED,8BAA+B,CAC9B,KACA,KAED,sBAAuB,CACtB,KACA,KAED,mBAAoB,CACnB,KACA,KAED,eAAgB,CACf,KACA,KAED,sBAAuB,CACtB,KACA,KAED,2BAA4B,CAC3B,KACA,KAED,2BAA4B,CAC3B,KACA,IAED,iCAAkC,CACjC,KACA,KAED,eAAgB,CACf,KACA,KAED,sBAAuB,CACtB,KACA,KAED,eAAgB,CACf,KACA,MAGF,SAASqQ,EAAoBC,GAC5B,IAAIC,EAAoBzQ,EAAEE,EAAKsQ,GAC9B,OAAO5H,QAAQC,UAAU6H,MAAK,WAC7B,IAAIhO,EAAI,IAAIiO,MAAM,uBAAyBH,EAAM,KAEjD,MADA9N,EAAEkO,KAAO,mBACHlO,CACP,IAGD,IAAImO,EAAM3Q,EAAIsQ,GAAM7K,EAAKkL,EAAI,GAC7B,OAAOJ,EAAoB/N,EAAEmO,EAAI,IAAIH,MAAK,WACzC,OAAOD,EAAoB9K,EAC5B,GACD,CACA4K,EAAoBO,KAAO,WAAa,OAAOxF,OAAOwF,KAAK5Q,EAAM,EACjEqQ,EAAoB5K,GAAK,IACzB8F,EAAOsF,QAAUR,C", "sources": ["webpack://portal-ui/./src/components/common/mider/Mider.vue", "webpack://portal-ui/src/components/common/mider/Mider.vue", "webpack://portal-ui/./src/components/common/mider/Mider.vue?6cd8", "webpack://portal-ui/./src/components/common/mider/Mider.vue?9943", "webpack://portal-ui/./src/components/common/mider/chatAi.vue", "webpack://portal-ui/src/components/common/mider/chatAi.vue", "webpack://portal-ui/./src/components/common/mider/chatAi.vue?09db", "webpack://portal-ui/./src/components/common/mider/chatAi.vue?3a6e", "webpack://portal-ui/./src/views/HelpView.vue", "webpack://portal-ui/./src/views/HelpContent.vue", "webpack://portal-ui/src/views/HelpContent.vue", "webpack://portal-ui/./src/views/HelpContent.vue?cbaf", "webpack://portal-ui/./src/views/HelpContent.vue?7799", "webpack://portal-ui/src/views/HelpView.vue", "webpack://portal-ui/./src/views/HelpView.vue?9498", "webpack://portal-ui/./src/views/HelpView.vue?a84f", "webpack://portal-ui/./src/docs/ lazy ^\\.\\/.*\\.md$ chunkName: docs namespace object"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mider-container\"},[_c('div',{staticClass:\"mider-sidebar\"},[_c('div',{staticClass:\"icon-wrapper\"},[_c('div',{staticClass:\"icon-item\",on:{\"mouseenter\":function($event){return _vm.showPopup('wechat')},\"mouseleave\":function($event){return _vm.hidePopup('wechat')}}},[_c('i',{staticClass:\"iconfont icon-wechat\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"24\",\"height\":\"24\"}},[_c('path',{attrs:{\"d\":\"M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5-2.9 10.5-4.6 16.3-4.6 3 0 5.9 0.5 8.7 1.4 35.9 10.5 74.6 16.2 114.2 16.2 9.8 0 19.5-0.3 29-1C624.9 597 702.1 497.6 702.1 378.1c0-0.2-0.1-0.4-0.1-0.7 0 0-0.1-0.3-0.1-0.5-0.8 0-1.8-0.1-2.8-0.1-3.1 0-6.2 0.2-9 0.6z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M380.5 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7zM534.3 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M683.5 673.6c82.1 0 151.2-56 171.5-131.8 4.1-15.2-9.3-29.5-24.9-26.6-3.5 0.6-7.1 1-10.7 1-34.4 0-62.4-28-62.4-62.4 0-1.6 0.1-3.3 0.2-4.9 0.8-19.3-24.5-28.7-35.7-13.5-39.1 53-102.7 87.5-174.5 87.5-12.3 0-24.4-1-36.1-3-5.2-0.9-10.5 0.8-14.1 4.5-4.6 4.6-16.7 17.2-16.7 17.2l-0.6 0.6c-5.4 5.4-8.7 12.3-9.5 19.8-0.9 8.5 1.9 17.1 7.7 23.6l1.3 1.3c6.3 7.5 15.6 11.7 25.6 11.7 7.1 0 14.1-2.3 19.7-6.5l10.6-7.8c6.2-4.5 14.8-3.3 19.3 2.8 4.3 5.8 3.4 14-2.1 18.8-14.4 12.6-32.9 19.7-51.9 19.7-4.3 0-8.7-0.3-13-1-21.3-3.5-40.2-14.4-54.3-31.3l-0.3-0.3c-12.9-15.7-19.4-34.9-18.3-54.5 0.8-14.5 6.4-28.5 16.1-39.7 1.2-1.4 2.5-2.6 3.8-3.9l31.5-31.5c1.9-1.9 3.8-3.9 5.4-6.1 5.8-7.7 8.9-17 8.9-26.8 0-11.3-4.2-22.1-11.8-30.4-12.4-13.5-31.5-15.2-48.5-11.7-91.3 18.9-160.7 100.1-160.8 196.9 0 109.5 89.1 198.6 198.6 198.6 36.7 0 71.9-9.9 102.8-28.5 5.7-3.4 12.8-3.3 18.5 0.2 36.7 22.7 79.1 35.3 123.2 35.3z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M770.9 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M602.4 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\",\"fill\":\"#82c91e\"}})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.activePopup === 'wechat'),expression:\"activePopup === 'wechat'\"}],staticClass:\"popup-container wechat-popup\"},[_c('div',{staticClass:\"popup-content\"},[_c('h3',[_vm._v(\"微信扫码咨询客服\")]),_c('div',{staticClass:\"qr-code\"},[_c('img',{attrs:{\"src\":_vm.wechatQRCode,\"alt\":\"微信客服二维码\"}})])])])]),_c('div',{staticClass:\"icon-item\",on:{\"mouseenter\":function($event){return _vm.showPopup('contact')},\"mouseleave\":function($event){return _vm.hidePopup('contact')}}},[_c('i',{staticClass:\"iconfont icon-phone\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"24\",\"height\":\"24\"}},[_c('path',{attrs:{\"d\":\"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7c-20.6 47.8-49.6 90.6-86.4 127.3-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3-18.5 0-35.8 7.2-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-0.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4z\",\"fill\":\"#1677ff\"}})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.activePopup === 'contact'),expression:\"activePopup === 'contact'\"}],staticClass:\"popup-container contact-popup\"},[_c('div',{staticClass:\"popup-content\"},[_c('h3',[_vm._v(\"商务合作请联系电话\")]),_c('p',{staticClass:\"phone-number\"},[_vm._v(\"13913283376\")]),_c('p',[_vm._v(\"使用问题请咨询微信客服\")]),_c('div',{staticClass:\"qr-code\"},[_c('img',{attrs:{\"src\":_vm.contactQRCode,\"alt\":\"联系电话二维码\"}})])])])]),_c('div',{staticClass:\"icon-item\",on:{\"click\":_vm.showFeedbackModal,\"mouseenter\":_vm.showTooltip,\"mouseleave\":_vm.hideTooltip}},[_c('i',{staticClass:\"iconfont icon-feedback\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"24\",\"height\":\"24\"}},[_c('path',{attrs:{\"d\":\"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zM293 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z\",\"fill\":\"#fa8c16\"}}),_c('path',{attrs:{\"d\":\"M894 345c-48.1-66-115.3-110.1-189-130v0.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l0.8 132.6c0 3.2 0.5 6.4 1.5 9.4 5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.4 11.9 68.4 18 104.7 18 72.2 0 143.1-21.4 205.3-61.8 57.4-37.3 104-89.9 131.8-150.1 23.9-52 35.2-106.8 33.6-160.9 28.8-29.9 50.4-64.7 63.6-103 17.8-52.7 13.1-108.7-14-155.1z m-68 106.7c-25.8 53.9-71.3 89.7-126.2 99.8-5.7 1-9.7 3.9-13 10.3-8.1 16.1-19.1 30.6-32.7 43.1-11.8 10.9-24.9 20.5-42.9 14.6-9.7-3.2-15.2-9.4-19.1-20.8-6.6-19.2-12.9-18.2-27.2-9.9-81.5 47.5-165.2 37.3-232.5-25.3-30.5-28.6-54.2-65.1-62.7-109-8.4-44 0.4-89.6 26.7-130.5 22.9-35.6 52.4-62 89.9-79.3 24.8-11.5 50.9-18.7 78.5-20.5 101.5-6.2 197.2 41.2 244.1 132.2 12.8 24.8 25.6 60.7 30.4 87.3 5.5 30.3 2.9 53.5-13.3 87z\",\"fill\":\"#fa8c16\"}})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showFeedbackTooltip),expression:\"showFeedbackTooltip\"}],staticClass:\"tooltip\"},[_vm._v(\" 反馈与建议 \")])])])]),(_vm.showModal)?_c('div',{staticClass:\"modal-overlay\",on:{\"click\":function($event){if($event.target !== $event.currentTarget)return null;return _vm.closeFeedbackModal.apply(null, arguments)}}},[_c('div',{staticClass:\"feedback-modal\"},[_c('div',{staticClass:\"modal-header\"},[_c('h3',[_vm._v(\"反馈与建议\")]),_c('span',{staticClass:\"close-btn\",on:{\"click\":_vm.closeFeedbackModal}},[_vm._v(\"×\")])]),_c('div',{staticClass:\"modal-body\"},[_c('div',{staticClass:\"alert alert-warning\"},[_c('i',{staticClass:\"iconfont icon-warning\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"16\",\"height\":\"16\"}},[_c('path',{attrs:{\"d\":\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z\",\"fill\":\"#faad14\"}})])]),_vm._v(\" 您的反馈我们将认真对待，不断优化产品功能和体验 \")]),_c('div',{staticClass:\"form-group\"},[_c('label',{staticClass:\"required\"},[_vm._v(\"问题类型：\")]),_c('div',{staticClass:\"select-wrapper\"},[_c('select',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.feedback.type),expression:\"feedback.type\"}],attrs:{\"required\":\"\"},on:{\"change\":function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = \"_value\" in o ? o._value : o.value;return val}); _vm.$set(_vm.feedback, \"type\", $event.target.multiple ? $$selectedVal : $$selectedVal[0])}}},[_c('option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_c('option',{attrs:{\"value\":\"功能建议\"}},[_vm._v(\"功能建议\")]),_c('option',{attrs:{\"value\":\"产品故障\"}},[_vm._v(\"产品故障\")]),_c('option',{attrs:{\"value\":\"体验不佳\"}},[_vm._v(\"体验不佳\")]),_c('option',{attrs:{\"value\":\"账户相关\"}},[_vm._v(\"账户相关\")]),_c('option',{attrs:{\"value\":\"其他\"}},[_vm._v(\"其他\")])])]),(!_vm.feedback.type && _vm.showErrors)?_c('p',{staticClass:\"error-text\"},[_vm._v(\"请选择问题类型\")]):_vm._e()]),_c('div',{staticClass:\"form-group\"},[_c('label',{staticClass:\"required\"},[_vm._v(\"问题描述：\")]),_c('textarea',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.feedback.description),expression:\"feedback.description\"}],attrs:{\"placeholder\":\"请输入\",\"required\":\"\"},domProps:{\"value\":(_vm.feedback.description)},on:{\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.feedback, \"description\", $event.target.value)}}}),(!_vm.feedback.description && _vm.showErrors)?_c('p',{staticClass:\"error-text\"},[_vm._v(\"请输入问题描述\")]):_vm._e()]),_c('div',{staticClass:\"form-group\"},[_c('label',{staticClass:\"required1\"},[_vm._v(\"问题截图：\")]),_c('div',{staticClass:\"image-uploader\",on:{\"click\":_vm.triggerFileUpload,\"dragover\":function($event){$event.preventDefault();},\"drop\":function($event){$event.preventDefault();return _vm.onFileDrop.apply(null, arguments)}}},[_c('input',{ref:\"fileInput\",staticStyle:{\"display\":\"none\"},attrs:{\"type\":\"file\",\"accept\":\"image/*\"},on:{\"change\":_vm.onFileChange}}),(!_vm.feedback.image)?_c('div',{staticClass:\"upload-placeholder\"},[_c('i',{staticClass:\"iconfont icon-upload\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"28\",\"height\":\"28\"}},[_c('path',{attrs:{\"d\":\"M518.3 459c-3.2-4.1-9.4-4.1-12.6 0l-112 141.7c-4.1 5.2-0.4 12.9 6.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z\",\"fill\":\"#bfbfbf\"}}),_c('path',{attrs:{\"d\":\"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c26.1 6.9 49.6 22.5 66.3 43.8 16.4 21 25.4 45.9 26.3 72.3 1.1 33.9-11.4 66.5-34.9 90.7-23.6 24.4-55.3 37.7-89 37.7h-40c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C758.5 760 848 670.5 848 560c0-92.7-63.1-170.7-148.6-193.3z\",\"fill\":\"#bfbfbf\"}})])]),_c('p',[_vm._v(\"点击/拖拽至此处添加图片\")])]):_c('div',{staticClass:\"preview-container\"},[_c('img',{staticClass:\"image-preview\",attrs:{\"src\":_vm.feedback.imagePreview,\"alt\":\"问题截图预览\"}}),_c('div',{staticClass:\"remove-image\",on:{\"click\":function($event){$event.stopPropagation();return _vm.removeImage.apply(null, arguments)}}},[_vm._v(\"×\")])])])])]),_c('div',{staticClass:\"modal-footer\"},[_c('button',{staticClass:\"btn btn-cancel\",on:{\"click\":_vm.closeFeedbackModal}},[_vm._v(\"取消\")]),_c('button',{staticClass:\"btn btn-submit\",on:{\"click\":_vm.confirmSubmit}},[_vm._v(\"提交\")])])])]):_vm._e(),(_vm.showConfirmation)?_c('div',{staticClass:\"modal-overlay\"},[_c('div',{staticClass:\"confirmation-dialog\"},[_c('div',{staticClass:\"confirmation-icon\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"32\",\"height\":\"32\"}},[_c('path',{attrs:{\"d\":\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z\",\"fill\":\"#52c41a\"}})])]),_c('div',{staticClass:\"confirmation-title\"},[_vm._v(\"提交成功\")]),_c('div',{staticClass:\"confirmation-message\"},[_vm._v(\"感谢您的反馈，我们会尽快处理\")]),_c('div',{staticClass:\"confirmation-actions\"},[_c('button',{staticClass:\"btn btn-primary\",on:{\"click\":_vm.closeConfirmation}},[_vm._v(\"确定\")])])])]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"mider-container\">\r\n    <!-- Floating sidebar with icons -->\r\n    <div class=\"mider-sidebar\">\r\n<!--      <div class=\"coupon-tag\">-->\r\n<!--        <span>领</span>-->\r\n<!--        <span>优</span>-->\r\n<!--        <span>惠</span>-->\r\n<!--        <span>券</span>-->\r\n<!--      </div>-->\r\n\r\n      <div class=\"icon-wrapper\">\r\n        <div class=\"icon-item\" @mouseenter=\"showPopup('wechat')\" @mouseleave=\"hidePopup('wechat')\">\r\n          <i class=\"iconfont icon-wechat\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5-2.9 10.5-4.6 16.3-4.6 3 0 5.9 0.5 8.7 1.4 35.9 10.5 74.6 16.2 114.2 16.2 9.8 0 19.5-0.3 29-1C624.9 597 702.1 497.6 702.1 378.1c0-0.2-0.1-0.4-0.1-0.7 0 0-0.1-0.3-0.1-0.5-0.8 0-1.8-0.1-2.8-0.1-3.1 0-6.2 0.2-9 0.6z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M380.5 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7zM534.3 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M683.5 673.6c82.1 0 151.2-56 171.5-131.8 4.1-15.2-9.3-29.5-24.9-26.6-3.5 0.6-7.1 1-10.7 1-34.4 0-62.4-28-62.4-62.4 0-1.6 0.1-3.3 0.2-4.9 0.8-19.3-24.5-28.7-35.7-13.5-39.1 53-102.7 87.5-174.5 87.5-12.3 0-24.4-1-36.1-3-5.2-0.9-10.5 0.8-14.1 4.5-4.6 4.6-16.7 17.2-16.7 17.2l-0.6 0.6c-5.4 5.4-8.7 12.3-9.5 19.8-0.9 8.5 1.9 17.1 7.7 23.6l1.3 1.3c6.3 7.5 15.6 11.7 25.6 11.7 7.1 0 14.1-2.3 19.7-6.5l10.6-7.8c6.2-4.5 14.8-3.3 19.3 2.8 4.3 5.8 3.4 14-2.1 18.8-14.4 12.6-32.9 19.7-51.9 19.7-4.3 0-8.7-0.3-13-1-21.3-3.5-40.2-14.4-54.3-31.3l-0.3-0.3c-12.9-15.7-19.4-34.9-18.3-54.5 0.8-14.5 6.4-28.5 16.1-39.7 1.2-1.4 2.5-2.6 3.8-3.9l31.5-31.5c1.9-1.9 3.8-3.9 5.4-6.1 5.8-7.7 8.9-17 8.9-26.8 0-11.3-4.2-22.1-11.8-30.4-12.4-13.5-31.5-15.2-48.5-11.7-91.3 18.9-160.7 100.1-160.8 196.9 0 109.5 89.1 198.6 198.6 198.6 36.7 0 71.9-9.9 102.8-28.5 5.7-3.4 12.8-3.3 18.5 0.2 36.7 22.7 79.1 35.3 123.2 35.3z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M770.9 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M602.4 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\" fill=\"#82c91e\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- WeChat QR code popup -->\r\n          <div class=\"popup-container wechat-popup\" v-show=\"activePopup === 'wechat'\">\r\n            <div class=\"popup-content\">\r\n              <h3>微信扫码咨询客服</h3>\r\n              <div class=\"qr-code\">\r\n                <img :src=\"wechatQRCode\" alt=\"微信客服二维码\">\r\n              </div>\r\n<!--              <div class=\"popup-footer\">-->\r\n<!--                <button class=\"btn-consult\">桌面版微信点击咨询客服</button>-->\r\n<!--              </div>-->\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"icon-item\" @mouseenter=\"showPopup('contact')\" @mouseleave=\"hidePopup('contact')\">\r\n          <i class=\"iconfont icon-phone\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7c-20.6 47.8-49.6 90.6-86.4 127.3-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3-18.5 0-35.8 7.2-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-0.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4z\" fill=\"#1677ff\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- Contact information popup -->\r\n          <div class=\"popup-container contact-popup\" v-show=\"activePopup === 'contact'\">\r\n            <div class=\"popup-content\">\r\n              <h3>商务合作请联系电话</h3>\r\n              <p class=\"phone-number\">13913283376</p>\r\n              <p>使用问题请咨询微信客服</p>\r\n              <div class=\"qr-code\">\r\n                <img :src=\"contactQRCode\" alt=\"联系电话二维码\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"icon-item\" @click=\"showFeedbackModal\" @mouseenter=\"showTooltip\" @mouseleave=\"hideTooltip\">\r\n          <i class=\"iconfont icon-feedback\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zM293 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z\" fill=\"#fa8c16\"></path>\r\n              <path d=\"M894 345c-48.1-66-115.3-110.1-189-130v0.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l0.8 132.6c0 3.2 0.5 6.4 1.5 9.4 5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.4 11.9 68.4 18 104.7 18 72.2 0 143.1-21.4 205.3-61.8 57.4-37.3 104-89.9 131.8-150.1 23.9-52 35.2-106.8 33.6-160.9 28.8-29.9 50.4-64.7 63.6-103 17.8-52.7 13.1-108.7-14-155.1z m-68 106.7c-25.8 53.9-71.3 89.7-126.2 99.8-5.7 1-9.7 3.9-13 10.3-8.1 16.1-19.1 30.6-32.7 43.1-11.8 10.9-24.9 20.5-42.9 14.6-9.7-3.2-15.2-9.4-19.1-20.8-6.6-19.2-12.9-18.2-27.2-9.9-81.5 47.5-165.2 37.3-232.5-25.3-30.5-28.6-54.2-65.1-62.7-109-8.4-44 0.4-89.6 26.7-130.5 22.9-35.6 52.4-62 89.9-79.3 24.8-11.5 50.9-18.7 78.5-20.5 101.5-6.2 197.2 41.2 244.1 132.2 12.8 24.8 25.6 60.7 30.4 87.3 5.5 30.3 2.9 53.5-13.3 87z\" fill=\"#fa8c16\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- Tooltip for feedback icon -->\r\n          <div class=\"tooltip\" v-show=\"showFeedbackTooltip\">\r\n            反馈与建议\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- Feedback modal - Reduced size -->\r\n    <div class=\"modal-overlay\" v-if=\"showModal\" @click.self=\"closeFeedbackModal\">\r\n      <div class=\"feedback-modal\">\r\n        <div class=\"modal-header\">\r\n          <h3>反馈与建议</h3>\r\n          <span class=\"close-btn\" @click=\"closeFeedbackModal\">×</span>\r\n        </div>\r\n\r\n        <div class=\"modal-body\">\r\n          <div class=\"alert alert-warning\">\r\n            <i class=\"iconfont icon-warning\">\r\n              <svg viewBox=\"0 0 1024 1024\" width=\"16\" height=\"16\">\r\n                <path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z\" fill=\"#faad14\"></path>\r\n              </svg>\r\n            </i>\r\n            您的反馈我们将认真对待，不断优化产品功能和体验\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required\">问题类型：</label>\r\n            <div class=\"select-wrapper\">\r\n              <select v-model=\"feedback.type\" required>\r\n                <option value=\"\">请选择</option>\r\n                <option value=\"功能建议\">功能建议</option>\r\n                <option value=\"产品故障\">产品故障</option>\r\n                <option value=\"体验不佳\">体验不佳</option>\r\n                <option value=\"账户相关\">账户相关</option>\r\n                <option value=\"其他\">其他</option>\r\n              </select>\r\n            </div>\r\n            <p class=\"error-text\" v-if=\"!feedback.type && showErrors\">请选择问题类型</p>\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required\">问题描述：</label>\r\n            <textarea v-model=\"feedback.description\" placeholder=\"请输入\" required></textarea>\r\n            <p class=\"error-text\" v-if=\"!feedback.description && showErrors\">请输入问题描述</p>\r\n          </div>\r\n\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required1\">问题截图：</label>\r\n            <div\r\n                class=\"image-uploader\"\r\n                @click=\"triggerFileUpload\"\r\n                @dragover.prevent\r\n                @drop.prevent=\"onFileDrop\"\r\n            >\r\n              <input\r\n                  type=\"file\"\r\n                  ref=\"fileInput\"\r\n                  accept=\"image/*\"\r\n                  @change=\"onFileChange\"\r\n                  style=\"display: none\"\r\n              >\r\n              <div v-if=\"!feedback.image\" class=\"upload-placeholder\">\r\n                <i class=\"iconfont icon-upload\">\r\n                  <svg viewBox=\"0 0 1024 1024\" width=\"28\" height=\"28\">\r\n                    <path d=\"M518.3 459c-3.2-4.1-9.4-4.1-12.6 0l-112 141.7c-4.1 5.2-0.4 12.9 6.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z\" fill=\"#bfbfbf\"></path>\r\n                    <path d=\"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c26.1 6.9 49.6 22.5 66.3 43.8 16.4 21 25.4 45.9 26.3 72.3 1.1 33.9-11.4 66.5-34.9 90.7-23.6 24.4-55.3 37.7-89 37.7h-40c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C758.5 760 848 670.5 848 560c0-92.7-63.1-170.7-148.6-193.3z\" fill=\"#bfbfbf\"></path>\r\n                  </svg>\r\n                </i>\r\n                <p>点击/拖拽至此处添加图片</p>\r\n              </div>\r\n              <div v-else class=\"preview-container\">\r\n                <img :src=\"feedback.imagePreview\" alt=\"问题截图预览\" class=\"image-preview\">\r\n                <div class=\"remove-image\" @click.stop=\"removeImage\">×</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"modal-footer\">\r\n          <button class=\"btn btn-cancel\" @click=\"closeFeedbackModal\">取消</button>\r\n          <button class=\"btn btn-submit\" @click=\"confirmSubmit\">提交</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Confirmation Dialog -->\r\n    <div class=\"modal-overlay\" v-if=\"showConfirmation\">\r\n      <div class=\"confirmation-dialog\">\r\n        <div class=\"confirmation-icon\">\r\n          <svg viewBox=\"0 0 1024 1024\" width=\"32\" height=\"32\">\r\n            <path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z\" fill=\"#52c41a\"></path>\r\n          </svg>\r\n        </div>\r\n        <div class=\"confirmation-title\">提交成功</div>\r\n        <div class=\"confirmation-message\">感谢您的反馈，我们会尽快处理</div>\r\n        <div class=\"confirmation-actions\">\r\n          <button class=\"btn btn-primary\" @click=\"closeConfirmation\">确定</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Mider',\r\n  data() {\r\n    return {\r\n      activePopup: null,\r\n      showModal: false,\r\n      showErrors: false,\r\n      showFeedbackTooltip: false,\r\n      showConfirmation: false,\r\n      wechatQRCode: require('@/assets/images/footer/wechat.jpg'), // 替换为实际路径\r\n      contactQRCode: require('@/assets/images/footer/wechat.jpg'), // 替换为实际路径\r\n      feedback: {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    showPopup(type) {\r\n      this.activePopup = type;\r\n    },\r\n    hidePopup(type) {\r\n      if (this.activePopup === type) {\r\n        this.activePopup = null;\r\n      }\r\n    },\r\n    showTooltip() {\r\n      this.showFeedbackTooltip = true;\r\n    },\r\n    hideTooltip() {\r\n      this.showFeedbackTooltip = false;\r\n    },\r\n    showFeedbackModal() {\r\n      this.showModal = true;\r\n      this.showErrors = false;\r\n    },\r\n    closeFeedbackModal() {\r\n      this.showModal = false;\r\n      // Reset form\r\n      this.feedback = {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      };\r\n      this.showErrors = false;\r\n    },\r\n    triggerFileUpload() {\r\n      this.$refs.fileInput.click();\r\n    },\r\n    onFileChange(event) {\r\n      const file = event.target.files[0];\r\n      if (file) {\r\n        this.processImage(file);\r\n      }\r\n    },\r\n    onFileDrop(event) {\r\n      const file = event.dataTransfer.files[0];\r\n      if (file && file.type.match('image.*')) {\r\n        this.processImage(file);\r\n      }\r\n    },\r\n    processImage(file) {\r\n      if (file && file.type.match('image.*')) {\r\n        this.feedback.image = file;\r\n\r\n        // Create preview\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => {\r\n          this.feedback.imagePreview = e.target.result;\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    },\r\n    removeImage() {\r\n      this.feedback.image = null;\r\n      this.feedback.imagePreview = null;\r\n    },\r\n    confirmSubmit() {\r\n      // Validate form\r\n      this.showErrors = true;\r\n      if (!this.feedback.type || !this.feedback.description) {\r\n        return;\r\n      }\r\n\r\n      // Submit feedback\r\n      this.submitFeedback();\r\n    },\r\n    submitFeedback() {\r\n      // Here you would typically send the data to your backend\r\n\r\n      // Create FormData object if there's an image\r\n      if (this.feedback.image) {\r\n        const formData = new FormData();\r\n        formData.append('type', this.feedback.type);\r\n        formData.append('description', this.feedback.description);\r\n        formData.append('instanceId', this.feedback.instanceId);\r\n        formData.append('image', this.feedback.image);\r\n\r\n        // Send formData to your API\r\n        // this.$axios.post('/api/feedback', formData)\r\n      }\r\n\r\n      // Close feedback modal and show confirmation dialog\r\n      this.showModal = false;\r\n      this.showConfirmation = true;\r\n\r\n      // Reset form data\r\n      this.feedback = {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      };\r\n    },\r\n    closeConfirmation() {\r\n      this.showConfirmation = false;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.mider-container {\r\n  position: relative;\r\n\r\n}\r\n\r\n/* Sidebar styles */\r\n.mider-sidebar {\r\n  /* 原有定位属性 */\r\n  position: fixed;\r\n  right: 0;\r\n  top: 85%;             /* 当前纵向定位 */\r\n  transform: translateY(-50%);\r\n  z-index: 1000;\r\n\r\n  /* 新增尺寸控制 */\r\n  width: 42px;         /* 固定宽度 */\r\n  height: 50vh;         /* 视口高度的50% */\r\n  max-width: 90%;       /* 防溢出保护 */\r\n  max-height: 80vh;     /* 高度上限 */\r\n  min-height: 200px;    /* 高度下限 */\r\n  /*box-sizing: 0; !* 包含内边距 *!*/\r\n\r\n  /* 布局优化 */\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n\r\n.coupon-tag {\r\n  background-color: #ff6b6b;\r\n  color: white;\r\n  padding: 8px 12px;\r\n  border-radius: 8px 0 0 8px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.icon-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: white;\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.icon-item {\r\n  position: relative;\r\n  padding: 15px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.icon-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.icon-item i {\r\n  font-size: 24px;\r\n  color: #666;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.icon-item:hover i {\r\n  color: #1890ff;\r\n}\r\n\r\n.icon-item:hover i svg path {\r\n  fill: #1890ff;\r\n}\r\n\r\n/* Tooltip styles */\r\n.tooltip {\r\n  position: absolute;\r\n  left: -90px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background-color: #fff;\r\n  color: #333;\r\n  padding: 6px 10px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  z-index: 1000;\r\n}\r\n\r\n.tooltip:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: -6px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-width: 6px 0 6px 6px;\r\n  border-style: solid;\r\n  border-color: transparent transparent transparent white;\r\n}\r\n\r\n/* Popup styles */\r\n.popup-container {\r\n  position: absolute;\r\n  right: 60px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  width: 240px;\r\n  z-index: 1000;\r\n}\r\n\r\n.popup-container:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: -10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-width: 10px 0 10px 10px;\r\n  border-style: solid;\r\n  border-color: transparent transparent transparent white;\r\n}\r\n\r\n.popup-content {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.popup-content h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n  font-size: 16px;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.qr-code {\r\n  margin: 10px 0;\r\n}\r\n\r\n.qr-code img {\r\n  width: 150px;\r\n  height: 150px;\r\n}\r\n\r\n.phone-number {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin: 5px 0;\r\n}\r\n\r\n.popup-footer {\r\n  margin-top: 10px;\r\n}\r\n\r\n.btn-consult {\r\n  background-color: #f5f5f5;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 8px 16px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  color: #333;\r\n  width: 100%;\r\n}\r\n\r\n/* Modal styles - Reduced size */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1001;\r\n}\r\n\r\n.feedback-modal {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  width: 500px;\r\n  max-width: 100vw;\r\n  max-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.modal-header {\r\n  padding: 14px 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.modal-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  color: #333;\r\n}\r\n\r\n.close-btn {\r\n  cursor: pointer;\r\n  font-size: 20px;\r\n  color: #999;\r\n}\r\n\r\n.modal-body {\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.alert {\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 13px;\r\n}\r\n\r\n.alert-warning {\r\n  background-color: #fffbe6;\r\n  border: 1px solid #ffe58f;\r\n  font-size: 10px;\r\n  color: #d48806;\r\n}\r\n\r\n.alert i {\r\n  margin-right: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 6px;\r\n  font-size: 13px;\r\n  color: #333;\r\n}\r\n\r\n.required:before {\r\n  content: '*';\r\n  color: #ff4d4f;\r\n  margin-left: 4px;\r\n}\r\n.required1:before {\r\n  content: '';\r\n  color: #ff4d4f;\r\n  margin-left: 9px;\r\n}\r\n\r\ninput, select, textarea {\r\n  width: 100%;\r\n  padding: 8px 12px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  font-size: 13px;\r\n  transition: all 0.3s;\r\n}\r\n\r\ninput:focus, select:focus, textarea:focus {\r\n  outline: none;\r\n  border-color: #40a9ff;\r\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\ntextarea {\r\n  min-height: 70px;\r\n  resize: vertical;\r\n}\r\n\r\n.select-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.select-wrapper:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 12px;\r\n  color: #999;\r\n  pointer-events: none;\r\n}\r\n\r\n.error-text {\r\n  color: #ff4d4f;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.image-uploader {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 4px;\r\n  padding: 20px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  background-color: #fafafa;\r\n  min-height: 120px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.image-uploader:hover {\r\n  border-color: #40a9ff;\r\n}\r\n\r\n.upload-placeholder {\r\n  color: #999;\r\n}\r\n\r\n.upload-placeholder i {\r\n  font-size: 32px;\r\n  display: block;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.preview-container {\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.image-preview {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.remove-image {\r\n  position: absolute;\r\n  top: -10px;\r\n  right: -10px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.modal-footer {\r\n  padding: 10px 24px 24px;\r\n  text-align: right;\r\n}\r\n\r\n.btn {\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  border: none;\r\n}\r\n\r\n.btn-cancel {\r\n  background-color: white;\r\n  border: 1px solid #d9d9d9;\r\n  color: #333;\r\n  margin-right: 12px;\r\n}\r\n\r\n.btn-cancel:hover {\r\n  color: #40a9ff;\r\n  border-color: #40a9ff;\r\n}\r\n\r\n.btn-submit {\r\n  background-color: #1890ff;\r\n  color: white;\r\n}\r\n\r\n.btn-submit:hover {\r\n  background-color: #40a9ff;\r\n}\r\n\r\n/* For responsiveness */\r\n@media (max-width: 768px) {\r\n  .popup-container {\r\n    width: 200px;\r\n  }\r\n\r\n  .qr-code img {\r\n    width: 120px;\r\n    height: 120px;\r\n  }\r\n}\r\n\r\n.confirmation-dialog {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  /*margin-left: 50%;*/\r\n  /*text-align: left;*/\r\n  width: 500px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.confirmation-icon {\r\n  margin-left: 1%;\r\n  margin-bottom: -37px;\r\n}\r\n\r\n.confirmation-title {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  margin-left: 10%;\r\n  color: #333;\r\n}\r\n\r\n.confirmation-message {\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin-left: 10%;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.confirmation-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.btn-primary {\r\n  background-color: #1677ff;\r\n  /*margin-right: 2px;*/\r\n  color: white;\r\n  cursor: pointer;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 8px 32px;\r\n  font-size: 14px;\r\n  margin-left: 80%;\r\n}\r\n\r\n.btn-primary:hover {\r\n  background-color: #4096ff;\r\n}\r\n\r\n/* Form styles */\r\n.form-group {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.required:after {\r\n  content: '*';\r\n  color: #f5222d;\r\n  margin-left: 4px;\r\n}\r\n\r\n.select-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.select-wrapper:after {\r\n  content: '▼';\r\n  font-size: 10px;\r\n  color: #999;\r\n  position: absolute;\r\n  right: 10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  pointer-events: none;\r\n}\r\n\r\nselect {\r\n  width: 100%;\r\n  padding: 8px 10px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  background-color: white;\r\n  font-size: 14px;\r\n  appearance: none;\r\n}\r\n\r\ntextarea {\r\n  width: 100%;\r\n  padding: 8px 10px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  min-height: 100px;\r\n  resize: vertical;\r\n  font-size: 14px;\r\n}\r\n\r\n.error-text {\r\n  color: #f5222d;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.image-uploader {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  background-color: #fafafa;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.image-uploader:hover {\r\n  border-color: #1890ff;\r\n}\r\n\r\n.upload-placeholder {\r\n  color: #bfbfbf;\r\n}\r\n\r\n.upload-placeholder p {\r\n  margin-top: 8px;\r\n  font-size: 13px;\r\n}\r\n\r\n.preview-container {\r\n  position: relative;\r\n}\r\n\r\n.image-preview {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n}\r\n\r\n.remove-image {\r\n  position: absolute;\r\n  top: -10px;\r\n  right: -10px;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.modal-footer {\r\n  padding: 16px 20px;\r\n  border-top: 1px solid #f0f0f0;\r\n  text-align: right;\r\n}\r\n\r\n.btn {\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.btn-cancel {\r\n  background-color: white;\r\n  border: 1px solid #d9d9d9;\r\n  color: #666;\r\n  margin-right: 8px;\r\n}\r\n\r\n.btn-cancel:hover {\r\n  color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n.btn-submit {\r\n  background-color: #1677ff;\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.btn-submit:hover {\r\n  background-color: #4096ff;\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Mider.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Mider.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Mider.vue?vue&type=template&id=37397f14&scoped=true&\"\nimport script from \"./Mider.vue?vue&type=script&lang=js&\"\nexport * from \"./Mider.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Mider.vue?vue&type=style&index=0&id=37397f14&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"37397f14\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('div',{staticClass:\"chat-container\"},[_c('div',{staticClass:\"question-carousel\",on:{\"mouseenter\":_vm.pauseCarousel,\"mouseleave\":_vm.resumeCarousel}},[_c('transition-group',{staticClass:\"carousel-wrapper\",attrs:{\"name\":\"slide\",\"tag\":\"div\"}},_vm._l((_vm.questions),function(question,index){return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.currentQuestionIndex === index),expression:\"currentQuestionIndex === index\"}],key:question,staticClass:\"question-item\",on:{\"click\":function($event){return _vm.sendCarouselQuestion(question)},\"mouseenter\":function($event){return _vm.witde(index)}}},[_vm._v(\" \"+_vm._s(question)+\" \")])}),0)],1),_c('div',{staticClass:\"chat-icon\",class:{ 'chat-icon-active': _vm.showChat },on:{\"click\":_vm.toggleChat}},[_c('i',{staticClass:\"fas fa-comment\"})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showChat),expression:\"showChat\"}],staticClass:\"chat-window\"},[_c('div',{staticClass:\"chat-header\"},[_vm._m(0),_c('div',{staticClass:\"chat-controls\"},[_c('i',{staticClass:\"fas fa-times\",on:{\"click\":_vm.toggleChat}})])]),_c('div',{ref:\"messagesContainer\",staticClass:\"chat-messages\"},[_vm._l((_vm.messages),function(message,index){return _c('div',{key:index,class:['message', message.type]},[(message.type === 'bot')?_c('div',{staticClass:\"avatar\"},[_c('i',{staticClass:\"fas fa-robot\"})]):_vm._e(),_c('div',{staticClass:\"message-content\"},[_c('div',{staticClass:\"message-text\",domProps:{\"innerHTML\":_vm._s(_vm.formatMessage(message.text))}}),_c('div',{staticClass:\"message-time\"},[_vm._v(_vm._s(_vm.formatTime(message.time)))])])])}),(_vm.loading)?_c('div',{staticClass:\"typing-indicator\"},[_c('div',{staticClass:\"dot\"}),_c('div',{staticClass:\"dot\"}),_c('div',{staticClass:\"dot\"})]):_vm._e()],2),_c('div',{staticClass:\"chat-input\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.userInput),expression:\"userInput\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入您的问题...\",\"disabled\":_vm.loading},domProps:{\"value\":(_vm.userInput)},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.sendMessage.apply(null, arguments)},\"input\":function($event){if($event.target.composing)return;_vm.userInput=$event.target.value}}}),_c('button',{attrs:{\"disabled\":_vm.loading || !_vm.userInput.trim()},on:{\"click\":_vm.sendMessage}},[_c('i',{staticClass:\"fas fa-paper-plane\"})])])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"chat-title\"},[_c('i',{staticClass:\"fas fa-robot\"}),_c('span',[_vm._v(\"智能客服\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n    <div >\r\n<!--        问题轮播-->\r\n        <!-- 悬浮客服容器 -->\r\n        <div class=\"chat-container\">\r\n            <!-- 问题轮播区 -->\r\n            <div class=\"question-carousel\"\r\n                 @mouseenter=\"pauseCarousel\"\r\n                 @mouseleave=\"resumeCarousel\">\r\n                <transition-group name=\"slide\" tag=\"div\" class=\"carousel-wrapper\">\r\n                    <div v-for=\"(question, index) in questions\"\r\n                         :key=\"question\"\r\n                         class=\"question-item\"\r\n                         v-show=\"currentQuestionIndex === index\"\r\n                         @click=\"sendCarouselQuestion(question)\"\r\n                         @mouseenter=\"witde(index)\"\r\n                    >\r\n                        {{ question }}\r\n                    </div>\r\n                </transition-group>\r\n            </div>\r\n\r\n            <!-- 原有悬浮按钮 -->\r\n            <div class=\"chat-icon\"\r\n                 :class=\"{ 'chat-icon-active': showChat }\"\r\n                 @click=\"toggleChat\">\r\n                <i class=\"fas fa-comment\"></i>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 聊天窗口 -->\r\n        <div class=\"chat-window\" v-show=\"showChat\">\r\n            <div class=\"chat-header\">\r\n                <div class=\"chat-title\">\r\n                    <i class=\"fas fa-robot\"></i>\r\n                    <span>智能客服</span>\r\n                </div>\r\n                <div class=\"chat-controls\">\r\n                    <i class=\"fas fa-times\" @click=\"toggleChat\"></i>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-messages\" ref=\"messagesContainer\">\r\n                <div\r\n                        v-for=\"(message, index) in messages\"\r\n                        :key=\"index\"\r\n                        :class=\"['message', message.type]\"\r\n                >\r\n                    <div class=\"avatar\" v-if=\"message.type === 'bot'\">\r\n                        <i class=\"fas fa-robot\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"message-text\" v-html=\"formatMessage(message.text)\"></div>\r\n                        <div class=\"message-time\">{{ formatTime(message.time) }}</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"typing-indicator\" v-if=\"loading\">\r\n                    <div class=\"dot\"></div>\r\n                    <div class=\"dot\"></div>\r\n                    <div class=\"dot\"></div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-input\">\r\n                <input\r\n                        type=\"text\"\r\n                        v-model=\"userInput\"\r\n                        placeholder=\"请输入您的问题...\"\r\n                        @keyup.enter=\"sendMessage\"\r\n                        :disabled=\"loading\"\r\n                />\r\n                <button @click=\"sendMessage\" :disabled=\"loading || !userInput.trim()\">\r\n                    <i class=\"fas fa-paper-plane\"></i>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        name: 'chatAi',\r\n\r\n        data() {\r\n            return {\r\n                showChat: false,\r\n                userInput: '',\r\n                messages: [\r\n                    {\r\n                        type: 'bot',\r\n                        text: '您好！我是智能客服助手，有什么可以帮您？',\r\n                        time: new Date()\r\n                    }\r\n                ],\r\n                loading: false,\r\n                historyMessages:[],\r\n                questions: [\r\n                    \"如何租赁GPU算力？\",\r\n                    \"支持哪些支付方式？\",\r\n                    \"如何查看订单状态？\"\r\n                ],\r\n                currentQuestionIndex: 0,\r\n                carouselTimer: null,\r\n                carouselInterval: 3000,\r\n                isPaused: false\r\n            }\r\n        },\r\n        beforeDestroy() {\r\n            this.clearCarousel()\r\n        },\r\n        mounted() {\r\n            this.startCarousel();\r\n            // 导入 Font Awesome 图标库\r\n            if (!document.getElementById('font-awesome')) {\r\n                const link = document.createElement('link');\r\n                link.id = 'font-awesome';\r\n                link.rel = 'stylesheet';\r\n                link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';\r\n                document.head.appendChild(link);\r\n            }\r\n        },\r\n\r\n        methods: {\r\n            witde(index){\r\n                this.currentQuestionIndex = index;\r\n                this.pauseCarousel();\r\n            },\r\n            startCarousel() {\r\n                let that = this\r\n                this.clearCarousel();\r\n                this.carouselTimer = setInterval(() => {\r\n                    if (!that.isPaused) {\r\n                        this.currentQuestionIndex =\r\n                            (this.currentQuestionIndex + 1) % this.questions.length\r\n                        console.log(\"数据\", this.currentQuestionIndex)\r\n                        console.log(\"ispasued\",that.isPaused)\r\n                    }\r\n                }, this.carouselInterval)\r\n            },\r\n            pauseCarousel() {\r\n                this.isPaused = true;\r\n            },\r\n            resumeCarousel() {\r\n                this.isPaused = false;\r\n            },\r\n            clearCarousel() {\r\n                if (this.carouselTimer) {\r\n                    clearInterval(this.carouselTimer);\r\n                    this.carouselTimer = null;\r\n                }\r\n            },\r\n            // 点击轮播问题自动提问\r\n            sendCarouselQuestion(question) {\r\n                this.userInput = question;\r\n                this.sendMessage();\r\n            },\r\n            toggleChat() {\r\n                this.showChat = !this.showChat;\r\n\r\n                if (this.showChat) {\r\n                    this.$nextTick(() => {\r\n                        this.scrollToBottom();\r\n                    });\r\n                }\r\n            },\r\n\r\n            async sendMessage() {\r\n                if (!this.userInput.trim() || this.loading) return;\r\n\r\n                // 添加用户消息\r\n                this.messages.push({\r\n                    type: 'user',\r\n                    text: this.userInput,\r\n                    time: new Date()\r\n                });\r\n\r\n                const userQuestion = this.userInput;\r\n                this.userInput = '';\r\n                this.loading = true;\r\n                //添加历史记录\r\n                this.historyMessages.push({\r\n                    role:'user',\r\n                    content:userQuestion,\r\n                })\r\n\r\n                // 构造请求体\r\n                const requestBody = {\r\n                    model: 'Qwen/QwQ-32B',\r\n                    messages: [{role:\"system\",content: \"你是天工开物算力租赁网站的客服，当我问到算力租赁相关问题的时候再进行回答，如果我的问题与算力无关则回答“小天也不知道怎么办了呢，不如我们来聊聊算力相关的话题吧！”,如果问题涉及到你是谁或者你好的这种礼貌性踊跃，可以进行礼貌性的回复\"},...this.historyMessages], // 携带上下文历史\r\n                    stream: true,\r\n                    options: {\r\n                        presence_penalty: 1.2,  // 重复内容惩罚（0-2）\r\n                        frequency_penalty: 1.5, // 高频词惩罚（0-2）\r\n                        // repeat_last_n: 64,      // 检查重复的上下文长度\r\n                        seed: 12345             // 固定随机种子\r\n                    }\r\n                };\r\n                // 滚动到底部\r\n                this.$nextTick(() => {\r\n                    this.scrollToBottom();\r\n                });\r\n\r\n                try {\r\n                    // 调用后端API获取回复\r\n                    // 替换为你的实际API\r\n                    const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {\r\n                        method: 'POST',\r\n                        headers: {\r\n                            Authorization: 'Bearer sk-yxjtnfarjndtcwbjwmscdejrgtlpaaebnfdxcfnhhuanbqty',\r\n                            'Content-Type': 'application/json',\r\n                        },\r\n                        body: JSON.stringify(requestBody)\r\n                    });\r\n\r\n                    // 处理流式数据\r\n                    const reader = response.body.getReader();\r\n                    const decoder = new TextDecoder();\r\n                    const aiResponseIndex = this.messages.push({\r\n                        type: 'bot',\r\n                        text: '',\r\n                        time:new Date()\r\n                    }) - 1;\r\n                    while (true) {\r\n                        const { done, value } = await reader.read();\r\n                        if (done) break;\r\n\r\n                        // 解析流式数据块（可能包含多个JSON对象）\r\n                        const chunk = decoder.decode(value);\r\n                        const lines = chunk.split('\\n').filter(line => line.trim());\r\n\r\n                        for (const line of lines) {\r\n                            try {\r\n                                const jsonString = line.slice(6).trim();\r\n                                if (jsonString === \"\" || jsonString === \"[DONE]\") continue;\r\n\r\n                                let data = JSON.parse(jsonString)\r\n                                if (data.choices) {\r\n                                    if (data.choices[0].delta.reasoning_content!=null){\r\n                                        continue\r\n                                    }\r\n                                    if (data.choices[0].delta.content == '\\n\\n'){\r\n                                        continue\r\n                                    }\r\n                                    this.messages[aiResponseIndex].text += data.choices[0].delta.content;\r\n                                }\r\n                            } catch (e) {\r\n                            }\r\n                        }\r\n                    }\r\n                    this.historyMessages.push({\r\n                        role:\"assistant\",\r\n                        content:this.messages[aiResponseIndex].text\r\n                    })\r\n\r\n                    // 添加机器人回复\r\n                    // this.messages.push({\r\n                    //     type: 'bot',\r\n                    //     text: response,\r\n                    //     time: new Date()\r\n                    // });\r\n                } catch (error) {\r\n\r\n                    // 添加错误消息\r\n                    this.messages.push({\r\n                        type: 'bot',\r\n                        text: '抱歉，系统暂时无法响应，请稍后再试。',\r\n                        time: new Date()\r\n                    });\r\n                } finally {\r\n                    this.loading = false;\r\n\r\n                    // 滚动到底部\r\n                    this.$nextTick(() => {\r\n                        this.scrollToBottom();\r\n                    });\r\n                }\r\n            },\r\n\r\n            // 模拟API调用，实际使用时替换为真实API\r\n            async callChatAPI(message) {\r\n                // 模拟网络延迟\r\n                await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n                // TODO: 替换为实际的API调用\r\n                // const response = await fetch('YOUR_API_ENDPOINT', {\r\n                //   method: 'POST',\r\n                //   headers: {\r\n                //     'Content-Type': 'application/json',\r\n                //   },\r\n                //   body: JSON.stringify({ message }),\r\n                // });\r\n                // return await response.json();\r\n\r\n                // 模拟返回数据\r\n                return `感谢您的提问: \"${message}\"。这是一个模拟回复，请替换为真实API调用。`;\r\n            },\r\n\r\n            scrollToBottom() {\r\n                const container = this.$refs.messagesContainer;\r\n                container.scrollTop = container.scrollHeight;\r\n            },\r\n\r\n            formatTime(date) {\r\n                return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n            },\r\n\r\n            formatMessage(text) {\r\n                // 处理文本中的链接、表情等\r\n                return text\r\n                    .replace(/\\n/g, '<br>')\r\n                    .replace(/(https?:\\/\\/[^\\s]+)/g, '<a href=\"$1\" target=\"_blank\">$1</a>');\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n    /* 修正后的轮播样式 */\r\n    .chat-container {\r\n        position: fixed;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n        gap: 10px;\r\n        z-index: 1001;\r\n    }\r\n\r\n    .question-carousel {\r\n        left: 20px;\r\n        padding-bottom: 85px;\r\n        padding-top: 20px;\r\n        color: white;\r\n        min-width: 150px;\r\n        text-align: left;\r\n        cursor: pointer;\r\n        overflow: hidden;\r\n        position: relative;\r\n        height: 60px; /* 固定高度避免跳动 */\r\n    }\r\n\r\n    .carousel-wrapper {\r\n        position: relative;\r\n        height: 100%;\r\n    }\r\n\r\n    .question-item {\r\n        position: absolute;\r\n        border-radius: 20px 20px 20px 20px;\r\n        background-color: black;\r\n        width: 100%;\r\n        left: 0;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        padding: 10px 10px;\r\n        font-size: 14px;\r\n        opacity: 1;\r\n        transition: all 0.5s ease;\r\n    }\r\n\r\n    .question-item:hover {\r\n        color: #4286f4;\r\n    }\r\n\r\n    /* 过渡动画修正 */\r\n    .slide-enter-active,\r\n    .slide-leave-active {\r\n        transition: all 0.5s ease;\r\n    }\r\n    .slide-enter-from {\r\n        opacity: 0;\r\n        transform: translateY(20px) translateY(-50%);\r\n    }\r\n    .slide-leave-to {\r\n        opacity: 0;\r\n        transform: translateY(-20px) translateY(-50%);\r\n    }\r\n    .slide-enter-to,\r\n    .slide-leave-from {\r\n        opacity: 1;\r\n        transform: translateY(0) translateY(-50%);\r\n    }\r\n    .chat-icon {\r\n        position: fixed;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        width: 60px;\r\n        height: 60px;\r\n        background-color: blue;\r\n        border-radius: 50%;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n        transition: all 0.3s ease;\r\n        z-index: 1001;\r\n    }\r\n\r\n    .chat-icon i {\r\n        color: white;\r\n        font-size: 24px;\r\n    }\r\n\r\n    .chat-icon:hover, .chat-icon-active {\r\n        background-color: #3367d6;\r\n        transform: scale(1.05);\r\n    }\r\n\r\n    .chat-window {\r\n        position: fixed;\r\n        bottom: 90px;\r\n        right: 20px;\r\n        width: 350px;\r\n        height: 500px;\r\n        background-color: #fff;\r\n        border-radius: 10px;\r\n        box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16);\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: hidden;\r\n        z-index: 1002;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 15px;\r\n        background-color: #4286f4;\r\n        color: white;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n    }\r\n\r\n    .chat-title {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 10px;\r\n        font-weight: bold;\r\n    }\r\n\r\n    .chat-controls i {\r\n        cursor: pointer;\r\n        font-size: 18px;\r\n    }\r\n\r\n    .chat-messages {\r\n        flex: 1;\r\n        padding: 15px;\r\n        overflow-y: auto;\r\n        background-color: #f5f5f5;\r\n    }\r\n\r\n    .message {\r\n        display: flex;\r\n        margin-bottom: 15px;\r\n        align-items: flex-start;\r\n    }\r\n\r\n    .message.user {\r\n        flex-direction: row-reverse;\r\n    }\r\n\r\n    .avatar {\r\n        width: 36px;\r\n        height: 36px;\r\n        border-radius: 50%;\r\n        background-color: #4286f4;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        color: white;\r\n        margin-right: 10px;\r\n    }\r\n\r\n    .message-content {\r\n        max-width: 70%;\r\n    }\r\n\r\n    .message-text {\r\n        padding: 10px 15px;\r\n        border-radius: 18px;\r\n        margin-bottom: 5px;\r\n        word-break: break-word;\r\n    }\r\n\r\n    .message.bot .message-text {\r\n        background-color: white;\r\n        border: 1px solid #e0e0e0;\r\n    }\r\n\r\n    .message.user .message-text {\r\n        background-color: #4286f4;\r\n        color: white;\r\n        text-align: right;\r\n    }\r\n\r\n    .message-time {\r\n        font-size: 12px;\r\n        color: #999;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .message.user .message-time {\r\n        text-align: right;\r\n    }\r\n\r\n    .typing-indicator {\r\n        display: flex;\r\n        padding: 10px 15px;\r\n        background-color: white;\r\n        border-radius: 18px;\r\n        border: 1px solid #e0e0e0;\r\n        width: fit-content;\r\n        margin-bottom: 15px;\r\n    }\r\n\r\n    .dot {\r\n        width: 8px;\r\n        height: 8px;\r\n        background-color: #999;\r\n        border-radius: 50%;\r\n        margin: 0 2px;\r\n        animation: bounce 1.5s infinite;\r\n    }\r\n\r\n    .dot:nth-child(2) {\r\n        animation-delay: 0.2s;\r\n    }\r\n\r\n    .dot:nth-child(3) {\r\n        animation-delay: 0.4s;\r\n    }\r\n\r\n    @keyframes bounce {\r\n        0%, 60%, 100% {\r\n            transform: translateY(0);\r\n        }\r\n        30% {\r\n            transform: translateY(-4px);\r\n        }\r\n    }\r\n\r\n    .chat-input {\r\n        padding: 15px;\r\n        display: flex;\r\n        border-top: 1px solid #e0e0e0;\r\n        background-color: white;\r\n    }\r\n\r\n    .chat-input input {\r\n        flex: 1;\r\n        padding: 10px 15px;\r\n        border: 1px solid #e0e0e0;\r\n        border-radius: 20px;\r\n        font-size: 14px;\r\n        outline: none;\r\n    }\r\n\r\n    .chat-input button {\r\n        margin-left: 10px;\r\n        width: 40px;\r\n        height: 40px;\r\n        border-radius: 50%;\r\n        background-color: #4286f4;\r\n        color: white;\r\n        border: none;\r\n        cursor: pointer;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n    }\r\n\r\n    .chat-input button:disabled {\r\n        background-color: #b3c9f4;\r\n        cursor: not-allowed;\r\n    }\r\n\r\n    .chat-input button i {\r\n        font-size: 16px;\r\n    }\r\n\r\n    /* 移动端适配 */\r\n    @media (max-width: 480px) {\r\n        .chat-window {\r\n            width: 100%;\r\n            height: 100%;\r\n            bottom: 0;\r\n            right: 0;\r\n            border-radius: 0;\r\n        }\r\n\r\n        .chat-icon {\r\n            bottom: 15px;\r\n            right: 15px;\r\n        }\r\n    }\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./chatAi.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./chatAi.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./chatAi.vue?vue&type=template&id=46c63c47&scoped=true&\"\nimport script from \"./chatAi.vue?vue&type=script&lang=js&\"\nexport * from \"./chatAi.vue?vue&type=script&lang=js&\"\nimport style0 from \"./chatAi.vue?vue&type=style&index=0&id=46c63c47&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"46c63c47\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"help-layout\"},[(_vm.isMobile)?_c('div',{staticClass:\"mobile-controls\"},[_c('button',{staticClass:\"sidebar-toggle\",class:{ 'active': _vm.sidebarVisible },on:{\"click\":_vm.toggleSidebar}},[_c('i',{staticClass:\"icon-menu\"}),_c('span',[_vm._v(\"菜单\")])]),_c('button',{staticClass:\"toc-toggle\",class:{ 'active': _vm.tocVisible },on:{\"click\":_vm.toggleToc}},[_c('i',{staticClass:\"icon-list\"}),_c('span',[_vm._v(\"目录\")])])]):_vm._e(),(_vm.isMobile && (_vm.sidebarVisible || _vm.tocVisible))?_c('div',{staticClass:\"overlay\",on:{\"click\":_vm.closeAllPanels}}):_vm._e(),_c('aside',{ref:\"sidebar\",staticClass:\"sidebar\",class:{\n      'sidebar-hidden': !_vm.sidebarVisible && _vm.isMobile,\n      'sidebar-visible': _vm.sidebarVisible || !_vm.isMobile\n    }},[(_vm.isMobile)?_c('div',{staticClass:\"sidebar-header\"},[_c('span',{staticClass:\"sidebar-title\"},[_vm._v(\"帮助文档\")]),_c('button',{staticClass:\"close-btn\",on:{\"click\":_vm.closeSidebar}},[_c('i',{staticClass:\"icon-close\"},[_vm._v(\"×\")])])]):_vm._e(),_c('div',{staticClass:\"sidebar-menu\"},_vm._l((_vm.menu),function(category){return _c('div',{key:category.title,staticClass:\"menu-category\"},[_c('div',{staticClass:\"category-title\"},[_vm._v(_vm._s(category.title))]),_c('ul',{staticClass:\"menu-list\"},_vm._l((category.items),function(item){return _c('li',{key:item.path,staticClass:\"menu-item\"},[_c('router-link',{staticClass:\"menu-link\",class:{ 'menu-link-active': _vm.isMenuActive(item.path) },attrs:{\"to\":item.path},on:{\"click\":_vm.onMenuItemClick}},[_vm._v(\" \"+_vm._s(item.name)+\" \")])],1)}),0)])}),0)]),_c('main',{ref:\"mainContent\",staticClass:\"main-content\",class:{\n      'content-expanded': (!_vm.sidebarVisible || !_vm.tocVisible) && _vm.isMobile,\n      'content-full': !_vm.sidebarVisible && !_vm.tocVisible && _vm.isMobile\n    }},[_c('HelpContent',{attrs:{\"doc\":_vm.currentDoc,\"prev-page\":_vm.getPrevPage(),\"next-page\":_vm.getNextPage()},on:{\"content-loaded\":_vm.buildToc}})],1),_c('aside',{staticClass:\"toc\",class:{\n      'toc-hidden': !_vm.tocVisible && _vm.isMobile,\n      'toc-visible': _vm.tocVisible || !_vm.isMobile\n    }},[(_vm.isMobile)?_c('div',{staticClass:\"toc-header\"},[_c('span',{staticClass:\"toc-title\"},[_vm._v(\"文章导航\")]),_c('button',{staticClass:\"close-btn\",on:{\"click\":_vm.closeToc}},[_c('i',{staticClass:\"icon-close\"},[_vm._v(\"×\")])])]):_vm._e(),(!_vm.isMobile)?_c('div',{staticClass:\"toc-title\"},[_vm._v(\"文章导航\")]):_vm._e(),_c('ul',{staticClass:\"toc-list\"},_vm._l((_vm.toc),function(item){return _c('li',{key:item.id,staticClass:\"toc-item\",class:{ 'toc-item-h3': item.level === 3, 'active': item.id === _vm.activeTocId }},[_c('a',{staticClass:\"toc-link\",attrs:{\"href\":'#' + item.id},on:{\"click\":function($event){$event.preventDefault();return _vm.scrollToAnchor(item.id)}}},[_vm._v(\" \"+_vm._s(item.text)+\" \")])])}),0)]),_c('Mider'),_c('chatAi')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"doc-content\"},[(_vm.loading)?_c('div',{staticClass:\"loading\"},[_vm._v(\"文档加载中...\")]):(_vm.error)?_c('div',{staticClass:\"error\"},[_vm._v(\"文档加载失败: \"+_vm._s(_vm.error))]):_c('div',[_c('div',{ref:\"contentRef\",domProps:{\"innerHTML\":_vm._s(_vm.markdownContent)}}),_c('div',{staticClass:\"page-navigation\"},[_c('div',{staticClass:\"prev-next-container\"},[(_vm.prevPage)?_c('router-link',{staticClass:\"prev-page\",attrs:{\"to\":_vm.prevPage.path}},[_c('div',{staticClass:\"nav-label\"},[_vm._v(\"上一篇\")]),_c('div',{staticClass:\"nav-title\"},[_vm._v(_vm._s(_vm.prevPage.name))])]):_c('div',{staticClass:\"prev-page empty\"}),(_vm.nextPage)?_c('router-link',{staticClass:\"next-page\",attrs:{\"to\":_vm.nextPage.path}},[_c('div',{staticClass:\"nav-label\"},[_vm._v(\"下一篇\")]),_c('div',{staticClass:\"nav-title\"},[_vm._v(_vm._s(_vm.nextPage.name))])]):_c('div',{staticClass:\"next-page empty\"})],1)])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"doc-content\">\r\n    <div v-if=\"loading\" class=\"loading\">文档加载中...</div>\r\n    <div v-else-if=\"error\" class=\"error\">文档加载失败: {{ error }}</div>\r\n    <div v-else>\r\n      <div v-html=\"markdownContent\" ref=\"contentRef\"></div>\r\n      \r\n      <!-- 上一页/下一页导航 -->\r\n      <div class=\"page-navigation\">\r\n        <div class=\"prev-next-container\">\r\n          <router-link \r\n            v-if=\"prevPage\" \r\n            :to=\"prevPage.path\" \r\n            class=\"prev-page\">\r\n            <div class=\"nav-label\">上一篇</div>\r\n            <div class=\"nav-title\">{{ prevPage.name }}</div>\r\n          </router-link>\r\n          <div v-else class=\"prev-page empty\"></div>\r\n          \r\n          <router-link \r\n            v-if=\"nextPage\" \r\n            :to=\"nextPage.path\" \r\n            class=\"next-page\">\r\n            <div class=\"nav-label\">下一篇</div>\r\n            <div class=\"nav-title\">{{ nextPage.name }}</div>\r\n          </router-link>\r\n          <div v-else class=\"next-page empty\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    doc: String,\r\n    prevPage: Object,\r\n    nextPage: Object\r\n  },\r\n  data() {\r\n    return {\r\n      markdownContent: '',\r\n      loading: false,\r\n      error: null\r\n    };\r\n  },\r\n  watch: {\r\n    doc: {\r\n      immediate: true,\r\n      async handler(doc) {\r\n        this.loading = true;\r\n        this.error = null;\r\n        try {\r\n          const module = await import(/* webpackChunkName: \"docs\" */ `../docs/${doc}.md`);\r\n          this.markdownContent = module.default;\r\n          this.$nextTick(() => {\r\n            this.processContent();\r\n            this.$emit('content-loaded');\r\n          });\r\n        } catch (e) {\r\n          this.error = e.message;\r\n          this.markdownContent = '<h1>文档加载失败</h1>';\r\n        } finally {\r\n          this.loading = false;\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    slugify(text) {\r\n      let slug = text\r\n        .toLowerCase()\r\n        .replace(/\\s+/g, '-')           // 空格转为-\r\n        .replace(/[^a-z0-9\\-]+/g, '')   // 只保留小写字母、数字、短横线\r\n        .replace(/\\-\\-+/g, '-')         // 多个-合并为一个\r\n        .replace(/^-+/, '')              // 去除开头-\r\n        .replace(/-+$/, '');             // 去除结尾-\r\n      if (!slug || /^[0-9]+$/.test(slug)) {\r\n        // 添加随机数确保唯一性\r\n        slug = 'content-section-' + slug + '-' + Math.random().toString(36).substring(2, 11);\r\n      }\r\n      return slug;\r\n    },\r\n    processContent() {\r\n      if (this.$refs.contentRef) {\r\n        const headings = this.$refs.contentRef.querySelectorAll('h2, h3');\r\n        headings.forEach(heading => {\r\n          heading.id = this.slugify(heading.textContent);\r\n        });\r\n        \r\n        const codeBlocks = this.$refs.contentRef.querySelectorAll('pre code');\r\n        codeBlocks.forEach(block => {\r\n          block.classList.add('hljs');\r\n        });\r\n        \r\n        const links = this.$refs.contentRef.querySelectorAll('a[href^=\"#\"]');\r\n        links.forEach(link => {\r\n          link.addEventListener('click', (e) => {\r\n            e.preventDefault();\r\n            const id = link.getAttribute('href').substring(1);\r\n            const targetElement = document.getElementById(id);\r\n            if (targetElement) {\r\n              targetElement.scrollIntoView({ behavior: 'smooth' });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n/* 注意：这里不使用scoped，以便样式应用到动态生成的内容 */\r\n.doc-content h1 {\r\n  font-size: 28px;\r\n  margin-bottom: 20px;\r\n  color: #333;\r\n}\r\n\r\n.doc-content h2 {\r\n  font-size: 22px;\r\n  margin: 30px 0 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #eee;\r\n  color: #333;\r\n}\r\n\r\n.doc-content h3 {\r\n  font-size: 18px;\r\n  margin: 25px 0 15px;\r\n  color: #333;\r\n}\r\n\r\n.doc-content p {\r\n  margin: 15px 0;\r\n  line-height: 1.6;\r\n  color: #555;\r\n}\r\n\r\n.doc-content ul, .doc-content ol {\r\n  padding-left: 25px;\r\n  margin: 15px 0;\r\n}\r\n\r\n.doc-content li {\r\n  margin-bottom: 8px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.doc-content code {\r\n  background-color: #f5f5f5;\r\n  padding: 2px 5px;\r\n  border-radius: 3px;\r\n  font-family: Consolas, Monaco, 'Andale Mono', monospace;\r\n  color: #d63384;\r\n}\r\n\r\n.doc-content pre {\r\n  background-color: #f8f8f8;\r\n  padding: 15px;\r\n  border-radius: 5px;\r\n  overflow-x: auto;\r\n  margin: 20px 0;\r\n}\r\n\r\n.doc-content pre code {\r\n  background-color: transparent;\r\n  padding: 0;\r\n  color: #333;\r\n  display: block;\r\n}\r\n\r\n.doc-content a {\r\n  color: #1890ff;\r\n  text-decoration: none;\r\n}\r\n\r\n.doc-content a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.doc-content blockquote {\r\n  border-left: 4px solid #1890ff !important;\r\n  padding: 16px 20px 16px 50px !important;\r\n  color: #555 !important;\r\n  background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%) !important;\r\n  margin: 20px 0 !important;\r\n  border-radius: 8px !important;\r\n  font-family: inherit !important;\r\n  position: relative !important;\r\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1) !important;\r\n  border: 1px solid rgba(24, 144, 255, 0.2) !important;\r\n}\r\n\r\n.doc-content blockquote::before {\r\n  content: 'ℹ️';\r\n  position: absolute;\r\n  left: 16px;\r\n  top: 16px;\r\n  font-size: 16px;\r\n  background: #1890ff;\r\n  color: white;\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);\r\n}\r\n\r\n.doc-content blockquote p {\r\n  margin: 0 !important;\r\n  line-height: 1.7 !important;\r\n  font-size: 14px !important;\r\n}\r\n\r\n.doc-content blockquote p:not(:last-child) {\r\n  margin-bottom: 12px !important;\r\n}\r\n\r\n/* 确保引用块内的font标签样式正常显示 */\r\n.doc-content blockquote font {\r\n  color: inherit !important;\r\n}\r\n\r\n/* 引用块内的链接样式 */\r\n.doc-content blockquote a {\r\n  color: #1890ff !important;\r\n  text-decoration: none !important;\r\n  font-weight: 500 !important;\r\n}\r\n\r\n.doc-content blockquote a:hover {\r\n  text-decoration: underline !important;\r\n  color: #40a9ff !important;\r\n}\r\n\r\n/* 引用块内的代码样式 */\r\n.doc-content blockquote code {\r\n  background-color: rgba(255, 255, 255, 0.8) !important;\r\n  padding: 2px 6px !important;\r\n  border-radius: 3px !important;\r\n  border: 1px solid rgba(24, 144, 255, 0.2) !important;\r\n  color: #1890ff !important;\r\n  font-weight: 500 !important;\r\n}\r\n\r\n/* 引用块内的列表样式 */\r\n.doc-content blockquote ul,\r\n.doc-content blockquote ol {\r\n  margin: 8px 0 !important;\r\n  padding-left: 20px !important;\r\n}\r\n\r\n.doc-content blockquote li {\r\n  margin-bottom: 4px !important;\r\n  line-height: 1.6 !important;\r\n}\r\n\r\n.doc-content table {\r\n  border-collapse: collapse;\r\n  width: 100%;\r\n  margin: 20px 0;\r\n}\r\n\r\n.doc-content table th, .doc-content table td {\r\n  border: 1px solid #ddd;\r\n  padding: 10px;\r\n  text-align: left;\r\n}\r\n\r\n.doc-content table th {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.loading, .error {\r\n  padding: 20px;\r\n  text-align: center;\r\n  font-size: 18px;\r\n}\r\n\r\n.error {\r\n  color: red;\r\n}\r\n\r\n/* 上一页/下一页导航样式 */\r\n.page-navigation {\r\n  /* margin-top: 60px; */\r\n  padding-top: 20px;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.prev-next-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.prev-page, .next-page {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 45%;\r\n  padding: 15px;\r\n  border: 1px solid #eee;\r\n  border-radius: 5px;\r\n  text-decoration: none;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.prev-page {\r\n  text-align: left;\r\n}\r\n\r\n.next-page {\r\n  text-align: right;\r\n}\r\n\r\n.prev-page:hover, .next-page:hover {\r\n  border-color: #1890ff;\r\n  background-color: #f0f8ff;\r\n}\r\n\r\n.nav-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.nav-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #1890ff;\r\n}\r\n\r\n.empty {\r\n  visibility: hidden;\r\n}\r\n\r\n.doc-content img {\r\n  display: block;\r\n  max-width: 100%;\r\n  height: auto;\r\n  margin: 24px auto;  /* 上下间距24px，左右自动居中 */\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.04);\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 引用块响应式样式 */\r\n@media (max-width: 768px) {\r\n  .doc-content blockquote {\r\n    padding: 12px 16px 12px 40px !important;\r\n    margin: 16px 0 !important;\r\n    border-radius: 6px !important;\r\n  }\r\n\r\n  .doc-content blockquote::before {\r\n    left: 12px !important;\r\n    top: 12px !important;\r\n    width: 20px !important;\r\n    height: 20px !important;\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  .doc-content blockquote p {\r\n    font-size: 13px !important;\r\n    line-height: 1.6 !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .doc-content blockquote {\r\n    padding: 10px 12px 10px 36px !important;\r\n    margin: 12px 0 !important;\r\n  }\r\n\r\n  .doc-content blockquote::before {\r\n    left: 10px !important;\r\n    top: 10px !important;\r\n    width: 18px !important;\r\n    height: 18px !important;\r\n    font-size: 10px !important;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./HelpContent.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./HelpContent.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./HelpContent.vue?vue&type=template&id=63deeb16&\"\nimport script from \"./HelpContent.vue?vue&type=script&lang=js&\"\nexport * from \"./HelpContent.vue?vue&type=script&lang=js&\"\nimport style0 from \"./HelpContent.vue?vue&type=style&index=0&id=63deeb16&prod&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div class=\"help-layout\">\r\n    <!-- 移动端控制按钮 -->\r\n    <div class=\"mobile-controls\" v-if=\"isMobile\">\r\n      <button\r\n        class=\"sidebar-toggle\"\r\n        @click=\"toggleSidebar\"\r\n        :class=\"{ 'active': sidebarVisible }\"\r\n      >\r\n        <i class=\"icon-menu\"></i>\r\n        <span>菜单</span>\r\n      </button>\r\n      <button\r\n        class=\"toc-toggle\"\r\n        @click=\"toggleToc\"\r\n        :class=\"{ 'active': tocVisible }\"\r\n      >\r\n        <i class=\"icon-list\"></i>\r\n        <span>目录</span>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 遮罩层 -->\r\n    <div\r\n      class=\"overlay\"\r\n      v-if=\"isMobile && (sidebarVisible || tocVisible)\"\r\n      @click=\"closeAllPanels\"\r\n    ></div>\r\n\r\n    <!-- 左侧边栏 -->\r\n    <aside\r\n      ref=\"sidebar\"\r\n      class=\"sidebar\"\r\n      :class=\"{\r\n        'sidebar-hidden': !sidebarVisible && isMobile,\r\n        'sidebar-visible': sidebarVisible || !isMobile\r\n      }\"\r\n    >\r\n      <div class=\"sidebar-header\" v-if=\"isMobile\">\r\n        <span class=\"sidebar-title\">帮助文档</span>\r\n        <button class=\"close-btn\" @click=\"closeSidebar\">\r\n          <i class=\"icon-close\">×</i>\r\n        </button>\r\n      </div>\r\n      <div class=\"sidebar-menu\">\r\n        <div v-for=\"category in menu\" :key=\"category.title\" class=\"menu-category\">\r\n          <div class=\"category-title\">{{ category.title }}</div>\r\n          <ul class=\"menu-list\">\r\n            <li v-for=\"item in category.items\" :key=\"item.path\" class=\"menu-item\">\r\n              <router-link\r\n                :to=\"item.path\"\r\n                class=\"menu-link\"\r\n                :class=\"{ 'menu-link-active': isMenuActive(item.path) }\"\r\n                @click=\"onMenuItemClick\"\r\n              >\r\n                {{ item.name }}\r\n              </router-link>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </aside>\r\n\r\n    <!-- 主内容区 -->\r\n    <main\r\n      class=\"main-content\"\r\n      ref=\"mainContent\"\r\n      :class=\"{\r\n        'content-expanded': (!sidebarVisible || !tocVisible) && isMobile,\r\n        'content-full': !sidebarVisible && !tocVisible && isMobile\r\n      }\"\r\n    >\r\n      <HelpContent\r\n        :doc=\"currentDoc\"\r\n        @content-loaded=\"buildToc\"\r\n        :prev-page=\"getPrevPage()\"\r\n        :next-page=\"getNextPage()\"\r\n      />\r\n    </main>\r\n\r\n    <!-- 右侧目录 -->\r\n    <aside\r\n      class=\"toc\"\r\n      :class=\"{\r\n        'toc-hidden': !tocVisible && isMobile,\r\n        'toc-visible': tocVisible || !isMobile\r\n      }\"\r\n    >\r\n      <div class=\"toc-header\" v-if=\"isMobile\">\r\n        <span class=\"toc-title\">文章导航</span>\r\n        <button class=\"close-btn\" @click=\"closeToc\">\r\n          <i class=\"icon-close\">×</i>\r\n        </button>\r\n      </div>\r\n      <div class=\"toc-title\" v-if=\"!isMobile\">文章导航</div>\r\n      <ul class=\"toc-list\">\r\n        <li v-for=\"item in toc\" :key=\"item.id\" class=\"toc-item\" :class=\"{ 'toc-item-h3': item.level === 3, 'active': item.id === activeTocId }\">\r\n          <a\r\n            :href=\"'#' + item.id\"\r\n            class=\"toc-link\"\r\n            @click.prevent=\"scrollToAnchor(item.id)\"\r\n          >\r\n            {{ item.text }}\r\n          </a>\r\n        </li>\r\n      </ul>\r\n    </aside>\r\n    <!-- 悬浮窗组件 -->\r\n    <Mider></Mider>\r\n    <chatAi></chatAi>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport HelpContent from './HelpContent';\r\nimport chatAi from \"@/components/common/mider/chatAi\";\r\nimport Mider from '@/components/common/mider/Mider';\r\nexport default {\r\n  components: { HelpContent, chatAi , Mider },\r\n  data() {\r\n    return {\r\n      menu: [\r\n        { title: '弹性部署服务', items: [\r\n          { name: '平台概要', path: '/help/summary' },\r\n          { name: '快速开始', path: '/help/quick-start' },\r\n          { name: '常见问题', path: '/help/qustion' }\r\n        ]},\r\n        { title: '功能介绍', items: [\r\n          { name: '镜像仓库', path: '/help/mirror' },\r\n          { name: 'GPU选型指南', path: '/help/gpu-selection' },\r\n          { name: '健康检查', path: '/help/health-check' },\r\n          { name: 'K8S YAML 导入', path: '/help/k8s-yaml-import' },\r\n          { name: '云存储加速', path: '/help/cloud-storage' }\r\n        ]},\r\n        { title: '最佳实践', items: [\r\n          { name: '弹性部署服务-Serverless 基础认识', path: '/help/deploy-serverless' },\r\n          { name: '容器化部署 Ollama+Qwen3', path: '/help/ollama-qwen' },\r\n          { name: '容器化部署 Ollama+Qwen3+Open WebUI', path: '/help/ollama-qwen-webui' },\r\n          { name: '容器化部署 JupyterLab', path: '/help/jupyter-lab' },\r\n          { name: '容器化部署 Flux.1-dev 文生图模型应用', path: '/help/flux-dev' },\r\n          { name: '容器化部署 FramePack 图生视频框架', path: '/help/frame-pack' },\r\n          { name: '容器化部署 Whisper', path: '/help/whisper' },\r\n          { name: '容器化部署 StableDiffusion1.5-WebUI 应用', path: '/help/stable-diffusion1.5' },\r\n          { name: '容器化部署 StableDiffusion2.1-WebUI 应用', path: '/help/stable-diffusion2.1' },\r\n          { name: '容器化部署 StableDiffusion3.5-large-文生图模型应用', path: '/help/stable-diffusion3.5-large' },\r\n        ]},\r\n        { title: '账户与实名', items: [\r\n          { name: '手机号注册与登录', path: '/help/register-login' },\r\n          { name: '个人用户实名', path: '/help/personal-certification' }\r\n        ]},\r\n        { title: '服务协议', items: [\r\n          { name: '服务协议', path: '/help/user-agreement' },\r\n          { name: '隐私政策', path: '/help/privacy-policy' }\r\n        ]},\r\n        { title: '其他', items: [\r\n          { name: 'Docker 教程', path: '/help/docker-tutorial' }\r\n        ]}\r\n      ],\r\n      toc: [],\r\n      allPages: [],\r\n      activeTocId: null,\r\n      isAnchorClicking: false,\r\n      // 响应式状态\r\n      isMobile: false,\r\n      windowWidth: 0,\r\n      sidebarVisible: true,\r\n      tocVisible: true,\r\n      // 侧边栏滚动位置保存\r\n      sidebarScrollPosition: 0\r\n    };\r\n  },\r\n  computed: {\r\n    currentDoc() {\r\n      return this.$route.params.doc || 'summary';\r\n    },\r\n    currentPath() {\r\n      return this.$route.path;\r\n    }\r\n  },\r\n  created() {\r\n    this.flattenPages();\r\n    this.checkScreenSize();\r\n  },\r\n  mounted() {\r\n    this.updatePageTitle();\r\n\r\n    // 从 localStorage 恢复滚动位置\r\n    const savedScrollPosition = localStorage.getItem('helpSidebarScrollPosition');\r\n    if (savedScrollPosition) {\r\n      this.sidebarScrollPosition = parseInt(savedScrollPosition, 10);\r\n    }\r\n\r\n    this.$nextTick(() => {\r\n      const mainContent = this.$refs.mainContent;\r\n      if (mainContent) {\r\n        mainContent.addEventListener('scroll', this.handleContentScroll);\r\n      }\r\n\r\n      const sidebar = this.$refs.sidebar;\r\n      if (sidebar) {\r\n        sidebar.addEventListener('scroll', this.handleSidebarScroll);\r\n      }\r\n\r\n      this.restoreSidebarScrollPosition();\r\n    });\r\n\r\n    window.addEventListener('resize', this.handleResize);\r\n    this.checkScreenSize();\r\n  },\r\n  beforeDestroy() {\r\n    const mainContent = this.$refs.mainContent;\r\n    if (mainContent) {\r\n      mainContent.removeEventListener('scroll', this.handleContentScroll);\r\n    }\r\n\r\n    const sidebar = this.$refs.sidebar;\r\n    if (sidebar) {\r\n      sidebar.removeEventListener('scroll', this.handleSidebarScroll);\r\n    }\r\n\r\n    window.removeEventListener('resize', this.handleResize);\r\n  },\r\n  watch: {\r\n    '$route.path'() {\r\n      this.updatePageTitle();\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          this.restoreSidebarScrollPosition();\r\n        }, 50);\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    slugify(text) {\r\n      let slug = text\r\n        .toLowerCase()\r\n        .replace(/\\s+/g, '-')           // 空格转为-\r\n        .replace(/[^a-z0-9\\-]+/g, '')   // 只保留小写字母、数字、短横线\r\n        .replace(/\\-\\-+/g, '-')         // 多个-合并为一个\r\n        .replace(/^-+/, '')              // 去除开头-\r\n        .replace(/-+$/, '');             // 去除结尾-\r\n      if (!slug || /^[0-9]+$/.test(slug)) {\r\n        // 添加时间戳确保唯一性\r\n        slug = 'toc-section-' + slug + '-' + Date.now();\r\n      }\r\n      return slug;\r\n    },\r\n    flattenPages() {\r\n      this.allPages = [];\r\n      this.menu.forEach(category => {\r\n        category.items.forEach(item => {\r\n          this.allPages.push({\r\n            name: item.name,\r\n            path: item.path,\r\n            category: category.title\r\n          });\r\n        });\r\n      });\r\n    },\r\n    getPrevPage() {\r\n      const currentIndex = this.allPages.findIndex(page => page.path === this.currentPath);\r\n      return currentIndex > 0 ? this.allPages[currentIndex - 1] : null;\r\n    },\r\n    getNextPage() {\r\n      const currentIndex = this.allPages.findIndex(page => page.path === this.currentPath);\r\n      return (currentIndex !== -1 && currentIndex < this.allPages.length - 1) ? this.allPages[currentIndex + 1] : null;\r\n    },\r\n    buildToc() {\r\n      this.$nextTick(() => {\r\n        const mainContent = this.$refs.mainContent;\r\n        const docContent = mainContent.querySelector('.doc-content');\r\n        if (!docContent) return;\r\n        const headings = docContent.querySelectorAll('h2, h3');\r\n        this.toc = Array.from(headings).map(h => {\r\n          // 如果元素已经有ID，使用现有的ID，否则生成新的\r\n          const existingId = h.id;\r\n          const id = existingId || this.slugify(h.textContent);\r\n          // 只有当元素没有ID时才设置ID\r\n          if (!existingId) {\r\n            h.id = id;\r\n          }\r\n          return {\r\n            id: id,\r\n            text: h.textContent,\r\n            level: parseInt(h.tagName.substring(1))\r\n          };\r\n        });\r\n        this.$nextTick(this.handleContentScroll);\r\n      });\r\n    },\r\n    updatePageTitle() {\r\n      const currentPath = this.$route.path;\r\n      for (const category of this.menu) {\r\n        for (const item of category.items) {\r\n          if (item.path === currentPath) {\r\n            this.currentPageTitle = item.name;\r\n            return;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    scrollToAnchor(id) {\r\n      const OFFSET = 30;\r\n      this.isAnchorClicking = true;\r\n      this.activeTocId = id;\r\n      const mainContent = this.$refs.mainContent;\r\n      const docContent = mainContent.querySelector('.doc-content');\r\n      const element = document.getElementById(id);\r\n      if (element) {\r\n        const offsetTop = element.offsetTop - docContent.offsetTop;\r\n        mainContent.scrollTop = offsetTop - OFFSET;\r\n      }\r\n      setTimeout(() => {\r\n        this.isAnchorClicking = false;\r\n      }, 100);\r\n    },\r\n    handleContentScroll() {\r\n      if (this.isAnchorClicking) return;\r\n      const OFFSET = 30;\r\n      const mainContent = this.$refs.mainContent;\r\n      const docContent = mainContent.querySelector('.doc-content');\r\n      if (!docContent) return;\r\n      const headings = docContent.querySelectorAll('h2, h3');\r\n      let activeId = null;\r\n      const scrollTop = mainContent.scrollTop;\r\n      for (let i = headings.length - 1; i >= 0; i--) {\r\n        const heading = headings[i];\r\n        if (heading.offsetTop - OFFSET <= scrollTop) {\r\n          activeId = heading.id;\r\n          break;\r\n        }\r\n      }\r\n      this.activeTocId = activeId;\r\n    },\r\n    isMenuActive(path) {\r\n      if (this.$route.path === path) return true;\r\n      if ((this.$route.path === '/help' || this.$route.path === '/help/') && this.menu[0].items[0].path === path) return true;\r\n      return false;\r\n    },\r\n\r\n    checkScreenSize() {\r\n      this.windowWidth = window.innerWidth;\r\n      const wasMobile = this.isMobile;\r\n      this.isMobile = this.windowWidth <= 992;\r\n\r\n      // 如果从移动端切换到桌面端，显示所有面板\r\n      if (wasMobile && !this.isMobile) {\r\n        this.sidebarVisible = true;\r\n        this.tocVisible = true;\r\n      }\r\n      // 如果从桌面端切换到移动端，隐藏侧边栏\r\n      else if (!wasMobile && this.isMobile) {\r\n        this.sidebarVisible = false;\r\n        this.tocVisible = false;\r\n      }\r\n    },\r\n\r\n    handleResize() {\r\n      clearTimeout(this.resizeTimer);\r\n      this.resizeTimer = setTimeout(() => {\r\n        this.checkScreenSize();\r\n      }, 250);\r\n    },\r\n\r\n    toggleSidebar() {\r\n      this.sidebarVisible = !this.sidebarVisible;\r\n      if (this.sidebarVisible && this.tocVisible) {\r\n        this.tocVisible = false;\r\n      }\r\n    },\r\n\r\n    toggleToc() {\r\n      this.tocVisible = !this.tocVisible;\r\n      if (this.tocVisible && this.sidebarVisible) {\r\n        this.sidebarVisible = false;\r\n      }\r\n    },\r\n\r\n    closeSidebar() {\r\n      this.sidebarVisible = false;\r\n    },\r\n\r\n    closeToc() {\r\n      this.tocVisible = false;\r\n    },\r\n\r\n    closeAllPanels() {\r\n      this.sidebarVisible = false;\r\n      this.tocVisible = false;\r\n    },\r\n\r\n    onMenuItemClick() {\r\n      if (this.isMobile) {\r\n        this.sidebarVisible = false;\r\n      }\r\n    },\r\n\r\n    handleSidebarScroll() {\r\n      const sidebar = this.$refs.sidebar;\r\n      if (sidebar) {\r\n        this.sidebarScrollPosition = sidebar.scrollTop;\r\n        localStorage.setItem('helpSidebarScrollPosition', sidebar.scrollTop.toString());\r\n      }\r\n    },\r\n\r\n    restoreSidebarScrollPosition() {\r\n      const sidebar = this.$refs.sidebar;\r\n      if (sidebar && this.sidebarScrollPosition >= 0) {\r\n        requestAnimationFrame(() => {\r\n          sidebar.scrollTop = this.sidebarScrollPosition;\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.help-layout {\r\n  display: flex;\r\n  min-height: calc(100vh - 60px);\r\n  background-color: #fff;\r\n  height: calc(100vh - 60px);\r\n  position: relative;\r\n}\r\n\r\n/* 移动端控制按钮 */\r\n.mobile-controls {\r\n  position: fixed;\r\n  top: 70px;\r\n  left: 10px;\r\n  z-index: 1001;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.sidebar-toggle,\r\n.toc-toggle {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  padding: 8px 12px;\r\n  background: #1890ff;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n}\r\n\r\n.sidebar-toggle:hover,\r\n.toc-toggle:hover {\r\n  background: #40a9ff;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.sidebar-toggle.active,\r\n.toc-toggle.active {\r\n  background: #096dd9;\r\n}\r\n\r\n.icon-menu,\r\n.icon-list {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n.icon-menu::before {\r\n  content: '☰';\r\n}\r\n\r\n.icon-list::before {\r\n  content: '📋';\r\n}\r\n\r\n/* 遮罩层 */\r\n.overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 999;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n/* 侧边栏样式 */\r\n.sidebar {\r\n  width: 300px;\r\n  border-right: 1px solid #eee;\r\n  background-color: #f8f8f8;\r\n  overflow-y: auto;\r\n  height: 100%;\r\n  transition: transform 0.3s ease;\r\n  z-index: 1000;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.sidebar-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  font-size: 18px;\r\n  cursor: pointer;\r\n  color: #666;\r\n  padding: 0;\r\n  width: 24px;\r\n  height: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.close-btn:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.sidebar-menu { padding: 10px 0; }\r\n.menu-category { margin-bottom: 10px; margin-left: 40px; border-bottom: 1px solid #e7e7e7;}\r\n.category-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  padding: 8px 20px;\r\n  color: #333;\r\n}\r\n.menu-list { list-style: none; padding: 0; margin: 0; }\r\n.menu-item { padding: 0; }\r\n.menu-link {\r\n  display: block;\r\n  padding: 8px 20px 8px 30px;\r\n  color: #666;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n  transition: all 0.3s;\r\n}\r\n.menu-link:hover, .router-link-active {\r\n  color: #1890ff;\r\n  background-color: #e6f7ff;\r\n  border-right: 3px solid #1890ff;\r\n}\r\n/* 主内容区 */\r\n.main-content {\r\n  flex: 1;\r\n  padding: 30px 40px;\r\n  overflow-y: auto;\r\n  height: 100%;\r\n  transition: margin 0.3s ease;\r\n}\r\n\r\n.content-expanded {\r\n  margin-left: 0;\r\n  margin-right: 0;\r\n}\r\n\r\n.content-full {\r\n  margin-left: 0;\r\n  margin-right: 0;\r\n  padding: 20px;\r\n}\r\n\r\n/* 目录区域 */\r\n.toc {\r\n  width: 300px;\r\n  padding: 10px 20px 10px 20px;\r\n  border-left: 1px solid #eee;\r\n  background-color: #fff;\r\n  position: sticky;\r\n  top: 0;\r\n  max-height: 100vh;\r\n  overflow-y: auto;\r\n  transition: transform 0.3s ease;\r\n  z-index: 1000;\r\n}\r\n\r\n.toc-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 0;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.toc-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n.toc-list { list-style: none; padding: 0; margin: 0; }\r\n.toc-item { margin-bottom: 0px; }\r\n.toc-item-h3 { padding-left: 15px; }\r\n.toc-link {\r\n  color: #666;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n  display: block;\r\n  padding: 5px 0;\r\n  transition: color 0.3s;\r\n}\r\n.toc-link:hover { color: #1890ff; }\r\n.toc-item.active > .toc-link {\r\n  color: #1890ff;\r\n  font-weight: bold;\r\n  background: #e6f7ff;\r\n  border-radius: 3px;\r\n}\r\n.menu-link-active {\r\n  color: #1890ff;\r\n  background-color: #e6f7ff;\r\n  border-right: 3px solid #1890ff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 992px) {\r\n  .help-layout {\r\n    position: relative;\r\n  }\r\n\r\n  .mobile-controls {\r\n    display: flex;\r\n  }\r\n\r\n  .sidebar {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    height: 100vh;\r\n    z-index: 1000;\r\n    transform: translateX(-100%);\r\n  }\r\n\r\n  .sidebar-visible {\r\n    transform: translateX(0);\r\n  }\r\n\r\n  .sidebar-hidden {\r\n    transform: translateX(-100%);\r\n  }\r\n\r\n  .main-content {\r\n    width: 100%;\r\n    padding: 80px 20px 20px;\r\n  }\r\n\r\n  .toc {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    height: 100vh;\r\n    z-index: 1000;\r\n    transform: translateX(100%);\r\n  }\r\n\r\n  .toc-visible {\r\n    transform: translateX(0);\r\n  }\r\n\r\n  .toc-hidden {\r\n    transform: translateX(100%);\r\n  }\r\n}\r\n\r\n@media (min-width: 993px) {\r\n  .mobile-controls {\r\n    display: none;\r\n  }\r\n\r\n  .overlay {\r\n    display: none;\r\n  }\r\n\r\n  .sidebar-header,\r\n  .toc-header {\r\n    display: none;\r\n  }\r\n\r\n  .sidebar,\r\n  .toc {\r\n    position: static;\r\n    transform: none;\r\n  }\r\n}\r\n\r\n/* 平板适配 */\r\n@media (max-width: 1200px) and (min-width: 993px) {\r\n  .sidebar {\r\n    width: 250px;\r\n  }\r\n\r\n  .toc {\r\n    width: 250px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 20px 30px;\r\n  }\r\n}\r\n\r\n/* 小屏幕优化 */\r\n@media (max-width: 576px) {\r\n  .sidebar,\r\n  .toc {\r\n    width: 280px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 80px 15px 15px;\r\n  }\r\n\r\n  .mobile-controls {\r\n    left: 5px;\r\n    top: 65px;\r\n  }\r\n\r\n  .sidebar-toggle,\r\n  .toc-toggle {\r\n    padding: 6px 10px;\r\n    font-size: 11px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./HelpView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./HelpView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./HelpView.vue?vue&type=template&id=51c5a1c2&scoped=true&\"\nimport script from \"./HelpView.vue?vue&type=script&lang=js&\"\nexport * from \"./HelpView.vue?vue&type=script&lang=js&\"\nimport style0 from \"./HelpView.vue?vue&type=style&index=0&id=51c5a1c2&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"51c5a1c2\",\n  null\n  \n)\n\nexport default component.exports", "var map = {\n\t\"./cloud-storage.md\": [\n\t\t412,\n\t\t594\n\t],\n\t\"./deploy-serverless.md\": [\n\t\t7838,\n\t\t168\n\t],\n\t\"./docker-tutorial.md\": [\n\t\t354,\n\t\t912\n\t],\n\t\"./flux-dev.md\": [\n\t\t328,\n\t\t61\n\t],\n\t\"./frame-pack.md\": [\n\t\t1493,\n\t\t202\n\t],\n\t\"./gpu-selection.md\": [\n\t\t2696,\n\t\t198\n\t],\n\t\"./health-check.md\": [\n\t\t1063,\n\t\t719\n\t],\n\t\"./jupyter-lab.md\": [\n\t\t2395,\n\t\t469\n\t],\n\t\"./k8s-yaml-import.md\": [\n\t\t5305,\n\t\t956\n\t],\n\t\"./mirror.md\": [\n\t\t1628,\n\t\t675\n\t],\n\t\"./ollama-qwen-webui.md\": [\n\t\t9961,\n\t\t447\n\t],\n\t\"./ollama-qwen.md\": [\n\t\t6585,\n\t\t838\n\t],\n\t\"./personal-certification.md\": [\n\t\t2733,\n\t\t261\n\t],\n\t\"./privacy-policy.md\": [\n\t\t3751,\n\t\t802\n\t],\n\t\"./quick-start.md\": [\n\t\t1273,\n\t\t370\n\t],\n\t\"./qustion.md\": [\n\t\t4226,\n\t\t166\n\t],\n\t\"./register-login.md\": [\n\t\t4493,\n\t\t231\n\t],\n\t\"./stable-diffusion1.5.md\": [\n\t\t7507,\n\t\t576\n\t],\n\t\"./stable-diffusion2.1.md\": [\n\t\t1362,\n\t\t91\n\t],\n\t\"./stable-diffusion3.5-large.md\": [\n\t\t9045,\n\t\t371\n\t],\n\t\"./summary.md\": [\n\t\t1782,\n\t\t543\n\t],\n\t\"./user-agreement.md\": [\n\t\t7553,\n\t\t991\n\t],\n\t\"./whisper.md\": [\n\t\t3797,\n\t\t959\n\t]\n};\nfunction webpackAsyncContext(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\treturn Promise.resolve().then(function() {\n\t\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\t\te.code = 'MODULE_NOT_FOUND';\n\t\t\tthrow e;\n\t\t});\n\t}\n\n\tvar ids = map[req], id = ids[0];\n\treturn __webpack_require__.e(ids[1]).then(function() {\n\t\treturn __webpack_require__(id);\n\t});\n}\nwebpackAsyncContext.keys = function() { return Object.keys(map); };\nwebpackAsyncContext.id = 817;\nmodule.exports = webpackAsyncContext;"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "on", "$event", "showPopup", "hidePopup", "attrs", "directives", "name", "rawName", "value", "activePopup", "expression", "_v", "wechatQRCode", "contactQRCode", "showFeedbackModal", "showTooltip", "hideTooltip", "showFeedbackTooltip", "showModal", "target", "currentTarget", "closeFeedbackModal", "apply", "arguments", "feedback", "type", "$$selectedVal", "Array", "prototype", "filter", "call", "options", "o", "selected", "map", "val", "_value", "$set", "multiple", "showErrors", "_e", "description", "domProps", "composing", "triggerFileUpload", "preventDefault", "onFileDrop", "ref", "staticStyle", "onFileChange", "image", "imagePreview", "stopPropagation", "removeImage", "confirmSubmit", "showConfirmation", "closeConfirmation", "staticRenderFns", "data", "require", "instanceId", "methods", "$refs", "fileInput", "click", "event", "file", "files", "processImage", "dataTransfer", "match", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "submitFeedback", "formData", "FormData", "append", "component", "pauseCarousel", "resumeCarousel", "_l", "questions", "question", "index", "currentQuestionIndex", "key", "sendCarouselQuestion", "witde", "_s", "class", "showChat", "toggleChat", "_m", "messages", "message", "formatMessage", "text", "formatTime", "time", "loading", "userInput", "indexOf", "_k", "keyCode", "sendMessage", "trim", "Date", "historyMessages", "carouselTimer", "carouselI<PERSON>val", "isPaused", "<PERSON><PERSON><PERSON><PERSON>", "clearCarousel", "mounted", "startCarousel", "document", "getElementById", "link", "createElement", "id", "rel", "href", "head", "append<PERSON><PERSON><PERSON>", "that", "setInterval", "length", "console", "log", "clearInterval", "$nextTick", "scrollToBottom", "push", "userQuestion", "role", "content", "requestBody", "model", "stream", "presence_penalty", "frequency_penalty", "seed", "response", "fetch", "method", "headers", "Authorization", "body", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "aiResponseIndex", "done", "read", "chunk", "decode", "lines", "split", "line", "jsonString", "slice", "parse", "choices", "delta", "reasoning_content", "error", "Promise", "resolve", "setTimeout", "container", "messagesContainer", "scrollTop", "scrollHeight", "date", "toLocaleTimeString", "hour", "minute", "replace", "isMobile", "sidebarVisible", "toggleSidebar", "tocVisible", "toggleToc", "closeAllPanels", "closeSidebar", "menu", "category", "title", "items", "item", "path", "isMenuActive", "onMenuItemClick", "currentDoc", "getPrevPage", "getNextPage", "buildToc", "closeToc", "toc", "level", "activeTocId", "scrollToAnchor", "markdownContent", "prevPage", "nextPage", "props", "doc", "String", "Object", "watch", "immediate", "module", "default", "processContent", "$emit", "slugify", "slug", "toLowerCase", "test", "Math", "random", "toString", "substring", "contentRef", "headings", "querySelectorAll", "for<PERSON>ach", "heading", "textContent", "codeBlocks", "block", "classList", "add", "links", "addEventListener", "getAttribute", "targetElement", "scrollIntoView", "behavior", "components", "HelpContent", "chatAi", "<PERSON><PERSON>", "allPages", "isAnchorClicking", "windowWidth", "sidebarScrollPosition", "computed", "$route", "params", "currentPath", "created", "flattenPages", "checkScreenSize", "updatePageTitle", "savedScrollPosition", "localStorage", "getItem", "parseInt", "mainContent", "handleContentScroll", "sidebar", "handleSidebarScroll", "restoreSidebarScrollPosition", "window", "handleResize", "removeEventListener", "now", "currentIndex", "findIndex", "page", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "querySelector", "from", "h", "existingId", "tagName", "currentPageTitle", "OFFSET", "element", "offsetTop", "activeId", "i", "innerWidth", "was<PERSON><PERSON><PERSON>", "clearTimeout", "resizeTimer", "setItem", "requestAnimationFrame", "webpackAsyncContext", "req", "__webpack_require__", "then", "Error", "code", "ids", "keys", "exports"], "sourceRoot": ""}