{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('Layout', [_c('div', {\n    staticClass: \"coming-soon-container\"\n  }, [_c('h1', {\n    staticClass: \"title\"\n  }, [_vm._v(\"敬请期待\")]), _c('p', {\n    staticClass: \"subtitle\"\n  }, [_vm._v(\"我们正在努力建设中，敬请期待更多精彩内容！\")])])]);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/views/AlgorithmCommunity.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('Layout',[_c('div',{staticClass:\"coming-soon-container\"},[_c('h1',{staticClass:\"title\"},[_vm._v(\"敬请期待\")]),_c('p',{staticClass:\"subtitle\"},[_vm._v(\"我们正在努力建设中，敬请期待更多精彩内容！\")])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,QAAQ,EAAC,CAACA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAuB,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAO,CAAC,EAAC,CAACH,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACH,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAU,CAAC,EAAC,CAACH,GAAG,CAACI,EAAE,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtP,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AAExB,SAASN,MAAM,EAAEM,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}