"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[112],{442:function(a,t,i){i.r(t),i.d(t,{default:function(){return r}});var s=function(){var a=this,t=a._self._c;return t("Layout",[t("div",{staticClass:"layout-container",staticStyle:{width:"100%"}},[t("div",{staticClass:"page-header"},[t("div",{staticClass:"am-container"},[t("h1",{staticClass:"page-header-title"},[a._v("客户案例")])])]),t("div",{staticClass:"breadcrumb-box"},[t("div",{staticClass:"am-container"},[t("ol",{staticClass:"am-breadcrumb"},[t("li",[t("router-link",{attrs:{to:"/"}},[a._v("首页")])],1),t("li",{staticClass:"am-active"},[a._v("客户案例")])])])])]),t("div",{staticClass:"section example"},[t("div",{staticClass:"container",staticStyle:{"max-width":"1160px",margin:"0 auto"}},[t("div",{staticClass:"section--header"},[t("h2",{staticClass:"section--title"},[a._v("全球首创 自主创新")]),t("p",{staticClass:"section--description"},[a._v(" Enterplorer Studio是一套面向企业级移动信息化建设的开发平台。集聚开发、测试、 "),t("br"),a._v("打包、发布于一体的移动化开发综合平台。 ")])]),t("div",{staticClass:"example-container"},[t("div",{staticClass:"am-tabs"},[t("ul",{staticClass:"am-tabs-nav am-nav am-nav-tabs am-g"},a._l(a.tabList,(function(i,s){return t("li",{key:s,staticClass:"am-u-md-2",class:a.tabIndex===s?"am-active":"",on:{click:function(t){return t.preventDefault(),a.changeTab(s)}}},[t("a",{attrs:{href:"#"}},[t("i",{class:i.icon}),a._v(a._s(i.name))])])})),0),t("div",{staticClass:"tabs"},a._l(a.list,(function(a,i){return t("div",{key:i,staticClass:"tab"},[t("img",{attrs:{src:a,alt:""}})])})),0)])])])])])},e=[],n=(i(7658),i(9891)),c={name:"ExampleView",components:{Layout:n.Z},data(){return{tabIndex:0,tabList:[{id:1,name:"主要案例",icon:"am-icon-map-o"},{id:1,name:"客户案例一",icon:"am-icon-scribd"},{id:1,name:"客户案例二",icon:"am-icon-odnoklassniki"},{id:1,name:"客户案例三",icon:"am-icon-building-o"},{id:1,name:"客户案例四",icon:"am-icon-hand-scissors-o"},{id:1,name:"客户案例五",icon:"am-icon-camera"}],list:[]}},mounted(){this.findExampleByExampleType(0)},methods:{changeTab(a){this.tabIndex=a,this.findExampleByExampleType(a)},findExampleByExampleType(a){this.getRequest(`/findExampleByExampleType/${a}`).then((a=>{if(a){const t=a.data.data,i=[];for(let a=1;a<17;a++)null!=t["image"+a]&&i.push(t["image"+a]);this.list=i}}))}}},l=c,o=i(1001),m=(0,o.Z)(l,s,e,!1,null,"3ae80592",null),r=m.exports}}]);
//# sourceMappingURL=112.843bd72d.js.map