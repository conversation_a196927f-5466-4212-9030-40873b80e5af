{"ast": null, "code": "import Layout from \"@/components/common/Layout\";\nexport default {\n  name: \"NewsDetailsView\",\n  components: {\n    Layout\n  },\n  data() {\n    return {\n      article: {},\n      recentArticles: []\n    };\n  },\n  mounted() {\n    this.getArticleByArticleId(this.$route.params.newsId);\n  },\n  methods: {\n    getArticleByArticleId(articleId) {\n      this.getRequest(`/findArticleByArticleId/${articleId}`).then(resp => {\n        if (resp) {\n          this.article = resp.data.data;\n          this.getRecentArticle(articleId);\n        }\n      });\n    },\n    getRecentArticle(currentArticleId) {\n      this.getRequest(`/findRecentArticle/${currentArticleId}`).then(resp => {\n        if (resp) {\n          this.recentArticles = resp.data.data;\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "name", "components", "data", "article", "recentArticles", "mounted", "getArticleByArticleId", "$route", "params", "newsId", "methods", "articleId", "getRequest", "then", "resp", "getRecentArticle", "currentArticleId"], "sources": ["src/views/NewsDetailsView.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t\t\r\n\t\t<div class=\"layout-container\" style=\"width: 100%;\">\r\n\t\t\t<div class=\"page-header\" style=\"max-height: 14px;\"></div>\r\n\t\t\t<div class=\"breadcrumb-box\">\r\n\t\t\t\t<div class=\"am-container\">\r\n\t\t\t\t\t<ol class=\"am-breadcrumb\">\r\n\t\t\t\t\t\t<li><router-link to=\"/news\">公司动态</router-link></li>\r\n\t\t\t\t\t\t<li class=\"am-active\">文章详情</li>\r\n\t\t\t\t\t</ol>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t\t<div class=\"section\">\r\n\t\t\t<div class=\"container\" style=\"max-width: 1160px\">\r\n\t\t\t\t<div class=\"section--header\">\r\n\t\t\t\t\t<h2 class=\"section--title\">{{article.title}}</h2>\r\n\t\t\t\t\t<p class=\"section--description\">{{article.introduction}}</p>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div class=\"join-container\">\r\n\t\t\t\t\t<div class=\"am-g\">\r\n\t\t\t\t\t\t<div class=\"am-u-md-3\">\r\n\t\t\t\t\t\t\t<div class=\"careers--articles\">\r\n\t\t\t\t\t\t\t\t<!--<h3 class=\"careers&#45;&#45;subtitle\">文章信息</h3>-->\r\n\t\t\t\t\t\t\t\t<div class=\"careers_articles\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"careers_article\" v-for=\"(article,index) in recentArticles\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"image\">\r\n\t\t\t\t\t\t\t\t\t\t\t<img style=\"height: 160px;\" :src=\"article.cover\" alt=\"\">\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<h3 class=\"careers_article--title\">{{ article.title }}</h3>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"careers_article--text\">{{article.introduction}}</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"careers_article--footer\">\r\n\t\t\t\t\t\t\t\t\t\t\t<router-link :to=\"{name:'newsDetails',params:{newsId:article.articleId}}\" class=\"link\">查看更多</router-link>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<div class=\"am-u-md-9\">\r\n\t\t\t\t\t\t\t<!--<h3 class=\"careers&#45;&#45;subtitle\">文章内容</h3>-->\r\n\t\t\t\t\t\t\t<div class=\"careers--vacancies\">\r\n\t\t\t\t\t\t\t\t<div class=\"am-panel-group\" id=\"accordion\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"am-panel am-panel-default\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"am-panel-hd\">\r\n\t\t\t\t\t\t\t\t\t\t\t<h4 class=\"am-panel-title\" data-am-collapse=\"{parent: '#accordion', target: '#do-not-say-1'}\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t作者：{{article.author}} &nbsp;&nbsp;&nbsp;&nbsp; / &nbsp;&nbsp;&nbsp;&nbsp; 发布时间：{{article.createTime}}\r\n\t\t\t\t\t\t\t\t\t\t\t</h4>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"am-panel-collapse am-collapse am-in\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"am-panel-bd\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"vacancies--item_content js-accordion--pane_content\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-html=\"article.contentHtml\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\n\r\nexport default {\r\n\tname: \"NewsDetailsView\",\r\n\tcomponents: {Layout,},\r\n\tdata(){\r\n\t\treturn{\r\n\t\t\tarticle:{},\r\n\t\t\trecentArticles:[]\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.getArticleByArticleId(this.$route.params.newsId)\r\n\t},\r\n\tmethods:{\r\n\t\tgetArticleByArticleId(articleId){\r\n\t\t\tthis.getRequest(`/findArticleByArticleId/${articleId}`).then(resp =>{\r\n\t\t\t\tif (resp){\r\n\t\t\t\t\tthis.article = resp.data.data\r\n\t\t\t\t\tthis.getRecentArticle(articleId)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetRecentArticle(currentArticleId){\r\n\t\t\tthis.getRequest(`/findRecentArticle/${currentArticleId}`).then(resp =>{\r\n\t\t\t\tif (resp){\r\n\t\t\t\t\tthis.recentArticles = resp.data.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": "AAwEA,OAAAA,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,qBAAA,MAAAC,MAAA,CAAAC,MAAA,CAAAC,MAAA;EACA;EACAC,OAAA;IACAJ,sBAAAK,SAAA;MACA,KAAAC,UAAA,4BAAAD,SAAA,IAAAE,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACA,KAAAX,OAAA,GAAAW,IAAA,CAAAZ,IAAA,CAAAA,IAAA;UACA,KAAAa,gBAAA,CAAAJ,SAAA;QACA;MACA;IACA;IACAI,iBAAAC,gBAAA;MACA,KAAAJ,UAAA,uBAAAI,gBAAA,IAAAH,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACA,KAAAV,cAAA,GAAAU,IAAA,CAAAZ,IAAA,CAAAA,IAAA;QACA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}