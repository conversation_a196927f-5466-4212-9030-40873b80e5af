{"version": 3, "file": "js/docs4.1e1da226.js", "mappings": "wHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,aACrCK,EAA6B,IAAIL,IAAI,YACrCM,EAA6B,IAAIN,IAAI,aACrCO,EAA6B,IAAIP,IAAI,YACrCQ,EAA6B,IAAIR,IAAI,aAErCS,EAAO,mbAAwcV,EAA6B,4KAAsLE,EAA6B,wGAAkHC,EAA6B,kIAA4IC,EAA6B,4IAAsJC,EAA6B,kOAA8OC,EAA6B,iNAA2NC,EAA6B,kLAA8LC,EAA6B,8GAAsHC,EAA6B,2FAE/hE,c", "sources": ["webpack://portal-ui/./src/docs/frame-pack.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/universal1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/universal2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/frame-pack3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/frame-pack4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/frame-pack5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/frame-pack6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/frame-pack7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/frame-pack8.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_8___ = new URL(\"./imgs/frame-pack9.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"容器化部署-framepack-f1-图生视频框架\\\"><font style=\\\"color:#020817\\\">容器化部署 FramePack-F1 图生视频框架</font></h1> <h2 id=\\\"1-部署步骤\\\"><font style=\\\"color:#020817\\\">1 部署步骤</font></h2> <p><font style=\\\"color:#020817\\\">我们提供了构建完毕的 FramePack-F1 镜像可以直接部署使用。</font></p> <h3 id=\\\"11-访问天工开物控制台，点击新增部署。\\\"><font style=\\\"color:#020817\\\">1.1 访问</font><a href=\\\"https://tiangongkaiwu.top/#/console\\\">天工开物控制台</a><font style=\\\"color:#020817\\\">，点击新增部署。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"12-基于自身需要进行配置，参考配置为单卡-4090-和-1-个节点（初次使用进行调试）。\\\"><font style=\\\"color:#020817\\\">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"13-选择相应预制镜像\\\"><font style=\\\"color:#020817\\\">1.3 选择相应预制镜像</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"14-点击部署服务，耐心等待节点拉取镜像并启动。\\\"><font style=\\\"color:#020817\\\">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"15-节点启动后，你所在任务详情页中看到的内容可能如下：\\\"><font style=\\\"color:#020817\\\">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"16-我们可以点击快速访问下方7860端口的链接，测试-gradio-运行情况\\\"><font style=\\\"color:#020817\\\">1.6 我们可以点击快速访问下方“7860”端口的链接，测试 Gradio 运行情况</font></h3> <p><font style=\\\"color:#020817\\\">我们首先点击该输入框上传一张图片，如下图：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">接下来填写 prompt（该模型对英文支持性较好），描述我们希望图片中的人物如何活动。</font></p> <p><font style=\\\"color:#020817\\\">最后点击生成按钮，可以看到右侧已经出现了一个预览框，并且下方也出现了进度条，接下来我们耐心稍等片刻：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"17-保存视频\\\"><font style=\\\"color:#020817\\\">1.7 保存视频</font></h3> <p><font style=\\\"color:#020817\\\">如果我们需要保存该视频到本地，可以在视频生成完毕后，点击视频右上角的下载按钮：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">或者我们也可以选择直接右键该视频，选择“视频另存为”，选择想要保存的位置：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_8___ + \"\\\" alt=\\\"\\\"></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/17 17:22</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "___HTML_LOADER_IMPORT_8___", "code"], "sourceRoot": ""}