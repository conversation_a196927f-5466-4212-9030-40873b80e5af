{"version": 3, "file": "js/567.62bf4a79.js", "mappings": "iJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAUF,EAAIG,MAAMC,YAAY,OAAOF,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACH,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,IAAI,CAACG,YAAY,YAAYC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,SAAS,IAAI,CAACN,EAAG,MAAM,CAACG,YAAY,OAAOI,MAAM,CAAC,IAAMC,EAAQ,MAAkC,IAAM,cAAcV,EAAIW,GAAG,GAAGT,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,MAAM,CAACG,YAAY,uBAAuBL,EAAIY,GAAIZ,EAAIa,SAAS,SAASC,EAAOC,GAAO,OAAOb,EAAG,MAAM,CAACc,IAAID,EAAMV,YAAY,cAAcY,MAAO,CACrjBC,eAA2B,GAARH,EAAF,IACjBI,UAAY,cAAqB,EAARJ,SACvB,CAACb,EAAG,MAAM,CAACG,YAAY,kBAAkB,IAAG,GAAGH,EAAG,MAAM,CAACG,YAAY,kBAAkBH,EAAG,MAAM,CAACG,YAAY,YAAYL,EAAIY,GAAIZ,EAAIoB,UAAU,SAASC,EAAQN,GAAO,OAAOb,EAAG,MAAM,CAACc,IAAID,EAAMV,YAAY,gBAAgB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,KAAK,CAACF,EAAIsB,GAAGtB,EAAIuB,GAAGF,EAAQG,UAAUtB,EAAG,IAAI,CAACF,EAAIsB,GAAGtB,EAAIuB,GAAGF,EAAQI,mBAAmB,IAAG,GAAGvB,EAAG,MAAM,CAACG,YAAY,uBAAuBL,EAAIY,GAAI,IAAI,SAASc,GAAG,OAAOxB,EAAG,MAAM,CAACc,IAAIU,EAAErB,YAAY,oBAAoBY,MAAO,CAC7eU,KAAyB,IAAhBC,KAAKC,SAAP,IACPC,IAAwB,IAAhBF,KAAKC,SAAP,IACNE,kBAAsB,EAAoB,GAAhBH,KAAKC,SAAX,IACpBX,eAAmC,EAAhBU,KAAKC,SAAP,MACf,IAAG,IAClB,EACIG,EAAkB,CAAC,WAAY,IAAIhC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAUF,EAAIG,MAAMC,YAAY,OAAOF,EAAG,MAAM,CAACG,YAAY,eAAe,CAACH,EAAG,KAAK,CAACG,YAAY,UAAU,CAACL,EAAIsB,GAAG,iBAAiBpB,EAAG,IAAI,CAACG,YAAY,cAAc,CAACL,EAAIsB,GAAG,2BAC5O,G,mBCsDA,MAAAW,EAAA,CACAC,SAAA,gkBAeAC,EAAA,CACAD,SAAA,8bAUAE,EAAA,CACAF,SAAA,oRAOA,OAAAG,EAAAA,EAAAA,IAAA,CACAC,KAAA,kBACAC,WAAA,CACAN,kBACAE,aACAC,cAEAI,QAAA,CACAhC,WAAAiC,GAEA,KAAAC,aAAA,KAAAA,cAAAD,GACA,KAAAE,mBAAA,KAAAD,YAGA,KAAAE,WAAA,KACA,MAAAC,EAAAC,SAAAC,iBAAA,yBACAF,EAAAG,SAAAC,KACAA,EAAAC,UAAAC,SAAA,WACA,WAAAV,GAAAQ,EAAAC,UAAAC,SAAA,gBACAF,EAAAC,UAAAC,SAAA,iBACAF,EAAAC,UAAAE,IAAA,eAGAC,YAAA,KACAJ,EAAAC,UAAAI,OAAA,iBACA,KACA,IAIA,KAAAZ,YAAAD,CAAA,KAGA,KAAAC,YAAAD,EAIA,KAAAc,OAAAd,OAAAA,EACA,KAAAG,WAAA,KACAY,OAAAC,SAAA,CACA3B,IAAA,EACA4B,SAAA,YAEA,KAAAC,QAAAC,GAAA,OAIA,KAAAD,QAAAE,KAAApB,GACAe,OAAAC,SAAA,CACA3B,IAAA,EACA4B,SAAA,YAGA,GAEAI,QACA,MAAAC,GAAAC,EAAAA,EAAAA,IAAA,4BACAnD,GAAAmD,EAAAA,EAAAA,IAAAC,MAAA,GAAAC,KAAA,OAEA9C,GAAA4C,EAAAA,EAAAA,IAAA,CACA,CACAG,KAAA,kBACA3C,MAAA,QACAC,YAAA,iCAEA,CACA0C,KAAA,iBACA3C,MAAA,OACAC,YAAA,8BAKA,OACAsC,UACAlD,UACAO,WAEA,IClLwQ,I,UCQpQgD,GAAY,OACd,EACArE,EACAiC,GACA,EACA,KACA,WACA,MAIF,EAAeoC,EAAiB,O,oECnBhC,IAAIrE,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,oBAAoB,GAAGA,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,MAAM,CAACG,YAAY,wBAAwB,CAACH,EAAG,KAAK,CAACF,EAAIsB,GAAG,aAAapB,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,IAAI,CAACG,YAAY,aAAa,CAACL,EAAIsB,GAAG,mCAAmCpB,EAAG,MAAM,CAACG,YAAY,cAAcgE,MAAM,CAAE,MAASrE,EAAIsE,OAAOC,QAAS,CAACrE,EAAG,MAAM,CAACG,YAAY,yBAAyB,CAACH,EAAG,QAAQ,CAACsE,WAAW,CAAC,CAAClC,KAAK,QAAQmC,QAAQ,UAAUC,MAAO1E,EAAI2E,iBAAiBJ,MAAOK,WAAW,2BAA2BnE,MAAM,CAAC,KAAO,OAAO,YAAc,UAAUoE,SAAS,CAAC,MAAS7E,EAAI2E,iBAAiBJ,OAAQjE,GAAG,CAAC,KAAON,EAAI8E,cAAc,MAAQ,SAASvE,GAAWA,EAAOwE,OAAOC,WAAiBhF,EAAIiF,KAAKjF,EAAI2E,iBAAkB,QAASpE,EAAOwE,OAAOL,MAAM,OAAOxE,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAAEL,EAAIsE,OAAOC,MAAOrE,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACL,EAAIsB,GAAGtB,EAAIuB,GAAGvB,EAAIsE,OAAOC,UAAUvE,EAAIkF,SAAShF,EAAG,MAAM,CAACG,YAAY,cAAcgE,MAAM,CAAE,MAASrE,EAAIsE,OAAOa,WAAY,CAACjF,EAAG,MAAM,CAACG,YAAY,4BAA4B,CAAgD,cAA7CL,EAAIoF,gBAAkB,OAAS,YAA0BlF,EAAG,QAAQ,CAACsE,WAAW,CAAC,CAAClC,KAAK,QAAQmC,QAAQ,UAAUC,MAAO1E,EAAI2E,iBAAiBQ,SAAUP,WAAW,8BAA8BnE,MAAM,CAAC,YAAc,QAAQ,KAAO,YAAYoE,SAAS,CAAC,QAAUZ,MAAMoB,QAAQrF,EAAI2E,iBAAiBQ,UAAUnF,EAAIsF,GAAGtF,EAAI2E,iBAAiBQ,SAAS,OAAO,EAAGnF,EAAI2E,iBAAiBQ,UAAW7E,GAAG,CAAC,KAAON,EAAIuF,iBAAiB,OAAS,SAAShF,GAAQ,IAAIiF,EAAIxF,EAAI2E,iBAAiBQ,SAASM,EAAKlF,EAAOwE,OAAOW,IAAID,EAAKE,QAAuB,GAAG1B,MAAMoB,QAAQG,GAAK,CAAC,IAAII,EAAI,KAAKC,EAAI7F,EAAIsF,GAAGE,EAAII,GAAQH,EAAKE,QAASE,EAAI,GAAI7F,EAAIiF,KAAKjF,EAAI2E,iBAAkB,WAAYa,EAAIM,OAAO,CAACF,KAAaC,GAAK,GAAI7F,EAAIiF,KAAKjF,EAAI2E,iBAAkB,WAAYa,EAAIO,MAAM,EAAEF,GAAKC,OAAON,EAAIO,MAAMF,EAAI,IAAM,MAAM7F,EAAIiF,KAAKjF,EAAI2E,iBAAkB,WAAYe,EAAK,KAAoD,WAA7C1F,EAAIoF,gBAAkB,OAAS,YAAuBlF,EAAG,QAAQ,CAACsE,WAAW,CAAC,CAAClC,KAAK,QAAQmC,QAAQ,UAAUC,MAAO1E,EAAI2E,iBAAiBQ,SAAUP,WAAW,8BAA8BnE,MAAM,CAAC,YAAc,QAAQ,KAAO,SAASoE,SAAS,CAAC,QAAU7E,EAAIgG,GAAGhG,EAAI2E,iBAAiBQ,SAAS,OAAO7E,GAAG,CAAC,KAAON,EAAIuF,iBAAiB,OAAS,SAAShF,GAAQ,OAAOP,EAAIiF,KAAKjF,EAAI2E,iBAAkB,WAAY,KAAK,KAAKzE,EAAG,QAAQ,CAACsE,WAAW,CAAC,CAAClC,KAAK,QAAQmC,QAAQ,UAAUC,MAAO1E,EAAI2E,iBAAiBQ,SAAUP,WAAW,8BAA8BnE,MAAM,CAAC,YAAc,QAAQ,KAAOT,EAAIoF,gBAAkB,OAAS,YAAYP,SAAS,CAAC,MAAS7E,EAAI2E,iBAAiBQ,UAAW7E,GAAG,CAAC,KAAON,EAAIuF,iBAAiB,MAAQ,SAAShF,GAAWA,EAAOwE,OAAOC,WAAiBhF,EAAIiF,KAAKjF,EAAI2E,iBAAkB,WAAYpE,EAAOwE,OAAOL,MAAM,KAAKxE,EAAG,OAAO,CAACG,YAAY,kBAAkBC,GAAG,CAAC,MAAQN,EAAIiG,2BAA2B,CAAC/F,EAAG,IAAI,CAACmE,MAAM,CAAC,WAAYrE,EAAIoF,gBAAkB,UAAY,UAAUlF,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAAEL,EAAIsE,OAAOa,SAAUjF,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACL,EAAIsB,GAAGtB,EAAIuB,GAAGvB,EAAIsE,OAAOa,aAAanF,EAAIkF,SAAShF,EAAG,MAAM,CAACG,YAAY,cAAcgE,MAAM,CAAE,MAASrE,EAAIsE,OAAO4B,kBAAmB,CAAChG,EAAG,MAAM,CAACG,YAAY,4BAA4B,CAAuD,cAApDL,EAAImG,uBAAyB,OAAS,YAA0BjG,EAAG,QAAQ,CAACsE,WAAW,CAAC,CAAClC,KAAK,QAAQmC,QAAQ,UAAUC,MAAO1E,EAAI2E,iBAAiBuB,gBAAiBtB,WAAW,qCAAqCnE,MAAM,CAAC,YAAc,UAAU,KAAO,YAAYoE,SAAS,CAAC,QAAUZ,MAAMoB,QAAQrF,EAAI2E,iBAAiBuB,iBAAiBlG,EAAIsF,GAAGtF,EAAI2E,iBAAiBuB,gBAAgB,OAAO,EAAGlG,EAAI2E,iBAAiBuB,iBAAkB5F,GAAG,CAAC,KAAON,EAAIoG,wBAAwB,OAAS,SAAS7F,GAAQ,IAAIiF,EAAIxF,EAAI2E,iBAAiBuB,gBAAgBT,EAAKlF,EAAOwE,OAAOW,IAAID,EAAKE,QAAuB,GAAG1B,MAAMoB,QAAQG,GAAK,CAAC,IAAII,EAAI,KAAKC,EAAI7F,EAAIsF,GAAGE,EAAII,GAAQH,EAAKE,QAASE,EAAI,GAAI7F,EAAIiF,KAAKjF,EAAI2E,iBAAkB,kBAAmBa,EAAIM,OAAO,CAACF,KAAaC,GAAK,GAAI7F,EAAIiF,KAAKjF,EAAI2E,iBAAkB,kBAAmBa,EAAIO,MAAM,EAAEF,GAAKC,OAAON,EAAIO,MAAMF,EAAI,IAAM,MAAM7F,EAAIiF,KAAKjF,EAAI2E,iBAAkB,kBAAmBe,EAAK,KAA2D,WAApD1F,EAAImG,uBAAyB,OAAS,YAAuBjG,EAAG,QAAQ,CAACsE,WAAW,CAAC,CAAClC,KAAK,QAAQmC,QAAQ,UAAUC,MAAO1E,EAAI2E,iBAAiBuB,gBAAiBtB,WAAW,qCAAqCnE,MAAM,CAAC,YAAc,UAAU,KAAO,SAASoE,SAAS,CAAC,QAAU7E,EAAIgG,GAAGhG,EAAI2E,iBAAiBuB,gBAAgB,OAAO5F,GAAG,CAAC,KAAON,EAAIoG,wBAAwB,OAAS,SAAS7F,GAAQ,OAAOP,EAAIiF,KAAKjF,EAAI2E,iBAAkB,kBAAmB,KAAK,KAAKzE,EAAG,QAAQ,CAACsE,WAAW,CAAC,CAAClC,KAAK,QAAQmC,QAAQ,UAAUC,MAAO1E,EAAI2E,iBAAiBuB,gBAAiBtB,WAAW,qCAAqCnE,MAAM,CAAC,YAAc,UAAU,KAAOT,EAAImG,uBAAyB,OAAS,YAAYtB,SAAS,CAAC,MAAS7E,EAAI2E,iBAAiBuB,iBAAkB5F,GAAG,CAAC,KAAON,EAAIoG,wBAAwB,MAAQ,SAAS7F,GAAWA,EAAOwE,OAAOC,WAAiBhF,EAAIiF,KAAKjF,EAAI2E,iBAAkB,kBAAmBpE,EAAOwE,OAAOL,MAAM,KAAKxE,EAAG,OAAO,CAACG,YAAY,kBAAkBC,GAAG,CAAC,MAAQN,EAAIqG,kCAAkC,CAACnG,EAAG,IAAI,CAACmE,MAAM,CAAC,WAAYrE,EAAImG,uBAAyB,UAAY,UAAUjG,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAAEL,EAAIsE,OAAO4B,gBAAiBhG,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACL,EAAIsB,GAAGtB,EAAIuB,GAAGvB,EAAIsE,OAAO4B,oBAAoBlG,EAAIkF,SAAShF,EAAG,MAAM,CAACG,YAAY,gCAAgCgE,MAAM,CAAE,MAASrE,EAAIsE,OAAOgC,OAAQ,CAACpG,EAAG,MAAM,CAACG,YAAY,wBAAwB,CAACH,EAAG,QAAQ,CAACsE,WAAW,CAAC,CAAClC,KAAK,QAAQmC,QAAQ,UAAUC,MAAO1E,EAAI2E,iBAAiB2B,KAAM1B,WAAW,0BAA0BnE,MAAM,CAAC,KAAO,OAAO,YAAc,UAAUoE,SAAS,CAAC,MAAS7E,EAAI2E,iBAAiB2B,MAAOhG,GAAG,CAAC,KAAON,EAAIuG,kBAAkB,MAAQ,SAAShG,GAAWA,EAAOwE,OAAOC,WAAiBhF,EAAIiF,KAAKjF,EAAI2E,iBAAkB,OAAQpE,EAAOwE,OAAOL,MAAM,KAAKxE,EAAG,SAAS,CAACG,YAAY,sBAAsBI,MAAM,CAAC,UAAYT,EAAI2E,iBAAiBJ,OAASvE,EAAIsE,OAAOC,OAASvE,EAAIwG,UAAUlG,GAAG,CAAC,MAAQN,EAAIyG,sBAAsB,CAACzG,EAAIsB,GAAG,IAAItB,EAAIuB,GAAGvB,EAAIwG,SAAY,GAAExG,EAAI0G,gBAAkB,SAAS,SAASxG,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAAEL,EAAIsE,OAAOgC,KAAMpG,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACL,EAAIsB,GAAGtB,EAAIuB,GAAGvB,EAAIsE,OAAOgC,SAAStG,EAAIkF,SAAShF,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACL,EAAIsB,GAAG,qBAAqBpB,EAAG,cAAc,CAACO,MAAM,CAAC,GAAK,yBAAyB,CAACT,EAAIsB,GAAG,UAAUtB,EAAIsB,GAAG,MAAMpB,EAAG,cAAc,CAACO,MAAM,CAAC,GAAK,yBAAyB,CAACT,EAAIsB,GAAG,WAAW,GAAGpB,EAAG,SAAS,CAACG,YAAY,eAAeC,GAAG,CAAC,MAAQN,EAAI2G,WAAW,CAAC3G,EAAIsB,GAAG,UAAUpB,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,IAAI,CAACO,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,cAAc,IAAI,CAACR,EAAIsB,GAAG,UAAUpB,EAAG,OAAO,CAACG,YAAY,WAAW,CAACL,EAAIsB,GAAG,OAAOpB,EAAG,IAAI,CAACO,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOP,EAAIQ,WAAW,SAAS,IAAI,CAACR,EAAIsB,GAAG,oBAAqBtB,EAAI4G,aAAc1G,EAAG,oBAAoB,CAACO,MAAM,CAAC,QAAU,uBAAuB,KAAO,UAAU,SAAW,IAAK,UAAYT,EAAI6G,WAAWvG,GAAG,CAAC,MAAQ,SAASC,GAAQP,EAAI4G,cAAe,CAAK,KAAK5G,EAAIkF,MAAM,EACvmO,EACIlD,EAAkB,G,kDC0HtB,GACAM,KAAA,WACAC,WAAA,CACAuE,kBAAA,IACAC,gBAAAA,EAAAA,GAEAC,OACA,OACArC,iBAAA,CACAJ,MAAA,GACAY,SAAA,GACAe,gBAAA,GACAI,KAAA,GACAW,SAAA,QACAC,MAAA,OACAC,WAAA,GACAC,WAAA,GAEAC,eAAA,EACAjC,iBAAA,EACAe,wBAAA,EACA7B,OAAA,CACAC,MAAA,GACAY,SAAA,GACAe,gBAAA,GACAI,KAAA,IAEAE,UAAA,EACAE,UAAA,GACAY,MAAA,KACAV,cAAA,EACAC,UAAA,OAEA,EACAU,MAAA,CACA,yBAAAC,GACA,KAAA7C,iBAAAsC,SAAAO,CACA,GAEAC,UAEA,KAAAC,qBACA,KAAAC,MAAA,eACA,EACAC,SAAA,CACAC,cACA,OACA,KAAAlD,iBAAAJ,OACA,KAAAI,iBAAAQ,UACA,KAAAR,iBAAAuB,iBACA,KAAAvB,iBAAA2B,MACA,KAAA3B,iBAAAyC,YACA,KAAA9C,OAAAC,QACA,KAAAD,OAAAa,WACA,KAAAb,OAAA4B,kBACA,KAAA5B,OAAAgC,IAEA,GAEA9D,QAAA,CACAkF,qBAEA,MAAAI,EAAAC,aAAAC,QAAA,wBACA,GAAAF,EAAA,CACA,MAAAG,EAAAC,KAAAC,MAAAL,GAEA,GAAAG,EAAA1D,OAAA,KAAAI,iBAAAJ,OAAA0D,EAAA1D,QAAA,KAAAI,iBAAAJ,MAAA,CAEA,MAAA6D,EAAAC,KAAAC,MACAC,EAAAN,EAAAO,UAAA,IAEA,GAAAJ,EAAAG,EAAA,CAEA,MAAAE,EAAA7G,KAAA8G,MAAAH,EAAAH,GAAA,KACA,KAAA1B,UAAA+B,EACA,KAAAjC,UAAA,EACA,KAAAmC,gBACA,MAEAZ,aAAAa,WAAA,uBAEA,CACA,CACA,EACA9D,gBACA,MAAA+D,EAAA,gBACA,KAAAlE,iBAAAJ,MAEAsE,EAAAC,KAAA,KAAAnE,iBAAAJ,OAGA,KAAAD,OAAAC,MAAA,GAFA,KAAAD,OAAAC,MAAA,YAFA,KAAAD,OAAAC,MAAA,QAMA,EAEAgB,mBACA,MAAAJ,EAAA,KAAAR,iBAAAQ,SAGA,GAFA,KAAAb,OAAAa,SAAA,IAEAA,EAEA,YADA,KAAAb,OAAAa,SAAA,SAKA,GAAAA,EAAA4D,OAAA,EAEA,YADA,KAAAzE,OAAAa,SAAA,aAKA,MAAA6D,EAAA,WAAAF,KAAA3D,GACA8D,EAAA,QAAAH,KAAA3D,GACA+D,EAAA,sCAAAJ,KAAA3D,GACAgE,EAAA,QAAAL,KAAA3D,IAAA,QAAA2D,KAAA3D,GAEA,IAAAiE,EAAA,EAKA,GAJAJ,GAAAC,GAAAG,IACAF,GAAAE,IACAD,GAAAC,IAEAA,GAAA,EACA,KAAA9E,OAAAa,SAAA,OACA,CACA,MAAAkE,EAAA,GACAL,GAAAC,GAAAI,EAAAxF,KAAA,WACAqF,GAAAG,EAAAxF,KAAA,UACAsF,GAAAE,EAAAxF,KAAA,WAEA,KAAAS,OAAAa,SAAA,sBAAAkE,EAAAC,KAAA,MACA,CAGA,KAAA3E,iBAAAuB,iBACA,KAAAE,yBAEA,EAEA5F,WAAAiC,GAEA,KAAAc,OAAAd,OAAAA,EAEA,KAAAG,WAAA,KACAY,OAAAC,SAAA,CACA3B,IAAA,EACA4B,SAAA,YAEA,KAAAC,QAAAC,GAAA,OAIA,KAAAD,QAAAE,KAAApB,GACAe,OAAAC,SAAA,CACA3B,IAAA,EACA4B,SAAA,aAGA,KAAAhB,YAAAD,CACA,EACA2D,0BACA,KAAAzB,iBAAAuB,gBAEA,KAAAvB,iBAAAuB,kBAAA,KAAAvB,iBAAAQ,SACA,KAAAb,OAAA4B,gBAAA,aAEA,KAAA5B,OAAA4B,gBAAA,GAJA,KAAA5B,OAAA4B,gBAAA,SAMA,EACA,0BAKA,OAHA,KAAA5B,OAAAgC,KAAA,GAGA,KAAA3B,iBAAA2B,KAIA,SAAA3B,iBAAA2B,KAAAyC,QAAA,QAAAD,KAAA,KAAAnE,iBAAA2B,WAAA,GACA,KAAAhC,OAAAgC,KAAA,cACA,IALA,KAAAhC,OAAAgC,KAAA,UACA,EAMA,EACA,4BAEA,MAAAwB,EAAAC,aAAAC,QAAA,wBACA,GAAAF,EAAA,CACA,MAAAG,EAAAC,KAAAC,MAAAL,GACA,GAAAG,EAAA1D,QAAA,KAAAI,iBAAAJ,MAAA,CACA,MAAA6D,EAAAC,KAAAC,MACAC,EAAAN,EAAAO,UAAA,IAEA,GAAAJ,EAAAG,EAAA,CAEA,MAAAE,EAAA7G,KAAA8G,MAAAH,EAAAH,GAAA,KAIA,OAHA,KAAA1B,UAAA+B,EACA,KAAAjC,UAAA,OACA,KAAAmC,gBAEA,CACA,CACA,CAIA,GADA,KAAA7D,iBACA,KAAAR,OAAAC,MAEA,IAEA,KAAA8C,eAAA,EAGA,MAAAkC,QAAAC,EAAAA,EAAAA,IAAA,kBACAjF,MAAA,KAAAI,iBAAAJ,QAIA,SAAAgF,EAAAvC,KAAAV,KAAA,CAEA,MAAAmD,EAAA,CACAlF,MAAA,KAAAI,iBAAAJ,MACAiE,UAAAH,KAAAC,OAEAP,aAAA2B,QAAA,uBAAAxB,KAAAyB,UAAAF,IAEA,KAAAjD,UAAA,EACA,KAAAI,cAAA,EACA,KAAA+B,gBACA,MAEA,KAAArE,OAAAgC,KAAAiD,EAAAvC,KAAA4C,KAAA,SAGA,OAAAC,GACA,KAAAvF,OAAAgC,KAAA,YACA,SACA,KAAAe,eAAA,CACA,CACA,EACAsB,iBAEA,KAAArB,OACAwC,cAAA,KAAAxC,OAIA,KAAAA,MAAAyC,aAAA,KACA,QAAArD,WAAA,EACAoD,cAAA,KAAAxC,OACA,KAAAd,UAAA,EACA,KAAAE,UAAA,GAEAqB,aAAAa,WAAA,4BACA,CACA,KAAAlC,YAEA,MAAAoB,EAAAC,aAAAC,QAAA,wBACA,GAAAF,EAAA,CACA,MAAAG,EAAAC,KAAAC,MAAAL,GAEAG,EAAA1D,MAAA,KAAAI,iBAAAJ,KAGA,CACA,IACA,IACA,EACAoC,WACA,KAAA7B,gBACA,KAAAS,mBACA,KAAAa,0BAEA,KAAA9B,OAAAgC,KAAA,IAEAkD,EAAAA,EAAAA,IAAA,oBACAjF,MAAA,KAAAI,iBAAAJ,MACA+B,KAAA,KAAA3B,iBAAA2B,OACA0D,MAAAT,IAEA,MAAAA,EAAAvC,KAAAV,MAKA2D,EAAAA,EAAAA,IAAA,sBAAAtF,kBAAAqF,MAAAE,IACA,MAAAA,EAAAlD,KAAAV,MAEAyB,aAAAa,WAAA,wBAEA,KAAAjF,QAAAE,KAAA,WAGA,KAAAS,OAAAgC,KAAA4D,EAAAlD,KAAA4C,GACA,IAbA,KAAAtF,OAAAgC,KAAAiD,EAAAvC,KAAA4C,KAAA,OAcA,GAEA,EACA3D,2BACA,KAAAb,iBAAA,KAAAA,eACA,EACAiB,kCACA,KAAAF,wBAAA,KAAAA,sBACA,EACAgE,YAEA,GAGAC,gBACA,KAAA9C,OACAwC,cAAA,KAAAxC,OAEA,KAAAK,MAAA,eACA,GCpbiQ,I,UCQ7PvD,GAAY,OACd,EACArE,EACAiC,GACA,EACA,KACA,WACA,MAIF,EAAeoC,EAAiB,O", "sources": ["webpack://portal-ui/./src/views/Login/backgroundlogin.vue", "webpack://portal-ui/src/views/Login/backgroundlogin.vue", "webpack://portal-ui/./src/views/Login/backgroundlogin.vue?d758", "webpack://portal-ui/./src/views/Login/backgroundlogin.vue?eb08", "webpack://portal-ui/./src/views/Login/register.vue", "webpack://portal-ui/src/views/Login/register.vue", "webpack://portal-ui/./src/views/Login/register.vue?7f17", "webpack://portal-ui/./src/views/Login/register.vue?aa92"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c,_setup=_vm._self._setupProxy;return _c('div',{staticClass:\"login-left-side\"},[_c('div',{staticClass:\"logo-container\"},[_c('a',{staticClass:\"logo-link\",on:{\"click\":function($event){return _vm.navigateTo('/index')}}},[_c('img',{staticClass:\"logo\",attrs:{\"src\":require(\"../../assets/logo_tiangong.png\"),\"alt\":\"算力租赁\"}})])]),_vm._m(0),_c('div',{staticClass:\"visual-element\"},[_c('div',{staticClass:\"server-illustration\"},_vm._l((_vm.servers),function(server,index){return _c('div',{key:index,staticClass:\"server-unit\",style:({\n               animationDelay: `${index * 0.2}s`,\n               transform: `translateY(${index * 4}px)`\n             })},[_c('div',{staticClass:\"server-light\"})])}),0),_c('div',{staticClass:\"connections\"})]),_c('div',{staticClass:\"features\"},_vm._l((_vm.features),function(feature,index){return _c('div',{key:index,staticClass:\"feature-item\"},[_c('div',{staticClass:\"feature-text\"},[_c('h3',[_vm._v(_vm._s(feature.title))]),_c('p',[_vm._v(_vm._s(feature.description))])])])}),0),_c('div',{staticClass:\"background-elements\"},_vm._l((20),function(i){return _c('div',{key:i,staticClass:\"floating-particle\",style:({\n             left: `${Math.random() * 100}%`,\n             top: `${Math.random() * 100}%`,\n             animationDuration: `${3 + Math.random() * 10}s`,\n             animationDelay: `${Math.random() * 5}s`\n           })})}),0)])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c,_setup=_vm._self._setupProxy;return _c('div',{staticClass:\"bottom-text\"},[_c('h2',{staticClass:\"slogan\"},[_vm._v(\"高效算力 · 智慧未来\")]),_c('p',{staticClass:\"sub-slogan\"},[_vm._v(\"专业算力租赁服务，为您的业务提供强大支持\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"login-left-side\">\r\n    <!-- 公司Logo -->\r\n    <div class=\"logo-container\">\r\n      <a @click=\"navigateTo('/index')\" class=\"logo-link\">\r\n        <img class=\"logo\" src=\"../../assets/logo_tiangong.png\" alt=\"算力租赁\" />\r\n      </a>\r\n      <!--      <h1 class=\"company-name\">天工云</h1>-->\r\n    </div>\r\n\r\n    <div class=\"bottom-text\">\r\n      <h2 class=\"slogan\">高效算力 · 智慧未来</h2>\r\n      <p class=\"sub-slogan\">专业算力租赁服务，为您的业务提供强大支持</p>\r\n    </div>\r\n\r\n    <!-- 主要视觉元素 -->\r\n    <div class=\"visual-element\">\r\n      <div class=\"server-illustration\">\r\n        <div v-for=\"(server, index) in servers\" :key=\"index\"\r\n             class=\"server-unit\"\r\n             :style=\"{\r\n               animationDelay: `${index * 0.2}s`,\r\n               transform: `translateY(${index * 4}px)`\r\n             }\">\r\n          <div class=\"server-light\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"connections\">\r\n<!--        <div v-for=\"i in 10\" :key=\"i\" class=\"connection-line\"></div>-->\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 特点介绍 -->\r\n    <div class=\"features\">\r\n      <div v-for=\"(feature, index) in features\" :key=\"index\" class=\"feature-item\">\r\n<!--        <div class=\"feature-icon\">-->\r\n<!--          <component :is=\"feature.icon\" />-->\r\n<!--        </div>-->\r\n        <div class=\"feature-text\">\r\n          <h3>{{ feature.title }}</h3>\r\n          <p>{{ feature.description }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 背景动画元素 -->\r\n    <div class=\"background-elements\">\r\n      <div v-for=\"i in 20\" :key=\"i\"\r\n           class=\"floating-particle\"\r\n           :style=\"{\r\n             left: `${Math.random() * 100}%`,\r\n             top: `${Math.random() * 100}%`,\r\n             animationDuration: `${3 + Math.random() * 10}s`,\r\n             animationDelay: `${Math.random() * 5}s`\r\n           }\">\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { defineComponent, ref } from 'vue';\r\n\r\n// 图标组件\r\nconst PerformanceIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <path d=\"M12 2v4\"></path>\r\n      <path d=\"m16.24 7.76 2.83-2.83\"></path>\r\n      <path d=\"M18 12h4\"></path>\r\n      <path d=\"m16.24 16.24 2.83 2.83\"></path>\r\n      <path d=\"M12 18v4\"></path>\r\n      <path d=\"m7.76 16.24-2.83 2.83\"></path>\r\n      <path d=\"M6 12H2\"></path>\r\n      <path d=\"m7.76 7.76-2.83-2.83\"></path>\r\n      <circle cx=\"12\" cy=\"12\" r=\"4\"></circle>\r\n    </svg>\r\n  `\r\n}\r\n\r\nconst ServerIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <rect width=\"20\" height=\"8\" x=\"2\" y=\"2\" rx=\"2\" ry=\"2\"></rect>\r\n      <rect width=\"20\" height=\"8\" x=\"2\" y=\"14\" rx=\"2\" ry=\"2\"></rect>\r\n      <line x1=\"6\" x2=\"6\" y1=\"6\" y2=\"6\"></line>\r\n      <line x1=\"6\" x2=\"6\" y1=\"18\" y2=\"18\"></line>\r\n    </svg>\r\n  `\r\n}\r\n\r\nconst ShieldIcon = {\r\n  template: `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n      <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"></path>\r\n    </svg>\r\n  `\r\n}\r\n\r\nexport default defineComponent({\r\n  name: 'backgroundlogin',\r\n  components: {\r\n    PerformanceIcon,\r\n    ServerIcon,\r\n    ShieldIcon\r\n  },\r\n  methods:{\r\n    navigateTo(path) {\r\n      // 记录当前活动路径作为上一个活动路径\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        // 为当前活动链接和登录按钮添加 active-exit 类\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              // 等待动画完成后移除 active-exit 类\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)\r\n            }\r\n          });\r\n\r\n          // 更新当前路径\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    },\r\n  },\r\n  setup() {\r\n    const logoSrc = ref('/api/placeholder/100/100');\r\n    const servers = ref(Array(5).fill(null));\r\n\r\n    const features = ref([\r\n      {\r\n        icon: 'PerformanceIcon',\r\n        title: '高性能算力',\r\n        description: '提供GPU/CPU灵活配置，满足AI训练、渲染等高算力需求'\r\n      },\r\n      {\r\n        icon: 'am-icon-shield',\r\n        title: '安全可靠',\r\n        description: '数据加密传输，多重备份，确保您的业务安全稳定运行'\r\n      }\r\n    ]);\r\n\r\n\r\n    return {\r\n      logoSrc,\r\n      servers,\r\n      features\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.login-left-side {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background: linear-gradient(135deg, #025af7 0%, #2196f3 100%);\r\n  color: white;\r\n  padding: 40px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Logo样式 */\r\n.logo-container {\r\n  margin-top: -60px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  margin-left: -40px;\r\n  z-index: 10;\r\n}\r\n\r\n.logo {\r\n  width: 180px;\r\n  height: 140px;\r\n  border-radius: 12px;\r\n  margin-right: 15px;\r\n}\r\n\r\n/* 主视觉元素 */\r\n.visual-element {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  perspective: 1000px;\r\n  z-index: 5;\r\n}\r\n\r\n.server-illustration {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  transform: rotateY(25deg) rotateX(10deg);\r\n  transform-style: preserve-3d;\r\n}\r\n\r\n.server-unit {\r\n  width: 200px;\r\n  height: 30px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 6px;\r\n  position: relative;\r\n  backdrop-filter: blur(5px);\r\n  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.server-light {\r\n  position: absolute;\r\n  right: 10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: rgba(231, 12, 12, 0);\r\n  box-shadow: 0 0 10px #ffffff;\r\n  animation: blink 1.5s infinite;\r\n}\r\n\r\n.connections {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.connection-line {\r\n  position: absolute;\r\n  height: 2px;\r\n  width: 100px;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.7), transparent);\r\n  top: calc(30% + (40% * Math.random()));\r\n  left: calc(10% + (50% * Math.random()));\r\n  animation: move 4s infinite linear;\r\n  transform: rotate(calc(-30deg + (60deg * Math.random())));\r\n  opacity: 0.6;\r\n}\r\n\r\n/* 特点介绍 */\r\n.features {\r\n  margin-top: 40px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  z-index: 10;\r\n}\r\n\r\n.feature-item {\r\n  flex: 1;\r\n  min-width: 250px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 15px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 12px;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: transform 0.3s, box-shadow 0.3s;\r\n}\r\n\r\n.feature-item:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\r\n  border-color: rgba(255, 255, 255, 0.4);\r\n}\r\n\r\n.feature-icon {\r\n  margin-right: 15px;\r\n  color: white;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  padding: 10px;\r\n  border-radius: 10px;\r\n  height: 44px;\r\n  width: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.feature-icon svg {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.feature-text h3 {\r\n  margin: 0 0 5px 0;\r\n  font-size: 18px;\r\n}\r\n\r\n.feature-text p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  opacity: 0.8;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 底部文案 */\r\n.bottom-text {\r\n  margin-top: -20px;\r\n  text-align: center;\r\n  z-index: 10;\r\n}\r\n\r\n.slogan {\r\n  font-size: 28px;\r\n  margin: 0 0 10px 0;\r\n  background: whitesmoke;\r\n  /*background: linear-gradient(to right, #ffffff, #2196f3);*/\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  font-weight: bold;\r\n}\r\n\r\n.sub-slogan {\r\n  font-size: 16px;\r\n  opacity: 0.9;\r\n  color: whitesmoke;\r\n  margin: 0;\r\n}\r\n\r\n/* 背景元素 */\r\n.background-elements {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.floating-particle {\r\n  position: absolute;\r\n  width: 6px;\r\n  height: 6px;\r\n  background-color: rgba(255, 255, 255, 0.5);\r\n  border-radius: 50%;\r\n  animation: float 10s infinite linear;\r\n}\r\n\r\n/* 动画 */\r\n@keyframes pulse {\r\n  0% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }\r\n  50% { box-shadow: 0 0 15px rgba(255, 255, 255, 0.5); }\r\n  100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }\r\n}\r\n\r\n@keyframes blink {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n}\r\n\r\n@keyframes move {\r\n  0% { transform: translateX(-50px) rotate(var(--rotation, -20deg)); opacity: 0; }\r\n  50% { opacity: 0.8; }\r\n  100% { transform: translateX(150px) rotate(var(--rotation, -20deg)); opacity: 0; }\r\n}\r\n\r\n@keyframes float {\r\n  0% { transform: translate(0, 0); opacity: 0; }\r\n  25% { opacity: 0.8; }\r\n  50% { transform: translate(10px, 10px); }\r\n  75% { opacity: 0.4; }\r\n  100% { transform: translate(0, 0); opacity: 0; }\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./backgroundlogin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./backgroundlogin.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./backgroundlogin.vue?vue&type=template&id=771899f4&scoped=true&\"\nimport script from \"./backgroundlogin.vue?vue&type=script&lang=js&\"\nexport * from \"./backgroundlogin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./backgroundlogin.vue?vue&type=style&index=0&id=771899f4&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"771899f4\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"login-page\"},[_c('div',{staticClass:\"left-side\"},[_c('backgroundlogin')],1),_c('div',{staticClass:\"right-side\"},[_c('div',{staticClass:\"login-form-container\"},[_c('h3',[_vm._v(\"注册 天工开物\")]),_c('div',{staticClass:\"form-container\"},[_c('div',{staticClass:\"login-form\"},[_c('p',{staticClass:\"form-note\"},[_vm._v(\"只需一个 天工开物 账号，即可访问 天工开物 的所有服务。\")]),_c('div',{staticClass:\"input-group\",class:{ 'error': _vm.errors.phone }},[_c('div',{staticClass:\"phone-input-container\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.registrationForm.phone),expression:\"registrationForm.phone\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入手机号\"},domProps:{\"value\":(_vm.registrationForm.phone)},on:{\"blur\":_vm.validatePhone,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.registrationForm, \"phone\", $event.target.value)}}})]),_c('div',{staticClass:\"error-container\"},[(_vm.errors.phone)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.phone))]):_vm._e()])]),_c('div',{staticClass:\"input-group\",class:{ 'error': _vm.errors.password }},[_c('div',{staticClass:\"password-input-container\"},[((_vm.passwordVisible ? 'text' : 'password')==='checkbox')?_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.registrationForm.password),expression:\"registrationForm.password\"}],attrs:{\"placeholder\":\"请输入密码\",\"type\":\"checkbox\"},domProps:{\"checked\":Array.isArray(_vm.registrationForm.password)?_vm._i(_vm.registrationForm.password,null)>-1:(_vm.registrationForm.password)},on:{\"blur\":_vm.validatePassword,\"change\":function($event){var $$a=_vm.registrationForm.password,$$el=$event.target,$$c=$$el.checked?(true):(false);if(Array.isArray($$a)){var $$v=null,$$i=_vm._i($$a,$$v);if($$el.checked){$$i<0&&(_vm.$set(_vm.registrationForm, \"password\", $$a.concat([$$v])))}else{$$i>-1&&(_vm.$set(_vm.registrationForm, \"password\", $$a.slice(0,$$i).concat($$a.slice($$i+1))))}}else{_vm.$set(_vm.registrationForm, \"password\", $$c)}}}}):((_vm.passwordVisible ? 'text' : 'password')==='radio')?_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.registrationForm.password),expression:\"registrationForm.password\"}],attrs:{\"placeholder\":\"请输入密码\",\"type\":\"radio\"},domProps:{\"checked\":_vm._q(_vm.registrationForm.password,null)},on:{\"blur\":_vm.validatePassword,\"change\":function($event){return _vm.$set(_vm.registrationForm, \"password\", null)}}}):_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.registrationForm.password),expression:\"registrationForm.password\"}],attrs:{\"placeholder\":\"请输入密码\",\"type\":_vm.passwordVisible ? 'text' : 'password'},domProps:{\"value\":(_vm.registrationForm.password)},on:{\"blur\":_vm.validatePassword,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.registrationForm, \"password\", $event.target.value)}}}),_c('span',{staticClass:\"password-toggle\",on:{\"click\":_vm.togglePasswordVisibility}},[_c('i',{class:['eye-icon', _vm.passwordVisible ? 'visible' : '']})])]),_c('div',{staticClass:\"error-container\"},[(_vm.errors.password)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.password))]):_vm._e()])]),_c('div',{staticClass:\"input-group\",class:{ 'error': _vm.errors.confirmPassword }},[_c('div',{staticClass:\"password-input-container\"},[((_vm.confirmPasswordVisible ? 'text' : 'password')==='checkbox')?_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.registrationForm.confirmPassword),expression:\"registrationForm.confirmPassword\"}],attrs:{\"placeholder\":\"请再次输入密码\",\"type\":\"checkbox\"},domProps:{\"checked\":Array.isArray(_vm.registrationForm.confirmPassword)?_vm._i(_vm.registrationForm.confirmPassword,null)>-1:(_vm.registrationForm.confirmPassword)},on:{\"blur\":_vm.validateConfirmPassword,\"change\":function($event){var $$a=_vm.registrationForm.confirmPassword,$$el=$event.target,$$c=$$el.checked?(true):(false);if(Array.isArray($$a)){var $$v=null,$$i=_vm._i($$a,$$v);if($$el.checked){$$i<0&&(_vm.$set(_vm.registrationForm, \"confirmPassword\", $$a.concat([$$v])))}else{$$i>-1&&(_vm.$set(_vm.registrationForm, \"confirmPassword\", $$a.slice(0,$$i).concat($$a.slice($$i+1))))}}else{_vm.$set(_vm.registrationForm, \"confirmPassword\", $$c)}}}}):((_vm.confirmPasswordVisible ? 'text' : 'password')==='radio')?_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.registrationForm.confirmPassword),expression:\"registrationForm.confirmPassword\"}],attrs:{\"placeholder\":\"请再次输入密码\",\"type\":\"radio\"},domProps:{\"checked\":_vm._q(_vm.registrationForm.confirmPassword,null)},on:{\"blur\":_vm.validateConfirmPassword,\"change\":function($event){return _vm.$set(_vm.registrationForm, \"confirmPassword\", null)}}}):_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.registrationForm.confirmPassword),expression:\"registrationForm.confirmPassword\"}],attrs:{\"placeholder\":\"请再次输入密码\",\"type\":_vm.confirmPasswordVisible ? 'text' : 'password'},domProps:{\"value\":(_vm.registrationForm.confirmPassword)},on:{\"blur\":_vm.validateConfirmPassword,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.registrationForm, \"confirmPassword\", $event.target.value)}}}),_c('span',{staticClass:\"password-toggle\",on:{\"click\":_vm.toggleConfirmPasswordVisibility}},[_c('i',{class:['eye-icon', _vm.confirmPasswordVisible ? 'visible' : '']})])]),_c('div',{staticClass:\"error-container\"},[(_vm.errors.confirmPassword)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.confirmPassword))]):_vm._e()])]),_c('div',{staticClass:\"input-group verification-code\",class:{ 'error': _vm.errors.code }},[_c('div',{staticClass:\"code-input-container\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.registrationForm.code),expression:\"registrationForm.code\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入验证码\"},domProps:{\"value\":(_vm.registrationForm.code)},on:{\"blur\":_vm.validateCodegeshi,\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.registrationForm, \"code\", $event.target.value)}}}),_c('button',{staticClass:\"get-code-btn-inline\",attrs:{\"disabled\":!_vm.registrationForm.phone || _vm.errors.phone || _vm.codeSent},on:{\"click\":_vm.getVerificationCode}},[_vm._v(\" \"+_vm._s(_vm.codeSent ? `${_vm.countdown}秒后重试` : '获取验证码')+\" \")])]),_c('div',{staticClass:\"error-container\"},[(_vm.errors.code)?_c('div',{staticClass:\"error-message\"},[_vm._v(_vm._s(_vm.errors.code))]):_vm._e()])]),_c('div',{staticClass:\"agreement-text\"},[_vm._v(\" 注册视为您已阅读并同意天工开物 \"),_c('router-link',{attrs:{\"to\":\"/help/user-agreement\"}},[_vm._v(\"服务条款\")]),_vm._v(\" 和\"),_c('router-link',{attrs:{\"to\":\"/help/privacy-policy\"}},[_vm._v(\"隐私政策\")])],1),_c('button',{staticClass:\"register-btn\",on:{\"click\":_vm.register}},[_vm._v(\" 注册 \")]),_c('div',{staticClass:\"login-link\"},[_c('a',{attrs:{\"href\":\"#\"},on:{\"click\":function($event){return _vm.navigateTo('/forgetpass')}}},[_vm._v(\"忘记密码\")]),_c('span',{staticClass:\"divider\"},[_vm._v(\"|\")]),_c('a',{attrs:{\"href\":\"#\"},on:{\"click\":function($event){return _vm.navigateTo('/login')}}},[_vm._v(\"返回登录\")])])])])])]),(_vm.showCodeSent)?_c('SlideNotification',{attrs:{\"message\":\"验证码已发送，可能会有延迟，请耐心等待！\",\"type\":\"success\",\"duration\":3000,\"minHeight\":_vm.minHeight},on:{\"close\":function($event){_vm.showCodeSent = false}}}):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"login-page\">\r\n    <div class=\"left-side\">\r\n      <backgroundlogin />\r\n    </div>\r\n\r\n    <div class=\"right-side\">\r\n      <div class=\"login-form-container\">\r\n        <h3>注册 天工开物</h3>\r\n\r\n        <div class=\"form-container\">\r\n          <div class=\"login-form\">\r\n            <p class=\"form-note\">只需一个 天工开物 账号，即可访问 天工开物 的所有服务。</p>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.phone }\">\r\n              <div class=\"phone-input-container\">\r\n                <input\r\n                    type=\"text\"\r\n                    v-model=\"registrationForm.phone\"\r\n                    placeholder=\"请输入手机号\"\r\n                    @blur=\"validatePhone\"\r\n                />\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.phone\" class=\"error-message\">{{ errors.phone }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.password }\">\r\n              <div class=\"password-input-container\">\r\n                <input\r\n                    :type=\"passwordVisible ? 'text' : 'password'\"\r\n                    v-model=\"registrationForm.password\"\r\n                    placeholder=\"请输入密码\"\r\n                    @blur=\"validatePassword\"\r\n                />\r\n                <span class=\"password-toggle\" @click=\"togglePasswordVisibility\">\r\n                  <i :class=\"['eye-icon', passwordVisible ? 'visible' : '']\"></i>\r\n                </span>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.password\" class=\"error-message\">{{ errors.password }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.confirmPassword }\">\r\n              <div class=\"password-input-container\">\r\n                <input\r\n                    :type=\"confirmPasswordVisible ? 'text' : 'password'\"\r\n                    v-model=\"registrationForm.confirmPassword\"\r\n                    placeholder=\"请再次输入密码\"\r\n                    @blur=\"validateConfirmPassword\"\r\n                />\r\n                <span class=\"password-toggle\" @click=\"toggleConfirmPasswordVisibility\">\r\n                  <i :class=\"['eye-icon', confirmPasswordVisible ? 'visible' : '']\"></i>\r\n                </span>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.confirmPassword\" class=\"error-message\">{{ errors.confirmPassword }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group verification-code\" :class=\"{ 'error': errors.code }\">\r\n              <div class=\"code-input-container\">\r\n                <input\r\n                    type=\"text\"\r\n                    v-model=\"registrationForm.code\"\r\n                    placeholder=\"请输入验证码\"\r\n                    @blur=\"validateCodegeshi\"\r\n                />\r\n                <button\r\n                    class=\"get-code-btn-inline\"\r\n                    @click=\"getVerificationCode\"\r\n                    :disabled=\"!registrationForm.phone || errors.phone || codeSent\"\r\n                >\r\n                  {{ codeSent ? `${countdown}秒后重试` : '获取验证码' }}\r\n                </button>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.code\" class=\"error-message\">{{ errors.code }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"agreement-text\">\r\n              注册视为您已阅读并同意天工开物 \r\n              <router-link to=\"/help/user-agreement\" >服务条款</router-link> 和<router-link to=\"/help/privacy-policy\" >隐私政策</router-link>\r\n            </div>\r\n\r\n            <button\r\n                class=\"register-btn\"\r\n                @click=\"register\"\r\n            >\r\n              注册\r\n            </button>\r\n\r\n            <div class=\"login-link\">\r\n              <a href=\"#\" @click=\"navigateTo('/forgetpass')\">忘记密码</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"#\" @click=\"navigateTo('/login')\">返回登录</a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 使用 SlideNotification 组件替换原来的提示 -->\r\n    <SlideNotification\r\n        v-if=\"showCodeSent\"\r\n        message=\"验证码已发送，可能会有延迟，请耐心等待！\"\r\n        type=\"success\"\r\n        :duration=\"3000\"\r\n        @close=\"showCodeSent = false\"\r\n        :minHeight=\"minHeight\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { postAnyData, getAnyData, postLogin, postNotAuth } from \"@/api/login\";\r\nimport { getToken, setToken, removeToken } from '@/utils/auth'\r\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\r\nimport backgroundlogin from '@/views/Login/backgroundlogin.vue';\r\n\r\n\r\nexport default {\r\n  name: \"register\",\r\n  components: {\r\n    SlideNotification,\r\n    backgroundlogin\r\n  },\r\n  data() {\r\n    return {\r\n      registrationForm: {\r\n        phone: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        code: '',\r\n        username: 'user1',\r\n        usage: '商业办公',\r\n        otherUsage: '',\r\n        agreement: false\r\n      },\r\n      isSendingCode: false,\r\n      passwordVisible: false,\r\n      confirmPasswordVisible: false,\r\n      errors: {\r\n        phone: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        code: ''\r\n      },\r\n      codeSent: false,\r\n      countdown: 60,\r\n      timer: null,\r\n      showCodeSent: false,\r\n      minHeight: '50px'\r\n    }\r\n  },\r\n  watch: {\r\n    'registrationForm.phone'(newVal) {\r\n      this.registrationForm.username = newVal;\r\n    }\r\n  },\r\n  created() {\r\n    // Check for existing timer on page load\r\n    this.checkExistingTimer();\r\n    this.$emit('hiden-layout')\r\n  },\r\n  computed: {\r\n    isFormValid() {\r\n      return (\r\n          this.registrationForm.phone &&\r\n          this.registrationForm.password &&\r\n          this.registrationForm.confirmPassword &&\r\n          this.registrationForm.code &&\r\n          this.registrationForm.agreement &&\r\n          !this.errors.phone &&\r\n          !this.errors.password &&\r\n          !this.errors.confirmPassword &&\r\n          !this.errors.code\r\n      );\r\n    }\r\n  },\r\n  methods: {\r\n    checkExistingTimer() {\r\n      // Get stored verification code data\r\n      const storedCodeData = localStorage.getItem('verificationCodeData');\r\n      if (storedCodeData) {\r\n        const codeData = JSON.parse(storedCodeData);\r\n        // Check if phone number matches\r\n        if (codeData.phone && this.registrationForm.phone && codeData.phone === this.registrationForm.phone) {\r\n          // Check if timer is still valid\r\n          const currentTime = Date.now();\r\n          const expiryTime = codeData.timestamp + (60 * 1000); // 60 seconds from sent time\r\n\r\n          if (currentTime < expiryTime) {\r\n            // Calculate remaining time\r\n            const remainingTime = Math.ceil((expiryTime - currentTime) / 1000);\r\n            this.countdown = remainingTime;\r\n            this.codeSent = true;\r\n            this.startCountdown();\r\n          } else {\r\n            // Timer expired, clear storage\r\n            localStorage.removeItem('verificationCodeData');\r\n          }\r\n        }\r\n      }\r\n    },\r\n    validatePhone() {\r\n      const phoneRegex = /^1[3-9]\\d{9}$/;\r\n      if (!this.registrationForm.phone) {\r\n        this.errors.phone = '请输入手机号';\r\n      } else if (!phoneRegex.test(this.registrationForm.phone)) {\r\n        this.errors.phone = '请输入有效的手机号';\r\n      } else {\r\n        this.errors.phone = '';\r\n      }\r\n    },\r\n\r\n    validatePassword() {\r\n      const password = this.registrationForm.password;\r\n      this.errors.password = ''; // 重置错误信息\r\n\r\n      if (!password) {\r\n        this.errors.password = '请输入密码';\r\n        return;\r\n      }\r\n\r\n      // 强制最小长度要求\r\n      if (password.length < 8) {\r\n        this.errors.password = '密码长度至少为8位';\r\n        return;\r\n      }\r\n\r\n      // 三选二的强度要求\r\n      const hasLetter = /[a-zA-Z]/.test(password);\r\n      const hasNumber = /[0-9]/.test(password);\r\n      const hasSymbol = /[!@#$%^&*()_+\\-=$${};':\"\\\\|,.<>\\/?]/.test(password);\r\n      const hasUpperLower = /[A-Z]/.test(password) && /[a-z]/.test(password);\r\n\r\n      let strengthCount = 0;\r\n      if (hasLetter && hasNumber) strengthCount++;\r\n      if (hasSymbol) strengthCount++;\r\n      if (hasUpperLower) strengthCount++;\r\n\r\n      if (strengthCount >= 2) {\r\n        this.errors.password = ''; // 密码符合要求\r\n      } else {\r\n        const missingRequirements = [];\r\n        if (!(hasLetter && hasNumber)) missingRequirements.push('包含数字和字母');\r\n        if (!hasSymbol) missingRequirements.push('包含特殊符号');\r\n        if (!hasUpperLower) missingRequirements.push('包含大小写字母');\r\n\r\n        this.errors.password = `密码强度不足，请满足以下至少两项要求：${missingRequirements.join('、')}`;\r\n      }\r\n\r\n      // 如果确认密码已填写，联动验证\r\n      if (this.registrationForm.confirmPassword) {\r\n        this.validateConfirmPassword();\r\n      }\r\n    },\r\n\r\n    navigateTo(path) {\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        // 先跳转到一个临时路由（如果有的话）或者重新加载页面\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n      this.currentPath = path;\r\n    },\r\n    validateConfirmPassword() {\r\n      if (!this.registrationForm.confirmPassword) {\r\n        this.errors.confirmPassword = '请再次输入密码';\r\n      } else if (this.registrationForm.confirmPassword !== this.registrationForm.password) {\r\n        this.errors.confirmPassword = '两次输入的密码不一致';\r\n      } else {\r\n        this.errors.confirmPassword = '';\r\n      }\r\n    },\r\n    async validateCodegeshi() {\r\n      // 清空错误\r\n      this.errors.code = '';\r\n\r\n      // 前端基础验证（保持原有长度判断）\r\n      if (!this.registrationForm.code) {\r\n        this.errors.code = '请输入验证码';\r\n        return false;\r\n      }\r\n      if (this.registrationForm.code.length !== 4 || !/^\\d+$/.test(this.registrationForm.code)) {\r\n        this.errors.code = '验证码必须为4位数字';\r\n        return false;\r\n      }\r\n    },\r\n    async getVerificationCode() {\r\n      // First check if there's an existing timer for this phone number\r\n      const storedCodeData = localStorage.getItem('verificationCodeData');\r\n      if (storedCodeData) {\r\n        const codeData = JSON.parse(storedCodeData);\r\n        if (codeData.phone === this.registrationForm.phone) {\r\n          const currentTime = Date.now();\r\n          const expiryTime = codeData.timestamp + (60 * 1000);\r\n\r\n          if (currentTime < expiryTime) {\r\n            // Timer still active, update countdown and prevent new request\r\n            const remainingTime = Math.ceil((expiryTime - currentTime) / 1000);\r\n            this.countdown = remainingTime;\r\n            this.codeSent = true;\r\n            this.startCountdown();\r\n            return;\r\n          }\r\n        }\r\n      }\r\n\r\n      // 先验证手机号格式\r\n      this.validatePhone();\r\n      if (this.errors.phone) return;\r\n\r\n      try {\r\n        // 显示发送中状态\r\n        this.isSendingCode = true;\r\n\r\n        // 调用发送验证码接口\r\n        const response = await postLogin(\"/auth/sendCode\", {\r\n          phone: this.registrationForm.phone\r\n        });\r\n\r\n        // 处理响应 - 只有在成功时才显示通知和启动倒计时\r\n        if (response.data.code === 200) {\r\n          // Store sent time and phone in localStorage\r\n          const verificationData = {\r\n            phone: this.registrationForm.phone,\r\n            timestamp: Date.now()\r\n          };\r\n          localStorage.setItem('verificationCodeData', JSON.stringify(verificationData));\r\n\r\n          this.codeSent = true;\r\n          this.showCodeSent = true;\r\n          this.startCountdown();\r\n        } else {\r\n          // 处理失败响应\r\n          this.errors.code = response.data.msg || '验证码发送失败';\r\n          // 不启动倒计时，允许用户重试\r\n        }\r\n      } catch (error) {\r\n        this.errors.code = '网络异常，请稍后重试';\r\n      } finally {\r\n        this.isSendingCode = false;\r\n      }\r\n    },\r\n    startCountdown() {\r\n      // 清除可能存在的旧定时器\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n      }\r\n\r\n      // 使用固定的时间间隔\r\n      this.timer = setInterval(() => {\r\n        if (this.countdown <= 1) {\r\n          clearInterval(this.timer);\r\n          this.codeSent = false;\r\n          this.countdown = 60;\r\n          // Clear localStorage when timer expires\r\n          localStorage.removeItem('verificationCodeData');\r\n        } else {\r\n          this.countdown--;\r\n          // Update remaining time in localStorage\r\n          const storedCodeData = localStorage.getItem('verificationCodeData');\r\n          if (storedCodeData) {\r\n            const codeData = JSON.parse(storedCodeData);\r\n            // Only update if it's for the current phone\r\n            if (codeData.phone === this.registrationForm.phone) {\r\n              // Just keep the original timestamp, no need to update it\r\n            }\r\n          }\r\n        }\r\n      }, 1000);\r\n    },\r\n    register() {\r\n      this.validatePhone();\r\n      this.validatePassword();\r\n      this.validateConfirmPassword();\r\n      // 清空错误\r\n      this.errors.code = '';\r\n      // 新增接口验证（参数与发送接口一致）\r\n      postLogin(\"/auth/verifyCode\", {\r\n        phone: this.registrationForm.phone, // 必须携带手机号\r\n        code: this.registrationForm.code\r\n      }).then(response => {\r\n        // 统一响应码判断标准\r\n        if (response.data.code !== 200) {\r\n          this.errors.code = response.data.msg || '验证码错误';\r\n          return;\r\n        }\r\n        // 注册API调用\r\n        postNotAuth(\"/auth/register\",this.registrationForm).then(res =>{\r\n          if (res.data.code === 200) {\r\n            // Clear verification code data after successful registration\r\n            localStorage.removeItem('verificationCodeData');\r\n            // 跳转到登录页面\r\n            this.$router.push('/login')\r\n          }\r\n          else {\r\n            this.errors.code = res.data.msg;\r\n          }\r\n        })\r\n      })\r\n    },\r\n    togglePasswordVisibility(){\r\n      this.passwordVisible = !this.passwordVisible;\r\n    },\r\n    toggleConfirmPasswordVisibility() {\r\n      this.confirmPasswordVisible = !this.confirmPasswordVisible;\r\n    },\r\n    goToLogin() {\r\n      // 在实际应用中重定向到登录页面\r\n      // this.$router.push('/login');\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    if (this.timer) {\r\n      clearInterval(this.timer);\r\n    }\r\n    this.$emit('hiden-layout')\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* Full page layout */\r\n.login-page {\r\n  display: flex;\r\n  min-height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Left side styling - keeping original styles */\r\n.left-side {\r\n  flex: 1;\r\n  position: relative;\r\n  background: linear-gradient(135deg, #cdb3e5, #5127d5);\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 0rem;\r\n  color: #030303;\r\n}\r\n\r\n/* Animated background - keeping original styles */\r\n.animated-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  overflow: hidden;\r\n  opacity: 0.4;\r\n}\r\n\r\n.hex-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.hex {\r\n  position: absolute;\r\n  width: 100px;\r\n  height: 110px;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);\r\n}\r\n\r\n.hex1 {\r\n  top: 20%;\r\n  left: 10%;\r\n  transform: scale(1.5);\r\n  animation: float 8s infinite ease-in-out;\r\n}\r\n\r\n.hex2 {\r\n  top: 60%;\r\n  left: 20%;\r\n  transform: scale(1.2);\r\n  animation: float 7s infinite ease-in-out reverse;\r\n}\r\n\r\n.hex3 {\r\n  top: 30%;\r\n  left: 50%;\r\n  transform: scale(1.3);\r\n  animation: float 10s infinite ease-in-out 1s;\r\n}\r\n\r\n.hex4 {\r\n  top: 70%;\r\n  left: 70%;\r\n  transform: scale(1.1);\r\n  animation: float 6s infinite ease-in-out 2s;\r\n}\r\n\r\n.hex5 {\r\n  top: 40%;\r\n  left: 80%;\r\n  transform: scale(1.4);\r\n  animation: float 9s infinite ease-in-out 3s;\r\n}\r\n\r\n.floating-cube {\r\n  position: absolute;\r\n  width: 50px;\r\n  height: 50px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  top: 25%;\r\n  left: 30%;\r\n  animation: float 8s infinite ease-in-out, rotate 15s infinite linear;\r\n}\r\n\r\n.floating-sphere {\r\n  position: absolute;\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  top: 50%;\r\n  left: 40%;\r\n  animation: float 10s infinite ease-in-out reverse;\r\n}\r\n\r\n.floating-diamond {\r\n  position: absolute;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: rotate(45deg);\r\n  top: 65%;\r\n  left: 60%;\r\n  animation: float 7s infinite ease-in-out 2s;\r\n}\r\n\r\n.ripple-effect {\r\n  position: absolute;\r\n  width: 200px;\r\n  height: 200px;\r\n  border-radius: 50%;\r\n  border: 3px solid rgba(255, 255, 255, 0.1);\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  animation: ripple 6s infinite linear;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(20px);\r\n  }\r\n}\r\n\r\n@keyframes rotate {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n@keyframes ripple {\r\n  0% {\r\n    width: 0;\r\n    height: 0;\r\n    opacity: 0.8;\r\n  }\r\n  100% {\r\n    width: 300px;\r\n    height: 300px;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* Right side styling */\r\n.right-side {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #f8f9fa;\r\n  padding: 2rem;\r\n}\r\n\r\n.login-form-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  padding: 2rem;\r\n  width: 100%;\r\n  max-width: 400px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.login-form-container h3 {\r\n  font-size: 24px;\r\n  font-weight: 500;\r\n  margin-bottom: 20px;\r\n  text-align: center;\r\n  color: #333;\r\n}\r\n\r\n/* Form container */\r\n.form-container {\r\n  min-height: 300px;\r\n  position: relative;\r\n}\r\n\r\n.login-form {\r\n  margin-top: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.form-note {\r\n  color: #999;\r\n  font-size: 12px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.input-group {\r\n  margin-bottom: 15px;\r\n  position: relative;\r\n}\r\n\r\n.input-group input {\r\n  width: 100%;\r\n  padding: 12px 15px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.input-group input:focus {\r\n  outline: none;\r\n  border-color: #6a26cd;\r\n}\r\n\r\n/* Phone input with prefix */\r\n.phone-input-container {\r\n  display: flex;\r\n  align-items: center;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n}\r\n\r\n.phone-prefix {\r\n  padding: 0 10px;\r\n  color: #333;\r\n  border-right: 1px solid #ddd;\r\n  background-color: #f5f5f5;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 42px;\r\n}\r\n\r\n.phone-input-container input {\r\n  border: none;\r\n  flex: 1;\r\n}\r\n\r\n.error-container {\r\n  min-height: 19px;\r\n  display: block;\r\n}\r\n\r\n.error-message {\r\n  color: #f44336;\r\n  font-size: 12px;\r\n}\r\n\r\n/* Verification code input and button */\r\n.verification-code .code-input-container {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.verification-code input {\r\n  flex: 1;\r\n}\r\n\r\n.get-code-btn-inline {\r\n  flex-shrink: 0;\r\n  width: 110px;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.get-code-btn-inline:hover:not(:disabled) {\r\n  background-color: #4169E1;\r\n}\r\n\r\n.get-code-btn-inline:disabled {\r\n  border-color: #2196f3;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Password input with toggle */\r\n.password-input-container {\r\n  position: relative;\r\n}\r\n\r\n.password-toggle {\r\n  position: absolute;\r\n  right: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\ninput[type=\"password\"]::-ms-reveal,\r\ninput[type=\"password\"]::-webkit-credentials-auto-fill-button,\r\ninput[type=\"password\"]::-webkit-clear-button {\r\n  display: none !important;\r\n  pointer-events: none;\r\n}\r\n\r\n.eye-icon {\r\n  display: inline-block;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle></svg>');\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  opacity: 0.5;\r\n}\r\n\r\n.eye-icon.visible {\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle><line x1=\"1\" y1=\"1\" x2=\"23\" y2=\"23\"></line></svg>');\r\n}\r\n\r\n/* Usage section */\r\n.usage-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.usage-title {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.usage-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.usage-option {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.usage-option input {\r\n  margin-right: 5px;\r\n  width: auto;\r\n}\r\n\r\n.other-usage {\r\n  margin-top: 10px;\r\n}\r\n\r\n.other-usage input {\r\n  width: 100%;\r\n  padding: 8px 12px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* Agreement checkbox */\r\n.agreement-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.agreement-checkbox {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  cursor: pointer;\r\n}\r\n\r\n.agreement-checkbox input {\r\n  margin-right: 8px;\r\n  margin-top: 2px;\r\n  width: auto;\r\n}\r\n\r\n.agreement-checkbox span {\r\n  font-size: 13px;\r\n  color: #666;\r\n}\r\n\r\n.link {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n\r\n/* Register button */\r\n.register-btn {\r\n  width: 100%;\r\n  padding: 12px 0;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.register-btn:hover:not(:disabled) {\r\n  background-color: #4169E1;\r\n}\r\n\r\n.register-btn:disabled {\r\n  background-color: #4169E1;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Login link */\r\n.login-link {\r\n  text-align: center;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-link a {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n\r\n.divider {\r\n  margin: 0 10px;\r\n  color: #ddd;\r\n}\r\n\r\n.logo-area {\r\n  flex: 0 0 auto;\r\n  margin-right: 10px;\r\n}\r\n\r\n.logo-link {\r\n  display: flex;\r\n  align-items: center;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n}\r\n\r\n.logo-area img {\r\n  height: 30px;\r\n  max-width: 100%;\r\n}\r\n\r\n.logo-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-left: 10px;\r\n}\r\n.agreement-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.agreement-text a {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n@media screen and (max-width: 768px) {\r\n  .left-side {\r\n    display: none; /* 在手机端隐藏左侧背景 */\r\n  }\r\n\r\n  .right-side {\r\n    flex: 1 0 100%; /* 让右侧占据全部宽度 */\r\n    padding: 1rem; /* 减少内边距以适应小屏幕 */\r\n  }\r\n\r\n  .login-form-container {\r\n    max-width: 100%; /* 让登录表单占据全部可用宽度 */\r\n    box-shadow: none; /* 移除阴影以节省空间 */\r\n    padding: 1.5rem; /* 调整内边距 */\r\n  }\r\n\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./register.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./register.vue?vue&type=template&id=40892a8f&scoped=true&\"\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&id=40892a8f&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"40892a8f\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "_setupProxy", "staticClass", "on", "$event", "navigateTo", "attrs", "require", "_m", "_l", "servers", "server", "index", "key", "style", "animationDelay", "transform", "features", "feature", "_v", "_s", "title", "description", "i", "left", "Math", "random", "top", "animationDuration", "staticRenderFns", "PerformanceIcon", "template", "ServerIcon", "ShieldIcon", "defineComponent", "name", "components", "methods", "path", "currentPath", "previousActivePath", "$nextTick", "navLinks", "document", "querySelectorAll", "for<PERSON>ach", "link", "classList", "contains", "add", "setTimeout", "remove", "$route", "window", "scrollTo", "behavior", "$router", "go", "push", "setup", "logoSrc", "ref", "Array", "fill", "icon", "component", "class", "errors", "phone", "directives", "rawName", "value", "registrationForm", "expression", "domProps", "validatePhone", "target", "composing", "$set", "_e", "password", "passwordVisible", "isArray", "_i", "validatePassword", "$$a", "$$el", "$$c", "checked", "$$v", "$$i", "concat", "slice", "_q", "togglePasswordVisibility", "confirmPassword", "confirmPasswordVisible", "validateConfirmPassword", "toggleConfirmPasswordVisibility", "code", "validate<PERSON><PERSON><PERSON><PERSON>", "codeSent", "getVerificationCode", "countdown", "register", "showCodeSent", "minHeight", "SlideNotification", "backgroundlogin", "data", "username", "usage", "otherUsage", "agreement", "isSendingCode", "timer", "watch", "newVal", "created", "checkExistingTimer", "$emit", "computed", "isFormValid", "storedCodeData", "localStorage", "getItem", "codeData", "JSON", "parse", "currentTime", "Date", "now", "expiryTime", "timestamp", "remainingTime", "ceil", "startCountdown", "removeItem", "phoneRegex", "test", "length", "hasLetter", "hasNumber", "hasSymbol", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "strengthCount", "missingRequirements", "join", "response", "postLogin", "verificationData", "setItem", "stringify", "msg", "error", "clearInterval", "setInterval", "then", "postNotAuth", "res", "goToLogin", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": ""}