{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: \"Footer\",\n  data() {\n    return {\n      // Data can be added here if needed\n    };\n  },\n  methods: {\n    navigateTo(path) {\n      // 记录当前活动路径作为上一个活动路径\n      if (this.currentPath && this.currentPath !== path) {\n        this.previousActivePath = this.currentPath;\n\n        // 为当前活动链接和登录按钮添加 active-exit 类\n        this.$nextTick(() => {\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\n          navLinks.forEach(link => {\n            if ((link.classList.contains('active') || path === '/login' && link.classList.contains('btn-login')) && !link.classList.contains('active-exit')) {\n              link.classList.add('active-exit');\n\n              // 等待动画完成后移除 active-exit 类\n              setTimeout(() => {\n                link.classList.remove('active-exit');\n              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)\n            }\n          });\n\n          // 更新当前路径\n          this.currentPath = path;\n        });\n      } else {\n        this.currentPath = path;\n      }\n\n      // 如果当前路径与目标路径相同，则重新加载页面\n      if (this.$route.path === path) {\n        this.$nextTick(() => {\n          window.scrollTo({\n            top: 0,\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\n          });\n\n          this.$router.go(0); // 刷新当前页面\n        });\n      } else {\n        // 不同路径，正常导航并滚动到顶部\n        this.$router.push(path);\n        window.scrollTo({\n          top: 0,\n          behavior: 'instant'\n        });\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "methods", "navigateTo", "path", "currentPath", "previousActivePath", "$nextTick", "navLinks", "document", "querySelectorAll", "for<PERSON>ach", "link", "classList", "contains", "add", "setTimeout", "remove", "$route", "window", "scrollTo", "top", "behavior", "$router", "go", "push"], "sources": ["src/components/common/footer/Footer.vue"], "sourcesContent": ["<template>\r\n  <div class=\"footer\">\r\n    <div class=\"footer-content\">\r\n      <!-- First column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">售前咨询热线</div>\r\n        <div class=\"footer-phone\">13913283376</div>\r\n        <div class=\"footer-links\">\r\n          <a @click=\"navigateTo('/index')\"><div class=\"footer-link\">首页</div></a>\r\n          <a @click=\"navigateTo('/product')\"><div class=\"footer-link\">AI算力市场</div></a>\r\n\r\n<!--          <div class=\"footer-link\">AI算力市场</div>-->\r\n\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Second column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">支持与服务</div>\r\n        <div class=\"footer-links\">\r\n          <div class=\"footer-link\">联系我们</div>\r\n          <a @click=\"navigateTo('/help')\"><div class=\"footer-link\">帮助文档</div></a>\r\n          <div class=\"footer-link\">公告</div>\r\n          <div class=\"footer-link\">提交建议</div>\r\n        </div>\r\n      </div>\r\n\r\n<!--      &lt;!&ndash; Third column &ndash;&gt;-->\r\n<!--      <div class=\"footer-column\">-->\r\n<!--        <div class=\"footer-title\">账户管理</div>-->\r\n<!--        <div class=\"footer-links\">-->\r\n<!--          <div class=\"footer-link\">控制台</div>-->\r\n<!--          <div class=\"footer-link\">账号管理</div>-->\r\n<!--          <div class=\"footer-link\">充值付款</div>-->\r\n<!--          <div class=\"footer-link\">线下款 / 电汇</div>-->\r\n<!--          <div class=\"footer-link\">索取发票</div>-->\r\n<!--          <div class=\"footer-link\">合规性</div>-->\r\n<!--        </div>-->\r\n<!--      </div>-->\r\n\r\n      <!-- Fourth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">关注天工开物</div>\r\n        <div class=\"footer-links\">\r\n          <div class=\"footer-link\">关注天工开物</div>\r\n          <div class=\"footer-link\">天工开物公众号</div>\r\n          <div class=\"footer-link\">天工开物微博</div>\r\n          <div class=\"footer-link\">天工开物支持与服务</div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Fifth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">联系专属客服</div>\r\n        <div class=\"footer-qrcode\">\r\n          <img src=\"@/assets/images/footer/wechat.jpg\" alt=\"联系专属客服二维码\" />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Sixth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">官方公众号</div>\r\n        <div class=\"footer-qrcode\">\r\n          <img src=\"@/assets/images/footer/wechat.jpg\" alt=\"官方公众号二维码\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Bottom footer links -->\r\n    <div class=\"footer-bottom\">\r\n\r\n      <div class=\"footer-copyright\">\r\n        <a href=\"https://beian.miit.gov.cn\" target=\"_blank\" style=\"color: inherit; text-decoration: none;\">\r\n          苏ICP备2025171841号-1\r\n        </a>\r\n           天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Footer\",\r\n  data() {\r\n    return {\r\n      // Data can be added here if needed\r\n    }\r\n  },\r\n  methods: {\r\n    navigateTo(path) {\r\n      // 记录当前活动路径作为上一个活动路径\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        // 为当前活动链接和登录按钮添加 active-exit 类\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              // 等待动画完成后移除 active-exit 类\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)\r\n            }\r\n          });\r\n\r\n          // 更新当前路径\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.footer {\r\n  max-width: 2560px;\r\n  width: 100%;\r\n  background-color: #424242;\r\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n  color: white;\r\n  padding: 0;\r\n  margin: 0px;\r\n}\r\n\r\n.footer-content {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  max-width: 2560px;\r\n  margin: 0 40px;\r\n  padding: 20px 0;\r\n}\r\n\r\n.footer-column {\r\n  flex: 1;\r\n  min-width: 150px;\r\n  padding: 0 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.footer-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  color: white;\r\n}\r\n\r\n.footer-phone {\r\n  font-size: 16px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.footer-links {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.footer-link {\r\n  margin-bottom: 10px;\r\n  cursor: pointer;\r\n  color: white;\r\n  font-size: 14px;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.footer-link:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.footer-qrcode {\r\n  width: 120px;\r\n  height: 120px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.footer-qrcode img {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  margin-left: -18%;\r\n}\r\n\r\n.footer-bottom {\r\n  border-top: 1px solid #e8e8e8;\r\n  padding: 20px 0;\r\n  text-align: center;\r\n  max-width: 2560px;\r\n  margin: 0 40px;\r\n}\r\n\r\n.footer-bottom-links {\r\n  margin-bottom: 10px;\r\n  text-align: left;\r\n}\r\n\r\n.footer-bottom-link {\r\n  color: white;\r\n  margin: 0 10px;\r\n  font-size: 14px;\r\n  text-decoration: none;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.footer-bottom-link:hover {\r\n  color: #1890ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.footer-copyright, .footer-license {\r\n  font-size: 15px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-left: 10px;\r\n}\r\n</style>"], "mappings": ";AAiFA;EACAA,IAAA;EACAC,KAAA;IACA;MACA;IAAA,CACA;EACA;EACAC,OAAA;IACAC,WAAAC,IAAA;MACA;MACA,SAAAC,WAAA,SAAAA,WAAA,KAAAD,IAAA;QACA,KAAAE,kBAAA,QAAAD,WAAA;;QAEA;QACA,KAAAE,SAAA;UACA,MAAAC,QAAA,GAAAC,QAAA,CAAAC,gBAAA;UACAF,QAAA,CAAAG,OAAA,CAAAC,IAAA;YACA,KAAAA,IAAA,CAAAC,SAAA,CAAAC,QAAA,cACAV,IAAA,iBAAAQ,IAAA,CAAAC,SAAA,CAAAC,QAAA,kBACA,CAAAF,IAAA,CAAAC,SAAA,CAAAC,QAAA;cACAF,IAAA,CAAAC,SAAA,CAAAE,GAAA;;cAEA;cACAC,UAAA;gBACAJ,IAAA,CAAAC,SAAA,CAAAI,MAAA;cACA;YACA;UACA;;UAEA;UACA,KAAAZ,WAAA,GAAAD,IAAA;QACA;MACA;QACA,KAAAC,WAAA,GAAAD,IAAA;MACA;;MAEA;MACA,SAAAc,MAAA,CAAAd,IAAA,KAAAA,IAAA;QACA,KAAAG,SAAA;UACAY,MAAA,CAAAC,QAAA;YACAC,GAAA;YACAC,QAAA;UACA;;UACA,KAAAC,OAAA,CAAAC,EAAA;QACA;MACA;QACA;QACA,KAAAD,OAAA,CAAAE,IAAA,CAAArB,IAAA;QACAe,MAAA,CAAAC,QAAA;UACAC,GAAA;UACAC,QAAA;QACA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}