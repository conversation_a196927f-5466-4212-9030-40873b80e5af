"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[842,648],{9484:function(e,s,t){t.d(s,{Z:function(){return c}});var i=function(){var e=this,s=e._self._c;return s("div",[s("div",{staticClass:"chat-container"},[s("div",{staticClass:"question-carousel",on:{mouseenter:e.pauseCarousel,mouseleave:e.resumeCarousel}},[s("transition-group",{staticClass:"carousel-wrapper",attrs:{name:"slide",tag:"div"}},e._l(e.questions,(function(t,i){return s("div",{directives:[{name:"show",rawName:"v-show",value:e.currentQuestionIndex===i,expression:"currentQuestionIndex === index"}],key:t,staticClass:"question-item",on:{click:function(s){return e.sendCarouselQuestion(t)},mouseenter:function(s){return e.witde(i)}}},[e._v(" "+e._s(t)+" ")])})),0)],1),s("div",{staticClass:"chat-icon",class:{"chat-icon-active":e.showChat},on:{click:e.toggleChat}},[s("i",{staticClass:"fas fa-comment"})])]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.showChat,expression:"showChat"}],staticClass:"chat-window"},[s("div",{staticClass:"chat-header"},[e._m(0),s("div",{staticClass:"chat-controls"},[s("i",{staticClass:"fas fa-times",on:{click:e.toggleChat}})])]),s("div",{ref:"messagesContainer",staticClass:"chat-messages"},[e._l(e.messages,(function(t,i){return s("div",{key:i,class:["message",t.type]},["bot"===t.type?s("div",{staticClass:"avatar"},[s("i",{staticClass:"fas fa-robot"})]):e._e(),s("div",{staticClass:"message-content"},[s("div",{staticClass:"message-text",domProps:{innerHTML:e._s(e.formatMessage(t.text))}}),s("div",{staticClass:"message-time"},[e._v(e._s(e.formatTime(t.time)))])])])})),e.loading?s("div",{staticClass:"typing-indicator"},[s("div",{staticClass:"dot"}),s("div",{staticClass:"dot"}),s("div",{staticClass:"dot"})]):e._e()],2),s("div",{staticClass:"chat-input"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.userInput,expression:"userInput"}],attrs:{type:"text",placeholder:"请输入您的问题...",disabled:e.loading},domProps:{value:e.userInput},on:{keyup:function(s){return!s.type.indexOf("key")&&e._k(s.keyCode,"enter",13,s.key,"Enter")?null:e.sendMessage.apply(null,arguments)},input:function(s){s.target.composing||(e.userInput=s.target.value)}}}),s("button",{attrs:{disabled:e.loading||!e.userInput.trim()},on:{click:e.sendMessage}},[s("i",{staticClass:"fas fa-paper-plane"})])])])])},a=[function(){var e=this,s=e._self._c;return s("div",{staticClass:"chat-title"},[s("i",{staticClass:"fas fa-robot"}),s("span",[e._v("智能客服")])])}],l=(t(7658),{name:"chatAi",data(){return{showChat:!1,userInput:"",messages:[{type:"bot",text:"您好！我是智能客服助手，有什么可以帮您？",time:new Date}],loading:!1,historyMessages:[],questions:["如何租赁GPU算力？","支持哪些支付方式？","如何查看订单状态？"],currentQuestionIndex:0,carouselTimer:null,carouselInterval:3e3,isPaused:!1}},beforeDestroy(){this.clearCarousel()},mounted(){if(this.startCarousel(),!document.getElementById("font-awesome")){const e=document.createElement("link");e.id="font-awesome",e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},methods:{witde(e){this.currentQuestionIndex=e,this.pauseCarousel()},startCarousel(){let e=this;this.clearCarousel(),this.carouselTimer=setInterval((()=>{e.isPaused||(this.currentQuestionIndex=(this.currentQuestionIndex+1)%this.questions.length,console.log("数据",this.currentQuestionIndex),console.log("ispasued",e.isPaused))}),this.carouselInterval)},pauseCarousel(){this.isPaused=!0},resumeCarousel(){this.isPaused=!1},clearCarousel(){this.carouselTimer&&(clearInterval(this.carouselTimer),this.carouselTimer=null)},sendCarouselQuestion(e){this.userInput=e,this.sendMessage()},toggleChat(){this.showChat=!this.showChat,this.showChat&&this.$nextTick((()=>{this.scrollToBottom()}))},async sendMessage(){if(!this.userInput.trim()||this.loading)return;this.messages.push({type:"user",text:this.userInput,time:new Date});const e=this.userInput;this.userInput="",this.loading=!0,this.historyMessages.push({role:"user",content:e});const s={model:"Qwen/QwQ-32B",messages:[{role:"system",content:"你是天工开物算力租赁网站的客服，当我问到算力租赁相关问题的时候再进行回答，如果我的问题与算力无关则回答“小天也不知道怎么办了呢，不如我们来聊聊算力相关的话题吧！”,如果问题涉及到你是谁或者你好的这种礼貌性踊跃，可以进行礼貌性的回复"},...this.historyMessages],stream:!0,options:{presence_penalty:1.2,frequency_penalty:1.5,seed:12345}};this.$nextTick((()=>{this.scrollToBottom()}));try{const e=await fetch("https://api.siliconflow.cn/v1/chat/completions",{method:"POST",headers:{Authorization:"Bearer sk-yxjtnfarjndtcwbjwmscdejrgtlpaaebnfdxcfnhhuanbqty","Content-Type":"application/json"},body:JSON.stringify(s)}),i=e.body.getReader(),a=new TextDecoder,l=this.messages.push({type:"bot",text:"",time:new Date})-1;while(1){const{done:e,value:s}=await i.read();if(e)break;const r=a.decode(s),o=r.split("\n").filter((e=>e.trim()));for(const i of o)try{const e=i.slice(6).trim();if(""===e||"[DONE]"===e)continue;let s=JSON.parse(e);if(s.choices){if(null!=s.choices[0].delta.reasoning_content)continue;if("\n\n"==s.choices[0].delta.content)continue;this.messages[l].text+=s.choices[0].delta.content}}catch(t){}}this.historyMessages.push({role:"assistant",content:this.messages[l].text})}catch(i){this.messages.push({type:"bot",text:"抱歉，系统暂时无法响应，请稍后再试。",time:new Date})}finally{this.loading=!1,this.$nextTick((()=>{this.scrollToBottom()}))}},async callChatAPI(e){return await new Promise((e=>setTimeout(e,1e3))),`感谢您的提问: "${e}"。这是一个模拟回复，请替换为真实API调用。`},scrollToBottom(){const e=this.$refs.messagesContainer;e.scrollTop=e.scrollHeight},formatTime(e){return new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},formatMessage(e){return e.replace(/\n/g,"<br>").replace(/(https?:\/\/[^\s]+)/g,'<a href="$1" target="_blank">$1</a>')}}}),r=l,o=t(1001),n=(0,o.Z)(r,i,a,!1,null,"46c63c47",null),c=n.exports},5648:function(e,s,t){t.r(s),t.d(s,{default:function(){return c}});var i=function(){var e=this,s=e._self._c;return s("div",[e.visible?s("div",{staticClass:"modal-overlay"},[s("div",{staticClass:"modal-container"},[e._m(0),s("div",{staticClass:"modal-body"},[s("div",{staticClass:"section-title"},[e._v("计费方式")]),s("div",{staticClass:"billing-tabs"},e._l(e.billingOptions,(function(t,i){return s("div",{key:i,staticClass:"billing-tab",class:{active:e.selectedBillingMethod===t.value},on:{click:function(s){e.selectedBillingMethod=t.value}}},[e._v(" "+e._s(t.label)+" ")])})),0),s("div",{staticClass:"section-title"},[e._v("选择主机")]),s("div",{staticClass:"specs-example-table"},[e._m(1),s("div",{staticClass:"specs-example-row"},[s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.name))]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.graphicsCardNumber)+"卡")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.videoMemory)+"GB")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.gpuNuclearNumber)+"核")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.internalMemory)+"GB")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.systemDisk)+"GB")])]),s("div",{staticClass:"server-card-footer"},[s("div",{staticClass:"server-price"},[e._v("¥ "+e._s(e.server[e.selectedBillingMethod])),s("span",{staticClass:"spec-label"},[e._v(" "+e._s(e.priceUnit))])])])]),s("div",{staticClass:"section-title"},[e._v("实例规格")]),s("div",{staticClass:"specs-example-table"},[e._m(2),s("div",{staticClass:"specs-example-row"},[s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.name))]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.gpuNuclearNumber)+"核心")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.internalMemory)+"GB")]),s("div",{staticClass:"specs-example-cell"},[e._v(e._s(e.server.systemDisk)+"GB")]),s("div",{staticClass:"specs-example-cell"},[e._v("免费"+e._s(e.server.dataDisk)+"GB SSD")])])]),s("div",{staticClass:"rental-duration"},[s("div",{staticClass:"duration-label"},[e._v("租用时长：")]),s("div",{staticClass:"duration-selector"},[s("select",{directives:[{name:"model",rawName:"v-model",value:e.selectedDuration,expression:"selectedDuration"}],staticClass:"duration-select",on:{change:function(s){var t=Array.prototype.filter.call(s.target.options,(function(e){return e.selected})).map((function(e){var s="_value"in e?e._value:e.value;return s}));e.selectedDuration=s.target.multiple?t:t[0]}}},e._l(e.currentDurationOptions,(function(t,i){return s("option",{key:i,domProps:{value:t.value}},[e._v(" "+e._s(t.label)+" ")])})),0)])]),s("div",{staticClass:"price-summary"},[s("div",{staticClass:"price-label"},[e._v("配置费用：")]),s("div",{staticClass:"price-value"},[e._v("¥ "+e._s(e.totalPrice)+" 元 ")])])]),s("div",{staticClass:"modal-footer"},[s("button",{staticClass:"cancel-button",on:{click:e.closeModal}},[e._v("取消")]),s("button",{staticClass:"confirm-button",on:{click:e.showConfirmDialog}},[e._v(" 立即租赁 ")])])])]):e._e(),e.showConfirmation?s("div",{staticClass:"confirm-overlay"},[s("div",{staticClass:"confirm-dialog"},[s("div",{staticClass:"confirm-message"},[e._v("是否确认订单？")]),s("div",{staticClass:"confirm-footer"},[s("button",{staticClass:"confirm-cancel",on:{click:function(s){e.showConfirmation=!1}}},[e._v("取消")]),s("button",{staticClass:"confirm-ok",on:{click:e.confirmOrder}},[e._v("确认")])])])]):e._e()])},a=[function(){var e=this,s=e._self._c;return s("div",{staticClass:"modal-header1"},[s("h2",[e._v("订单确认")])])},function(){var e=this,s=e._self._c;return s("div",{staticClass:"specs-example-row"},[s("div",{staticClass:"specs-example-cell"},[e._v("GPU型号")]),s("div",{staticClass:"specs-example-cell"},[e._v("GPU")]),s("div",{staticClass:"specs-example-cell"},[e._v("显存")]),s("div",{staticClass:"specs-example-cell"},[e._v("vCPU")]),s("div",{staticClass:"specs-example-cell"},[e._v("内存")]),s("div",{staticClass:"specs-example-cell"},[e._v("系统盘")])])},function(){var e=this,s=e._self._c;return s("div",{staticClass:"specs-example-row"},[s("div",{staticClass:"specs-example-cell"},[e._v("GPU型号")]),s("div",{staticClass:"specs-example-cell"},[e._v("vCPU")]),s("div",{staticClass:"specs-example-cell"},[e._v("内存")]),s("div",{staticClass:"specs-example-cell"},[e._v("系统盘")]),s("div",{staticClass:"specs-example-cell"},[e._v("数据盘")])])}],l={name:"OrderDetail",props:{visible:{type:Boolean,default:!0},server:{type:Object,default:()=>({})},selectedBillingMethod:{type:String}},data(){return{defaultDiskSize:50,selectedBillingMethod:"priceDay",selectedDuration:1,needExtraDisk:!1,showConfirmation:!1,billingOptions:[{label:"按量计费",value:"priceHour",unit:"/小时"},{label:"包日",value:"priceDay",unit:"/日"},{label:"包月",value:"priceMouth",unit:"/月"},{label:"包年",value:"priceYear",unit:"/年"}],durationOptionsHour:[{label:"1小时",value:1},{label:"2小时",value:2},{label:"4小时",value:4},{label:"8小时",value:8},{label:"12小时",value:12},{label:"24小时",value:24}],durationOptionsDay:[{label:"1天",value:1},{label:"2天",value:2},{label:"3天",value:3},{label:"4天",value:4},{label:"5天",value:5},{label:"6天",value:6}],durationOptionsWeek:[{label:"1月",value:1},{label:"2月",value:2},{label:"3月",value:3},{label:"4月",value:4},{label:"5月",value:5},{label:"6月",value:6},{label:"7月",value:7},{label:"8月",value:8}],durationOptionsMonth:[{label:"1年",value:1},{label:"2年",value:2},{label:"3年",value:3}]}},computed:{currentDurationOptions(){switch(this.selectedBillingMethod){case"priceHour":return this.totalPrice=this.server["priceHour"],this.durationOptionsHour;case"priceDay":return this.totalPrice=this.server["priceDay"],this.durationOptionsDay;case"priceMouth":return this.totalPrice=this.server["priceMouth"],this.durationOptionsWeek;case"priceYear":return this.totalPrice=this.server["priceYear"],this.durationOptionsMonth;default:return this.durationOptionsDay}},totalPrice(){const e=this.server[this.selectedBillingMethod]||0;return(e*this.selectedDuration).toFixed(2)},priceUnit(){switch(this.selectedBillingMethod){case"priceHour":return"/小时";case"priceDay":return"/日";case"priceMouth":return"/月";case"priceYear":return"/年";default:return""}},totalTime(){switch(this.selectedBillingMethod){case"priceHour":return"小时";case"priceDay":return"天";case"priceMouth":return"月";case"priceYear":return"年";default:return"小时"}}},watch:{selectedBillingMethod(){this.selectedDuration=this.currentDurationOptions[0]?.value||1},selectedDuration(){this.totalPrice=this.server[this.selectedBillingMethod]*this.selectedDuration},selectedDuration:{handler(e){this.$emit("time-updated",e+this.totalTime)},immediate:!0},totalPrice:{handler(e){this.$emit("price-updated",e)},immediate:!0}},methods:{closeModal(){this.$emit("close")},showConfirmDialog(){this.showConfirmation=!0},confirmOrder(){this.showConfirmation=!1,this.$emit("orderSubmitted");const e={serverId:this.server.id,serverName:this.server.name,billingMethod:this.selectedBillingMethod,duration:this.selectedDuration,needExtraDisk:this.needExtraDisk,price:this.totalPrice,specs:{gpuModel:this.server.name,vcpu:this.server.vcpu,systemDisk:this.server.systemDisk,cloudDisk:this.server.cloudDisk||0,memory:this.server.memory,videoMemory:this.server.videoMemory}};this.$emit("order-success",e),this.closeModal()}}},r=l,o=t(1001),n=(0,o.Z)(r,i,a,!1,null,"50b2ba60",null),c=n.exports},2842:function(e,s,t){t.r(s),t.d(s,{default:function(){return m}});var i=function(){var e=this,s=e._self._c;return s("div",{staticStyle:{"margin-left":"7%"}},[e.showComingSoon?s("SlideNotification",{attrs:{message:e.notificationMessage,type:"warning",duration:2e3},on:{close:function(s){e.showComingSoon=!1}}}):e._e(),s("div",{staticStyle:{width:"95%"}},[s("div",{staticClass:"compute-market"},[s("div",{staticClass:"filter-section"},[s("div",{staticClass:"filter-header",on:{click:e.toggleFilterVisibility}},[s("span",{staticClass:"filter-title"},[e._v("筛选")]),s("i",{staticClass:"filter-icon",class:{collapsed:!e.isFilterVisible}},[e._v(e._s(e.isFilterVisible?"▼":"►"))])]),s("transition",{attrs:{name:"slide"},on:{"before-enter":e.beforeEnter,enter:e.enter,"after-enter":e.afterEnter,"before-leave":e.beforeLeave,leave:e.leave,"after-leave":e.afterLeave}},[e.isFilterVisible?s("div",{ref:"filterContent",staticClass:"filter-content"},[s("div",{staticClass:"filter-row"},[s("div",{staticClass:"filter-label"},[e._v("计费模式")]),s("div",{staticClass:"filter-options checkbox-group"},[s("label",{staticClass:"radio-item"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.showindex,expression:"showindex"}],attrs:{type:"radio",value:"priceHour",name:"billing"},domProps:{checked:e._q(e.showindex,"priceHour")},on:{change:function(s){e.showindex="priceHour"}}}),s("span",{staticClass:"radio-item"}),s("span",{staticClass:"radio-text"},[e._v("按量")])]),s("label",{staticClass:"radio-item"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.showindex,expression:"showindex"}],attrs:{type:"radio",value:"priceDay",name:"billing"},domProps:{checked:e._q(e.showindex,"priceDay")},on:{change:function(s){e.showindex="priceDay"}}}),s("span",{staticClass:"radio-item"}),s("span",{staticClass:"radio-text"},[e._v("包日")])]),s("label",{staticClass:"radio-item"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.showindex,expression:"showindex"}],attrs:{type:"radio",value:"priceMouth",name:"billing"},domProps:{checked:e._q(e.showindex,"priceMouth")},on:{change:function(s){e.showindex="priceMouth"}}}),s("span",{staticClass:"radio-item"}),s("span",{staticClass:"radio-text"},[e._v("包月")])]),s("label",{staticClass:"radio-item"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.showindex,expression:"showindex"}],attrs:{type:"radio",value:"priceYear",name:"billing"},domProps:{checked:e._q(e.showindex,"priceYear")},on:{change:function(s){e.showindex="priceYear"}}}),s("span",{staticClass:"radio-item"}),s("span",{staticClass:"radio-text"},[e._v("包年")])])])]),s("div",{staticClass:"filter-row"},[s("div",{staticClass:"filter-label"},[e._v("选择可用区")]),s("div",{staticClass:"filter-options checkbox-group"},[s("label",{staticClass:"checkbox-item"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.filters.allRegions,expression:"filters.allRegions"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.filters.allRegions)?e._i(e.filters.allRegions,null)>-1:e.filters.allRegions},on:{change:[function(s){var t=e.filters.allRegions,i=s.target,a=!!i.checked;if(Array.isArray(t)){var l=null,r=e._i(t,l);i.checked?r<0&&e.$set(e.filters,"allRegions",t.concat([l])):r>-1&&e.$set(e.filters,"allRegions",t.slice(0,r).concat(t.slice(r+1)))}else e.$set(e.filters,"allRegions",a)},e.toggleAllRegions]}}),s("span",{staticClass:"checkbox-text"},[e._v("全选")])]),e._l(e.regions,(function(t){return s("label",{key:t.id,staticClass:"checkbox-item"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.filters.selectedRegions,expression:"filters.selectedRegions"}],attrs:{type:"checkbox"},domProps:{value:t.id,checked:Array.isArray(e.filters.selectedRegions)?e._i(e.filters.selectedRegions,t.id)>-1:e.filters.selectedRegions},on:{change:[function(s){var i=e.filters.selectedRegions,a=s.target,l=!!a.checked;if(Array.isArray(i)){var r=t.id,o=e._i(i,r);a.checked?o<0&&e.$set(e.filters,"selectedRegions",i.concat([r])):o>-1&&e.$set(e.filters,"selectedRegions",i.slice(0,o).concat(i.slice(o+1)))}else e.$set(e.filters,"selectedRegions",l)},e.updateFilters]}}),s("span",{staticClass:"checkbox-text"},[e._v(e._s(t.name))])])}))],2)]),s("div",{staticClass:"filter-row"},[s("div",{staticClass:"filter-label"},[e._v("GPU型号")]),s("div",{staticClass:"filter-options"},[s("div",{staticClass:"gpu-brand"},[e._v("NVIDIA")]),e.availableGpuModels.length>0?s("div",{staticClass:"checkbox-group gpu-list"},[s("label",{staticClass:"checkbox-item"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.filters.allGpuModels,expression:"filters.allGpuModels"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.filters.allGpuModels)?e._i(e.filters.allGpuModels,null)>-1:e.filters.allGpuModels},on:{change:[function(s){var t=e.filters.allGpuModels,i=s.target,a=!!i.checked;if(Array.isArray(t)){var l=null,r=e._i(t,l);i.checked?r<0&&e.$set(e.filters,"allGpuModels",t.concat([l])):r>-1&&e.$set(e.filters,"allGpuModels",t.slice(0,r).concat(t.slice(r+1)))}else e.$set(e.filters,"allGpuModels",a)},e.toggleAllGpuModels]}}),s("span",{staticClass:"checkbox-text"},[e._v("全选")])]),e._l(e.availableGpuModels,(function(t){return s("label",{key:t.id,staticClass:"checkbox-item"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.filters.selectedGpuModels,expression:"filters.selectedGpuModels"}],attrs:{type:"checkbox"},domProps:{value:t.id,checked:Array.isArray(e.filters.selectedGpuModels)?e._i(e.filters.selectedGpuModels,t.id)>-1:e.filters.selectedGpuModels},on:{change:[function(s){var i=e.filters.selectedGpuModels,a=s.target,l=!!a.checked;if(Array.isArray(i)){var r=t.id,o=e._i(i,r);a.checked?o<0&&e.$set(e.filters,"selectedGpuModels",i.concat([r])):o>-1&&e.$set(e.filters,"selectedGpuModels",i.slice(0,o).concat(i.slice(o+1)))}else e.$set(e.filters,"selectedGpuModels",l)},e.updateFilters]}}),s("span",{staticClass:"checkbox-text"},[e._v(e._s(t.name))])])}))],2):s("div",{staticClass:"no-options-message"},[e._v(" 当前筛选条件下无可用GPU型号 ")])])]),s("div",{staticClass:"filter-row"},[s("div",{staticClass:"filter-label"},[e._v("使用场景")]),s("div",{staticClass:"filter-options checkbox-group"},[s("label",{staticClass:"checkbox-item"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.filters.usageScenarios.development,expression:"filters.usageScenarios.development"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.filters.usageScenarios.development)?e._i(e.filters.usageScenarios.development,null)>-1:e.filters.usageScenarios.development},on:{change:function(s){var t=e.filters.usageScenarios.development,i=s.target,a=!!i.checked;if(Array.isArray(t)){var l=null,r=e._i(t,l);i.checked?r<0&&e.$set(e.filters.usageScenarios,"development",t.concat([l])):r>-1&&e.$set(e.filters.usageScenarios,"development",t.slice(0,r).concat(t.slice(r+1)))}else e.$set(e.filters.usageScenarios,"development",a)}}}),s("span",{staticClass:"checkbox-text"},[e._v("开发机")])])])])]):e._e()])],1),e.filteredServers.length>0?s("div",{staticClass:"servers-grid"},e._l(e.filteredServers,(function(t){return s("div",{key:t.id,staticClass:"server-card",class:{"server-card-hovered":e.hoveredServer===t.id},on:{mouseenter:function(s){e.hoveredServer=t.id},mouseleave:function(s){e.hoveredServer=null}}},[s("div",{staticClass:"region-tag"},[e._v(e._s(e.getRegionName(t.region)))]),s("div",{staticClass:"server-title"},[e._v(" "+e._s(t.name)+" ")]),s("div",{directives:[{name:"show",rawName:"v-show",value:"priceHour"===e.showindex,expression:"showindex === 'priceHour'"}],staticClass:"server-price-section"},[s("div",{staticClass:"price"},[s("span",{staticClass:"currency"},[e._v("¥")]),s("span",{staticClass:"amount"},[e._v(e._s(t.priceHour))]),s("span",{staticClass:"unit"},[e._v("/小时")])]),s("div",{staticClass:"server-status",class:e.getServerStatusClass(t.inventoryNumber)},[e._v(" "+e._s(e.getServerStatusText(t.inventoryNumber))+" ")])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"priceDay"===e.showindex,expression:"showindex === 'priceDay'"}],staticClass:"server-price-section"},[s("div",{staticClass:"price"},[s("span",{staticClass:"currency"},[e._v("¥")]),s("span",{staticClass:"amount"},[e._v(e._s(t.priceDay))]),s("span",{staticClass:"unit"},[e._v("/天")])]),s("div",{staticClass:"server-status",class:e.getServerStatusClass(t.inventoryNumber)},[e._v(" "+e._s(e.getServerStatusText(t.inventoryNumber))+" ")])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"priceMouth"===e.showindex,expression:"showindex === 'priceMouth'"}],staticClass:"server-price-section"},[s("div",{staticClass:"price"},[s("span",{staticClass:"currency"},[e._v("¥")]),s("span",{staticClass:"amount"},[e._v(e._s(t.priceMouth))]),s("span",{staticClass:"unit"},[e._v("/月")])]),s("div",{staticClass:"server-status",class:e.getServerStatusClass(t.inventoryNumber)},[e._v(" "+e._s(e.getServerStatusText(t.inventoryNumber))+" ")])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"priceYear"===e.showindex,expression:"showindex === 'priceYear'"}],staticClass:"server-price-section"},[s("div",{staticClass:"price"},[s("span",{staticClass:"currency"},[e._v("¥")]),s("span",{staticClass:"amount"},[e._v(e._s(t.priceYear))]),s("span",{staticClass:"unit"},[e._v("/年")])]),s("div",{staticClass:"server-status",class:e.getServerStatusClass(t.inventoryNumber)},[e._v(" "+e._s(e.getServerStatusText(t.inventoryNumber))+" ")])]),s("div",{staticClass:"server-specs"},[s("div",{staticClass:"specs-grid"},[s("div",{staticClass:"spec-item"},[s("div",{staticClass:"spec-label"},[e._v("显卡数量")]),s("div",{staticClass:"spec-value"},[e._v(e._s(t.graphicsCardNumber))])]),s("div",{staticClass:"spec-item"},[s("div",{staticClass:"spec-label"},[e._v("显存(GB)")]),s("div",{staticClass:"spec-value"},[e._v(e._s(t.videoMemory))])]),s("div",{staticClass:"spec-item"},[s("div",{staticClass:"spec-label"},[e._v("VCPU核数")]),s("div",{staticClass:"spec-value"},[e._v(e._s(t.gpuNuclearNumber))])]),s("div",{staticClass:"spec-item"},[s("div",{staticClass:"spec-label"},[e._v("系统盘(GB)")]),s("div",{staticClass:"spec-value"},[e._v(e._s(t.systemDisk))])]),s("div",{staticClass:"spec-item"},[s("div",{staticClass:"spec-label"},[e._v("云盘(GB)")]),s("div",{staticClass:"spec-value"},[e._v(e._s(t.cloudDisk||"-"))])]),s("div",{staticClass:"spec-item"},[s("div",{staticClass:"spec-label"},[e._v("内存(GB)")]),s("div",{staticClass:"spec-value"},[e._v(e._s(t.internalMemory))])])])]),s("button",{staticClass:"buy-button",class:{disabled:0===t.inventoryNumber,"buy-button-hovered":e.hoveredServer===t.id},on:{click:function(s){t.inventoryNumber>0&&e.directToConsole()}}},[e._v(" 立即租赁 ")])])})),0):s("div",{staticClass:"empty-state"},[s("div",{staticClass:"empty-state-icon"}),s("div",{staticClass:"empty-state-text"},[e._v("暂无数据")])])])]),s("order-detail",{attrs:{visible:e.showDetail,server:e.serverss,selectedBillingMethod:e.selectedBillingMethod},on:{orderSubmitted:function(s){return e.buyGpu(e.serverss)},"price-updated":e.orderPirce,"time-updated":e.orderTimes,close:e.closeOrderDetail}}),s("chatAi")],1)},a=[],l=(t(7658),t(4863)),r=t(9484),o=t(2223),n=t(1836),c=t(5648),d=t(7234),u={name:"ProductView",components:{Layout:l.Z,orderDetail:c["default"],chatAi:r.Z,SlideNotification:d.Z},data(){return{orderTime:null,price:null,selectedBillingMethod:"priceHour",serverss:{},showComingSoon:!1,notificationMessage:"",showDetail:!1,showindex:"priceHour",isFilterVisible:!0,hoveredServer:null,regions:[{id:"宁夏-B",name:"宁夏-B"},{id:"四川-A",name:"四川-A"},{id:"广东-B",name:"广东-B"}],allGpuModels:[{id:"a100-80g-nvlink",name:"A100-80G NVLink"},{id:"a800-80g-pcie",name:"A800-80G PCIe"},{id:"rtx4090-24g-pcie",name:"RTX4090-24G PCIe"},{id:"a100-80g-nvlink-2",name:"A100-80G NVLink"},{id:"h100-80g-nvlink",name:"H100-80G NVLink"},{id:"a800-80g-nvlink",name:"A800-80G NVLink"},{id:"h800-80g-nvlink",name:"H800-80G NVLink"}],serverStatuses:{AVAILABLE:"available",SHORTAGE:"shortage",UNAVAILABLE:"unavailable"},filters:{allRegions:!0,selectedRegions:[],billingMethod:"hourly",allGpuModels:!0,selectedGpuModels:[],usageScenarios:{development:!0}},allServers:[{id:1,gpuModel:"NVIDIA A100-80G NVLink",gpuModelId:"a100-80g-nvlink",gpuCount:8,gpuMemory:80,vcpu:112,systemDisk:50,cloudDisk:null,memory:912,pricePerHour:69.76,status:"unavailable",regionId:"ningxia-b"},{id:2,gpuModel:"NVIDIA A100-80G NVLink",gpuModelId:"a100-80g-nvlink",gpuCount:4,gpuMemory:80,vcpu:56,systemDisk:50,cloudDisk:null,memory:456,pricePerHour:34.88,status:"shortage",regionId:"ningxia-b"},{id:3,gpuModel:"NVIDIA A100-80G NVLink",gpuModelId:"a100-80g-nvlink",gpuCount:2,gpuMemory:80,vcpu:28,systemDisk:50,cloudDisk:null,memory:228,pricePerHour:17.44,status:"available",regionId:"ningxia-b"},{id:4,gpuModel:"NVIDIA H100-80G NVLink",gpuModelId:"h100-80g-nvlink",gpuCount:8,gpuMemory:80,vcpu:128,systemDisk:100,cloudDisk:500,memory:1024,pricePerHour:99.88,status:"available",regionId:"guangdong-b"},{id:5,gpuModel:"NVIDIA RTX4090-24G PCIe",gpuModelId:"rtx4090-24g-pcie",gpuCount:4,gpuMemory:24,vcpu:48,systemDisk:50,cloudDisk:null,memory:256,pricePerHour:18.56,status:"shortage",regionId:"sichuan-a"},{id:6,gpuModel:"NVIDIA A800-80G PCIe",gpuModelId:"a800-80g-pcie",gpuCount:4,gpuMemory:80,vcpu:56,systemDisk:50,cloudDisk:null,memory:456,pricePerHour:32.64,status:"unavailable",regionId:"sichuan-a"}]}},computed:{availableGpuModels(){if(0===this.filters.selectedRegions.length)return[];const e=this.allServers.filter((e=>this.filters.selectedRegions.includes(e.region))).map((e=>e.name)),s=[...new Set(e)];return this.allGpuModels.filter((e=>s.includes(e.name)))},filteredServers(){return this.filters.allGpuModels&&0===this.filters.selectedGpuModels.length&&this.availableGpuModels.length>0&&(this.filters.selectedGpuModels=this.availableGpuModels.map((e=>e.id))),0===this.filters.selectedRegions.length||0===this.filters.selectedGpuModels.length?[]:this.allServers.filter((e=>!!this.filters.selectedRegions.includes(e.region)&&(!!this.filters.selectedGpuModels.includes(e.name)&&!!this.filters.usageScenarios.development)))}},methods:{closeOrderDetail(){this.showDetail=!1},orderTimes(e){this.orderTime=e},orderPirce(e){this.price=e},async directToConsole(){if(!(0,n.LP)())return this.$router.push("/login"),void this.$toast.success("请先登录后再进行购买");try{const e=await(0,o.fB)("/logout/cilent/getInfo");if(200===e.data.code){const s=e.data.data.isReal;if(1!==s)return this.notificationMessage="请先完成实名认证，正在为您跳转到对应页面",this.showComingSoon=!0,void setTimeout((()=>{this.$router.push({path:"/personal",query:{activeTab:"verification"}})}),2e3);this.$router.push("/console")}}catch(e){this.$toast.error("获取用户信息失败")}},orderOK(e){if(e.inventoryNumber<=0)this.$toast.error("该资源已售罄，无法购买");else{if(!(0,n.LP)())return this.$router.push("/login"),void this.$toast.success("请先登录后再进行购买");this.serverss=e,this.selectedBillingMethod=this.showindex,this.showDetail=!0}},buyGpu(e){let s={id:e.id,price:"",name:e.name,time:this.orderTime};s.price=this.price,s.time=this.orderTime,(0,o.Zx)("/system/parameter/decrease",s).then((e=>{"余额不足"!==e.data.msg?"库存不足"!==e.data.msg?this.$toast.success("租赁成功，请前往控制台使用"):this.$toast.error("库存不足，请稍后再试"):this.$toast.error("余额不足,请前往充值")}))},toggleFilterVisibility(){this.isFilterVisible=!this.isFilterVisible},beforeEnter(e){e.style.height="0",e.style.opacity="0",e.style.overflow="hidden"},enter(e){const s=e.scrollHeight;requestAnimationFrame((()=>{e.style.height=s+"px",e.style.opacity="1"}))},afterEnter(e){e.style.height="",e.style.overflow=""},beforeLeave(e){e.style.height=e.scrollHeight+"px",e.style.overflow="hidden"},leave(e){e.offsetHeight,requestAnimationFrame((()=>{e.style.height="0",e.style.opacity="0"}))},afterLeave(e){e.style.height="",e.style.overflow=""},toggleAllRegions(){this.filters.allRegions?this.filters.selectedRegions=this.regions.map((e=>e.name)):this.filters.selectedRegions=[],this.updateFilters()},toggleAllGpuModels(){this.filters.allGpuModels?this.filters.selectedGpuModels=this.availableGpuModels.map((e=>e.id)):this.filters.selectedGpuModels=[],this.updateFilters()},updateFilters(){this.filters.allRegions=this.filters.selectedRegions.length===this.regions.length,this.$nextTick((()=>{this.filters.allGpuModels=this.availableGpuModels.length>0&&this.filters.selectedGpuModels.length===this.availableGpuModels.length}))},getRegionName(e){const s=this.regions.find((s=>s.id===e));return s?s.name:"未知区域"},getServerStatusText(e){switch(e>0&&e<=3?e="shortage":e>3?e="available":0==e&&(e="unavailable"),e){case this.serverStatuses.AVAILABLE:return"资源充足";case this.serverStatuses.SHORTAGE:return"资源紧张";case this.serverStatuses.UNAVAILABLE:return"已售罄";default:return"未知状态"}},getServerStatusClass(e){switch(e>0&&e<=3?e="shortage":e>3?e="available":0==e&&(e="unavailable"),e){case this.serverStatuses.AVAILABLE:return"server-available";case this.serverStatuses.SHORTAGE:return"server-shortage";case this.serverStatuses.UNAVAILABLE:return"server-unavailable";default:return""}},fetchServers(){let e;(0,o.xf)("/auth/getGpuList").then((s=>{this.allServers=s.data.data,(0,o.km)("/system/parameter/getGpu").then((e=>{this.allGpuModels=e.data.data;for(let s=0;s<this.allGpuModels.length;s++)this.allGpuModels[s].id=this.allGpuModels[s].name;(0,o.km)("/system/parameter/getRegion").then((e=>{this.regions=e.data.data;for(let s=0;s<this.regions.length;s++)this.regions[s].id=this.regions[s].region,this.regions[s].name=this.regions[s].region;this.toggleAllRegions(),this.toggleAllGpuModels()}))})),e={regions:this.filters.selectedRegions,gpuModels:this.filters.selectedGpuModels,billingMethod:this.filters.billingMethod}}))}},created(){this.fetchServers(),this.$nextTick((()=>{this.filters.selectedGpuModels=this.availableGpuModels.map((e=>e.id))}))},watch:{"filters.selectedRegions":{handler(){if(this.filters.allGpuModels)this.filters.selectedGpuModels=this.availableGpuModels.map((e=>e.id));else{const e=this.availableGpuModels.map((e=>e.id));this.filters.selectedGpuModels=this.filters.selectedGpuModels.filter((s=>e.includes(s)))}},immediate:!0}}},v=u,p=t(1001),h=(0,p.Z)(v,i,a,!1,null,"4dbfce5b",null),m=h.exports}}]);
//# sourceMappingURL=842.943c9bb9.js.map