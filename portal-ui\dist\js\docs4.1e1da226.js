"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[202],{1493:function(o,t,n){n.r(t);var p=new URL(n(1404),n.b),e=new URL(n(4117),n.b),f=new URL(n(8650),n.b),r=new URL(n(5689),n.b),c=new URL(n(6016),n.b),l=new URL(n(185),n.b),i=new URL(n(1973),n.b),a=new URL(n(971),n.b),s=new URL(n(1910),n.b),g='<h1 id="容器化部署-framepack-f1-图生视频框架"><font style="color:#020817">容器化部署 FramePack-F1 图生视频框架</font></h1> <h2 id="1-部署步骤"><font style="color:#020817">1 部署步骤</font></h2> <p><font style="color:#020817">我们提供了构建完毕的 FramePack-F1 镜像可以直接部署使用。</font></p> <h3 id="11-访问天工开物控制台，点击新增部署。"><font style="color:#020817">1.1 访问</font><a href="https://tiangongkaiwu.top/#/console">天工开物控制台</a><font style="color:#020817">，点击新增部署。</font></h3> <p><img src="'+p+'" alt=""></p> <h3 id="12-基于自身需要进行配置，参考配置为单卡-4090-和-1-个节点（初次使用进行调试）。"><font style="color:#020817">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></h3> <p><img src="'+e+'" alt=""></p> <h3 id="13-选择相应预制镜像"><font style="color:#020817">1.3 选择相应预制镜像</font></h3> <p><img src="'+f+'" alt=""></p> <h3 id="14-点击部署服务，耐心等待节点拉取镜像并启动。"><font style="color:#020817">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</font></h3> <p><img src="'+r+'" alt=""></p> <h3 id="15-节点启动后，你所在任务详情页中看到的内容可能如下："><font style="color:#020817">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font></h3> <p><img src="'+c+'" alt=""></p> <h3 id="16-我们可以点击快速访问下方7860端口的链接，测试-gradio-运行情况"><font style="color:#020817">1.6 我们可以点击快速访问下方“7860”端口的链接，测试 Gradio 运行情况</font></h3> <p><font style="color:#020817">我们首先点击该输入框上传一张图片，如下图：</font></p> <p><img src="'+l+'" alt=""></p> <p><font style="color:#020817">接下来填写 prompt（该模型对英文支持性较好），描述我们希望图片中的人物如何活动。</font></p> <p><font style="color:#020817">最后点击生成按钮，可以看到右侧已经出现了一个预览框，并且下方也出现了进度条，接下来我们耐心稍等片刻：</font></p> <p><img src="'+i+'" alt=""></p> <h3 id="17-保存视频"><font style="color:#020817">1.7 保存视频</font></h3> <p><font style="color:#020817">如果我们需要保存该视频到本地，可以在视频生成完毕后，点击视频右上角的下载按钮：</font></p> <p><img src="'+a+'" alt=""></p> <p><font style="color:#020817">或者我们也可以选择直接右键该视频，选择“视频另存为”，选择想要保存的位置：</font></p> <p><img src="'+s+'" alt=""></p> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/17 17:22</font></p> ';t["default"]=g},8650:function(o,t,n){o.exports=n.p+"img/frame-pack3.415fdfe1.png"},5689:function(o,t,n){o.exports=n.p+"img/frame-pack4.30184e00.png"},6016:function(o,t,n){o.exports=n.p+"img/frame-pack5.21527144.png"},185:function(o,t,n){o.exports=n.p+"img/frame-pack6.869eef76.png"},1973:function(o,t,n){o.exports=n.p+"img/frame-pack7.2c96908b.png"},971:function(o,t,n){o.exports=n.p+"img/frame-pack8.b88c6ee7.png"},1910:function(o,t,n){o.exports=n.p+"img/frame-pack9.3f58c02a.png"},1404:function(o,t,n){o.exports=n.p+"img/universal1.9a1b3f4b.png"},4117:function(o,t,n){o.exports=n.p+"img/universal2.4306636e.png"}}]);
//# sourceMappingURL=docs4.1e1da226.js.map