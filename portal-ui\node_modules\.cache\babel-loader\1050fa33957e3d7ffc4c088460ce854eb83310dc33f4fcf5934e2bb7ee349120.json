{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', [_c('div', {\n    staticClass: \"chat-container\"\n  }, [_c('div', {\n    staticClass: \"question-carousel\",\n    on: {\n      \"mouseenter\": _vm.pauseCarousel,\n      \"mouseleave\": _vm.resumeCarousel\n    }\n  }, [_c('transition-group', {\n    staticClass: \"carousel-wrapper\",\n    attrs: {\n      \"name\": \"slide\",\n      \"tag\": \"div\"\n    }\n  }, _vm._l(_vm.questions, function (question, index) {\n    return _c('div', {\n      directives: [{\n        name: \"show\",\n        rawName: \"v-show\",\n        value: _vm.currentQuestionIndex === index,\n        expression: \"currentQuestionIndex === index\"\n      }],\n      key: question,\n      staticClass: \"question-item\",\n      on: {\n        \"click\": function ($event) {\n          return _vm.sendCarouselQuestion(question);\n        },\n        \"mouseenter\": function ($event) {\n          return _vm.witde(index);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(question) + \" \")]);\n  }), 0)], 1), _c('div', {\n    staticClass: \"chat-icon\",\n    class: {\n      'chat-icon-active': _vm.showChat\n    },\n    on: {\n      \"click\": _vm.toggleChat\n    }\n  }, [_c('i', {\n    staticClass: \"fas fa-comment\"\n  })])]), _c('div', {\n    directives: [{\n      name: \"show\",\n      rawName: \"v-show\",\n      value: _vm.showChat,\n      expression: \"showChat\"\n    }],\n    staticClass: \"chat-window\"\n  }, [_c('div', {\n    staticClass: \"chat-header\"\n  }, [_vm._m(0), _c('div', {\n    staticClass: \"chat-controls\"\n  }, [_c('i', {\n    staticClass: \"fas fa-times\",\n    on: {\n      \"click\": _vm.toggleChat\n    }\n  })])]), _c('div', {\n    ref: \"messagesContainer\",\n    staticClass: \"chat-messages\"\n  }, [_vm._l(_vm.messages, function (message, index) {\n    return _c('div', {\n      key: index,\n      class: ['message', message.type]\n    }, [message.type === 'bot' ? _c('div', {\n      staticClass: \"avatar\"\n    }, [_c('i', {\n      staticClass: \"fas fa-robot\"\n    })]) : _vm._e(), _c('div', {\n      staticClass: \"message-content\"\n    }, [_c('div', {\n      staticClass: \"message-text\",\n      domProps: {\n        \"innerHTML\": _vm._s(_vm.formatMessage(message.text))\n      }\n    }), _c('div', {\n      staticClass: \"message-time\"\n    }, [_vm._v(_vm._s(_vm.formatTime(message.time)))])])]);\n  }), _vm.loading ? _c('div', {\n    staticClass: \"typing-indicator\"\n  }, [_c('div', {\n    staticClass: \"dot\"\n  }), _c('div', {\n    staticClass: \"dot\"\n  }), _c('div', {\n    staticClass: \"dot\"\n  })]) : _vm._e()], 2), _c('div', {\n    staticClass: \"chat-input\"\n  }, [_c('input', {\n    directives: [{\n      name: \"model\",\n      rawName: \"v-model\",\n      value: _vm.userInput,\n      expression: \"userInput\"\n    }],\n    attrs: {\n      \"type\": \"text\",\n      \"placeholder\": \"请输入您的问题...\",\n      \"disabled\": _vm.loading\n    },\n    domProps: {\n      \"value\": _vm.userInput\n    },\n    on: {\n      \"keyup\": function ($event) {\n        if (!$event.type.indexOf('key') && _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")) return null;\n        return _vm.sendMessage.apply(null, arguments);\n      },\n      \"input\": function ($event) {\n        if ($event.target.composing) return;\n        _vm.userInput = $event.target.value;\n      }\n    }\n  }), _c('button', {\n    attrs: {\n      \"disabled\": _vm.loading || !_vm.userInput.trim()\n    },\n    on: {\n      \"click\": _vm.sendMessage\n    }\n  }, [_c('i', {\n    staticClass: \"fas fa-paper-plane\"\n  })])])])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"chat-title\"\n  }, [_c('i', {\n    staticClass: \"fas fa-robot\"\n  }), _c('span', [_vm._v(\"智能客服\")])]);\n}];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "on", "pauseCarousel", "resumeCarousel", "attrs", "_l", "questions", "question", "index", "directives", "name", "rawName", "value", "currentQuestionIndex", "expression", "key", "click", "$event", "sendCarouselQuestion", "mouseenter", "witde", "_v", "_s", "class", "showChat", "toggleChat", "_m", "ref", "messages", "message", "type", "_e", "domProps", "formatMessage", "text", "formatTime", "time", "loading", "userInput", "keyup", "indexOf", "_k", "keyCode", "sendMessage", "apply", "arguments", "input", "target", "composing", "trim", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/components/common/mider/chatAi.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('div',{staticClass:\"chat-container\"},[_c('div',{staticClass:\"question-carousel\",on:{\"mouseenter\":_vm.pauseCarousel,\"mouseleave\":_vm.resumeCarousel}},[_c('transition-group',{staticClass:\"carousel-wrapper\",attrs:{\"name\":\"slide\",\"tag\":\"div\"}},_vm._l((_vm.questions),function(question,index){return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.currentQuestionIndex === index),expression:\"currentQuestionIndex === index\"}],key:question,staticClass:\"question-item\",on:{\"click\":function($event){return _vm.sendCarouselQuestion(question)},\"mouseenter\":function($event){return _vm.witde(index)}}},[_vm._v(\" \"+_vm._s(question)+\" \")])}),0)],1),_c('div',{staticClass:\"chat-icon\",class:{ 'chat-icon-active': _vm.showChat },on:{\"click\":_vm.toggleChat}},[_c('i',{staticClass:\"fas fa-comment\"})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showChat),expression:\"showChat\"}],staticClass:\"chat-window\"},[_c('div',{staticClass:\"chat-header\"},[_vm._m(0),_c('div',{staticClass:\"chat-controls\"},[_c('i',{staticClass:\"fas fa-times\",on:{\"click\":_vm.toggleChat}})])]),_c('div',{ref:\"messagesContainer\",staticClass:\"chat-messages\"},[_vm._l((_vm.messages),function(message,index){return _c('div',{key:index,class:['message', message.type]},[(message.type === 'bot')?_c('div',{staticClass:\"avatar\"},[_c('i',{staticClass:\"fas fa-robot\"})]):_vm._e(),_c('div',{staticClass:\"message-content\"},[_c('div',{staticClass:\"message-text\",domProps:{\"innerHTML\":_vm._s(_vm.formatMessage(message.text))}}),_c('div',{staticClass:\"message-time\"},[_vm._v(_vm._s(_vm.formatTime(message.time)))])])])}),(_vm.loading)?_c('div',{staticClass:\"typing-indicator\"},[_c('div',{staticClass:\"dot\"}),_c('div',{staticClass:\"dot\"}),_c('div',{staticClass:\"dot\"})]):_vm._e()],2),_c('div',{staticClass:\"chat-input\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.userInput),expression:\"userInput\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入您的问题...\",\"disabled\":_vm.loading},domProps:{\"value\":(_vm.userInput)},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.sendMessage.apply(null, arguments)},\"input\":function($event){if($event.target.composing)return;_vm.userInput=$event.target.value}}}),_c('button',{attrs:{\"disabled\":_vm.loading || !_vm.userInput.trim()},on:{\"click\":_vm.sendMessage}},[_c('i',{staticClass:\"fas fa-paper-plane\"})])])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"chat-title\"},[_c('i',{staticClass:\"fas fa-robot\"}),_c('span',[_vm._v(\"智能客服\")])])\n}]\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC,CAACA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,mBAAmB;IAACC,EAAE,EAAC;MAAC,YAAY,EAACJ,GAAG,CAACK,aAAa;MAAC,YAAY,EAACL,GAAG,CAACM;IAAc;EAAC,CAAC,EAAC,CAACL,EAAE,CAAC,kBAAkB,EAAC;IAACE,WAAW,EAAC,kBAAkB;IAACI,KAAK,EAAC;MAAC,MAAM,EAAC,OAAO;MAAC,KAAK,EAAC;IAAK;EAAC,CAAC,EAACP,GAAG,CAACQ,EAAE,CAAER,GAAG,CAACS,SAAS,EAAE,UAASC,QAAQ,EAACC,KAAK,EAAC;IAAC,OAAOV,EAAE,CAAC,KAAK,EAAC;MAACW,UAAU,EAAC,CAAC;QAACC,IAAI,EAAC,MAAM;QAACC,OAAO,EAAC,QAAQ;QAACC,KAAK,EAAEf,GAAG,CAACgB,oBAAoB,KAAKL,KAAM;QAACM,UAAU,EAAC;MAAgC,CAAC,CAAC;MAACC,GAAG,EAACR,QAAQ;MAACP,WAAW,EAAC,eAAe;MAACC,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAe,CAASC,MAAM,EAAC;UAAC,OAAOpB,GAAG,CAACqB,oBAAoB,CAACX,QAAQ,CAAC;QAAA,CAAC;QAAC,YAAY,EAAC,SAAAY,CAASF,MAAM,EAAC;UAAC,OAAOpB,GAAG,CAACuB,KAAK,CAACZ,KAAK,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAACX,GAAG,CAACwB,EAAE,CAAC,GAAG,GAACxB,GAAG,CAACyB,EAAE,CAACf,QAAQ,CAAC,GAAC,GAAG,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACT,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,WAAW;IAACuB,KAAK,EAAC;MAAE,kBAAkB,EAAE1B,GAAG,CAAC2B;IAAS,CAAC;IAACvB,EAAE,EAAC;MAAC,OAAO,EAACJ,GAAG,CAAC4B;IAAU;EAAC,CAAC,EAAC,CAAC3B,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACF,EAAE,CAAC,KAAK,EAAC;IAACW,UAAU,EAAC,CAAC;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,QAAQ;MAACC,KAAK,EAAEf,GAAG,CAAC2B,QAAS;MAACV,UAAU,EAAC;IAAU,CAAC,CAAC;IAACd,WAAW,EAAC;EAAa,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAACH,GAAG,CAAC6B,EAAE,CAAC,CAAC,CAAC,EAAC5B,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,cAAc;IAACC,EAAE,EAAC;MAAC,OAAO,EAACJ,GAAG,CAAC4B;IAAU;EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC3B,EAAE,CAAC,KAAK,EAAC;IAAC6B,GAAG,EAAC,mBAAmB;IAAC3B,WAAW,EAAC;EAAe,CAAC,EAAC,CAACH,GAAG,CAACQ,EAAE,CAAER,GAAG,CAAC+B,QAAQ,EAAE,UAASC,OAAO,EAACrB,KAAK,EAAC;IAAC,OAAOV,EAAE,CAAC,KAAK,EAAC;MAACiB,GAAG,EAACP,KAAK;MAACe,KAAK,EAAC,CAAC,SAAS,EAAEM,OAAO,CAACC,IAAI;IAAC,CAAC,EAAC,CAAED,OAAO,CAACC,IAAI,KAAK,KAAK,GAAEhC,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAQ,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;MAACE,WAAW,EAAC;IAAc,CAAC,CAAC,CAAC,CAAC,GAACH,GAAG,CAACkC,EAAE,EAAE,EAACjC,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC,cAAc;MAACgC,QAAQ,EAAC;QAAC,WAAW,EAACnC,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAACoC,aAAa,CAACJ,OAAO,CAACK,IAAI,CAAC;MAAC;IAAC,CAAC,CAAC,EAACpC,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAc,CAAC,EAAC,CAACH,GAAG,CAACwB,EAAE,CAACxB,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAACsC,UAAU,CAACN,OAAO,CAACO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAEvC,GAAG,CAACwC,OAAO,GAAEvC,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAkB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAK,CAAC,CAAC,EAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAK,CAAC,CAAC,EAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAK,CAAC,CAAC,CAAC,CAAC,GAACH,GAAG,CAACkC,EAAE,EAAE,CAAC,EAAC,CAAC,CAAC,EAACjC,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAY,CAAC,EAAC,CAACF,EAAE,CAAC,OAAO,EAAC;IAACW,UAAU,EAAC,CAAC;MAACC,IAAI,EAAC,OAAO;MAACC,OAAO,EAAC,SAAS;MAACC,KAAK,EAAEf,GAAG,CAACyC,SAAU;MAACxB,UAAU,EAAC;IAAW,CAAC,CAAC;IAACV,KAAK,EAAC;MAAC,MAAM,EAAC,MAAM;MAAC,aAAa,EAAC,YAAY;MAAC,UAAU,EAACP,GAAG,CAACwC;IAAO,CAAC;IAACL,QAAQ,EAAC;MAAC,OAAO,EAAEnC,GAAG,CAACyC;IAAU,CAAC;IAACrC,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAsC,CAAStB,MAAM,EAAC;QAAC,IAAG,CAACA,MAAM,CAACa,IAAI,CAACU,OAAO,CAAC,KAAK,CAAC,IAAE3C,GAAG,CAAC4C,EAAE,CAACxB,MAAM,CAACyB,OAAO,EAAC,OAAO,EAAC,EAAE,EAACzB,MAAM,CAACF,GAAG,EAAC,OAAO,CAAC,EAAC,OAAO,IAAI;QAAC,OAAOlB,GAAG,CAAC8C,WAAW,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAAA,CAAC;MAAC,OAAO,EAAC,SAAAC,CAAS7B,MAAM,EAAC;QAAC,IAAGA,MAAM,CAAC8B,MAAM,CAACC,SAAS,EAAC;QAAOnD,GAAG,CAACyC,SAAS,GAACrB,MAAM,CAAC8B,MAAM,CAACnC,KAAK;MAAA;IAAC;EAAC,CAAC,CAAC,EAACd,EAAE,CAAC,QAAQ,EAAC;IAACM,KAAK,EAAC;MAAC,UAAU,EAACP,GAAG,CAACwC,OAAO,IAAI,CAACxC,GAAG,CAACyC,SAAS,CAACW,IAAI;IAAE,CAAC;IAAChD,EAAE,EAAC;MAAC,OAAO,EAACJ,GAAG,CAAC8C;IAAW;EAAC,CAAC,EAAC,CAAC7C,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACz8E,CAAC;AACD,IAAIkD,eAAe,GAAG,CAAC,YAAW;EAAC,IAAIrD,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAY,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,CAAC,EAACF,EAAE,CAAC,MAAM,EAAC,CAACD,GAAG,CAACwB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9K,CAAC,CAAC;AAEF,SAASzB,MAAM,EAAEsD,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}