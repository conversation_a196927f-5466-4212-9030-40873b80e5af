(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[24],{3644:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mider-container"},[t("div",{staticClass:"mider-sidebar"},[t("div",{staticClass:"icon-wrapper"},[t("div",{staticClass:"icon-item",on:{mouseenter:function(t){return e.showPopup("wechat")},mouseleave:function(t){return e.hidePopup("wechat")}}},[t("i",{staticClass:"iconfont icon-wechat"},[t("svg",{attrs:{viewBox:"0 0 1024 1024",width:"24",height:"24"}},[t("path",{attrs:{d:"M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5-2.9 10.5-4.6 16.3-4.6 3 0 5.9 0.5 8.7 1.4 35.9 10.5 74.6 16.2 114.2 16.2 9.8 0 19.5-0.3 29-1C624.9 597 702.1 497.6 702.1 378.1c0-0.2-0.1-0.4-0.1-0.7 0 0-0.1-0.3-0.1-0.5-0.8 0-1.8-0.1-2.8-0.1-3.1 0-6.2 0.2-9 0.6z",fill:"#82c91e"}}),t("path",{attrs:{d:"M380.5 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7zM534.3 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7z",fill:"#82c91e"}}),t("path",{attrs:{d:"M683.5 673.6c82.1 0 151.2-56 171.5-131.8 4.1-15.2-9.3-29.5-24.9-26.6-3.5 0.6-7.1 1-10.7 1-34.4 0-62.4-28-62.4-62.4 0-1.6 0.1-3.3 0.2-4.9 0.8-19.3-24.5-28.7-35.7-13.5-39.1 53-102.7 87.5-174.5 87.5-12.3 0-24.4-1-36.1-3-5.2-0.9-10.5 0.8-14.1 4.5-4.6 4.6-16.7 17.2-16.7 17.2l-0.6 0.6c-5.4 5.4-8.7 12.3-9.5 19.8-0.9 8.5 1.9 17.1 7.7 23.6l1.3 1.3c6.3 7.5 15.6 11.7 25.6 11.7 7.1 0 14.1-2.3 19.7-6.5l10.6-7.8c6.2-4.5 14.8-3.3 19.3 2.8 4.3 5.8 3.4 14-2.1 18.8-14.4 12.6-32.9 19.7-51.9 19.7-4.3 0-8.7-0.3-13-1-21.3-3.5-40.2-14.4-54.3-31.3l-0.3-0.3c-12.9-15.7-19.4-34.9-18.3-54.5 0.8-14.5 6.4-28.5 16.1-39.7 1.2-1.4 2.5-2.6 3.8-3.9l31.5-31.5c1.9-1.9 3.8-3.9 5.4-6.1 5.8-7.7 8.9-17 8.9-26.8 0-11.3-4.2-22.1-11.8-30.4-12.4-13.5-31.5-15.2-48.5-11.7-91.3 18.9-160.7 100.1-160.8 196.9 0 109.5 89.1 198.6 198.6 198.6 36.7 0 71.9-9.9 102.8-28.5 5.7-3.4 12.8-3.3 18.5 0.2 36.7 22.7 79.1 35.3 123.2 35.3z",fill:"#82c91e"}}),t("path",{attrs:{d:"M770.9 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z",fill:"#82c91e"}}),t("path",{attrs:{d:"M602.4 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z",fill:"#82c91e"}})])]),t("div",{directives:[{name:"show",rawName:"v-show",value:"wechat"===e.activePopup,expression:"activePopup === 'wechat'"}],staticClass:"popup-container wechat-popup"},[t("div",{staticClass:"popup-content"},[t("h3",[e._v("微信扫码咨询客服")]),t("div",{staticClass:"qr-code"},[t("img",{attrs:{src:e.wechatQRCode,alt:"微信客服二维码"}})])])])]),t("div",{staticClass:"icon-item",on:{mouseenter:function(t){return e.showPopup("contact")},mouseleave:function(t){return e.hidePopup("contact")}}},[t("i",{staticClass:"iconfont icon-phone"},[t("svg",{attrs:{viewBox:"0 0 1024 1024",width:"24",height:"24"}},[t("path",{attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7c-20.6 47.8-49.6 90.6-86.4 127.3-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3-18.5 0-35.8 7.2-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-0.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4z",fill:"#1677ff"}})])]),t("div",{directives:[{name:"show",rawName:"v-show",value:"contact"===e.activePopup,expression:"activePopup === 'contact'"}],staticClass:"popup-container contact-popup"},[t("div",{staticClass:"popup-content"},[t("h3",[e._v("商务合作请联系电话")]),t("p",{staticClass:"phone-number"},[e._v("13913283376")]),t("p",[e._v("使用问题请咨询微信客服")]),t("div",{staticClass:"qr-code"},[t("img",{attrs:{src:e.contactQRCode,alt:"联系电话二维码"}})])])])]),t("div",{staticClass:"icon-item",on:{click:e.showFeedbackModal,mouseenter:e.showTooltip,mouseleave:e.hideTooltip}},[t("i",{staticClass:"iconfont icon-feedback"},[t("svg",{attrs:{viewBox:"0 0 1024 1024",width:"24",height:"24"}},[t("path",{attrs:{d:"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zM293 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z",fill:"#fa8c16"}}),t("path",{attrs:{d:"M894 345c-48.1-66-115.3-110.1-189-130v0.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l0.8 132.6c0 3.2 0.5 6.4 1.5 9.4 5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.4 11.9 68.4 18 104.7 18 72.2 0 143.1-21.4 205.3-61.8 57.4-37.3 104-89.9 131.8-150.1 23.9-52 35.2-106.8 33.6-160.9 28.8-29.9 50.4-64.7 63.6-103 17.8-52.7 13.1-108.7-14-155.1z m-68 106.7c-25.8 53.9-71.3 89.7-126.2 99.8-5.7 1-9.7 3.9-13 10.3-8.1 16.1-19.1 30.6-32.7 43.1-11.8 10.9-24.9 20.5-42.9 14.6-9.7-3.2-15.2-9.4-19.1-20.8-6.6-19.2-12.9-18.2-27.2-9.9-81.5 47.5-165.2 37.3-232.5-25.3-30.5-28.6-54.2-65.1-62.7-109-8.4-44 0.4-89.6 26.7-130.5 22.9-35.6 52.4-62 89.9-79.3 24.8-11.5 50.9-18.7 78.5-20.5 101.5-6.2 197.2 41.2 244.1 132.2 12.8 24.8 25.6 60.7 30.4 87.3 5.5 30.3 2.9 53.5-13.3 87z",fill:"#fa8c16"}})])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.showFeedbackTooltip,expression:"showFeedbackTooltip"}],staticClass:"tooltip"},[e._v(" 反馈与建议 ")])])])]),e.showModal?t("div",{staticClass:"modal-overlay",on:{click:function(t){return t.target!==t.currentTarget?null:e.closeFeedbackModal.apply(null,arguments)}}},[t("div",{staticClass:"feedback-modal"},[t("div",{staticClass:"modal-header"},[t("h3",[e._v("反馈与建议")]),t("span",{staticClass:"close-btn",on:{click:e.closeFeedbackModal}},[e._v("×")])]),t("div",{staticClass:"modal-body"},[t("div",{staticClass:"alert alert-warning"},[t("i",{staticClass:"iconfont icon-warning"},[t("svg",{attrs:{viewBox:"0 0 1024 1024",width:"16",height:"16"}},[t("path",{attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z",fill:"#faad14"}})])]),e._v(" 您的反馈我们将认真对待，不断优化产品功能和体验 ")]),t("div",{staticClass:"form-group"},[t("label",{staticClass:"required"},[e._v("问题类型：")]),t("div",{staticClass:"select-wrapper"},[t("select",{directives:[{name:"model",rawName:"v-model",value:e.feedback.type,expression:"feedback.type"}],attrs:{required:""},on:{change:function(t){var s=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.$set(e.feedback,"type",t.target.multiple?s:s[0])}}},[t("option",{attrs:{value:""}},[e._v("请选择")]),t("option",{attrs:{value:"功能建议"}},[e._v("功能建议")]),t("option",{attrs:{value:"产品故障"}},[e._v("产品故障")]),t("option",{attrs:{value:"体验不佳"}},[e._v("体验不佳")]),t("option",{attrs:{value:"账户相关"}},[e._v("账户相关")]),t("option",{attrs:{value:"其他"}},[e._v("其他")])])]),!e.feedback.type&&e.showErrors?t("p",{staticClass:"error-text"},[e._v("请选择问题类型")]):e._e()]),t("div",{staticClass:"form-group"},[t("label",{staticClass:"required"},[e._v("问题描述：")]),t("textarea",{directives:[{name:"model",rawName:"v-model",value:e.feedback.description,expression:"feedback.description"}],attrs:{placeholder:"请输入",required:""},domProps:{value:e.feedback.description},on:{input:function(t){t.target.composing||e.$set(e.feedback,"description",t.target.value)}}}),!e.feedback.description&&e.showErrors?t("p",{staticClass:"error-text"},[e._v("请输入问题描述")]):e._e()]),t("div",{staticClass:"form-group"},[t("label",{staticClass:"required1"},[e._v("问题截图：")]),t("div",{staticClass:"image-uploader",on:{click:e.triggerFileUpload,dragover:function(e){e.preventDefault()},drop:function(t){return t.preventDefault(),e.onFileDrop.apply(null,arguments)}}},[t("input",{ref:"fileInput",staticStyle:{display:"none"},attrs:{type:"file",accept:"image/*"},on:{change:e.onFileChange}}),e.feedback.image?t("div",{staticClass:"preview-container"},[t("img",{staticClass:"image-preview",attrs:{src:e.feedback.imagePreview,alt:"问题截图预览"}}),t("div",{staticClass:"remove-image",on:{click:function(t){return t.stopPropagation(),e.removeImage.apply(null,arguments)}}},[e._v("×")])]):t("div",{staticClass:"upload-placeholder"},[t("i",{staticClass:"iconfont icon-upload"},[t("svg",{attrs:{viewBox:"0 0 1024 1024",width:"28",height:"28"}},[t("path",{attrs:{d:"M518.3 459c-3.2-4.1-9.4-4.1-12.6 0l-112 141.7c-4.1 5.2-0.4 12.9 6.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z",fill:"#bfbfbf"}}),t("path",{attrs:{d:"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c26.1 6.9 49.6 22.5 66.3 43.8 16.4 21 25.4 45.9 26.3 72.3 1.1 33.9-11.4 66.5-34.9 90.7-23.6 24.4-55.3 37.7-89 37.7h-40c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C758.5 760 848 670.5 848 560c0-92.7-63.1-170.7-148.6-193.3z",fill:"#bfbfbf"}})])]),t("p",[e._v("点击/拖拽至此处添加图片")])])])])]),t("div",{staticClass:"modal-footer"},[t("button",{staticClass:"btn btn-cancel",on:{click:e.closeFeedbackModal}},[e._v("取消")]),t("button",{staticClass:"btn btn-submit",on:{click:e.confirmSubmit}},[e._v("提交")])])])]):e._e(),e.showConfirmation?t("div",{staticClass:"modal-overlay"},[t("div",{staticClass:"confirmation-dialog"},[t("div",{staticClass:"confirmation-icon"},[t("svg",{attrs:{viewBox:"0 0 1024 1024",width:"32",height:"32"}},[t("path",{attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z",fill:"#52c41a"}})])]),t("div",{staticClass:"confirmation-title"},[e._v("提交成功")]),t("div",{staticClass:"confirmation-message"},[e._v("感谢您的反馈，我们会尽快处理")]),t("div",{staticClass:"confirmation-actions"},[t("button",{staticClass:"btn btn-primary",on:{click:e.closeConfirmation}},[e._v("确定")])])])]):e._e()])},a=[],o={name:"Mider",data(){return{activePopup:null,showModal:!1,showErrors:!1,showFeedbackTooltip:!1,showConfirmation:!1,wechatQRCode:s(46),contactQRCode:s(46),feedback:{type:"",description:"",instanceId:"",image:null,imagePreview:null}}},methods:{showPopup(e){this.activePopup=e},hidePopup(e){this.activePopup===e&&(this.activePopup=null)},showTooltip(){this.showFeedbackTooltip=!0},hideTooltip(){this.showFeedbackTooltip=!1},showFeedbackModal(){this.showModal=!0,this.showErrors=!1},closeFeedbackModal(){this.showModal=!1,this.feedback={type:"",description:"",instanceId:"",image:null,imagePreview:null},this.showErrors=!1},triggerFileUpload(){this.$refs.fileInput.click()},onFileChange(e){const t=e.target.files[0];t&&this.processImage(t)},onFileDrop(e){const t=e.dataTransfer.files[0];t&&t.type.match("image.*")&&this.processImage(t)},processImage(e){if(e&&e.type.match("image.*")){this.feedback.image=e;const t=new FileReader;t.onload=e=>{this.feedback.imagePreview=e.target.result},t.readAsDataURL(e)}},removeImage(){this.feedback.image=null,this.feedback.imagePreview=null},confirmSubmit(){this.showErrors=!0,this.feedback.type&&this.feedback.description&&this.submitFeedback()},submitFeedback(){if(this.feedback.image){const e=new FormData;e.append("type",this.feedback.type),e.append("description",this.feedback.description),e.append("instanceId",this.feedback.instanceId),e.append("image",this.feedback.image)}this.showModal=!1,this.showConfirmation=!0,this.feedback={type:"",description:"",instanceId:"",image:null,imagePreview:null}},closeConfirmation(){this.showConfirmation=!1}}},n=o,l=s(1001),c=(0,l.Z)(n,i,a,!1,null,"37397f14",null),r=c.exports},9484:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});var i=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"chat-container"},[t("div",{staticClass:"question-carousel",on:{mouseenter:e.pauseCarousel,mouseleave:e.resumeCarousel}},[t("transition-group",{staticClass:"carousel-wrapper",attrs:{name:"slide",tag:"div"}},e._l(e.questions,(function(s,i){return t("div",{directives:[{name:"show",rawName:"v-show",value:e.currentQuestionIndex===i,expression:"currentQuestionIndex === index"}],key:s,staticClass:"question-item",on:{click:function(t){return e.sendCarouselQuestion(s)},mouseenter:function(t){return e.witde(i)}}},[e._v(" "+e._s(s)+" ")])})),0)],1),t("div",{staticClass:"chat-icon",class:{"chat-icon-active":e.showChat},on:{click:e.toggleChat}},[t("i",{staticClass:"fas fa-comment"})])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.showChat,expression:"showChat"}],staticClass:"chat-window"},[t("div",{staticClass:"chat-header"},[e._m(0),t("div",{staticClass:"chat-controls"},[t("i",{staticClass:"fas fa-times",on:{click:e.toggleChat}})])]),t("div",{ref:"messagesContainer",staticClass:"chat-messages"},[e._l(e.messages,(function(s,i){return t("div",{key:i,class:["message",s.type]},["bot"===s.type?t("div",{staticClass:"avatar"},[t("i",{staticClass:"fas fa-robot"})]):e._e(),t("div",{staticClass:"message-content"},[t("div",{staticClass:"message-text",domProps:{innerHTML:e._s(e.formatMessage(s.text))}}),t("div",{staticClass:"message-time"},[e._v(e._s(e.formatTime(s.time)))])])])})),e.loading?t("div",{staticClass:"typing-indicator"},[t("div",{staticClass:"dot"}),t("div",{staticClass:"dot"}),t("div",{staticClass:"dot"})]):e._e()],2),t("div",{staticClass:"chat-input"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.userInput,expression:"userInput"}],attrs:{type:"text",placeholder:"请输入您的问题...",disabled:e.loading},domProps:{value:e.userInput},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.sendMessage.apply(null,arguments)},input:function(t){t.target.composing||(e.userInput=t.target.value)}}}),t("button",{attrs:{disabled:e.loading||!e.userInput.trim()},on:{click:e.sendMessage}},[t("i",{staticClass:"fas fa-paper-plane"})])])])])},a=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"chat-title"},[t("i",{staticClass:"fas fa-robot"}),t("span",[e._v("智能客服")])])}],o=(s(7658),{name:"chatAi",data(){return{showChat:!1,userInput:"",messages:[{type:"bot",text:"您好！我是智能客服助手，有什么可以帮您？",time:new Date}],loading:!1,historyMessages:[],questions:["如何租赁GPU算力？","支持哪些支付方式？","如何查看订单状态？"],currentQuestionIndex:0,carouselTimer:null,carouselInterval:3e3,isPaused:!1}},beforeDestroy(){this.clearCarousel()},mounted(){if(this.startCarousel(),!document.getElementById("font-awesome")){const e=document.createElement("link");e.id="font-awesome",e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},methods:{witde(e){this.currentQuestionIndex=e,this.pauseCarousel()},startCarousel(){let e=this;this.clearCarousel(),this.carouselTimer=setInterval((()=>{e.isPaused||(this.currentQuestionIndex=(this.currentQuestionIndex+1)%this.questions.length,console.log("数据",this.currentQuestionIndex),console.log("ispasued",e.isPaused))}),this.carouselInterval)},pauseCarousel(){this.isPaused=!0},resumeCarousel(){this.isPaused=!1},clearCarousel(){this.carouselTimer&&(clearInterval(this.carouselTimer),this.carouselTimer=null)},sendCarouselQuestion(e){this.userInput=e,this.sendMessage()},toggleChat(){this.showChat=!this.showChat,this.showChat&&this.$nextTick((()=>{this.scrollToBottom()}))},async sendMessage(){if(!this.userInput.trim()||this.loading)return;this.messages.push({type:"user",text:this.userInput,time:new Date});const e=this.userInput;this.userInput="",this.loading=!0,this.historyMessages.push({role:"user",content:e});const t={model:"Qwen/QwQ-32B",messages:[{role:"system",content:"你是天工开物算力租赁网站的客服，当我问到算力租赁相关问题的时候再进行回答，如果我的问题与算力无关则回答“小天也不知道怎么办了呢，不如我们来聊聊算力相关的话题吧！”,如果问题涉及到你是谁或者你好的这种礼貌性踊跃，可以进行礼貌性的回复"},...this.historyMessages],stream:!0,options:{presence_penalty:1.2,frequency_penalty:1.5,seed:12345}};this.$nextTick((()=>{this.scrollToBottom()}));try{const e=await fetch("https://api.siliconflow.cn/v1/chat/completions",{method:"POST",headers:{Authorization:"Bearer sk-yxjtnfarjndtcwbjwmscdejrgtlpaaebnfdxcfnhhuanbqty","Content-Type":"application/json"},body:JSON.stringify(t)}),i=e.body.getReader(),a=new TextDecoder,o=this.messages.push({type:"bot",text:"",time:new Date})-1;while(1){const{done:e,value:t}=await i.read();if(e)break;const n=a.decode(t),l=n.split("\n").filter((e=>e.trim()));for(const i of l)try{const e=i.slice(6).trim();if(""===e||"[DONE]"===e)continue;let t=JSON.parse(e);if(t.choices){if(null!=t.choices[0].delta.reasoning_content)continue;if("\n\n"==t.choices[0].delta.content)continue;this.messages[o].text+=t.choices[0].delta.content}}catch(s){}}this.historyMessages.push({role:"assistant",content:this.messages[o].text})}catch(i){this.messages.push({type:"bot",text:"抱歉，系统暂时无法响应，请稍后再试。",time:new Date})}finally{this.loading=!1,this.$nextTick((()=>{this.scrollToBottom()}))}},async callChatAPI(e){return await new Promise((e=>setTimeout(e,1e3))),`感谢您的提问: "${e}"。这是一个模拟回复，请替换为真实API调用。`},scrollToBottom(){const e=this.$refs.messagesContainer;e.scrollTop=e.scrollHeight},formatTime(e){return new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},formatMessage(e){return e.replace(/\n/g,"<br>").replace(/(https?:\/\/[^\s]+)/g,'<a href="$1" target="_blank">$1</a>')}}}),n=o,l=s(1001),c=(0,l.Z)(n,i,a,!1,null,"46c63c47",null),r=c.exports},4024:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return g}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"help-layout"},[e.isMobile?t("div",{staticClass:"mobile-controls"},[t("button",{staticClass:"sidebar-toggle",class:{active:e.sidebarVisible},on:{click:e.toggleSidebar}},[t("i",{staticClass:"icon-menu"}),t("span",[e._v("菜单")])]),t("button",{staticClass:"toc-toggle",class:{active:e.tocVisible},on:{click:e.toggleToc}},[t("i",{staticClass:"icon-list"}),t("span",[e._v("目录")])])]):e._e(),e.isMobile&&(e.sidebarVisible||e.tocVisible)?t("div",{staticClass:"overlay",on:{click:e.closeAllPanels}}):e._e(),t("aside",{ref:"sidebar",staticClass:"sidebar",class:{"sidebar-hidden":!e.sidebarVisible&&e.isMobile,"sidebar-visible":e.sidebarVisible||!e.isMobile}},[e.isMobile?t("div",{staticClass:"sidebar-header"},[t("span",{staticClass:"sidebar-title"},[e._v("帮助文档")]),t("button",{staticClass:"close-btn",on:{click:e.closeSidebar}},[t("i",{staticClass:"icon-close"},[e._v("×")])])]):e._e(),t("div",{staticClass:"sidebar-menu"},e._l(e.menu,(function(s){return t("div",{key:s.title,staticClass:"menu-category"},[t("div",{staticClass:"category-title"},[e._v(e._s(s.title))]),t("ul",{staticClass:"menu-list"},e._l(s.items,(function(s){return t("li",{key:s.path,staticClass:"menu-item"},[t("router-link",{staticClass:"menu-link",class:{"menu-link-active":e.isMenuActive(s.path)},attrs:{to:s.path},on:{click:e.onMenuItemClick}},[e._v(" "+e._s(s.name)+" ")])],1)})),0)])})),0)]),t("main",{ref:"mainContent",staticClass:"main-content",class:{"content-expanded":(!e.sidebarVisible||!e.tocVisible)&&e.isMobile,"content-full":!e.sidebarVisible&&!e.tocVisible&&e.isMobile}},[t("HelpContent",{attrs:{doc:e.currentDoc,"prev-page":e.getPrevPage(),"next-page":e.getNextPage()},on:{"content-loaded":e.buildToc}})],1),t("aside",{staticClass:"toc",class:{"toc-hidden":!e.tocVisible&&e.isMobile,"toc-visible":e.tocVisible||!e.isMobile}},[e.isMobile?t("div",{staticClass:"toc-header"},[t("span",{staticClass:"toc-title"},[e._v("文章导航")]),t("button",{staticClass:"close-btn",on:{click:e.closeToc}},[t("i",{staticClass:"icon-close"},[e._v("×")])])]):e._e(),e.isMobile?e._e():t("div",{staticClass:"toc-title"},[e._v("文章导航")]),t("ul",{staticClass:"toc-list"},e._l(e.toc,(function(s){return t("li",{key:s.id,staticClass:"toc-item",class:{"toc-item-h3":3===s.level,active:s.id===e.activeTocId}},[t("a",{staticClass:"toc-link",attrs:{href:"#"+s.id},on:{click:function(t){return t.preventDefault(),e.scrollToAnchor(s.id)}}},[e._v(" "+e._s(s.text)+" ")])])})),0)]),t("Mider"),t("chatAi")],1)},a=[],o=(s(7658),function(){var e=this,t=e._self._c;return t("div",{staticClass:"doc-content"},[e.loading?t("div",{staticClass:"loading"},[e._v("文档加载中...")]):e.error?t("div",{staticClass:"error"},[e._v("文档加载失败: "+e._s(e.error))]):t("div",[t("div",{ref:"contentRef",domProps:{innerHTML:e._s(e.markdownContent)}}),t("div",{staticClass:"page-navigation"},[t("div",{staticClass:"prev-next-container"},[e.prevPage?t("router-link",{staticClass:"prev-page",attrs:{to:e.prevPage.path}},[t("div",{staticClass:"nav-label"},[e._v("上一篇")]),t("div",{staticClass:"nav-title"},[e._v(e._s(e.prevPage.name))])]):t("div",{staticClass:"prev-page empty"}),e.nextPage?t("router-link",{staticClass:"next-page",attrs:{to:e.nextPage.path}},[t("div",{staticClass:"nav-label"},[e._v("下一篇")]),t("div",{staticClass:"nav-title"},[e._v(e._s(e.nextPage.name))])]):t("div",{staticClass:"next-page empty"})],1)])])])}),n=[],l={props:{doc:String,prevPage:Object,nextPage:Object},data(){return{markdownContent:"",loading:!1,error:null}},watch:{doc:{immediate:!0,async handler(e){this.loading=!0,this.error=null;try{const t=await s(817)(`./${e}.md`);this.markdownContent=t.default,this.$nextTick((()=>{this.processContent(),this.$emit("content-loaded")}))}catch(t){this.error=t.message,this.markdownContent="<h1>文档加载失败</h1>"}finally{this.loading=!1}}}},methods:{slugify(e){let t=e.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9\-]+/g,"").replace(/\-\-+/g,"-").replace(/^-+/,"").replace(/-+$/,"");return t&&!/^[0-9]+$/.test(t)||(t="content-section-"+t+"-"+Math.random().toString(36).substring(2,11)),t},processContent(){if(this.$refs.contentRef){const e=this.$refs.contentRef.querySelectorAll("h2, h3");e.forEach((e=>{e.id=this.slugify(e.textContent)}));const t=this.$refs.contentRef.querySelectorAll("pre code");t.forEach((e=>{e.classList.add("hljs")}));const s=this.$refs.contentRef.querySelectorAll('a[href^="#"]');s.forEach((e=>{e.addEventListener("click",(t=>{t.preventDefault();const s=e.getAttribute("href").substring(1),i=document.getElementById(s);i&&i.scrollIntoView({behavior:"smooth"})}))}))}}}},c=l,r=s(1001),d=(0,r.Z)(c,o,n,!1,null,null,null),h=d.exports,u=s(9484),p=s(3644),m={components:{HelpContent:h,chatAi:u.Z,Mider:p.Z},data(){return{menu:[{title:"弹性部署服务",items:[{name:"平台概要",path:"/help/summary"},{name:"快速开始",path:"/help/quick-start"},{name:"常见问题",path:"/help/qustion"}]},{title:"功能介绍",items:[{name:"镜像仓库",path:"/help/mirror"},{name:"GPU选型指南",path:"/help/gpu-selection"},{name:"健康检查",path:"/help/health-check"},{name:"K8S YAML 导入",path:"/help/k8s-yaml-import"},{name:"云存储加速",path:"/help/cloud-storage"}]},{title:"最佳实践",items:[{name:"弹性部署服务-Serverless 基础认识",path:"/help/deploy-serverless"},{name:"容器化部署 Ollama+Qwen3",path:"/help/ollama-qwen"},{name:"容器化部署 Ollama+Qwen3+Open WebUI",path:"/help/ollama-qwen-webui"},{name:"容器化部署 JupyterLab",path:"/help/jupyter-lab"},{name:"容器化部署 Flux.1-dev 文生图模型应用",path:"/help/flux-dev"},{name:"容器化部署 FramePack 图生视频框架",path:"/help/frame-pack"},{name:"容器化部署 Whisper",path:"/help/whisper"},{name:"容器化部署 StableDiffusion1.5-WebUI 应用",path:"/help/stable-diffusion1.5"},{name:"容器化部署 StableDiffusion2.1-WebUI 应用",path:"/help/stable-diffusion2.1"},{name:"容器化部署 StableDiffusion3.5-large-文生图模型应用",path:"/help/stable-diffusion3.5-large"}]},{title:"账户与实名",items:[{name:"手机号注册与登录",path:"/help/register-login"},{name:"个人用户实名",path:"/help/personal-certification"}]},{title:"服务协议",items:[{name:"服务协议",path:"/help/user-agreement"},{name:"隐私政策",path:"/help/privacy-policy"}]},{title:"其他",items:[{name:"Docker 教程",path:"/help/docker-tutorial"}]}],toc:[],allPages:[],activeTocId:null,isAnchorClicking:!1,isMobile:!1,windowWidth:0,sidebarVisible:!0,tocVisible:!0,sidebarScrollPosition:0}},computed:{currentDoc(){return this.$route.params.doc||"summary"},currentPath(){return this.$route.path}},created(){this.flattenPages(),this.checkScreenSize()},mounted(){this.updatePageTitle();const e=localStorage.getItem("helpSidebarScrollPosition");e&&(this.sidebarScrollPosition=parseInt(e,10)),this.$nextTick((()=>{const e=this.$refs.mainContent;e&&e.addEventListener("scroll",this.handleContentScroll);const t=this.$refs.sidebar;t&&t.addEventListener("scroll",this.handleSidebarScroll),this.restoreSidebarScrollPosition()})),window.addEventListener("resize",this.handleResize),this.checkScreenSize()},beforeDestroy(){const e=this.$refs.mainContent;e&&e.removeEventListener("scroll",this.handleContentScroll);const t=this.$refs.sidebar;t&&t.removeEventListener("scroll",this.handleSidebarScroll),window.removeEventListener("resize",this.handleResize)},watch:{"$route.path"(){this.updatePageTitle(),this.$nextTick((()=>{setTimeout((()=>{this.restoreSidebarScrollPosition()}),50)}))}},methods:{slugify(e){let t=e.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9\-]+/g,"").replace(/\-\-+/g,"-").replace(/^-+/,"").replace(/-+$/,"");return t&&!/^[0-9]+$/.test(t)||(t="toc-section-"+t+"-"+Date.now()),t},flattenPages(){this.allPages=[],this.menu.forEach((e=>{e.items.forEach((t=>{this.allPages.push({name:t.name,path:t.path,category:e.title})}))}))},getPrevPage(){const e=this.allPages.findIndex((e=>e.path===this.currentPath));return e>0?this.allPages[e-1]:null},getNextPage(){const e=this.allPages.findIndex((e=>e.path===this.currentPath));return-1!==e&&e<this.allPages.length-1?this.allPages[e+1]:null},buildToc(){this.$nextTick((()=>{const e=this.$refs.mainContent,t=e.querySelector(".doc-content");if(!t)return;const s=t.querySelectorAll("h2, h3");this.toc=Array.from(s).map((e=>{const t=e.id,s=t||this.slugify(e.textContent);return t||(e.id=s),{id:s,text:e.textContent,level:parseInt(e.tagName.substring(1))}})),this.$nextTick(this.handleContentScroll)}))},updatePageTitle(){const e=this.$route.path;for(const t of this.menu)for(const s of t.items)if(s.path===e)return void(this.currentPageTitle=s.name)},scrollToAnchor(e){const t=30;this.isAnchorClicking=!0,this.activeTocId=e;const s=this.$refs.mainContent,i=s.querySelector(".doc-content"),a=document.getElementById(e);if(a){const e=a.offsetTop-i.offsetTop;s.scrollTop=e-t}setTimeout((()=>{this.isAnchorClicking=!1}),100)},handleContentScroll(){if(this.isAnchorClicking)return;const e=30,t=this.$refs.mainContent,s=t.querySelector(".doc-content");if(!s)return;const i=s.querySelectorAll("h2, h3");let a=null;const o=t.scrollTop;for(let n=i.length-1;n>=0;n--){const t=i[n];if(t.offsetTop-e<=o){a=t.id;break}}this.activeTocId=a},isMenuActive(e){return this.$route.path===e||("/help"===this.$route.path||"/help/"===this.$route.path)&&this.menu[0].items[0].path===e},checkScreenSize(){this.windowWidth=window.innerWidth;const e=this.isMobile;this.isMobile=this.windowWidth<=992,e&&!this.isMobile?(this.sidebarVisible=!0,this.tocVisible=!0):!e&&this.isMobile&&(this.sidebarVisible=!1,this.tocVisible=!1)},handleResize(){clearTimeout(this.resizeTimer),this.resizeTimer=setTimeout((()=>{this.checkScreenSize()}),250)},toggleSidebar(){this.sidebarVisible=!this.sidebarVisible,this.sidebarVisible&&this.tocVisible&&(this.tocVisible=!1)},toggleToc(){this.tocVisible=!this.tocVisible,this.tocVisible&&this.sidebarVisible&&(this.sidebarVisible=!1)},closeSidebar(){this.sidebarVisible=!1},closeToc(){this.tocVisible=!1},closeAllPanels(){this.sidebarVisible=!1,this.tocVisible=!1},onMenuItemClick(){this.isMobile&&(this.sidebarVisible=!1)},handleSidebarScroll(){const e=this.$refs.sidebar;e&&(this.sidebarScrollPosition=e.scrollTop,localStorage.setItem("helpSidebarScrollPosition",e.scrollTop.toString()))},restoreSidebarScrollPosition(){const e=this.$refs.sidebar;e&&this.sidebarScrollPosition>=0&&requestAnimationFrame((()=>{e.scrollTop=this.sidebarScrollPosition}))}}},v=m,f=(0,r.Z)(v,i,a,!1,null,"51c5a1c2",null),g=f.exports},817:function(e,t,s){var i={"./cloud-storage.md":[412,594],"./deploy-serverless.md":[7838,168],"./docker-tutorial.md":[354,912],"./flux-dev.md":[328,61],"./frame-pack.md":[1493,202],"./gpu-selection.md":[2696,198],"./health-check.md":[1063,719],"./jupyter-lab.md":[2395,469],"./k8s-yaml-import.md":[5305,956],"./mirror.md":[1628,675],"./ollama-qwen-webui.md":[9961,447],"./ollama-qwen.md":[6585,838],"./personal-certification.md":[2733,261],"./privacy-policy.md":[3751,802],"./quick-start.md":[1273,370],"./qustion.md":[4226,166],"./register-login.md":[4493,231],"./stable-diffusion1.5.md":[7507,576],"./stable-diffusion2.1.md":[1362,91],"./stable-diffusion3.5-large.md":[9045,371],"./summary.md":[1782,543],"./user-agreement.md":[7553,991],"./whisper.md":[3797,959]};function a(e){if(!s.o(i,e))return Promise.resolve().then((function(){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=i[e],a=t[0];return s.e(t[1]).then((function(){return s(a)}))}a.keys=function(){return Object.keys(i)},a.id=817,e.exports=a},46:function(e,t,s){"use strict";e.exports=s.p+"img/wechat.7b433d2e.jpg"}}]);
//# sourceMappingURL=24.1bbfac43.js.map