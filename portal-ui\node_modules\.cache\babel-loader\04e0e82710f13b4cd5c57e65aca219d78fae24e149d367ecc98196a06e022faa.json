{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c('div', {\n    staticClass: \"login-left-side\"\n  }, [_c('div', {\n    staticClass: \"logo-container\"\n  }, [_c('a', {\n    staticClass: \"logo-link\",\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/index');\n      }\n    }\n  }, [_c('img', {\n    staticClass: \"logo\",\n    attrs: {\n      \"src\": require(\"../../assets/logo_tiangong.png\"),\n      \"alt\": \"算力租赁\"\n    }\n  })])]), _vm._m(0), _c('div', {\n    staticClass: \"visual-element\"\n  }, [_c('div', {\n    staticClass: \"server-illustration\"\n  }, _vm._l(_vm.servers, function (server, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"server-unit\",\n      style: {\n        animationDelay: `${index * 0.2}s`,\n        transform: `translateY(${index * 4}px)`\n      }\n    }, [_c('div', {\n      staticClass: \"server-light\"\n    })]);\n  }), 0), _c('div', {\n    staticClass: \"connections\"\n  })]), _c('div', {\n    staticClass: \"features\"\n  }, _vm._l(_vm.features, function (feature, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"feature-item\"\n    }, [_c('div', {\n      staticClass: \"feature-text\"\n    }, [_c('h3', [_vm._v(_vm._s(feature.title))]), _c('p', [_vm._v(_vm._s(feature.description))])])]);\n  }), 0), _c('div', {\n    staticClass: \"background-elements\"\n  }, _vm._l(20, function (i) {\n    return _c('div', {\n      key: i,\n      staticClass: \"floating-particle\",\n      style: {\n        left: `${Math.random() * 100}%`,\n        top: `${Math.random() * 100}%`,\n        animationDuration: `${3 + Math.random() * 10}s`,\n        animationDelay: `${Math.random() * 5}s`\n      }\n    });\n  }), 0)]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy;\n  return _c('div', {\n    staticClass: \"bottom-text\"\n  }, [_c('h2', {\n    staticClass: \"slogan\"\n  }, [_vm._v(\"高效算力 · 智慧未来\")]), _c('p', {\n    staticClass: \"sub-slogan\"\n  }, [_vm._v(\"专业算力租赁服务，为您的业务提供强大支持\")])]);\n}];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "_setup", "_setupProxy", "staticClass", "on", "click", "$event", "navigateTo", "attrs", "require", "_m", "_l", "servers", "server", "index", "key", "style", "animationDelay", "transform", "features", "feature", "_v", "_s", "title", "description", "i", "left", "Math", "random", "top", "animationDuration", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/views/Login/backgroundlogin.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c,_setup=_vm._self._setupProxy;return _c('div',{staticClass:\"login-left-side\"},[_c('div',{staticClass:\"logo-container\"},[_c('a',{staticClass:\"logo-link\",on:{\"click\":function($event){return _vm.navigateTo('/index')}}},[_c('img',{staticClass:\"logo\",attrs:{\"src\":require(\"../../assets/logo_tiangong.png\"),\"alt\":\"算力租赁\"}})])]),_vm._m(0),_c('div',{staticClass:\"visual-element\"},[_c('div',{staticClass:\"server-illustration\"},_vm._l((_vm.servers),function(server,index){return _c('div',{key:index,staticClass:\"server-unit\",style:({\n               animationDelay: `${index * 0.2}s`,\n               transform: `translateY(${index * 4}px)`\n             })},[_c('div',{staticClass:\"server-light\"})])}),0),_c('div',{staticClass:\"connections\"})]),_c('div',{staticClass:\"features\"},_vm._l((_vm.features),function(feature,index){return _c('div',{key:index,staticClass:\"feature-item\"},[_c('div',{staticClass:\"feature-text\"},[_c('h3',[_vm._v(_vm._s(feature.title))]),_c('p',[_vm._v(_vm._s(feature.description))])])])}),0),_c('div',{staticClass:\"background-elements\"},_vm._l((20),function(i){return _c('div',{key:i,staticClass:\"floating-particle\",style:({\n             left: `${Math.random() * 100}%`,\n             top: `${Math.random() * 100}%`,\n             animationDuration: `${3 + Math.random() * 10}s`,\n             animationDelay: `${Math.random() * 5}s`\n           })})}),0)])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c,_setup=_vm._self._setupProxy;return _c('div',{staticClass:\"bottom-text\"},[_c('h2',{staticClass:\"slogan\"},[_vm._v(\"高效算力 · 智慧未来\")]),_c('p',{staticClass:\"sub-slogan\"},[_vm._v(\"专业算力租赁服务，为您的业务提供强大支持\")])])\n}]\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;IAACE,MAAM,GAACH,GAAG,CAACE,KAAK,CAACE,WAAW;EAAC,OAAOH,EAAE,CAAC,KAAK,EAAC;IAACI,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACJ,EAAE,CAAC,KAAK,EAAC;IAACI,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACJ,EAAE,CAAC,GAAG,EAAC;IAACI,WAAW,EAAC,WAAW;IAACC,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;QAAC,OAAOR,GAAG,CAACS,UAAU,CAAC,QAAQ,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACR,EAAE,CAAC,KAAK,EAAC;IAACI,WAAW,EAAC,MAAM;IAACK,KAAK,EAAC;MAAC,KAAK,EAACC,OAAO,CAAC,gCAAgC,CAAC;MAAC,KAAK,EAAC;IAAM;EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACX,GAAG,CAACY,EAAE,CAAC,CAAC,CAAC,EAACX,EAAE,CAAC,KAAK,EAAC;IAACI,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACJ,EAAE,CAAC,KAAK,EAAC;IAACI,WAAW,EAAC;EAAqB,CAAC,EAACL,GAAG,CAACa,EAAE,CAAEb,GAAG,CAACc,OAAO,EAAE,UAASC,MAAM,EAACC,KAAK,EAAC;IAAC,OAAOf,EAAE,CAAC,KAAK,EAAC;MAACgB,GAAG,EAACD,KAAK;MAACX,WAAW,EAAC,aAAa;MAACa,KAAK,EAAE;QACrjBC,cAAc,EAAG,GAAEH,KAAK,GAAG,GAAI,GAAE;QACjCI,SAAS,EAAG,cAAaJ,KAAK,GAAG,CAAE;MACrC;IAAE,CAAC,EAAC,CAACf,EAAE,CAAC,KAAK,EAAC;MAACI,WAAW,EAAC;IAAc,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACI,WAAW,EAAC;EAAa,CAAC,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACI,WAAW,EAAC;EAAU,CAAC,EAACL,GAAG,CAACa,EAAE,CAAEb,GAAG,CAACqB,QAAQ,EAAE,UAASC,OAAO,EAACN,KAAK,EAAC;IAAC,OAAOf,EAAE,CAAC,KAAK,EAAC;MAACgB,GAAG,EAACD,KAAK;MAACX,WAAW,EAAC;IAAc,CAAC,EAAC,CAACJ,EAAE,CAAC,KAAK,EAAC;MAACI,WAAW,EAAC;IAAc,CAAC,EAAC,CAACJ,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACwB,EAAE,CAACF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,EAACxB,EAAE,CAAC,GAAG,EAAC,CAACD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACwB,EAAE,CAACF,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,EAACzB,EAAE,CAAC,KAAK,EAAC;IAACI,WAAW,EAAC;EAAqB,CAAC,EAACL,GAAG,CAACa,EAAE,CAAE,EAAE,EAAE,UAASc,CAAC,EAAC;IAAC,OAAO1B,EAAE,CAAC,KAAK,EAAC;MAACgB,GAAG,EAACU,CAAC;MAACtB,WAAW,EAAC,mBAAmB;MAACa,KAAK,EAAE;QAC7eU,IAAI,EAAG,GAAEC,IAAI,CAACC,MAAM,EAAE,GAAG,GAAI,GAAE;QAC/BC,GAAG,EAAG,GAAEF,IAAI,CAACC,MAAM,EAAE,GAAG,GAAI,GAAE;QAC9BE,iBAAiB,EAAG,GAAE,CAAC,GAAGH,IAAI,CAACC,MAAM,EAAE,GAAG,EAAG,GAAE;QAC/CX,cAAc,EAAG,GAAEU,IAAI,CAACC,MAAM,EAAE,GAAG,CAAE;MACvC;IAAE,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC;AACD,IAAIG,eAAe,GAAG,CAAC,YAAW;EAAC,IAAIjC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;IAACE,MAAM,GAACH,GAAG,CAACE,KAAK,CAACE,WAAW;EAAC,OAAOH,EAAE,CAAC,KAAK,EAAC;IAACI,WAAW,EAAC;EAAa,CAAC,EAAC,CAACJ,EAAE,CAAC,IAAI,EAAC;IAACI,WAAW,EAAC;EAAQ,CAAC,EAAC,CAACL,GAAG,CAACuB,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,GAAG,EAAC;IAACI,WAAW,EAAC;EAAY,CAAC,EAAC,CAACL,GAAG,CAACuB,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;AACvQ,CAAC,CAAC;AAEF,SAASxB,MAAM,EAAEkC,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}