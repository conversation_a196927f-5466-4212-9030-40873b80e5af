{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"doc-content\"\n  }, [_vm.loading ? _c('div', {\n    staticClass: \"loading\"\n  }, [_vm._v(\"文档加载中...\")]) : _vm.error ? _c('div', {\n    staticClass: \"error\"\n  }, [_vm._v(\"文档加载失败: \" + _vm._s(_vm.error))]) : _c('div', [_c('div', {\n    ref: \"contentRef\",\n    domProps: {\n      \"innerHTML\": _vm._s(_vm.markdownContent)\n    }\n  }), _c('div', {\n    staticClass: \"page-navigation\"\n  }, [_c('div', {\n    staticClass: \"prev-next-container\"\n  }, [_vm.prevPage ? _c('router-link', {\n    staticClass: \"prev-page\",\n    attrs: {\n      \"to\": _vm.prevPage.path\n    }\n  }, [_c('div', {\n    staticClass: \"nav-label\"\n  }, [_vm._v(\"上一篇\")]), _c('div', {\n    staticClass: \"nav-title\"\n  }, [_vm._v(_vm._s(_vm.prevPage.name))])]) : _c('div', {\n    staticClass: \"prev-page empty\"\n  }), _vm.nextPage ? _c('router-link', {\n    staticClass: \"next-page\",\n    attrs: {\n      \"to\": _vm.nextPage.path\n    }\n  }, [_c('div', {\n    staticClass: \"nav-label\"\n  }, [_vm._v(\"下一篇\")]), _c('div', {\n    staticClass: \"nav-title\"\n  }, [_vm._v(_vm._s(_vm.nextPage.name))])]) : _c('div', {\n    staticClass: \"next-page empty\"\n  })], 1)])])]);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "loading", "_v", "error", "_s", "ref", "domProps", "markdownContent", "prevPage", "attrs", "path", "name", "nextPage", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/views/HelpContent.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"doc-content\"},[(_vm.loading)?_c('div',{staticClass:\"loading\"},[_vm._v(\"文档加载中...\")]):(_vm.error)?_c('div',{staticClass:\"error\"},[_vm._v(\"文档加载失败: \"+_vm._s(_vm.error))]):_c('div',[_c('div',{ref:\"contentRef\",domProps:{\"innerHTML\":_vm._s(_vm.markdownContent)}}),_c('div',{staticClass:\"page-navigation\"},[_c('div',{staticClass:\"prev-next-container\"},[(_vm.prevPage)?_c('router-link',{staticClass:\"prev-page\",attrs:{\"to\":_vm.prevPage.path}},[_c('div',{staticClass:\"nav-label\"},[_vm._v(\"上一篇\")]),_c('div',{staticClass:\"nav-title\"},[_vm._v(_vm._s(_vm.prevPage.name))])]):_c('div',{staticClass:\"prev-page empty\"}),(_vm.nextPage)?_c('router-link',{staticClass:\"next-page\",attrs:{\"to\":_vm.nextPage.path}},[_c('div',{staticClass:\"nav-label\"},[_vm._v(\"下一篇\")]),_c('div',{staticClass:\"nav-title\"},[_vm._v(_vm._s(_vm.nextPage.name))])]):_c('div',{staticClass:\"next-page empty\"})],1)])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAAEH,GAAG,CAACI,OAAO,GAAEH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAS,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,GAAEL,GAAG,CAACM,KAAK,GAAEL,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAO,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,UAAU,GAACL,GAAG,CAACO,EAAE,CAACP,GAAG,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,GAACL,EAAE,CAAC,KAAK,EAAC,CAACA,EAAE,CAAC,KAAK,EAAC;IAACO,GAAG,EAAC,YAAY;IAACC,QAAQ,EAAC;MAAC,WAAW,EAACT,GAAG,CAACO,EAAE,CAACP,GAAG,CAACU,eAAe;IAAC;EAAC,CAAC,CAAC,EAACT,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAqB,CAAC,EAAC,CAAEH,GAAG,CAACW,QAAQ,GAAEV,EAAE,CAAC,aAAa,EAAC;IAACE,WAAW,EAAC,WAAW;IAACS,KAAK,EAAC;MAAC,IAAI,EAACZ,GAAG,CAACW,QAAQ,CAACE;IAAI;EAAC,CAAC,EAAC,CAACZ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAACL,GAAG,CAACO,EAAE,CAACP,GAAG,CAACW,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACb,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,CAAC,EAAEH,GAAG,CAACe,QAAQ,GAAEd,EAAE,CAAC,aAAa,EAAC;IAACE,WAAW,EAAC,WAAW;IAACS,KAAK,EAAC;MAAC,IAAI,EAACZ,GAAG,CAACe,QAAQ,CAACF;IAAI;EAAC,CAAC,EAAC,CAACZ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAACL,GAAG,CAACO,EAAE,CAACP,GAAG,CAACe,QAAQ,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACb,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACh8B,CAAC;AACD,IAAIa,eAAe,GAAG,EAAE;AAExB,SAASjB,MAAM,EAAEiB,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}