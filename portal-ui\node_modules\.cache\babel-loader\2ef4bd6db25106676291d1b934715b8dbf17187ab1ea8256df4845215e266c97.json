{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport HelpContent from './HelpContent';\nimport chatAi from \"@/components/common/mider/chatAi\";\nimport Mider from '@/components/common/mider/Mider';\nexport default {\n  components: {\n    HelpContent,\n    chatAi,\n    Mider\n  },\n  data() {\n    return {\n      menu: [{\n        title: '弹性部署服务',\n        items: [{\n          name: '平台概要',\n          path: '/help/summary'\n        }, {\n          name: '快速开始',\n          path: '/help/quick-start'\n        }, {\n          name: '常见问题',\n          path: '/help/qustion'\n        }]\n      }, {\n        title: '功能介绍',\n        items: [{\n          name: '镜像仓库',\n          path: '/help/mirror'\n        }, {\n          name: 'GPU选型指南',\n          path: '/help/gpu-selection'\n        }, {\n          name: '健康检查',\n          path: '/help/health-check'\n        }, {\n          name: 'K8S YAML 导入',\n          path: '/help/k8s-yaml-import'\n        }, {\n          name: '云存储加速',\n          path: '/help/cloud-storage'\n        }]\n      }, {\n        title: '最佳实践',\n        items: [{\n          name: '弹性部署服务-Serverless 基础认识',\n          path: '/help/deploy-serverless'\n        }, {\n          name: '容器化部署 Ollama+Qwen3',\n          path: '/help/ollama-qwen'\n        }, {\n          name: '容器化部署 Ollama+Qwen3+Open WebUI',\n          path: '/help/ollama-qwen-webui'\n        }, {\n          name: '容器化部署 JupyterLab',\n          path: '/help/jupyter-lab'\n        }, {\n          name: '容器化部署 Flux.1-dev 文生图模型应用',\n          path: '/help/flux-dev'\n        }, {\n          name: '容器化部署 FramePack 图生视频框架',\n          path: '/help/frame-pack'\n        }, {\n          name: '容器化部署 Whisper',\n          path: '/help/whisper'\n        }, {\n          name: '容器化部署 StableDiffusion1.5-WebUI 应用',\n          path: '/help/stable-diffusion1.5'\n        }, {\n          name: '容器化部署 StableDiffusion2.1-WebUI 应用',\n          path: '/help/stable-diffusion2.1'\n        }, {\n          name: '容器化部署 StableDiffusion3.5-large-文生图模型应用',\n          path: '/help/stable-diffusion3.5-large'\n        }]\n      }, {\n        title: '账户与实名',\n        items: [{\n          name: '手机号注册与登录',\n          path: '/help/register-login'\n        }, {\n          name: '个人用户实名',\n          path: '/help/personal-certification'\n        }]\n      }, {\n        title: '服务协议',\n        items: [{\n          name: '服务协议',\n          path: '/help/user-agreement'\n        }, {\n          name: '隐私政策',\n          path: '/help/privacy-policy'\n        }]\n      }, {\n        title: '其他',\n        items: [{\n          name: 'Docker 教程',\n          path: '/help/docker-tutorial'\n        }]\n      }],\n      toc: [],\n      allPages: [],\n      activeTocId: null,\n      isAnchorClicking: false,\n      // 响应式状态\n      isMobile: false,\n      windowWidth: 0,\n      sidebarVisible: true,\n      tocVisible: true,\n      // 侧边栏滚动位置保存\n      sidebarScrollPosition: 0\n    };\n  },\n  computed: {\n    currentDoc() {\n      return this.$route.params.doc || 'summary';\n    },\n    currentPath() {\n      return this.$route.path;\n    }\n  },\n  created() {\n    this.flattenPages();\n    this.checkScreenSize();\n  },\n  mounted() {\n    this.updatePageTitle();\n\n    // 从 localStorage 恢复滚动位置\n    const savedScrollPosition = localStorage.getItem('helpSidebarScrollPosition');\n    if (savedScrollPosition) {\n      this.sidebarScrollPosition = parseInt(savedScrollPosition, 10);\n    }\n    this.$nextTick(() => {\n      const mainContent = this.$refs.mainContent;\n      if (mainContent) {\n        mainContent.addEventListener('scroll', this.handleContentScroll);\n      }\n      const sidebar = this.$refs.sidebar;\n      if (sidebar) {\n        sidebar.addEventListener('scroll', this.handleSidebarScroll);\n      }\n      this.restoreSidebarScrollPosition();\n    });\n    window.addEventListener('resize', this.handleResize);\n    this.checkScreenSize();\n  },\n  beforeDestroy() {\n    const mainContent = this.$refs.mainContent;\n    if (mainContent) {\n      mainContent.removeEventListener('scroll', this.handleContentScroll);\n    }\n    const sidebar = this.$refs.sidebar;\n    if (sidebar) {\n      sidebar.removeEventListener('scroll', this.handleSidebarScroll);\n    }\n    window.removeEventListener('resize', this.handleResize);\n  },\n  watch: {\n    '$route.path'() {\n      this.updatePageTitle();\n      this.$nextTick(() => {\n        setTimeout(() => {\n          // 滚动到当前激活的菜单项\n          this.scrollToActiveMenuItem();\n        }, 100);\n      });\n    }\n  },\n  methods: {\n    slugify(text) {\n      let slug = text.toLowerCase().replace(/\\s+/g, '-') // 空格转为-\n      .replace(/[^a-z0-9\\-]+/g, '') // 只保留小写字母、数字、短横线\n      .replace(/\\-\\-+/g, '-') // 多个-合并为一个\n      .replace(/^-+/, '') // 去除开头-\n      .replace(/-+$/, ''); // 去除结尾-\n      if (!slug || /^[0-9]+$/.test(slug)) {\n        // 添加时间戳确保唯一性\n        slug = 'toc-section-' + slug + '-' + Date.now();\n      }\n      return slug;\n    },\n    flattenPages() {\n      this.allPages = [];\n      this.menu.forEach(category => {\n        category.items.forEach(item => {\n          this.allPages.push({\n            name: item.name,\n            path: item.path,\n            category: category.title\n          });\n        });\n      });\n    },\n    getPrevPage() {\n      const currentIndex = this.allPages.findIndex(page => page.path === this.currentPath);\n      return currentIndex > 0 ? this.allPages[currentIndex - 1] : null;\n    },\n    getNextPage() {\n      const currentIndex = this.allPages.findIndex(page => page.path === this.currentPath);\n      return currentIndex !== -1 && currentIndex < this.allPages.length - 1 ? this.allPages[currentIndex + 1] : null;\n    },\n    buildToc() {\n      this.$nextTick(() => {\n        const mainContent = this.$refs.mainContent;\n        const docContent = mainContent.querySelector('.doc-content');\n        if (!docContent) return;\n        const headings = docContent.querySelectorAll('h2, h3');\n        this.toc = Array.from(headings).map(h => {\n          // 如果元素已经有ID，使用现有的ID，否则生成新的\n          const existingId = h.id;\n          const id = existingId || this.slugify(h.textContent);\n          // 只有当元素没有ID时才设置ID\n          if (!existingId) {\n            h.id = id;\n          }\n          return {\n            id: id,\n            text: h.textContent,\n            level: parseInt(h.tagName.substring(1))\n          };\n        });\n        this.$nextTick(this.handleContentScroll);\n      });\n    },\n    updatePageTitle() {\n      const currentPath = this.$route.path;\n      for (const category of this.menu) {\n        for (const item of category.items) {\n          if (item.path === currentPath) {\n            this.currentPageTitle = item.name;\n            return;\n          }\n        }\n      }\n    },\n    scrollToAnchor(id) {\n      const OFFSET = 30;\n      this.isAnchorClicking = true;\n      this.activeTocId = id;\n      const mainContent = this.$refs.mainContent;\n      const docContent = mainContent.querySelector('.doc-content');\n      const element = document.getElementById(id);\n      if (element) {\n        const offsetTop = element.offsetTop - docContent.offsetTop;\n        mainContent.scrollTop = offsetTop - OFFSET;\n      }\n      setTimeout(() => {\n        this.isAnchorClicking = false;\n      }, 100);\n    },\n    handleContentScroll() {\n      if (this.isAnchorClicking) return;\n      const OFFSET = 30;\n      const mainContent = this.$refs.mainContent;\n      const docContent = mainContent.querySelector('.doc-content');\n      if (!docContent) return;\n      const headings = docContent.querySelectorAll('h2, h3');\n      let activeId = null;\n      const scrollTop = mainContent.scrollTop;\n      for (let i = headings.length - 1; i >= 0; i--) {\n        const heading = headings[i];\n        if (heading.offsetTop - OFFSET <= scrollTop) {\n          activeId = heading.id;\n          break;\n        }\n      }\n      this.activeTocId = activeId;\n    },\n    isMenuActive(path) {\n      if (this.$route.path === path) return true;\n      if ((this.$route.path === '/help' || this.$route.path === '/help/') && this.menu[0].items[0].path === path) return true;\n      return false;\n    },\n    checkScreenSize() {\n      this.windowWidth = window.innerWidth;\n      const wasMobile = this.isMobile;\n      this.isMobile = this.windowWidth <= 992;\n\n      // 如果从移动端切换到桌面端，显示所有面板\n      if (wasMobile && !this.isMobile) {\n        this.sidebarVisible = true;\n        this.tocVisible = true;\n      }\n      // 如果从桌面端切换到移动端，隐藏侧边栏\n      else if (!wasMobile && this.isMobile) {\n        this.sidebarVisible = false;\n        this.tocVisible = false;\n      }\n    },\n    handleResize() {\n      clearTimeout(this.resizeTimer);\n      this.resizeTimer = setTimeout(() => {\n        this.checkScreenSize();\n      }, 250);\n    },\n    toggleSidebar() {\n      this.sidebarVisible = !this.sidebarVisible;\n      if (this.sidebarVisible && this.tocVisible) {\n        this.tocVisible = false;\n      }\n    },\n    toggleToc() {\n      this.tocVisible = !this.tocVisible;\n      if (this.tocVisible && this.sidebarVisible) {\n        this.sidebarVisible = false;\n      }\n    },\n    closeSidebar() {\n      this.sidebarVisible = false;\n    },\n    closeToc() {\n      this.tocVisible = false;\n    },\n    closeAllPanels() {\n      this.sidebarVisible = false;\n      this.tocVisible = false;\n    },\n    onMenuItemClick() {\n      if (this.isMobile) {\n        this.sidebarVisible = false;\n      }\n    },\n    handleSidebarScroll() {\n      const sidebar = this.$refs.sidebar;\n      if (sidebar) {\n        this.sidebarScrollPosition = sidebar.scrollTop;\n        localStorage.setItem('helpSidebarScrollPosition', sidebar.scrollTop.toString());\n      }\n    },\n    restoreSidebarScrollPosition() {\n      const sidebar = this.$refs.sidebar;\n      if (sidebar && this.sidebarScrollPosition >= 0) {\n        requestAnimationFrame(() => {\n          sidebar.scrollTop = this.sidebarScrollPosition;\n        });\n      }\n    },\n    scrollToActiveMenuItem() {\n      const sidebar = this.$refs.sidebar;\n      if (!sidebar) return;\n\n      // 查找当前激活的菜单项\n      const activeLink = sidebar.querySelector('.router-link-active');\n      if (!activeLink) return;\n\n      // 计算菜单项相对于侧边栏的位置\n      const linkTop = activeLink.offsetTop;\n      const linkHeight = activeLink.offsetHeight;\n      const sidebarHeight = sidebar.clientHeight;\n      const sidebarScrollTop = sidebar.scrollTop;\n\n      // 检查菜单项是否在可视区域内\n      const isVisible = linkTop >= sidebarScrollTop && linkTop + linkHeight <= sidebarScrollTop + sidebarHeight;\n      if (!isVisible) {\n        // 计算滚动位置，让菜单项居中显示\n        const targetScrollTop = linkTop - sidebarHeight / 2 + linkHeight / 2;\n\n        // 平滑滚动到目标位置\n        sidebar.scrollTo({\n          top: Math.max(0, targetScrollTop),\n          behavior: 'smooth'\n        });\n\n        // 更新保存的滚动位置\n        setTimeout(() => {\n          this.sidebarScrollPosition = sidebar.scrollTop;\n          localStorage.setItem('helpSidebarScrollPosition', sidebar.scrollTop.toString());\n        }, 300);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["HelpContent", "chatAi", "<PERSON><PERSON>", "components", "data", "menu", "title", "items", "name", "path", "toc", "allPages", "activeTocId", "isAnchorClicking", "isMobile", "windowWidth", "sidebarVisible", "tocVisible", "sidebarScrollPosition", "computed", "currentDoc", "$route", "params", "doc", "currentPath", "created", "flattenPages", "checkScreenSize", "mounted", "updatePageTitle", "savedScrollPosition", "localStorage", "getItem", "parseInt", "$nextTick", "mainContent", "$refs", "addEventListener", "handleContentScroll", "sidebar", "handleSidebarScroll", "restoreSidebarScrollPosition", "window", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "watch", "$route.path", "setTimeout", "scrollToActiveMenuItem", "methods", "slugify", "text", "slug", "toLowerCase", "replace", "test", "Date", "now", "for<PERSON>ach", "category", "item", "push", "getPrevPage", "currentIndex", "findIndex", "page", "getNextPage", "length", "buildToc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "querySelector", "headings", "querySelectorAll", "Array", "from", "map", "h", "existingId", "id", "textContent", "level", "tagName", "substring", "currentPageTitle", "scrollToAnchor", "OFFSET", "element", "document", "getElementById", "offsetTop", "scrollTop", "activeId", "i", "heading", "isMenuActive", "innerWidth", "was<PERSON><PERSON><PERSON>", "clearTimeout", "resizeTimer", "toggleSidebar", "toggleToc", "closeSidebar", "closeToc", "closeAllPanels", "onMenuItemClick", "setItem", "toString", "requestAnimationFrame", "activeLink", "linkTop", "linkHeight", "offsetHeight", "sidebarHeight", "clientHeight", "sidebarScrollTop", "isVisible", "targetScrollTop", "scrollTo", "top", "Math", "max", "behavior"], "sources": ["src/views/HelpView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"help-layout\">\r\n    <!-- 移动端控制按钮 -->\r\n    <div class=\"mobile-controls\" v-if=\"isMobile\">\r\n      <button\r\n        class=\"sidebar-toggle\"\r\n        @click=\"toggleSidebar\"\r\n        :class=\"{ 'active': sidebarVisible }\"\r\n      >\r\n        <i class=\"icon-menu\"></i>\r\n        <span>菜单</span>\r\n      </button>\r\n      <button\r\n        class=\"toc-toggle\"\r\n        @click=\"toggleToc\"\r\n        :class=\"{ 'active': tocVisible }\"\r\n      >\r\n        <i class=\"icon-list\"></i>\r\n        <span>目录</span>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 遮罩层 -->\r\n    <div\r\n      class=\"overlay\"\r\n      v-if=\"isMobile && (sidebarVisible || tocVisible)\"\r\n      @click=\"closeAllPanels\"\r\n    ></div>\r\n\r\n    <!-- 左侧边栏 -->\r\n    <aside\r\n      ref=\"sidebar\"\r\n      class=\"sidebar\"\r\n      :class=\"{\r\n        'sidebar-hidden': !sidebarVisible && isMobile,\r\n        'sidebar-visible': sidebarVisible || !isMobile\r\n      }\"\r\n    >\r\n      <div class=\"sidebar-header\" v-if=\"isMobile\">\r\n        <span class=\"sidebar-title\">帮助文档</span>\r\n        <button class=\"close-btn\" @click=\"closeSidebar\">\r\n          <i class=\"icon-close\">×</i>\r\n        </button>\r\n      </div>\r\n      <div class=\"sidebar-menu\">\r\n        <div v-for=\"category in menu\" :key=\"category.title\" class=\"menu-category\">\r\n          <div class=\"category-title\">{{ category.title }}</div>\r\n          <ul class=\"menu-list\">\r\n            <li v-for=\"item in category.items\" :key=\"item.path\" class=\"menu-item\">\r\n              <router-link\r\n                :to=\"item.path\"\r\n                class=\"menu-link\"\r\n                :class=\"{ 'menu-link-active': isMenuActive(item.path) }\"\r\n                @click=\"onMenuItemClick\"\r\n              >\r\n                {{ item.name }}\r\n              </router-link>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </aside>\r\n\r\n    <!-- 主内容区 -->\r\n    <main\r\n      class=\"main-content\"\r\n      ref=\"mainContent\"\r\n      :class=\"{\r\n        'content-expanded': (!sidebarVisible || !tocVisible) && isMobile,\r\n        'content-full': !sidebarVisible && !tocVisible && isMobile\r\n      }\"\r\n    >\r\n      <HelpContent\r\n        :doc=\"currentDoc\"\r\n        @content-loaded=\"buildToc\"\r\n        :prev-page=\"getPrevPage()\"\r\n        :next-page=\"getNextPage()\"\r\n      />\r\n    </main>\r\n\r\n    <!-- 右侧目录 -->\r\n    <aside\r\n      class=\"toc\"\r\n      :class=\"{\r\n        'toc-hidden': !tocVisible && isMobile,\r\n        'toc-visible': tocVisible || !isMobile\r\n      }\"\r\n    >\r\n      <div class=\"toc-header\" v-if=\"isMobile\">\r\n        <span class=\"toc-title\">文章导航</span>\r\n        <button class=\"close-btn\" @click=\"closeToc\">\r\n          <i class=\"icon-close\">×</i>\r\n        </button>\r\n      </div>\r\n      <div class=\"toc-title\" v-if=\"!isMobile\">文章导航</div>\r\n      <ul class=\"toc-list\">\r\n        <li v-for=\"item in toc\" :key=\"item.id\" class=\"toc-item\" :class=\"{ 'toc-item-h3': item.level === 3, 'active': item.id === activeTocId }\">\r\n          <a\r\n            :href=\"'#' + item.id\"\r\n            class=\"toc-link\"\r\n            @click.prevent=\"scrollToAnchor(item.id)\"\r\n          >\r\n            {{ item.text }}\r\n          </a>\r\n        </li>\r\n      </ul>\r\n    </aside>\r\n    <!-- 悬浮窗组件 -->\r\n    <Mider></Mider>\r\n    <chatAi></chatAi>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport HelpContent from './HelpContent';\r\nimport chatAi from \"@/components/common/mider/chatAi\";\r\nimport Mider from '@/components/common/mider/Mider';\r\nexport default {\r\n  components: { HelpContent, chatAi , Mider },\r\n  data() {\r\n    return {\r\n      menu: [\r\n        { title: '弹性部署服务', items: [\r\n          { name: '平台概要', path: '/help/summary' },\r\n          { name: '快速开始', path: '/help/quick-start' },\r\n          { name: '常见问题', path: '/help/qustion' }\r\n        ]},\r\n        { title: '功能介绍', items: [\r\n          { name: '镜像仓库', path: '/help/mirror' },\r\n          { name: 'GPU选型指南', path: '/help/gpu-selection' },\r\n          { name: '健康检查', path: '/help/health-check' },\r\n          { name: 'K8S YAML 导入', path: '/help/k8s-yaml-import' },\r\n          { name: '云存储加速', path: '/help/cloud-storage' }\r\n        ]},\r\n        { title: '最佳实践', items: [\r\n          { name: '弹性部署服务-Serverless 基础认识', path: '/help/deploy-serverless' },\r\n          { name: '容器化部署 Ollama+Qwen3', path: '/help/ollama-qwen' },\r\n          { name: '容器化部署 Ollama+Qwen3+Open WebUI', path: '/help/ollama-qwen-webui' },\r\n          { name: '容器化部署 JupyterLab', path: '/help/jupyter-lab' },\r\n          { name: '容器化部署 Flux.1-dev 文生图模型应用', path: '/help/flux-dev' },\r\n          { name: '容器化部署 FramePack 图生视频框架', path: '/help/frame-pack' },\r\n          { name: '容器化部署 Whisper', path: '/help/whisper' },\r\n          { name: '容器化部署 StableDiffusion1.5-WebUI 应用', path: '/help/stable-diffusion1.5' },\r\n          { name: '容器化部署 StableDiffusion2.1-WebUI 应用', path: '/help/stable-diffusion2.1' },\r\n          { name: '容器化部署 StableDiffusion3.5-large-文生图模型应用', path: '/help/stable-diffusion3.5-large' },\r\n        ]},\r\n        { title: '账户与实名', items: [\r\n          { name: '手机号注册与登录', path: '/help/register-login' },\r\n          { name: '个人用户实名', path: '/help/personal-certification' }\r\n        ]},\r\n        { title: '服务协议', items: [\r\n          { name: '服务协议', path: '/help/user-agreement' },\r\n          { name: '隐私政策', path: '/help/privacy-policy' }\r\n        ]},\r\n        { title: '其他', items: [\r\n          { name: 'Docker 教程', path: '/help/docker-tutorial' }\r\n        ]}\r\n      ],\r\n      toc: [],\r\n      allPages: [],\r\n      activeTocId: null,\r\n      isAnchorClicking: false,\r\n      // 响应式状态\r\n      isMobile: false,\r\n      windowWidth: 0,\r\n      sidebarVisible: true,\r\n      tocVisible: true,\r\n      // 侧边栏滚动位置保存\r\n      sidebarScrollPosition: 0\r\n    };\r\n  },\r\n  computed: {\r\n    currentDoc() {\r\n      return this.$route.params.doc || 'summary';\r\n    },\r\n    currentPath() {\r\n      return this.$route.path;\r\n    }\r\n  },\r\n  created() {\r\n    this.flattenPages();\r\n    this.checkScreenSize();\r\n  },\r\n  mounted() {\r\n    this.updatePageTitle();\r\n\r\n    // 从 localStorage 恢复滚动位置\r\n    const savedScrollPosition = localStorage.getItem('helpSidebarScrollPosition');\r\n    if (savedScrollPosition) {\r\n      this.sidebarScrollPosition = parseInt(savedScrollPosition, 10);\r\n    }\r\n\r\n    this.$nextTick(() => {\r\n      const mainContent = this.$refs.mainContent;\r\n      if (mainContent) {\r\n        mainContent.addEventListener('scroll', this.handleContentScroll);\r\n      }\r\n\r\n      const sidebar = this.$refs.sidebar;\r\n      if (sidebar) {\r\n        sidebar.addEventListener('scroll', this.handleSidebarScroll);\r\n      }\r\n\r\n      this.restoreSidebarScrollPosition();\r\n    });\r\n\r\n    window.addEventListener('resize', this.handleResize);\r\n    this.checkScreenSize();\r\n  },\r\n  beforeDestroy() {\r\n    const mainContent = this.$refs.mainContent;\r\n    if (mainContent) {\r\n      mainContent.removeEventListener('scroll', this.handleContentScroll);\r\n    }\r\n\r\n    const sidebar = this.$refs.sidebar;\r\n    if (sidebar) {\r\n      sidebar.removeEventListener('scroll', this.handleSidebarScroll);\r\n    }\r\n\r\n    window.removeEventListener('resize', this.handleResize);\r\n  },\r\n  watch: {\r\n    '$route.path'() {\r\n      this.updatePageTitle();\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          // 滚动到当前激活的菜单项\r\n          this.scrollToActiveMenuItem();\r\n        }, 100);\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    slugify(text) {\r\n      let slug = text\r\n        .toLowerCase()\r\n        .replace(/\\s+/g, '-')           // 空格转为-\r\n        .replace(/[^a-z0-9\\-]+/g, '')   // 只保留小写字母、数字、短横线\r\n        .replace(/\\-\\-+/g, '-')         // 多个-合并为一个\r\n        .replace(/^-+/, '')              // 去除开头-\r\n        .replace(/-+$/, '');             // 去除结尾-\r\n      if (!slug || /^[0-9]+$/.test(slug)) {\r\n        // 添加时间戳确保唯一性\r\n        slug = 'toc-section-' + slug + '-' + Date.now();\r\n      }\r\n      return slug;\r\n    },\r\n    flattenPages() {\r\n      this.allPages = [];\r\n      this.menu.forEach(category => {\r\n        category.items.forEach(item => {\r\n          this.allPages.push({\r\n            name: item.name,\r\n            path: item.path,\r\n            category: category.title\r\n          });\r\n        });\r\n      });\r\n    },\r\n    getPrevPage() {\r\n      const currentIndex = this.allPages.findIndex(page => page.path === this.currentPath);\r\n      return currentIndex > 0 ? this.allPages[currentIndex - 1] : null;\r\n    },\r\n    getNextPage() {\r\n      const currentIndex = this.allPages.findIndex(page => page.path === this.currentPath);\r\n      return (currentIndex !== -1 && currentIndex < this.allPages.length - 1) ? this.allPages[currentIndex + 1] : null;\r\n    },\r\n    buildToc() {\r\n      this.$nextTick(() => {\r\n        const mainContent = this.$refs.mainContent;\r\n        const docContent = mainContent.querySelector('.doc-content');\r\n        if (!docContent) return;\r\n        const headings = docContent.querySelectorAll('h2, h3');\r\n        this.toc = Array.from(headings).map(h => {\r\n          // 如果元素已经有ID，使用现有的ID，否则生成新的\r\n          const existingId = h.id;\r\n          const id = existingId || this.slugify(h.textContent);\r\n          // 只有当元素没有ID时才设置ID\r\n          if (!existingId) {\r\n            h.id = id;\r\n          }\r\n          return {\r\n            id: id,\r\n            text: h.textContent,\r\n            level: parseInt(h.tagName.substring(1))\r\n          };\r\n        });\r\n        this.$nextTick(this.handleContentScroll);\r\n      });\r\n    },\r\n    updatePageTitle() {\r\n      const currentPath = this.$route.path;\r\n      for (const category of this.menu) {\r\n        for (const item of category.items) {\r\n          if (item.path === currentPath) {\r\n            this.currentPageTitle = item.name;\r\n            return;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    scrollToAnchor(id) {\r\n      const OFFSET = 30;\r\n      this.isAnchorClicking = true;\r\n      this.activeTocId = id;\r\n      const mainContent = this.$refs.mainContent;\r\n      const docContent = mainContent.querySelector('.doc-content');\r\n      const element = document.getElementById(id);\r\n      if (element) {\r\n        const offsetTop = element.offsetTop - docContent.offsetTop;\r\n        mainContent.scrollTop = offsetTop - OFFSET;\r\n      }\r\n      setTimeout(() => {\r\n        this.isAnchorClicking = false;\r\n      }, 100);\r\n    },\r\n    handleContentScroll() {\r\n      if (this.isAnchorClicking) return;\r\n      const OFFSET = 30;\r\n      const mainContent = this.$refs.mainContent;\r\n      const docContent = mainContent.querySelector('.doc-content');\r\n      if (!docContent) return;\r\n      const headings = docContent.querySelectorAll('h2, h3');\r\n      let activeId = null;\r\n      const scrollTop = mainContent.scrollTop;\r\n      for (let i = headings.length - 1; i >= 0; i--) {\r\n        const heading = headings[i];\r\n        if (heading.offsetTop - OFFSET <= scrollTop) {\r\n          activeId = heading.id;\r\n          break;\r\n        }\r\n      }\r\n      this.activeTocId = activeId;\r\n    },\r\n    isMenuActive(path) {\r\n      if (this.$route.path === path) return true;\r\n      if ((this.$route.path === '/help' || this.$route.path === '/help/') && this.menu[0].items[0].path === path) return true;\r\n      return false;\r\n    },\r\n\r\n    checkScreenSize() {\r\n      this.windowWidth = window.innerWidth;\r\n      const wasMobile = this.isMobile;\r\n      this.isMobile = this.windowWidth <= 992;\r\n\r\n      // 如果从移动端切换到桌面端，显示所有面板\r\n      if (wasMobile && !this.isMobile) {\r\n        this.sidebarVisible = true;\r\n        this.tocVisible = true;\r\n      }\r\n      // 如果从桌面端切换到移动端，隐藏侧边栏\r\n      else if (!wasMobile && this.isMobile) {\r\n        this.sidebarVisible = false;\r\n        this.tocVisible = false;\r\n      }\r\n    },\r\n\r\n    handleResize() {\r\n      clearTimeout(this.resizeTimer);\r\n      this.resizeTimer = setTimeout(() => {\r\n        this.checkScreenSize();\r\n      }, 250);\r\n    },\r\n\r\n    toggleSidebar() {\r\n      this.sidebarVisible = !this.sidebarVisible;\r\n      if (this.sidebarVisible && this.tocVisible) {\r\n        this.tocVisible = false;\r\n      }\r\n    },\r\n\r\n    toggleToc() {\r\n      this.tocVisible = !this.tocVisible;\r\n      if (this.tocVisible && this.sidebarVisible) {\r\n        this.sidebarVisible = false;\r\n      }\r\n    },\r\n\r\n    closeSidebar() {\r\n      this.sidebarVisible = false;\r\n    },\r\n\r\n    closeToc() {\r\n      this.tocVisible = false;\r\n    },\r\n\r\n    closeAllPanels() {\r\n      this.sidebarVisible = false;\r\n      this.tocVisible = false;\r\n    },\r\n\r\n    onMenuItemClick() {\r\n      if (this.isMobile) {\r\n        this.sidebarVisible = false;\r\n      }\r\n    },\r\n\r\n    handleSidebarScroll() {\r\n      const sidebar = this.$refs.sidebar;\r\n      if (sidebar) {\r\n        this.sidebarScrollPosition = sidebar.scrollTop;\r\n        localStorage.setItem('helpSidebarScrollPosition', sidebar.scrollTop.toString());\r\n      }\r\n    },\r\n\r\n    restoreSidebarScrollPosition() {\r\n      const sidebar = this.$refs.sidebar;\r\n      if (sidebar && this.sidebarScrollPosition >= 0) {\r\n        requestAnimationFrame(() => {\r\n          sidebar.scrollTop = this.sidebarScrollPosition;\r\n        });\r\n      }\r\n    },\r\n\r\n    scrollToActiveMenuItem() {\r\n      const sidebar = this.$refs.sidebar;\r\n      if (!sidebar) return;\r\n\r\n      // 查找当前激活的菜单项\r\n      const activeLink = sidebar.querySelector('.router-link-active');\r\n      if (!activeLink) return;\r\n\r\n      // 计算菜单项相对于侧边栏的位置\r\n      const linkTop = activeLink.offsetTop;\r\n      const linkHeight = activeLink.offsetHeight;\r\n      const sidebarHeight = sidebar.clientHeight;\r\n      const sidebarScrollTop = sidebar.scrollTop;\r\n\r\n      // 检查菜单项是否在可视区域内\r\n      const isVisible = linkTop >= sidebarScrollTop &&\r\n                       (linkTop + linkHeight) <= (sidebarScrollTop + sidebarHeight);\r\n\r\n      if (!isVisible) {\r\n        // 计算滚动位置，让菜单项居中显示\r\n        const targetScrollTop = linkTop - (sidebarHeight / 2) + (linkHeight / 2);\r\n\r\n        // 平滑滚动到目标位置\r\n        sidebar.scrollTo({\r\n          top: Math.max(0, targetScrollTop),\r\n          behavior: 'smooth'\r\n        });\r\n\r\n        // 更新保存的滚动位置\r\n        setTimeout(() => {\r\n          this.sidebarScrollPosition = sidebar.scrollTop;\r\n          localStorage.setItem('helpSidebarScrollPosition', sidebar.scrollTop.toString());\r\n        }, 300);\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.help-layout {\r\n  display: flex;\r\n  min-height: calc(100vh - 60px);\r\n  background-color: #fff;\r\n  height: calc(100vh - 60px);\r\n  position: relative;\r\n}\r\n\r\n/* 移动端控制按钮 */\r\n.mobile-controls {\r\n  position: fixed;\r\n  top: 70px;\r\n  left: 10px;\r\n  z-index: 1001;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.sidebar-toggle,\r\n.toc-toggle {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  padding: 8px 12px;\r\n  background: #1890ff;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n}\r\n\r\n.sidebar-toggle:hover,\r\n.toc-toggle:hover {\r\n  background: #40a9ff;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.sidebar-toggle.active,\r\n.toc-toggle.active {\r\n  background: #096dd9;\r\n}\r\n\r\n.icon-menu,\r\n.icon-list {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n.icon-menu::before {\r\n  content: '☰';\r\n}\r\n\r\n.icon-list::before {\r\n  content: '📋';\r\n}\r\n\r\n/* 遮罩层 */\r\n.overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 999;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n/* 侧边栏样式 */\r\n.sidebar {\r\n  width: 300px;\r\n  border-right: 1px solid #eee;\r\n  background-color: #f8f8f8;\r\n  overflow-y: auto;\r\n  height: 100%;\r\n  transition: transform 0.3s ease;\r\n  z-index: 1000;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.sidebar-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  font-size: 18px;\r\n  cursor: pointer;\r\n  color: #666;\r\n  padding: 0;\r\n  width: 24px;\r\n  height: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.close-btn:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.sidebar-menu { padding: 10px 0; }\r\n.menu-category { margin-bottom: 10px; margin-left: 40px; border-bottom: 1px solid #e7e7e7;}\r\n.category-title {\r\n  font-size: 16px;\r\n  font-weight: 900;\r\n  padding: 8px 20px;\r\n  color: #333;\r\n}\r\n.menu-list { list-style: none; padding: 0; margin: 0; }\r\n.menu-item { padding: 0; }\r\n.menu-link {\r\n  display: block;\r\n  padding: 0px 20px 5px 30px;\r\n  color: #666;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n  transition: all 0.3s;\r\n}\r\n.menu-link:hover, .router-link-active {\r\n  color: #1890ff;\r\n  background-color: #e6f7ff;\r\n  border-right: 3px solid #1890ff;\r\n}\r\n/* 主内容区 */\r\n.main-content {\r\n  flex: 1;\r\n  padding: 30px 40px;\r\n  overflow-y: auto;\r\n  height: 100%;\r\n  transition: margin 0.3s ease;\r\n}\r\n\r\n.content-expanded {\r\n  margin-left: 0;\r\n  margin-right: 0;\r\n}\r\n\r\n.content-full {\r\n  margin-left: 0;\r\n  margin-right: 0;\r\n  padding: 20px;\r\n}\r\n\r\n/* 目录区域 */\r\n.toc {\r\n  width: 300px;\r\n  padding: 10px 20px 10px 20px;\r\n  border-left: 1px solid #eee;\r\n  background-color: #fff;\r\n  position: sticky;\r\n  top: 0;\r\n  max-height: 100vh;\r\n  overflow-y: auto;\r\n  transition: transform 0.3s ease;\r\n  z-index: 1000;\r\n}\r\n\r\n.toc-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 0;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.toc-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n.toc-list { list-style: none; padding: 0; margin: 0; }\r\n.toc-item { margin-bottom: 0px; }\r\n.toc-item-h3 { padding-left: 15px; }\r\n.toc-link {\r\n  color: #666;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n  display: block;\r\n  padding: 2px 0;\r\n  transition: color 0.3s;\r\n}\r\n.toc-link:hover { color: #1890ff; }\r\n.toc-item.active > .toc-link {\r\n  color: #1890ff;\r\n  font-weight: bold;\r\n  background: #e6f7ff;\r\n  border-radius: 3px;\r\n}\r\n.menu-link-active {\r\n  color: #1890ff;\r\n  background-color: #e6f7ff;\r\n  border-right: 3px solid #1890ff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 992px) {\r\n  .help-layout {\r\n    position: relative;\r\n  }\r\n\r\n  .mobile-controls {\r\n    display: flex;\r\n  }\r\n\r\n  .sidebar {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    height: 100vh;\r\n    z-index: 1000;\r\n    transform: translateX(-100%);\r\n  }\r\n\r\n  .sidebar-visible {\r\n    transform: translateX(0);\r\n  }\r\n\r\n  .sidebar-hidden {\r\n    transform: translateX(-100%);\r\n  }\r\n\r\n  .main-content {\r\n    width: 100%;\r\n    padding: 80px 20px 20px;\r\n  }\r\n\r\n  .toc {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    height: 100vh;\r\n    z-index: 1000;\r\n    transform: translateX(100%);\r\n  }\r\n\r\n  .toc-visible {\r\n    transform: translateX(0);\r\n  }\r\n\r\n  .toc-hidden {\r\n    transform: translateX(100%);\r\n  }\r\n}\r\n\r\n@media (min-width: 993px) {\r\n  .mobile-controls {\r\n    display: none;\r\n  }\r\n\r\n  .overlay {\r\n    display: none;\r\n  }\r\n\r\n  .sidebar-header,\r\n  .toc-header {\r\n    display: none;\r\n  }\r\n\r\n  .sidebar,\r\n  .toc {\r\n    position: static;\r\n    transform: none;\r\n  }\r\n}\r\n\r\n/* 平板适配 */\r\n@media (max-width: 1200px) and (min-width: 993px) {\r\n  .sidebar {\r\n    width: 250px;\r\n  }\r\n\r\n  .toc {\r\n    width: 250px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 20px 30px;\r\n  }\r\n}\r\n\r\n/* 小屏幕优化 */\r\n@media (max-width: 576px) {\r\n  .sidebar,\r\n  .toc {\r\n    width: 280px;\r\n  }\r\n\r\n  .main-content {\r\n    padding: 80px 15px 15px;\r\n  }\r\n\r\n  .mobile-controls {\r\n    left: 5px;\r\n    top: 65px;\r\n  }\r\n\r\n  .sidebar-toggle,\r\n  .toc-toggle {\r\n    padding: 6px 10px;\r\n    font-size: 11px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AAkHA,OAAAA,WAAA;AACA,OAAAC,MAAA;AACA,OAAAC,KAAA;AACA;EACAC,UAAA;IAAAH,WAAA;IAAAC,MAAA;IAAAC;EAAA;EACAE,KAAA;IACA;MACAC,IAAA,GACA;QAAAC,KAAA;QAAAC,KAAA,GACA;UAAAC,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA;MACA,GACA;QAAAH,KAAA;QAAAC,KAAA,GACA;UAAAC,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA;MACA,GACA;QAAAH,KAAA;QAAAC,KAAA,GACA;UAAAC,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA;MACA,GACA;QAAAH,KAAA;QAAAC,KAAA,GACA;UAAAC,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA;MACA,GACA;QAAAH,KAAA;QAAAC,KAAA,GACA;UAAAC,IAAA;UAAAC,IAAA;QAAA,GACA;UAAAD,IAAA;UAAAC,IAAA;QAAA;MACA,GACA;QAAAH,KAAA;QAAAC,KAAA,GACA;UAAAC,IAAA;UAAAC,IAAA;QAAA;MACA,EACA;MACAC,GAAA;MACAC,QAAA;MACAC,WAAA;MACAC,gBAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,cAAA;MACAC,UAAA;MACA;MACAC,qBAAA;IACA;EACA;EACAC,QAAA;IACAC,WAAA;MACA,YAAAC,MAAA,CAAAC,MAAA,CAAAC,GAAA;IACA;IACAC,YAAA;MACA,YAAAH,MAAA,CAAAZ,IAAA;IACA;EACA;EACAgB,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,eAAA;EACA;EACAC,QAAA;IACA,KAAAC,eAAA;;IAEA;IACA,MAAAC,mBAAA,GAAAC,YAAA,CAAAC,OAAA;IACA,IAAAF,mBAAA;MACA,KAAAZ,qBAAA,GAAAe,QAAA,CAAAH,mBAAA;IACA;IAEA,KAAAI,SAAA;MACA,MAAAC,WAAA,QAAAC,KAAA,CAAAD,WAAA;MACA,IAAAA,WAAA;QACAA,WAAA,CAAAE,gBAAA,gBAAAC,mBAAA;MACA;MAEA,MAAAC,OAAA,QAAAH,KAAA,CAAAG,OAAA;MACA,IAAAA,OAAA;QACAA,OAAA,CAAAF,gBAAA,gBAAAG,mBAAA;MACA;MAEA,KAAAC,4BAAA;IACA;IAEAC,MAAA,CAAAL,gBAAA,gBAAAM,YAAA;IACA,KAAAhB,eAAA;EACA;EACAiB,cAAA;IACA,MAAAT,WAAA,QAAAC,KAAA,CAAAD,WAAA;IACA,IAAAA,WAAA;MACAA,WAAA,CAAAU,mBAAA,gBAAAP,mBAAA;IACA;IAEA,MAAAC,OAAA,QAAAH,KAAA,CAAAG,OAAA;IACA,IAAAA,OAAA;MACAA,OAAA,CAAAM,mBAAA,gBAAAL,mBAAA;IACA;IAEAE,MAAA,CAAAG,mBAAA,gBAAAF,YAAA;EACA;EACAG,KAAA;IACA,aAAAC,CAAA;MACA,KAAAlB,eAAA;MACA,KAAAK,SAAA;QACAc,UAAA;UACA;UACA,KAAAC,sBAAA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACAC,QAAAC,IAAA;MACA,IAAAC,IAAA,GAAAD,IAAA,CACAE,WAAA,GACAC,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MACA,KAAAF,IAAA,eAAAG,IAAA,CAAAH,IAAA;QACA;QACAA,IAAA,oBAAAA,IAAA,SAAAI,IAAA,CAAAC,GAAA;MACA;MACA,OAAAL,IAAA;IACA;IACA3B,aAAA;MACA,KAAAf,QAAA;MACA,KAAAN,IAAA,CAAAsD,OAAA,CAAAC,QAAA;QACAA,QAAA,CAAArD,KAAA,CAAAoD,OAAA,CAAAE,IAAA;UACA,KAAAlD,QAAA,CAAAmD,IAAA;YACAtD,IAAA,EAAAqD,IAAA,CAAArD,IAAA;YACAC,IAAA,EAAAoD,IAAA,CAAApD,IAAA;YACAmD,QAAA,EAAAA,QAAA,CAAAtD;UACA;QACA;MACA;IACA;IACAyD,YAAA;MACA,MAAAC,YAAA,QAAArD,QAAA,CAAAsD,SAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAzD,IAAA,UAAAe,WAAA;MACA,OAAAwC,YAAA,YAAArD,QAAA,CAAAqD,YAAA;IACA;IACAG,YAAA;MACA,MAAAH,YAAA,QAAArD,QAAA,CAAAsD,SAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAzD,IAAA,UAAAe,WAAA;MACA,OAAAwC,YAAA,WAAAA,YAAA,QAAArD,QAAA,CAAAyD,MAAA,YAAAzD,QAAA,CAAAqD,YAAA;IACA;IACAK,SAAA;MACA,KAAAnC,SAAA;QACA,MAAAC,WAAA,QAAAC,KAAA,CAAAD,WAAA;QACA,MAAAmC,UAAA,GAAAnC,WAAA,CAAAoC,aAAA;QACA,KAAAD,UAAA;QACA,MAAAE,QAAA,GAAAF,UAAA,CAAAG,gBAAA;QACA,KAAA/D,GAAA,GAAAgE,KAAA,CAAAC,IAAA,CAAAH,QAAA,EAAAI,GAAA,CAAAC,CAAA;UACA;UACA,MAAAC,UAAA,GAAAD,CAAA,CAAAE,EAAA;UACA,MAAAA,EAAA,GAAAD,UAAA,SAAA3B,OAAA,CAAA0B,CAAA,CAAAG,WAAA;UACA;UACA,KAAAF,UAAA;YACAD,CAAA,CAAAE,EAAA,GAAAA,EAAA;UACA;UACA;YACAA,EAAA,EAAAA,EAAA;YACA3B,IAAA,EAAAyB,CAAA,CAAAG,WAAA;YACAC,KAAA,EAAAhD,QAAA,CAAA4C,CAAA,CAAAK,OAAA,CAAAC,SAAA;UACA;QACA;QACA,KAAAjD,SAAA,MAAAI,mBAAA;MACA;IACA;IACAT,gBAAA;MACA,MAAAL,WAAA,QAAAH,MAAA,CAAAZ,IAAA;MACA,WAAAmD,QAAA,SAAAvD,IAAA;QACA,WAAAwD,IAAA,IAAAD,QAAA,CAAArD,KAAA;UACA,IAAAsD,IAAA,CAAApD,IAAA,KAAAe,WAAA;YACA,KAAA4D,gBAAA,GAAAvB,IAAA,CAAArD,IAAA;YACA;UACA;QACA;MACA;IACA;IACA6E,eAAAN,EAAA;MACA,MAAAO,MAAA;MACA,KAAAzE,gBAAA;MACA,KAAAD,WAAA,GAAAmE,EAAA;MACA,MAAA5C,WAAA,QAAAC,KAAA,CAAAD,WAAA;MACA,MAAAmC,UAAA,GAAAnC,WAAA,CAAAoC,aAAA;MACA,MAAAgB,OAAA,GAAAC,QAAA,CAAAC,cAAA,CAAAV,EAAA;MACA,IAAAQ,OAAA;QACA,MAAAG,SAAA,GAAAH,OAAA,CAAAG,SAAA,GAAApB,UAAA,CAAAoB,SAAA;QACAvD,WAAA,CAAAwD,SAAA,GAAAD,SAAA,GAAAJ,MAAA;MACA;MACAtC,UAAA;QACA,KAAAnC,gBAAA;MACA;IACA;IACAyB,oBAAA;MACA,SAAAzB,gBAAA;MACA,MAAAyE,MAAA;MACA,MAAAnD,WAAA,QAAAC,KAAA,CAAAD,WAAA;MACA,MAAAmC,UAAA,GAAAnC,WAAA,CAAAoC,aAAA;MACA,KAAAD,UAAA;MACA,MAAAE,QAAA,GAAAF,UAAA,CAAAG,gBAAA;MACA,IAAAmB,QAAA;MACA,MAAAD,SAAA,GAAAxD,WAAA,CAAAwD,SAAA;MACA,SAAAE,CAAA,GAAArB,QAAA,CAAAJ,MAAA,MAAAyB,CAAA,OAAAA,CAAA;QACA,MAAAC,OAAA,GAAAtB,QAAA,CAAAqB,CAAA;QACA,IAAAC,OAAA,CAAAJ,SAAA,GAAAJ,MAAA,IAAAK,SAAA;UACAC,QAAA,GAAAE,OAAA,CAAAf,EAAA;UACA;QACA;MACA;MACA,KAAAnE,WAAA,GAAAgF,QAAA;IACA;IACAG,aAAAtF,IAAA;MACA,SAAAY,MAAA,CAAAZ,IAAA,KAAAA,IAAA;MACA,UAAAY,MAAA,CAAAZ,IAAA,qBAAAY,MAAA,CAAAZ,IAAA,uBAAAJ,IAAA,IAAAE,KAAA,IAAAE,IAAA,KAAAA,IAAA;MACA;IACA;IAEAkB,gBAAA;MACA,KAAAZ,WAAA,GAAA2B,MAAA,CAAAsD,UAAA;MACA,MAAAC,SAAA,QAAAnF,QAAA;MACA,KAAAA,QAAA,QAAAC,WAAA;;MAEA;MACA,IAAAkF,SAAA,UAAAnF,QAAA;QACA,KAAAE,cAAA;QACA,KAAAC,UAAA;MACA;MACA;MAAA,KACA,KAAAgF,SAAA,SAAAnF,QAAA;QACA,KAAAE,cAAA;QACA,KAAAC,UAAA;MACA;IACA;IAEA0B,aAAA;MACAuD,YAAA,MAAAC,WAAA;MACA,KAAAA,WAAA,GAAAnD,UAAA;QACA,KAAArB,eAAA;MACA;IACA;IAEAyE,cAAA;MACA,KAAApF,cAAA,SAAAA,cAAA;MACA,SAAAA,cAAA,SAAAC,UAAA;QACA,KAAAA,UAAA;MACA;IACA;IAEAoF,UAAA;MACA,KAAApF,UAAA,SAAAA,UAAA;MACA,SAAAA,UAAA,SAAAD,cAAA;QACA,KAAAA,cAAA;MACA;IACA;IAEAsF,aAAA;MACA,KAAAtF,cAAA;IACA;IAEAuF,SAAA;MACA,KAAAtF,UAAA;IACA;IAEAuF,eAAA;MACA,KAAAxF,cAAA;MACA,KAAAC,UAAA;IACA;IAEAwF,gBAAA;MACA,SAAA3F,QAAA;QACA,KAAAE,cAAA;MACA;IACA;IAEAwB,oBAAA;MACA,MAAAD,OAAA,QAAAH,KAAA,CAAAG,OAAA;MACA,IAAAA,OAAA;QACA,KAAArB,qBAAA,GAAAqB,OAAA,CAAAoD,SAAA;QACA5D,YAAA,CAAA2E,OAAA,8BAAAnE,OAAA,CAAAoD,SAAA,CAAAgB,QAAA;MACA;IACA;IAEAlE,6BAAA;MACA,MAAAF,OAAA,QAAAH,KAAA,CAAAG,OAAA;MACA,IAAAA,OAAA,SAAArB,qBAAA;QACA0F,qBAAA;UACArE,OAAA,CAAAoD,SAAA,QAAAzE,qBAAA;QACA;MACA;IACA;IAEA+B,uBAAA;MACA,MAAAV,OAAA,QAAAH,KAAA,CAAAG,OAAA;MACA,KAAAA,OAAA;;MAEA;MACA,MAAAsE,UAAA,GAAAtE,OAAA,CAAAgC,aAAA;MACA,KAAAsC,UAAA;;MAEA;MACA,MAAAC,OAAA,GAAAD,UAAA,CAAAnB,SAAA;MACA,MAAAqB,UAAA,GAAAF,UAAA,CAAAG,YAAA;MACA,MAAAC,aAAA,GAAA1E,OAAA,CAAA2E,YAAA;MACA,MAAAC,gBAAA,GAAA5E,OAAA,CAAAoD,SAAA;;MAEA;MACA,MAAAyB,SAAA,GAAAN,OAAA,IAAAK,gBAAA,IACAL,OAAA,GAAAC,UAAA,IAAAI,gBAAA,GAAAF,aAAA;MAEA,KAAAG,SAAA;QACA;QACA,MAAAC,eAAA,GAAAP,OAAA,GAAAG,aAAA,OAAAF,UAAA;;QAEA;QACAxE,OAAA,CAAA+E,QAAA;UACAC,GAAA,EAAAC,IAAA,CAAAC,GAAA,IAAAJ,eAAA;UACAK,QAAA;QACA;;QAEA;QACA1E,UAAA;UACA,KAAA9B,qBAAA,GAAAqB,OAAA,CAAAoD,SAAA;UACA5D,YAAA,CAAA2E,OAAA,8BAAAnE,OAAA,CAAAoD,SAAA,CAAAgB,QAAA;QACA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}