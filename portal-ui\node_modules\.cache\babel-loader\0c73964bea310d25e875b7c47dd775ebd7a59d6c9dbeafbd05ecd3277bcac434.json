{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport './assets/css/style.css';\nimport Toast from 'vue-toastification';\nimport 'vue-toastification/dist/index.css';\nimport { getRequest } from \"@/api/api\";\nVue.prototype.getRequest = getRequest;\nVue.use(Toast, {\n  position: 'top-center',\n  timeout: 3000,\n  closeOnClick: true\n});\nVue.config.productionTip = false;\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "Toast", "getRequest", "prototype", "use", "position", "timeout", "closeOnClick", "config", "productionTip", "render", "h", "$mount"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/main.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport './assets/css/style.css'\r\nimport Toast from 'vue-toastification';\r\nimport 'vue-toastification/dist/index.css';\r\nimport {getRequest} from \"@/api/api\";\r\n\r\nVue.prototype.getRequest = getRequest;\r\n\r\nVue.use(Toast, {\r\n  position: 'top-center',\r\n  timeout: 3000,\r\n  closeOnClick: true\r\n});\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n  router,\r\n  render: h => h(App)\r\n}).$mount('#app')\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAO,wBAAwB;AAC/B,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAO,mCAAmC;AAC1C,SAAQC,UAAU,QAAO,WAAW;AAEpCJ,GAAG,CAACK,SAAS,CAACD,UAAU,GAAGA,UAAU;AAErCJ,GAAG,CAACM,GAAG,CAACH,KAAK,EAAE;EACbI,QAAQ,EAAE,YAAY;EACtBC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE;AAChB,CAAC,CAAC;AACFT,GAAG,CAACU,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIX,GAAG,CAAC;EACNE,MAAM;EACNU,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACZ,GAAG;AACpB,CAAC,CAAC,CAACa,MAAM,CAAC,MAAM,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}