{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('main', {\n    staticClass: \"page-wrapper\"\n  }, [_c('Header'), _vm._t(\"default\"), _c('Mider'), _c('Footer')], 2);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_t", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/components/common/Layout.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('main',{staticClass:\"page-wrapper\"},[_c('Header'),_vm._t(\"default\"),_c('Mider'),_c('Footer')],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,QAAQ,CAAC,EAACD,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,EAACH,EAAE,CAAC,OAAO,CAAC,EAACA,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC;AACtK,CAAC;AACD,IAAII,eAAe,GAAG,EAAE;AAExB,SAASN,MAAM,EAAEM,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}