{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', [!_vm.isMobile ? _c('div', {\n    staticClass: \"desktop-layout\"\n  }, [_c('div', {\n    staticClass: \"banner-section\"\n  }, [_c('div', {\n    staticClass: \"banner-container\"\n  }, [_c('div', {\n    staticClass: \"big-box\"\n  }, [_c('div', {\n    staticClass: \"img-box\"\n  }, [_c('div', {\n    staticClass: \"show-box\",\n    style: {\n      transform: 'translateX(' + _vm.translate + ')',\n      transition: _vm.tsion ? 'all 0.5s' : 'none'\n    }\n  }, _vm._l(_vm.bannerImages, function (item, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"slide-item\"\n    }, [_c('img', {\n      attrs: {\n        \"src\": item.img,\n        \"alt\": \"\"\n      }\n    }), _c('div', {\n      staticClass: \"banner-content\",\n      class: 'pos-' + item.content.position\n    }, [_c('h2', {\n      staticClass: \"banner-title\",\n      domProps: {\n        \"innerHTML\": _vm._s(item.content.title)\n      }\n    }), _c('p', {\n      staticClass: \"banner-text\"\n    }, [_vm._v(_vm._s(item.content.text))]), _c('div', {\n      staticClass: \"banner-actions\"\n    }, [!_vm.isLogin && item.content.secondaryLink ? _c('a', {\n      staticClass: \"banner-button secondary-btn\",\n      attrs: {\n        \"href\": \"#\"\n      },\n      on: {\n        \"click\": function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.secondaryLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.secondaryBtnText) + \" \")]) : _vm._e(), !_vm.isLogin && item.content.primaryLink ? _c('a', {\n      staticClass: \"banner-button primary-btn\",\n      attrs: {\n        \"href\": \"#\"\n      },\n      on: {\n        \"click\": function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.primaryLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.primaryBtnText) + \" \")]) : _vm._e(), item.content.thirdLink ? _c('a', {\n      staticClass: \"banner-button secondary-btn\",\n      attrs: {\n        \"href\": \"#\"\n      },\n      on: {\n        \"click\": function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.thirdLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.thirdBtnText) + \" \")]) : _vm._e()])])]);\n  }), 0)]), _c('div', {\n    staticClass: \"arrowhead-box\"\n  }, [_c('span', {\n    staticClass: \"nav-arrow left\",\n    on: {\n      \"click\": _vm.last\n    }\n  }, [_c('img', {\n    staticClass: \"arrow-icon rotated\",\n    attrs: {\n      \"src\": require(\"../../assets/images/index/right-arrow.png\"),\n      \"alt\": \"\"\n    }\n  })]), _c('span', {\n    staticClass: \"nav-arrow right\",\n    on: {\n      \"click\": _vm.next\n    }\n  }, [_c('img', {\n    staticClass: \"arrow-icon\",\n    attrs: {\n      \"src\": require(\"../../assets/images/index/right-arrow.png\"),\n      \"alt\": \"\"\n    }\n  })])]), _c('div', {\n    ref: \"swiperPagination\",\n    staticClass: \"swiper-pagination\"\n  }, _vm._l(_vm.bannerImages, function (item, index) {\n    return _c('span', {\n      key: index,\n      class: {\n        active: _vm.translateX === index\n      }\n    });\n  }), 0)])])]), _c('section', {\n    staticClass: \"section gpu-section\"\n  }, [_c('div', {\n    staticClass: \"container\"\n  }, [_vm._m(0), _c('div', {\n    staticClass: \"gpu-card-grid\"\n  }, _vm._l(_vm.gpus, function (gpu, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"gpu-card\",\n      class: {\n        'recommended': gpu.recommended\n      },\n      on: {\n        \"click\": function ($event) {\n          return _vm.navigateTo('/product');\n        }\n      }\n    }, [_c('div', {\n      staticClass: \"gpu-card-header\"\n    }, [_c('h3', {\n      staticClass: \"gpu-name\"\n    }, [_vm._v(_vm._s(gpu.name))]), gpu.recommended ? _c('span', {\n      staticClass: \"recommendation-tag\"\n    }, [_vm._v(\"推荐\")]) : _vm._e(), gpu.isNew ? _c('span', {\n      staticClass: \"new-tag\"\n    }, [_vm._v(\"NEW\")]) : _vm._e()]), _c('div', {\n      staticClass: \"gpu-specs-pricing\"\n    }, [_c('div', {\n      staticClass: \"specs-section\"\n    }, [_c('div', {\n      staticClass: \"spec-item\"\n    }, [_c('span', {\n      staticClass: \"spec-label\"\n    }, [_vm._v(\"单精度:\")]), _c('span', {\n      staticClass: \"spec-value\"\n    }, [_vm._v(_vm._s(gpu.singlePrecision) + \" TFLOPS\")])]), _c('div', {\n      staticClass: \"spec-item\"\n    }, [_c('span', {\n      staticClass: \"spec-label\"\n    }, [_vm._v(\"半精度:\")]), _c('span', {\n      staticClass: \"spec-value\"\n    }, [_vm._v(_vm._s(gpu.halfPrecision) + \" Tensor TFL\")])])]), _c('div', {\n      staticClass: \"price-section\"\n    }, [_c('div', {\n      staticClass: \"gpu-pricing\"\n    }, [gpu.originalPrice ? _c('span', {\n      staticClass: \"original-price\"\n    }, [_vm._v(\"¥\" + _vm._s(gpu.originalPrice) + \"/时\")]) : _vm._e(), _c('span', {\n      staticClass: \"current-price\"\n    }, [_vm._v(\"¥\"), _c('span', {\n      staticClass: \"price-value\"\n    }, [_vm._v(_vm._s(gpu.price))]), _vm._v(\"/时\")])])])])]);\n  }), 0)])]), _c('GpuComparison'), _c('section', {\n    staticClass: \"section services-section\"\n  }, [_c('div', {\n    staticClass: \"container\"\n  }, [_vm._m(1), _c('div', {\n    staticClass: \"services-grid\"\n  }, _vm._l(_vm.serviceList, function (service, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"service-item\"\n    }, [_c('div', {\n      staticClass: \"service-card\"\n    }, [_c('i', {\n      staticClass: \"service-icon\",\n      class: service.icon\n    }), _c('h3', {\n      staticClass: \"service-title\"\n    }, [_vm._v(_vm._s(service.title))]), _c('div', {\n      staticClass: \"service-text\"\n    }, [_c('p', [_vm._v(_vm._s(service.desc))])])])]);\n  }), 0)])]), _c('section', {\n    staticClass: \"appsec-section\"\n  }, [_vm._m(2), _c('div', {\n    staticClass: \"appsec-container\"\n  }, [_c('div', {\n    staticClass: \"appsec-grid\"\n  }, [_c('div', {\n    staticClass: \"appsec-item appsec-wide\"\n  }, [_c('div', {\n    staticClass: \"appsec-card\",\n    on: {\n      \"mouseover\": function ($event) {\n        _vm.firstRowWide.hover = true;\n      },\n      \"mouseleave\": function ($event) {\n        _vm.firstRowWide.hover = false;\n      }\n    }\n  }, [_c('div', {\n    staticClass: \"appsec-image\",\n    class: {\n      'appsec-hover': _vm.firstRowWide.hover\n    }\n  }, [_c('img', {\n    attrs: {\n      \"src\": _vm.firstRowWide.image,\n      \"alt\": _vm.firstRowWide.title\n    }\n  })]), _c('div', {\n    staticClass: \"appsec-cardtitle\",\n    class: {\n      'appsec-hover': _vm.firstRowWide.hover\n    }\n  }, [_vm._v(_vm._s(_vm.firstRowWide.title))])])]), _vm._l(_vm.firstRowTallApps, function (app, index) {\n    return _c('div', {\n      key: 'tall-' + index,\n      staticClass: \"appsec-item appsec-tall\"\n    }, [_c('div', {\n      staticClass: \"appsec-card\",\n      on: {\n        \"mouseover\": function ($event) {\n          app.hover = true;\n        },\n        \"mouseleave\": function ($event) {\n          app.hover = false;\n        }\n      }\n    }, [_c('div', {\n      staticClass: \"appsec-image\",\n      class: {\n        'appsec-hover': app.hover\n      }\n    }, [_c('img', {\n      attrs: {\n        \"src\": app.image,\n        \"alt\": app.title\n      }\n    })]), _c('div', {\n      staticClass: \"appsec-cardtitle\",\n      class: {\n        'appsec-hover': app.hover\n      }\n    }, [_vm._v(_vm._s(app.title))])])]);\n  }), _vm._l(_vm.secondRowApps, function (app, index) {\n    return _c('div', {\n      key: 'small-' + index,\n      staticClass: \"appsec-item appsec-small\"\n    }, [_c('div', {\n      staticClass: \"appsec-card\",\n      on: {\n        \"mouseover\": function ($event) {\n          app.hover = true;\n        },\n        \"mouseleave\": function ($event) {\n          app.hover = false;\n        }\n      }\n    }, [_c('div', {\n      staticClass: \"appsec-image\",\n      class: {\n        'appsec-hover': app.hover\n      }\n    }, [_c('img', {\n      attrs: {\n        \"src\": app.image,\n        \"alt\": app.title\n      }\n    })]), _c('div', {\n      staticClass: \"appsec-cardtitle\",\n      class: {\n        'appsec-hover': app.hover\n      }\n    }, [_vm._v(_vm._s(app.title))])])]);\n  }), _vm._l(_vm.thirdRowSmallApps, function (app, index) {\n    return _c('div', {\n      key: 'third-small-' + index,\n      staticClass: \"appsec-item appsec-small\"\n    }, [_c('div', {\n      staticClass: \"appsec-card\",\n      on: {\n        \"mouseover\": function ($event) {\n          app.hover = true;\n        },\n        \"mouseleave\": function ($event) {\n          app.hover = false;\n        }\n      }\n    }, [_c('div', {\n      staticClass: \"appsec-image\",\n      class: {\n        'appsec-hover': app.hover\n      }\n    }, [_c('img', {\n      attrs: {\n        \"src\": app.image,\n        \"alt\": app.title\n      }\n    })]), _c('div', {\n      staticClass: \"appsec-cardtitle\",\n      class: {\n        'appsec-hover': app.hover\n      }\n    }, [_vm._v(_vm._s(app.title))])])]);\n  }), _c('div', {\n    staticClass: \"appsec-item appsec-wide\"\n  }, [_c('div', {\n    staticClass: \"appsec-card\",\n    on: {\n      \"mouseover\": function ($event) {\n        _vm.thirdRowWide.hover = true;\n      },\n      \"mouseleave\": function ($event) {\n        _vm.thirdRowWide.hover = false;\n      }\n    }\n  }, [_c('div', {\n    staticClass: \"appsec-image\",\n    class: {\n      'appsec-hover': _vm.thirdRowWide.hover\n    }\n  }, [_c('img', {\n    attrs: {\n      \"src\": _vm.thirdRowWide.image,\n      \"alt\": _vm.thirdRowWide.title\n    }\n  })]), _c('div', {\n    staticClass: \"appsec-cardtitle\",\n    class: {\n      'appsec-hover': _vm.thirdRowWide.hover\n    }\n  }, [_vm._v(_vm._s(_vm.thirdRowWide.title))])])])], 2), _c('chat-ai')], 1)]), _c('div', {\n    staticClass: \"recommendation-tag1\"\n  }, [_c('div', {\n    staticClass: \"card1\"\n  }, [_c('h1', {\n    staticClass: \"banner-text1\"\n  }, [_vm._v(\"为AI+千行百业，提供高性能算力服务\")]), _c('button', {\n    staticClass: \"consult-button1\",\n    on: {\n      \"click\": _vm.openContactModal\n    }\n  }, [_vm._v(\"立即咨询\")])])]), _c('transition', {\n    attrs: {\n      \"name\": \"fade\"\n    }\n  }, [_vm.showContactModal ? _c('div', {\n    staticClass: \"contact-modal-overlay\",\n    on: {\n      \"click\": function ($event) {\n        if ($event.target !== $event.currentTarget) return null;\n        return _vm.closeContactModal.apply(null, arguments);\n      }\n    }\n  }, [_c('div', {\n    staticClass: \"contact-modal\"\n  }, [_c('button', {\n    staticClass: \"close-modal\",\n    on: {\n      \"click\": _vm.closeContactModal\n    }\n  }, [_vm._v(\" × \")]), _c('div', {\n    staticClass: \"contact-content\"\n  }, [_c('div', {\n    staticClass: \"contact-item\"\n  }, [_c('i', {\n    staticClass: \"am-icon-user\"\n  }), _c('span', [_vm._v(_vm._s(_vm.contactInfo.name))])]), _c('div', {\n    staticClass: \"contact-item\"\n  }, [_c('i', {\n    staticClass: \"am-icon-phone\"\n  }), _c('span', [_vm._v(_vm._s(_vm.contactInfo.phone))])])]), _c('div', {\n    staticClass: \"contact-note\"\n  }, [_c('p', [_vm._v(\"欢迎随时来电咨询\")])])])]) : _vm._e()])], 1) : _c('div', {\n    staticClass: \"mobile-layout\"\n  }, [_c('div', {\n    staticClass: \"mobile-banner\",\n    on: {\n      \"touchstart\": _vm.handleTouchStart,\n      \"touchmove\": _vm.handleTouchMove,\n      \"touchend\": _vm.handleTouchEnd\n    }\n  }, [_c('div', {\n    staticClass: \"mobile-banner-slider\",\n    style: {\n      transform: `translateX(${-_vm.mobileCurrentSlide * 100}%)`\n    }\n  }, _vm._l(_vm.bannerImages, function (item, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"mobile-slide\"\n    }, [_c('div', {\n      staticClass: \"mobile-slide-inner\"\n    }, [_c('img', {\n      staticClass: \"mobile-slide-img\",\n      attrs: {\n        \"src\": item.img,\n        \"alt\": \"\"\n      }\n    }), _c('div', {\n      staticClass: \"mobile-banner-content\",\n      class: 'pos-' + item.content.position\n    }, [_c('h2', {\n      staticClass: \"mobile-banner-title\",\n      domProps: {\n        \"innerHTML\": _vm._s(item.content.title)\n      }\n    }), _c('p', {\n      staticClass: \"mobile-banner-text\"\n    }, [_vm._v(_vm._s(item.content.text))]), _c('div', {\n      staticClass: \"mobile-banner-actions\"\n    }, [!_vm.isLogin && item.content.secondaryLink ? _c('a', {\n      staticClass: \"mobile-banner-button secondary-btn\",\n      attrs: {\n        \"href\": \"#\"\n      },\n      on: {\n        \"click\": function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.secondaryLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.secondaryBtnText) + \" \")]) : _vm._e(), !_vm.isLogin && item.content.primaryLink ? _c('a', {\n      staticClass: \"mobile-banner-button primary-btn\",\n      attrs: {\n        \"href\": \"#\"\n      },\n      on: {\n        \"click\": function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.primaryLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.primaryBtnText) + \" \")]) : _vm._e(), item.content.thirdLink ? _c('a', {\n      staticClass: \"banner-button secondary-btn\",\n      attrs: {\n        \"href\": \"#\"\n      },\n      on: {\n        \"click\": function ($event) {\n          $event.preventDefault();\n          return _vm.navigateTo(item.content.thirdLink);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.content.thirdBtnText) + \" \")]) : _vm._e()])])])]);\n  }), 0), _c('div', {\n    staticClass: \"mobile-banner-pagination\"\n  }, _vm._l(_vm.bannerImages, function (item, index) {\n    return _c('span', {\n      key: index,\n      class: {\n        active: _vm.mobileCurrentSlide === index\n      },\n      on: {\n        \"click\": function ($event) {\n          return _vm.goToSlide(index);\n        }\n      }\n    });\n  }), 0)]), _c('section', {\n    staticClass: \"mobile-section mobile-gpu-section\"\n  }, [_vm._m(3), _c('div', {\n    staticClass: \"mobile-gpu-list\"\n  }, _vm._l(_vm.gpus, function (gpu, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"mobile-gpu-card\",\n      class: {\n        'recommended': gpu.recommended\n      },\n      on: {\n        \"click\": function ($event) {\n          return _vm.navigateTo('/product');\n        }\n      }\n    }, [_c('div', {\n      staticClass: \"mobile-gpu-header\"\n    }, [_c('h3', {\n      staticClass: \"mobile-gpu-name\"\n    }, [_vm._v(_vm._s(gpu.name))]), _c('div', {\n      staticClass: \"mobile-gpu-tags\"\n    }, [gpu.recommended ? _c('span', {\n      staticClass: \"mobile-recommend-tag\"\n    }, [_vm._v(\"推荐\")]) : _vm._e(), gpu.isNew ? _c('span', {\n      staticClass: \"mobile-new-tag\"\n    }, [_vm._v(\"NEW\")]) : _vm._e()])]), _c('div', {\n      staticClass: \"mobile-gpu-specs\"\n    }, [_c('div', {\n      staticClass: \"mobile-spec-item\"\n    }, [_c('span', {\n      staticClass: \"mobile-spec-label\"\n    }, [_vm._v(\"单精度:\")]), _c('span', {\n      staticClass: \"mobile-spec-value\"\n    }, [_vm._v(_vm._s(gpu.singlePrecision) + \" TFLOPS\")])]), _c('div', {\n      staticClass: \"mobile-spec-item\"\n    }, [_c('span', {\n      staticClass: \"mobile-spec-label\"\n    }, [_vm._v(\"半精度:\")]), _c('span', {\n      staticClass: \"mobile-spec-value\"\n    }, [_vm._v(_vm._s(gpu.halfPrecision) + \" Tensor TFL\")])])]), _c('div', {\n      staticClass: \"mobile-gpu-price\"\n    }, [gpu.originalPrice ? _c('span', {\n      staticClass: \"mobile-original-price\"\n    }, [_vm._v(\"¥\" + _vm._s(gpu.originalPrice) + \"/时\")]) : _vm._e(), _c('span', {\n      staticClass: \"mobile-current-price\"\n    }, [_vm._v(\"¥\" + _vm._s(gpu.price) + \"/时\")])])]);\n  }), 0)]), _c('section', {\n    staticClass: \"mobile-section mobile-comparison-section\"\n  }, [_vm._m(4), _c('div', {\n    staticClass: \"mobile-comparison-container\"\n  }, [_c('div', {\n    staticClass: \"mobile-comparison-scroll\"\n  }, [_c('table', {\n    staticClass: \"mobile-comparison-table\"\n  }, [_c('thead', [_c('tr', [_c('th', [_vm._v(\"GPU型号\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('th', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.name))]);\n  })], 2)]), _c('tbody', [_c('tr', [_c('td', [_vm._v(\"架构\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.architecture))]);\n  })], 2), _c('tr', [_c('td', [_vm._v(\"FP16性能\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.fp16Performance))]);\n  })], 2), _c('tr', [_c('td', [_vm._v(\"FP32性能\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.fp32Performance))]);\n  })], 2), _c('tr', [_c('td', [_vm._v(\"显存\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.memory))]);\n  })], 2), _c('tr', [_c('td', [_vm._v(\"显存类型\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.memoryType))]);\n  })], 2), _c('tr', [_c('td', [_vm._v(\"带宽\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.bandwidth))]);\n  })], 2)])])])])]), _c('section', {\n    staticClass: \"mobile-section mobile-services-section\"\n  }, [_vm._m(5), _c('div', {\n    staticClass: \"mobile-services-list\"\n  }, _vm._l(_vm.serviceList, function (service, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"mobile-service-card\"\n    }, [_c('div', {\n      staticClass: \"mobile-service-icon\"\n    }, [_c('i', {\n      class: service.icon\n    })]), _c('h3', {\n      staticClass: \"mobile-service-title\"\n    }, [_vm._v(_vm._s(service.title))]), _c('p', {\n      staticClass: \"mobile-service-desc\"\n    }, [_vm._v(_vm._s(service.desc))])]);\n  }), 0)]), _c('section', {\n    staticClass: \"mobile-section mobile-applications-section\"\n  }, [_vm._m(6), _c('div', {\n    staticClass: \"mobile-applications-grid\"\n  }, _vm._l(_vm.mobileApplications, function (app, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"mobile-app-item\"\n    }, [_c('div', {\n      staticClass: \"mobile-app-image\"\n    }, [_c('img', {\n      attrs: {\n        \"src\": app.image,\n        \"alt\": app.title\n      }\n    })]), _c('h3', {\n      staticClass: \"mobile-app-title\"\n    }, [_vm._v(_vm._s(app.title))])]);\n  }), 0)]), _c('div', {\n    staticClass: \"mobile-consult-section\"\n  }, [_c('h3', {\n    staticClass: \"mobile-consult-title\"\n  }, [_vm._v(\"为AI+千行百业，提供高性能算力服务\")]), _c('button', {\n    staticClass: \"mobile-consult-button\",\n    on: {\n      \"click\": _vm.openContactModal\n    }\n  }, [_vm._v(\"立即咨询\")])]), _c('transition', {\n    attrs: {\n      \"name\": \"mobile-fade\"\n    }\n  }, [_vm.showContactModal ? _c('div', {\n    staticClass: \"mobile-contact-overlay\",\n    on: {\n      \"click\": function ($event) {\n        if ($event.target !== $event.currentTarget) return null;\n        return _vm.closeContactModal.apply(null, arguments);\n      }\n    }\n  }, [_c('div', {\n    staticClass: \"mobile-contact-modal\"\n  }, [_c('button', {\n    staticClass: \"mobile-close-modal\",\n    on: {\n      \"click\": _vm.closeContactModal\n    }\n  }, [_vm._v(\" × \")]), _c('div', {\n    staticClass: \"mobile-contact-content\"\n  }, [_c('div', {\n    staticClass: \"mobile-contact-item\"\n  }, [_c('i', {\n    staticClass: \"am-icon-user\"\n  }), _c('span', [_vm._v(_vm._s(_vm.contactInfo.name))])]), _c('div', {\n    staticClass: \"mobile-contact-item\"\n  }, [_c('i', {\n    staticClass: \"am-icon-phone\"\n  }), _c('span', [_vm._v(_vm._s(_vm.contactInfo.phone))])])]), _c('div', {\n    staticClass: \"mobile-contact-note\"\n  }, [_c('p', [_vm._v(\"欢迎随时来电咨询\")])])])]) : _vm._e()])], 1), !_vm.isMobile ? _c('Mider') : _vm._e(), _c('Footer'), _c('chatAi')], 1);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"section-header\"\n  }, [_c('h2', {\n    staticClass: \"section-title\"\n  }, [_vm._v(\"为您推荐\")]), _c('p', {\n    staticClass: \"section-description\"\n  }, [_vm._v(\" 专注于提供高性能、稳定可靠的 GPU 算力服务，兼具灵活配置与优质性价比。 \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"section-header\"\n  }, [_c('h2', {\n    staticClass: \"section-title\"\n  }, [_vm._v(\"核心优势\")]), _c('p', {\n    staticClass: \"section-description\"\n  }, [_vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴。 \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"section-header\"\n  }, [_c('h2', {\n    staticClass: \"section-title\"\n  }, [_vm._v(\"行业应用\")]), _c('p', {\n    staticClass: \"section-description\"\n  }, [_vm._v(\" Applications \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"mobile-section-header\"\n  }, [_c('h2', {\n    staticClass: \"mobile-section-title\"\n  }, [_vm._v(\"为您推荐\")]), _c('p', {\n    staticClass: \"mobile-section-description\"\n  }, [_vm._v(\" 专注于提供高性能、稳定可靠的 GPU 算力服务 \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"mobile-section-header\"\n  }, [_c('h2', {\n    staticClass: \"mobile-section-title\"\n  }, [_vm._v(\"GPU性能对比\")]), _c('p', {\n    staticClass: \"mobile-section-description\"\n  }, [_vm._v(\" 专业GPU性能详细对比 \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"mobile-section-header\"\n  }, [_c('h2', {\n    staticClass: \"mobile-section-title\"\n  }, [_vm._v(\"核心优势\")]), _c('p', {\n    staticClass: \"mobile-section-description\"\n  }, [_vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴 \")])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"mobile-section-header\"\n  }, [_c('h2', {\n    staticClass: \"mobile-section-title\"\n  }, [_vm._v(\"行业应用\")]), _c('p', {\n    staticClass: \"mobile-section-description\"\n  }, [_vm._v(\" Applications \")])]);\n}];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "isMobile", "staticClass", "style", "transform", "translate", "transition", "tsion", "_l", "bannerImages", "item", "index", "key", "attrs", "img", "class", "content", "position", "domProps", "_s", "title", "_v", "text", "is<PERSON>ogin", "secondaryLink", "on", "click", "$event", "preventDefault", "navigateTo", "secondaryBtnText", "_e", "primaryLink", "primaryBtnText", "thirdLink", "thirdBtnText", "last", "require", "next", "ref", "active", "translateX", "_m", "gpus", "gpu", "recommended", "name", "isNew", "singlePrecision", "halfPrecision", "originalPrice", "price", "serviceList", "service", "icon", "desc", "mouseover", "firstRowWide", "hover", "mouseleave", "image", "firstRowTallApps", "app", "secondRowApps", "thirdRowSmallApps", "thirdRowWide", "openContactModal", "showContactModal", "target", "currentTarget", "closeContactModal", "apply", "arguments", "contactInfo", "phone", "handleTouchStart", "handleTouchMove", "handleTouchEnd", "mobileCurrentSlide", "goToSlide", "comparisonGpus", "architecture", "fp16Performance", "fp32Performance", "memory", "memoryType", "bandwidth", "mobileApplications", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/views/Index/IndexView.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(!_vm.isMobile)?_c('div',{staticClass:\"desktop-layout\"},[_c('div',{staticClass:\"banner-section\"},[_c('div',{staticClass:\"banner-container\"},[_c('div',{staticClass:\"big-box\"},[_c('div',{staticClass:\"img-box\"},[_c('div',{staticClass:\"show-box\",style:({\n                  transform: 'translateX(' + _vm.translate + ')',\n                  transition: _vm.tsion ? 'all 0.5s' : 'none',\n                })},_vm._l((_vm.bannerImages),function(item,index){return _c('div',{key:index,staticClass:\"slide-item\"},[_c('img',{attrs:{\"src\":item.img,\"alt\":\"\"}}),_c('div',{staticClass:\"banner-content\",class:'pos-' + item.content.position},[_c('h2',{staticClass:\"banner-title\",domProps:{\"innerHTML\":_vm._s(item.content.title)}}),_c('p',{staticClass:\"banner-text\"},[_vm._v(_vm._s(item.content.text))]),_c('div',{staticClass:\"banner-actions\"},[(!_vm.isLogin && item.content.secondaryLink)?_c('a',{staticClass:\"banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.secondaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.secondaryBtnText)+\" \")]):_vm._e(),(!_vm.isLogin && item.content.primaryLink)?_c('a',{staticClass:\"banner-button primary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.primaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.primaryBtnText)+\" \")]):_vm._e(),(item.content.thirdLink)?_c('a',{staticClass:\"banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.thirdLink)}}},[_vm._v(\" \"+_vm._s(item.content.thirdBtnText)+\" \")]):_vm._e()])])])}),0)]),_c('div',{staticClass:\"arrowhead-box\"},[_c('span',{staticClass:\"nav-arrow left\",on:{\"click\":_vm.last}},[_c('img',{staticClass:\"arrow-icon rotated\",attrs:{\"src\":require(\"../../assets/images/index/right-arrow.png\"),\"alt\":\"\"}})]),_c('span',{staticClass:\"nav-arrow right\",on:{\"click\":_vm.next}},[_c('img',{staticClass:\"arrow-icon\",attrs:{\"src\":require(\"../../assets/images/index/right-arrow.png\"),\"alt\":\"\"}})])]),_c('div',{ref:\"swiperPagination\",staticClass:\"swiper-pagination\"},_vm._l((_vm.bannerImages),function(item,index){return _c('span',{key:index,class:{ active: _vm.translateX === index }})}),0)])])]),_c('section',{staticClass:\"section gpu-section\"},[_c('div',{staticClass:\"container\"},[_vm._m(0),_c('div',{staticClass:\"gpu-card-grid\"},_vm._l((_vm.gpus),function(gpu,index){return _c('div',{key:index,staticClass:\"gpu-card\",class:{ 'recommended': gpu.recommended },on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('div',{staticClass:\"gpu-card-header\"},[_c('h3',{staticClass:\"gpu-name\"},[_vm._v(_vm._s(gpu.name))]),(gpu.recommended)?_c('span',{staticClass:\"recommendation-tag\"},[_vm._v(\"推荐\")]):_vm._e(),(gpu.isNew)?_c('span',{staticClass:\"new-tag\"},[_vm._v(\"NEW\")]):_vm._e()]),_c('div',{staticClass:\"gpu-specs-pricing\"},[_c('div',{staticClass:\"specs-section\"},[_c('div',{staticClass:\"spec-item\"},[_c('span',{staticClass:\"spec-label\"},[_vm._v(\"单精度:\")]),_c('span',{staticClass:\"spec-value\"},[_vm._v(_vm._s(gpu.singlePrecision)+\" TFLOPS\")])]),_c('div',{staticClass:\"spec-item\"},[_c('span',{staticClass:\"spec-label\"},[_vm._v(\"半精度:\")]),_c('span',{staticClass:\"spec-value\"},[_vm._v(_vm._s(gpu.halfPrecision)+\" Tensor TFL\")])])]),_c('div',{staticClass:\"price-section\"},[_c('div',{staticClass:\"gpu-pricing\"},[(gpu.originalPrice)?_c('span',{staticClass:\"original-price\"},[_vm._v(\"¥\"+_vm._s(gpu.originalPrice)+\"/时\")]):_vm._e(),_c('span',{staticClass:\"current-price\"},[_vm._v(\"¥\"),_c('span',{staticClass:\"price-value\"},[_vm._v(_vm._s(gpu.price))]),_vm._v(\"/时\")])])])])])}),0)])]),_c('GpuComparison'),_c('section',{staticClass:\"section services-section\"},[_c('div',{staticClass:\"container\"},[_vm._m(1),_c('div',{staticClass:\"services-grid\"},_vm._l((_vm.serviceList),function(service,index){return _c('div',{key:index,staticClass:\"service-item\"},[_c('div',{staticClass:\"service-card\"},[_c('i',{staticClass:\"service-icon\",class:service.icon}),_c('h3',{staticClass:\"service-title\"},[_vm._v(_vm._s(service.title))]),_c('div',{staticClass:\"service-text\"},[_c('p',[_vm._v(_vm._s(service.desc))])])])])}),0)])]),_c('section',{staticClass:\"appsec-section\"},[_vm._m(2),_c('div',{staticClass:\"appsec-container\"},[_c('div',{staticClass:\"appsec-grid\"},[_c('div',{staticClass:\"appsec-item appsec-wide\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){_vm.firstRowWide.hover = true},\"mouseleave\":function($event){_vm.firstRowWide.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': _vm.firstRowWide.hover }},[_c('img',{attrs:{\"src\":_vm.firstRowWide.image,\"alt\":_vm.firstRowWide.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': _vm.firstRowWide.hover }},[_vm._v(_vm._s(_vm.firstRowWide.title))])])]),_vm._l((_vm.firstRowTallApps),function(app,index){return _c('div',{key:'tall-'+index,staticClass:\"appsec-item appsec-tall\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){app.hover = true},\"mouseleave\":function($event){app.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': app.hover }},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': app.hover }},[_vm._v(_vm._s(app.title))])])])}),_vm._l((_vm.secondRowApps),function(app,index){return _c('div',{key:'small-'+index,staticClass:\"appsec-item appsec-small\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){app.hover = true},\"mouseleave\":function($event){app.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': app.hover }},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': app.hover }},[_vm._v(_vm._s(app.title))])])])}),_vm._l((_vm.thirdRowSmallApps),function(app,index){return _c('div',{key:'third-small-'+index,staticClass:\"appsec-item appsec-small\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){app.hover = true},\"mouseleave\":function($event){app.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': app.hover }},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': app.hover }},[_vm._v(_vm._s(app.title))])])])}),_c('div',{staticClass:\"appsec-item appsec-wide\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){_vm.thirdRowWide.hover = true},\"mouseleave\":function($event){_vm.thirdRowWide.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': _vm.thirdRowWide.hover }},[_c('img',{attrs:{\"src\":_vm.thirdRowWide.image,\"alt\":_vm.thirdRowWide.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': _vm.thirdRowWide.hover }},[_vm._v(_vm._s(_vm.thirdRowWide.title))])])])],2),_c('chat-ai')],1)]),_c('div',{staticClass:\"recommendation-tag1\"},[_c('div',{staticClass:\"card1\"},[_c('h1',{staticClass:\"banner-text1\"},[_vm._v(\"为AI+千行百业，提供高性能算力服务\")]),_c('button',{staticClass:\"consult-button1\",on:{\"click\":_vm.openContactModal}},[_vm._v(\"立即咨询\")])])]),_c('transition',{attrs:{\"name\":\"fade\"}},[(_vm.showContactModal)?_c('div',{staticClass:\"contact-modal-overlay\",on:{\"click\":function($event){if($event.target !== $event.currentTarget)return null;return _vm.closeContactModal.apply(null, arguments)}}},[_c('div',{staticClass:\"contact-modal\"},[_c('button',{staticClass:\"close-modal\",on:{\"click\":_vm.closeContactModal}},[_vm._v(\" × \")]),_c('div',{staticClass:\"contact-content\"},[_c('div',{staticClass:\"contact-item\"},[_c('i',{staticClass:\"am-icon-user\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.name))])]),_c('div',{staticClass:\"contact-item\"},[_c('i',{staticClass:\"am-icon-phone\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.phone))])])]),_c('div',{staticClass:\"contact-note\"},[_c('p',[_vm._v(\"欢迎随时来电咨询\")])])])]):_vm._e()])],1):_c('div',{staticClass:\"mobile-layout\"},[_c('div',{staticClass:\"mobile-banner\",on:{\"touchstart\":_vm.handleTouchStart,\"touchmove\":_vm.handleTouchMove,\"touchend\":_vm.handleTouchEnd}},[_c('div',{staticClass:\"mobile-banner-slider\",style:({ transform: `translateX(${-_vm.mobileCurrentSlide * 100}%)` })},_vm._l((_vm.bannerImages),function(item,index){return _c('div',{key:index,staticClass:\"mobile-slide\"},[_c('div',{staticClass:\"mobile-slide-inner\"},[_c('img',{staticClass:\"mobile-slide-img\",attrs:{\"src\":item.img,\"alt\":\"\"}}),_c('div',{staticClass:\"mobile-banner-content\",class:'pos-' + item.content.position},[_c('h2',{staticClass:\"mobile-banner-title\",domProps:{\"innerHTML\":_vm._s(item.content.title)}}),_c('p',{staticClass:\"mobile-banner-text\"},[_vm._v(_vm._s(item.content.text))]),_c('div',{staticClass:\"mobile-banner-actions\"},[(!_vm.isLogin && item.content.secondaryLink)?_c('a',{staticClass:\"mobile-banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.secondaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.secondaryBtnText)+\" \")]):_vm._e(),(!_vm.isLogin && item.content.primaryLink)?_c('a',{staticClass:\"mobile-banner-button primary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.primaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.primaryBtnText)+\" \")]):_vm._e(),(item.content.thirdLink)?_c('a',{staticClass:\"banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.thirdLink)}}},[_vm._v(\" \"+_vm._s(item.content.thirdBtnText)+\" \")]):_vm._e()])])])])}),0),_c('div',{staticClass:\"mobile-banner-pagination\"},_vm._l((_vm.bannerImages),function(item,index){return _c('span',{key:index,class:{ active: _vm.mobileCurrentSlide === index },on:{\"click\":function($event){return _vm.goToSlide(index)}}})}),0)]),_c('section',{staticClass:\"mobile-section mobile-gpu-section\"},[_vm._m(3),_c('div',{staticClass:\"mobile-gpu-list\"},_vm._l((_vm.gpus),function(gpu,index){return _c('div',{key:index,staticClass:\"mobile-gpu-card\",class:{ 'recommended': gpu.recommended },on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('div',{staticClass:\"mobile-gpu-header\"},[_c('h3',{staticClass:\"mobile-gpu-name\"},[_vm._v(_vm._s(gpu.name))]),_c('div',{staticClass:\"mobile-gpu-tags\"},[(gpu.recommended)?_c('span',{staticClass:\"mobile-recommend-tag\"},[_vm._v(\"推荐\")]):_vm._e(),(gpu.isNew)?_c('span',{staticClass:\"mobile-new-tag\"},[_vm._v(\"NEW\")]):_vm._e()])]),_c('div',{staticClass:\"mobile-gpu-specs\"},[_c('div',{staticClass:\"mobile-spec-item\"},[_c('span',{staticClass:\"mobile-spec-label\"},[_vm._v(\"单精度:\")]),_c('span',{staticClass:\"mobile-spec-value\"},[_vm._v(_vm._s(gpu.singlePrecision)+\" TFLOPS\")])]),_c('div',{staticClass:\"mobile-spec-item\"},[_c('span',{staticClass:\"mobile-spec-label\"},[_vm._v(\"半精度:\")]),_c('span',{staticClass:\"mobile-spec-value\"},[_vm._v(_vm._s(gpu.halfPrecision)+\" Tensor TFL\")])])]),_c('div',{staticClass:\"mobile-gpu-price\"},[(gpu.originalPrice)?_c('span',{staticClass:\"mobile-original-price\"},[_vm._v(\"¥\"+_vm._s(gpu.originalPrice)+\"/时\")]):_vm._e(),_c('span',{staticClass:\"mobile-current-price\"},[_vm._v(\"¥\"+_vm._s(gpu.price)+\"/时\")])])])}),0)]),_c('section',{staticClass:\"mobile-section mobile-comparison-section\"},[_vm._m(4),_c('div',{staticClass:\"mobile-comparison-container\"},[_c('div',{staticClass:\"mobile-comparison-scroll\"},[_c('table',{staticClass:\"mobile-comparison-table\"},[_c('thead',[_c('tr',[_c('th',[_vm._v(\"GPU型号\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('th',{key:gpu.name},[_vm._v(_vm._s(gpu.name))])})],2)]),_c('tbody',[_c('tr',[_c('td',[_vm._v(\"架构\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.architecture))])})],2),_c('tr',[_c('td',[_vm._v(\"FP16性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp16Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"FP32性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp32Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"显存\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memory))])})],2),_c('tr',[_c('td',[_vm._v(\"显存类型\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memoryType))])})],2),_c('tr',[_c('td',[_vm._v(\"带宽\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.bandwidth))])})],2)])])])])]),_c('section',{staticClass:\"mobile-section mobile-services-section\"},[_vm._m(5),_c('div',{staticClass:\"mobile-services-list\"},_vm._l((_vm.serviceList),function(service,index){return _c('div',{key:index,staticClass:\"mobile-service-card\"},[_c('div',{staticClass:\"mobile-service-icon\"},[_c('i',{class:service.icon})]),_c('h3',{staticClass:\"mobile-service-title\"},[_vm._v(_vm._s(service.title))]),_c('p',{staticClass:\"mobile-service-desc\"},[_vm._v(_vm._s(service.desc))])])}),0)]),_c('section',{staticClass:\"mobile-section mobile-applications-section\"},[_vm._m(6),_c('div',{staticClass:\"mobile-applications-grid\"},_vm._l((_vm.mobileApplications),function(app,index){return _c('div',{key:index,staticClass:\"mobile-app-item\"},[_c('div',{staticClass:\"mobile-app-image\"},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('h3',{staticClass:\"mobile-app-title\"},[_vm._v(_vm._s(app.title))])])}),0)]),_c('div',{staticClass:\"mobile-consult-section\"},[_c('h3',{staticClass:\"mobile-consult-title\"},[_vm._v(\"为AI+千行百业，提供高性能算力服务\")]),_c('button',{staticClass:\"mobile-consult-button\",on:{\"click\":_vm.openContactModal}},[_vm._v(\"立即咨询\")])]),_c('transition',{attrs:{\"name\":\"mobile-fade\"}},[(_vm.showContactModal)?_c('div',{staticClass:\"mobile-contact-overlay\",on:{\"click\":function($event){if($event.target !== $event.currentTarget)return null;return _vm.closeContactModal.apply(null, arguments)}}},[_c('div',{staticClass:\"mobile-contact-modal\"},[_c('button',{staticClass:\"mobile-close-modal\",on:{\"click\":_vm.closeContactModal}},[_vm._v(\" × \")]),_c('div',{staticClass:\"mobile-contact-content\"},[_c('div',{staticClass:\"mobile-contact-item\"},[_c('i',{staticClass:\"am-icon-user\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.name))])]),_c('div',{staticClass:\"mobile-contact-item\"},[_c('i',{staticClass:\"am-icon-phone\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.phone))])])]),_c('div',{staticClass:\"mobile-contact-note\"},[_c('p',[_vm._v(\"欢迎随时来电咨询\")])])])]):_vm._e()])],1),(!_vm.isMobile)?_c('Mider'):_vm._e(),_c('Footer'),_c('chatAi')],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"为您推荐\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" 专注于提供高性能、稳定可靠的 GPU 算力服务，兼具灵活配置与优质性价比。 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"核心优势\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴。 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"行业应用\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" Applications \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"为您推荐\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" 专注于提供高性能、稳定可靠的 GPU 算力服务 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"GPU性能对比\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" 专业GPU性能详细对比 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"核心优势\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"行业应用\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" Applications \")])])\n}]\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC,CAAE,CAACD,GAAG,CAACG,QAAQ,GAAEF,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAkB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAS,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAS,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,UAAU;IAACC,KAAK,EAAE;MACpTC,SAAS,EAAE,aAAa,GAAGN,GAAG,CAACO,SAAS,GAAG,GAAG;MAC9CC,UAAU,EAAER,GAAG,CAACS,KAAK,GAAG,UAAU,GAAG;IACvC;EAAE,CAAC,EAACT,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACW,YAAY,EAAE,UAASC,IAAI,EAACC,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,KAAK,EAAC;MAACa,GAAG,EAACD,KAAK;MAACT,WAAW,EAAC;IAAY,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACc,KAAK,EAAC;QAAC,KAAK,EAACH,IAAI,CAACI,GAAG;QAAC,KAAK,EAAC;MAAE;IAAC,CAAC,CAAC,EAACf,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,gBAAgB;MAACa,KAAK,EAAC,MAAM,GAAGL,IAAI,CAACM,OAAO,CAACC;IAAQ,CAAC,EAAC,CAAClB,EAAE,CAAC,IAAI,EAAC;MAACG,WAAW,EAAC,cAAc;MAACgB,QAAQ,EAAC;QAAC,WAAW,EAACpB,GAAG,CAACqB,EAAE,CAACT,IAAI,CAACM,OAAO,CAACI,KAAK;MAAC;IAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,GAAG,EAAC;MAACG,WAAW,EAAC;IAAa,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACT,IAAI,CAACM,OAAO,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC,EAACvB,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAgB,CAAC,EAAC,CAAE,CAACJ,GAAG,CAACyB,OAAO,IAAIb,IAAI,CAACM,OAAO,CAACQ,aAAa,GAAEzB,EAAE,CAAC,GAAG,EAAC;MAACG,WAAW,EAAC,6BAA6B;MAACW,KAAK,EAAC;QAAC,MAAM,EAAC;MAAG,CAAC;MAACY,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;UAACA,MAAM,CAACC,cAAc,EAAE;UAAC,OAAO9B,GAAG,CAAC+B,UAAU,CAACnB,IAAI,CAACM,OAAO,CAACQ,aAAa,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAAC1B,GAAG,CAACuB,EAAE,CAAC,GAAG,GAACvB,GAAG,CAACqB,EAAE,CAACT,IAAI,CAACM,OAAO,CAACc,gBAAgB,CAAC,GAAC,GAAG,CAAC,CAAC,CAAC,GAAChC,GAAG,CAACiC,EAAE,EAAE,EAAE,CAACjC,GAAG,CAACyB,OAAO,IAAIb,IAAI,CAACM,OAAO,CAACgB,WAAW,GAAEjC,EAAE,CAAC,GAAG,EAAC;MAACG,WAAW,EAAC,2BAA2B;MAACW,KAAK,EAAC;QAAC,MAAM,EAAC;MAAG,CAAC;MAACY,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;UAACA,MAAM,CAACC,cAAc,EAAE;UAAC,OAAO9B,GAAG,CAAC+B,UAAU,CAACnB,IAAI,CAACM,OAAO,CAACgB,WAAW,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAAClC,GAAG,CAACuB,EAAE,CAAC,GAAG,GAACvB,GAAG,CAACqB,EAAE,CAACT,IAAI,CAACM,OAAO,CAACiB,cAAc,CAAC,GAAC,GAAG,CAAC,CAAC,CAAC,GAACnC,GAAG,CAACiC,EAAE,EAAE,EAAErB,IAAI,CAACM,OAAO,CAACkB,SAAS,GAAEnC,EAAE,CAAC,GAAG,EAAC;MAACG,WAAW,EAAC,6BAA6B;MAACW,KAAK,EAAC;QAAC,MAAM,EAAC;MAAG,CAAC;MAACY,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;UAACA,MAAM,CAACC,cAAc,EAAE;UAAC,OAAO9B,GAAG,CAAC+B,UAAU,CAACnB,IAAI,CAACM,OAAO,CAACkB,SAAS,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAACpC,GAAG,CAACuB,EAAE,CAAC,GAAG,GAACvB,GAAG,CAACqB,EAAE,CAACT,IAAI,CAACM,OAAO,CAACmB,YAAY,CAAC,GAAC,GAAG,CAAC,CAAC,CAAC,GAACrC,GAAG,CAACiC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAChC,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAe,CAAC,EAAC,CAACH,EAAE,CAAC,MAAM,EAAC;IAACG,WAAW,EAAC,gBAAgB;IAACuB,EAAE,EAAC;MAAC,OAAO,EAAC3B,GAAG,CAACsC;IAAI;EAAC,CAAC,EAAC,CAACrC,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,oBAAoB;IAACW,KAAK,EAAC;MAAC,KAAK,EAACwB,OAAO,CAAC,2CAA2C,CAAC;MAAC,KAAK,EAAC;IAAE;EAAC,CAAC,CAAC,CAAC,CAAC,EAACtC,EAAE,CAAC,MAAM,EAAC;IAACG,WAAW,EAAC,iBAAiB;IAACuB,EAAE,EAAC;MAAC,OAAO,EAAC3B,GAAG,CAACwC;IAAI;EAAC,CAAC,EAAC,CAACvC,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,YAAY;IAACW,KAAK,EAAC;MAAC,KAAK,EAACwB,OAAO,CAAC,2CAA2C,CAAC;MAAC,KAAK,EAAC;IAAE;EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACtC,EAAE,CAAC,KAAK,EAAC;IAACwC,GAAG,EAAC,kBAAkB;IAACrC,WAAW,EAAC;EAAmB,CAAC,EAACJ,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACW,YAAY,EAAE,UAASC,IAAI,EAACC,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,MAAM,EAAC;MAACa,GAAG,EAACD,KAAK;MAACI,KAAK,EAAC;QAAEyB,MAAM,EAAE1C,GAAG,CAAC2C,UAAU,KAAK9B;MAAM;IAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACZ,EAAE,CAAC,SAAS,EAAC;IAACG,WAAW,EAAC;EAAqB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAW,CAAC,EAAC,CAACJ,GAAG,CAAC4C,EAAE,CAAC,CAAC,CAAC,EAAC3C,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAe,CAAC,EAACJ,GAAG,CAACU,EAAE,CAAEV,GAAG,CAAC6C,IAAI,EAAE,UAASC,GAAG,EAACjC,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,KAAK,EAAC;MAACa,GAAG,EAACD,KAAK;MAACT,WAAW,EAAC,UAAU;MAACa,KAAK,EAAC;QAAE,aAAa,EAAE6B,GAAG,CAACC;MAAY,CAAC;MAACpB,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;UAAC,OAAO7B,GAAG,CAAC+B,UAAU,CAAC,UAAU,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAAC9B,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAiB,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;MAACG,WAAW,EAAC;IAAU,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEF,GAAG,CAACC,WAAW,GAAE9C,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAoB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAACvB,GAAG,CAACiC,EAAE,EAAE,EAAEa,GAAG,CAACG,KAAK,GAAEhD,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAS,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAACvB,GAAG,CAACiC,EAAE,EAAE,CAAC,CAAC,EAAChC,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAmB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAe,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAW,CAAC,EAAC,CAACH,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAY,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAY,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACI,eAAe,CAAC,GAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAACjD,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAW,CAAC,EAAC,CAACH,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAY,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAY,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACK,aAAa,CAAC,GAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAClD,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAe,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAa,CAAC,EAAC,CAAE0C,GAAG,CAACM,aAAa,GAAEnD,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAgB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,GAAG,GAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACM,aAAa,CAAC,GAAC,IAAI,CAAC,CAAC,CAAC,GAACpD,GAAG,CAACiC,EAAE,EAAE,EAAChC,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAe,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,GAAG,CAAC,EAACtB,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAa,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,EAACrD,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,eAAe,CAAC,EAACA,EAAE,CAAC,SAAS,EAAC;IAACG,WAAW,EAAC;EAA0B,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAW,CAAC,EAAC,CAACJ,GAAG,CAAC4C,EAAE,CAAC,CAAC,CAAC,EAAC3C,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAe,CAAC,EAACJ,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACsD,WAAW,EAAE,UAASC,OAAO,EAAC1C,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,KAAK,EAAC;MAACa,GAAG,EAACD,KAAK;MAACT,WAAW,EAAC;IAAc,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAc,CAAC,EAAC,CAACH,EAAE,CAAC,GAAG,EAAC;MAACG,WAAW,EAAC,cAAc;MAACa,KAAK,EAACsC,OAAO,CAACC;IAAI,CAAC,CAAC,EAACvD,EAAE,CAAC,IAAI,EAAC;MAACG,WAAW,EAAC;IAAe,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACkC,OAAO,CAACjC,KAAK,CAAC,CAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAc,CAAC,EAAC,CAACH,EAAE,CAAC,GAAG,EAAC,CAACD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACkC,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACxD,EAAE,CAAC,SAAS,EAAC;IAACG,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACJ,GAAG,CAAC4C,EAAE,CAAC,CAAC,CAAC,EAAC3C,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAkB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAa,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAyB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,aAAa;IAACuB,EAAE,EAAC;MAAC,WAAW,EAAC,SAAA+B,CAAS7B,MAAM,EAAC;QAAC7B,GAAG,CAAC2D,YAAY,CAACC,KAAK,GAAG,IAAI;MAAA,CAAC;MAAC,YAAY,EAAC,SAAAC,CAAShC,MAAM,EAAC;QAAC7B,GAAG,CAAC2D,YAAY,CAACC,KAAK,GAAG,KAAK;MAAA;IAAC;EAAC,CAAC,EAAC,CAAC3D,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,cAAc;IAACa,KAAK,EAAC;MAAE,cAAc,EAAEjB,GAAG,CAAC2D,YAAY,CAACC;IAAM;EAAC,CAAC,EAAC,CAAC3D,EAAE,CAAC,KAAK,EAAC;IAACc,KAAK,EAAC;MAAC,KAAK,EAACf,GAAG,CAAC2D,YAAY,CAACG,KAAK;MAAC,KAAK,EAAC9D,GAAG,CAAC2D,YAAY,CAACrC;IAAK;EAAC,CAAC,CAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,kBAAkB;IAACa,KAAK,EAAC;MAAE,cAAc,EAAEjB,GAAG,CAAC2D,YAAY,CAACC;IAAM;EAAC,CAAC,EAAC,CAAC5D,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC2D,YAAY,CAACrC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACtB,GAAG,CAACU,EAAE,CAAEV,GAAG,CAAC+D,gBAAgB,EAAE,UAASC,GAAG,EAACnD,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,KAAK,EAAC;MAACa,GAAG,EAAC,OAAO,GAACD,KAAK;MAACT,WAAW,EAAC;IAAyB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,aAAa;MAACuB,EAAE,EAAC;QAAC,WAAW,EAAC,SAAA+B,CAAS7B,MAAM,EAAC;UAACmC,GAAG,CAACJ,KAAK,GAAG,IAAI;QAAA,CAAC;QAAC,YAAY,EAAC,SAAAC,CAAShC,MAAM,EAAC;UAACmC,GAAG,CAACJ,KAAK,GAAG,KAAK;QAAA;MAAC;IAAC,CAAC,EAAC,CAAC3D,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,cAAc;MAACa,KAAK,EAAC;QAAE,cAAc,EAAE+C,GAAG,CAACJ;MAAM;IAAC,CAAC,EAAC,CAAC3D,EAAE,CAAC,KAAK,EAAC;MAACc,KAAK,EAAC;QAAC,KAAK,EAACiD,GAAG,CAACF,KAAK;QAAC,KAAK,EAACE,GAAG,CAAC1C;MAAK;IAAC,CAAC,CAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,kBAAkB;MAACa,KAAK,EAAC;QAAE,cAAc,EAAE+C,GAAG,CAACJ;MAAM;IAAC,CAAC,EAAC,CAAC5D,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAAC2C,GAAG,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAACtB,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACiE,aAAa,EAAE,UAASD,GAAG,EAACnD,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,KAAK,EAAC;MAACa,GAAG,EAAC,QAAQ,GAACD,KAAK;MAACT,WAAW,EAAC;IAA0B,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,aAAa;MAACuB,EAAE,EAAC;QAAC,WAAW,EAAC,SAAA+B,CAAS7B,MAAM,EAAC;UAACmC,GAAG,CAACJ,KAAK,GAAG,IAAI;QAAA,CAAC;QAAC,YAAY,EAAC,SAAAC,CAAShC,MAAM,EAAC;UAACmC,GAAG,CAACJ,KAAK,GAAG,KAAK;QAAA;MAAC;IAAC,CAAC,EAAC,CAAC3D,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,cAAc;MAACa,KAAK,EAAC;QAAE,cAAc,EAAE+C,GAAG,CAACJ;MAAM;IAAC,CAAC,EAAC,CAAC3D,EAAE,CAAC,KAAK,EAAC;MAACc,KAAK,EAAC;QAAC,KAAK,EAACiD,GAAG,CAACF,KAAK;QAAC,KAAK,EAACE,GAAG,CAAC1C;MAAK;IAAC,CAAC,CAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,kBAAkB;MAACa,KAAK,EAAC;QAAE,cAAc,EAAE+C,GAAG,CAACJ;MAAM;IAAC,CAAC,EAAC,CAAC5D,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAAC2C,GAAG,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAACtB,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACkE,iBAAiB,EAAE,UAASF,GAAG,EAACnD,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,KAAK,EAAC;MAACa,GAAG,EAAC,cAAc,GAACD,KAAK;MAACT,WAAW,EAAC;IAA0B,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,aAAa;MAACuB,EAAE,EAAC;QAAC,WAAW,EAAC,SAAA+B,CAAS7B,MAAM,EAAC;UAACmC,GAAG,CAACJ,KAAK,GAAG,IAAI;QAAA,CAAC;QAAC,YAAY,EAAC,SAAAC,CAAShC,MAAM,EAAC;UAACmC,GAAG,CAACJ,KAAK,GAAG,KAAK;QAAA;MAAC;IAAC,CAAC,EAAC,CAAC3D,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,cAAc;MAACa,KAAK,EAAC;QAAE,cAAc,EAAE+C,GAAG,CAACJ;MAAM;IAAC,CAAC,EAAC,CAAC3D,EAAE,CAAC,KAAK,EAAC;MAACc,KAAK,EAAC;QAAC,KAAK,EAACiD,GAAG,CAACF,KAAK;QAAC,KAAK,EAACE,GAAG,CAAC1C;MAAK;IAAC,CAAC,CAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,kBAAkB;MAACa,KAAK,EAAC;QAAE,cAAc,EAAE+C,GAAG,CAACJ;MAAM;IAAC,CAAC,EAAC,CAAC5D,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAAC2C,GAAG,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAACrB,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAyB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,aAAa;IAACuB,EAAE,EAAC;MAAC,WAAW,EAAC,SAAA+B,CAAS7B,MAAM,EAAC;QAAC7B,GAAG,CAACmE,YAAY,CAACP,KAAK,GAAG,IAAI;MAAA,CAAC;MAAC,YAAY,EAAC,SAAAC,CAAShC,MAAM,EAAC;QAAC7B,GAAG,CAACmE,YAAY,CAACP,KAAK,GAAG,KAAK;MAAA;IAAC;EAAC,CAAC,EAAC,CAAC3D,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,cAAc;IAACa,KAAK,EAAC;MAAE,cAAc,EAAEjB,GAAG,CAACmE,YAAY,CAACP;IAAM;EAAC,CAAC,EAAC,CAAC3D,EAAE,CAAC,KAAK,EAAC;IAACc,KAAK,EAAC;MAAC,KAAK,EAACf,GAAG,CAACmE,YAAY,CAACL,KAAK;MAAC,KAAK,EAAC9D,GAAG,CAACmE,YAAY,CAAC7C;IAAK;EAAC,CAAC,CAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,kBAAkB;IAACa,KAAK,EAAC;MAAE,cAAc,EAAEjB,GAAG,CAACmE,YAAY,CAACP;IAAM;EAAC,CAAC,EAAC,CAAC5D,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAACmE,YAAY,CAAC7C,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAACA,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAqB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAO,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;IAACG,WAAW,EAAC;EAAc,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,QAAQ,EAAC;IAACG,WAAW,EAAC,iBAAiB;IAACuB,EAAE,EAAC;MAAC,OAAO,EAAC3B,GAAG,CAACoE;IAAgB;EAAC,CAAC,EAAC,CAACpE,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,YAAY,EAAC;IAACc,KAAK,EAAC;MAAC,MAAM,EAAC;IAAM;EAAC,CAAC,EAAC,CAAEf,GAAG,CAACqE,gBAAgB,GAAEpE,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,uBAAuB;IAACuB,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;QAAC,IAAGA,MAAM,CAACyC,MAAM,KAAKzC,MAAM,CAAC0C,aAAa,EAAC,OAAO,IAAI;QAAC,OAAOvE,GAAG,CAACwE,iBAAiB,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACzE,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAe,CAAC,EAAC,CAACH,EAAE,CAAC,QAAQ,EAAC;IAACG,WAAW,EAAC,aAAa;IAACuB,EAAE,EAAC;MAAC,OAAO,EAAC3B,GAAG,CAACwE;IAAiB;EAAC,CAAC,EAAC,CAACxE,GAAG,CAACuB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAc,CAAC,EAAC,CAACH,EAAE,CAAC,GAAG,EAAC;IAACG,WAAW,EAAC;EAAc,CAAC,CAAC,EAACH,EAAE,CAAC,MAAM,EAAC,CAACD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC2E,WAAW,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC/C,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAc,CAAC,EAAC,CAACH,EAAE,CAAC,GAAG,EAAC;IAACG,WAAW,EAAC;EAAe,CAAC,CAAC,EAACH,EAAE,CAAC,MAAM,EAAC,CAACD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC2E,WAAW,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC3E,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAc,CAAC,EAAC,CAACH,EAAE,CAAC,GAAG,EAAC,CAACD,GAAG,CAACuB,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACvB,GAAG,CAACiC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAChC,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAe,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,eAAe;IAACuB,EAAE,EAAC;MAAC,YAAY,EAAC3B,GAAG,CAAC6E,gBAAgB;MAAC,WAAW,EAAC7E,GAAG,CAAC8E,eAAe;MAAC,UAAU,EAAC9E,GAAG,CAAC+E;IAAc;EAAC,CAAC,EAAC,CAAC9E,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,sBAAsB;IAACC,KAAK,EAAE;MAAEC,SAAS,EAAG,cAAa,CAACN,GAAG,CAACgF,kBAAkB,GAAG,GAAI;IAAI;EAAE,CAAC,EAAChF,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACW,YAAY,EAAE,UAASC,IAAI,EAACC,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,KAAK,EAAC;MAACa,GAAG,EAACD,KAAK;MAACT,WAAW,EAAC;IAAc,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAoB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,kBAAkB;MAACW,KAAK,EAAC;QAAC,KAAK,EAACH,IAAI,CAACI,GAAG;QAAC,KAAK,EAAC;MAAE;IAAC,CAAC,CAAC,EAACf,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC,uBAAuB;MAACa,KAAK,EAAC,MAAM,GAAGL,IAAI,CAACM,OAAO,CAACC;IAAQ,CAAC,EAAC,CAAClB,EAAE,CAAC,IAAI,EAAC;MAACG,WAAW,EAAC,qBAAqB;MAACgB,QAAQ,EAAC;QAAC,WAAW,EAACpB,GAAG,CAACqB,EAAE,CAACT,IAAI,CAACM,OAAO,CAACI,KAAK;MAAC;IAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,GAAG,EAAC;MAACG,WAAW,EAAC;IAAoB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACT,IAAI,CAACM,OAAO,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC,EAACvB,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAuB,CAAC,EAAC,CAAE,CAACJ,GAAG,CAACyB,OAAO,IAAIb,IAAI,CAACM,OAAO,CAACQ,aAAa,GAAEzB,EAAE,CAAC,GAAG,EAAC;MAACG,WAAW,EAAC,oCAAoC;MAACW,KAAK,EAAC;QAAC,MAAM,EAAC;MAAG,CAAC;MAACY,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;UAACA,MAAM,CAACC,cAAc,EAAE;UAAC,OAAO9B,GAAG,CAAC+B,UAAU,CAACnB,IAAI,CAACM,OAAO,CAACQ,aAAa,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAAC1B,GAAG,CAACuB,EAAE,CAAC,GAAG,GAACvB,GAAG,CAACqB,EAAE,CAACT,IAAI,CAACM,OAAO,CAACc,gBAAgB,CAAC,GAAC,GAAG,CAAC,CAAC,CAAC,GAAChC,GAAG,CAACiC,EAAE,EAAE,EAAE,CAACjC,GAAG,CAACyB,OAAO,IAAIb,IAAI,CAACM,OAAO,CAACgB,WAAW,GAAEjC,EAAE,CAAC,GAAG,EAAC;MAACG,WAAW,EAAC,kCAAkC;MAACW,KAAK,EAAC;QAAC,MAAM,EAAC;MAAG,CAAC;MAACY,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;UAACA,MAAM,CAACC,cAAc,EAAE;UAAC,OAAO9B,GAAG,CAAC+B,UAAU,CAACnB,IAAI,CAACM,OAAO,CAACgB,WAAW,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAAClC,GAAG,CAACuB,EAAE,CAAC,GAAG,GAACvB,GAAG,CAACqB,EAAE,CAACT,IAAI,CAACM,OAAO,CAACiB,cAAc,CAAC,GAAC,GAAG,CAAC,CAAC,CAAC,GAACnC,GAAG,CAACiC,EAAE,EAAE,EAAErB,IAAI,CAACM,OAAO,CAACkB,SAAS,GAAEnC,EAAE,CAAC,GAAG,EAAC;MAACG,WAAW,EAAC,6BAA6B;MAACW,KAAK,EAAC;QAAC,MAAM,EAAC;MAAG,CAAC;MAACY,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;UAACA,MAAM,CAACC,cAAc,EAAE;UAAC,OAAO9B,GAAG,CAAC+B,UAAU,CAACnB,IAAI,CAACM,OAAO,CAACkB,SAAS,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAACpC,GAAG,CAACuB,EAAE,CAAC,GAAG,GAACvB,GAAG,CAACqB,EAAE,CAACT,IAAI,CAACM,OAAO,CAACmB,YAAY,CAAC,GAAC,GAAG,CAAC,CAAC,CAAC,GAACrC,GAAG,CAACiC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,EAAChC,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAA0B,CAAC,EAACJ,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACW,YAAY,EAAE,UAASC,IAAI,EAACC,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,MAAM,EAAC;MAACa,GAAG,EAACD,KAAK;MAACI,KAAK,EAAC;QAAEyB,MAAM,EAAE1C,GAAG,CAACgF,kBAAkB,KAAKnE;MAAM,CAAC;MAACc,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;UAAC,OAAO7B,GAAG,CAACiF,SAAS,CAACpE,KAAK,CAAC;QAAA;MAAC;IAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAACZ,EAAE,CAAC,SAAS,EAAC;IAACG,WAAW,EAAC;EAAmC,CAAC,EAAC,CAACJ,GAAG,CAAC4C,EAAE,CAAC,CAAC,CAAC,EAAC3C,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAiB,CAAC,EAACJ,GAAG,CAACU,EAAE,CAAEV,GAAG,CAAC6C,IAAI,EAAE,UAASC,GAAG,EAACjC,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,KAAK,EAAC;MAACa,GAAG,EAACD,KAAK;MAACT,WAAW,EAAC,iBAAiB;MAACa,KAAK,EAAC;QAAE,aAAa,EAAE6B,GAAG,CAACC;MAAY,CAAC;MAACpB,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;UAAC,OAAO7B,GAAG,CAAC+B,UAAU,CAAC,UAAU,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAAC9B,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAmB,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;MAACG,WAAW,EAAC;IAAiB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC/C,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAiB,CAAC,EAAC,CAAE0C,GAAG,CAACC,WAAW,GAAE9C,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAsB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAACvB,GAAG,CAACiC,EAAE,EAAE,EAAEa,GAAG,CAACG,KAAK,GAAEhD,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAgB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAACvB,GAAG,CAACiC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAChC,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAkB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAkB,CAAC,EAAC,CAACH,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAmB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAmB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACI,eAAe,CAAC,GAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAACjD,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAkB,CAAC,EAAC,CAACH,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAmB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAmB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACK,aAAa,CAAC,GAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAClD,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAkB,CAAC,EAAC,CAAE0C,GAAG,CAACM,aAAa,GAAEnD,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAuB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,GAAG,GAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACM,aAAa,CAAC,GAAC,IAAI,CAAC,CAAC,CAAC,GAACpD,GAAG,CAACiC,EAAE,EAAE,EAAChC,EAAE,CAAC,MAAM,EAAC;MAACG,WAAW,EAAC;IAAsB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,GAAG,GAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACO,KAAK,CAAC,GAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAACpD,EAAE,CAAC,SAAS,EAAC;IAACG,WAAW,EAAC;EAA0C,CAAC,EAAC,CAACJ,GAAG,CAAC4C,EAAE,CAAC,CAAC,CAAC,EAAC3C,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAA6B,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAA0B,CAAC,EAAC,CAACH,EAAE,CAAC,OAAO,EAAC;IAACG,WAAW,EAAC;EAAyB,CAAC,EAAC,CAACH,EAAE,CAAC,OAAO,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACuB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAACvB,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACkF,cAAc,EAAE,UAASpC,GAAG,EAAC;IAAC,OAAO7C,EAAE,CAAC,IAAI,EAAC;MAACa,GAAG,EAACgC,GAAG,CAACE;IAAI,CAAC,EAAC,CAAChD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC/C,EAAE,CAAC,OAAO,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAACvB,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACkF,cAAc,EAAE,UAASpC,GAAG,EAAC;IAAC,OAAO7C,EAAE,CAAC,IAAI,EAAC;MAACa,GAAG,EAACgC,GAAG,CAACE;IAAI,CAAC,EAAC,CAAChD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACqC,YAAY,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAClF,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACuB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAACvB,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACkF,cAAc,EAAE,UAASpC,GAAG,EAAC;IAAC,OAAO7C,EAAE,CAAC,IAAI,EAAC;MAACa,GAAG,EAACgC,GAAG,CAACE;IAAI,CAAC,EAAC,CAAChD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACsC,eAAe,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACnF,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACuB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAACvB,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACkF,cAAc,EAAE,UAASpC,GAAG,EAAC;IAAC,OAAO7C,EAAE,CAAC,IAAI,EAAC;MAACa,GAAG,EAACgC,GAAG,CAACE;IAAI,CAAC,EAAC,CAAChD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACuC,eAAe,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACpF,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAACvB,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACkF,cAAc,EAAE,UAASpC,GAAG,EAAC;IAAC,OAAO7C,EAAE,CAAC,IAAI,EAAC;MAACa,GAAG,EAACgC,GAAG,CAACE;IAAI,CAAC,EAAC,CAAChD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACwC,MAAM,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACrF,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACvB,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACkF,cAAc,EAAE,UAASpC,GAAG,EAAC;IAAC,OAAO7C,EAAE,CAAC,IAAI,EAAC;MAACa,GAAG,EAACgC,GAAG,CAACE;IAAI,CAAC,EAAC,CAAChD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAACyC,UAAU,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACtF,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACuB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAACvB,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACkF,cAAc,EAAE,UAASpC,GAAG,EAAC;IAAC,OAAO7C,EAAE,CAAC,IAAI,EAAC;MAACa,GAAG,EAACgC,GAAG,CAACE;IAAI,CAAC,EAAC,CAAChD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACyB,GAAG,CAAC0C,SAAS,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACvF,EAAE,CAAC,SAAS,EAAC;IAACG,WAAW,EAAC;EAAwC,CAAC,EAAC,CAACJ,GAAG,CAAC4C,EAAE,CAAC,CAAC,CAAC,EAAC3C,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAsB,CAAC,EAACJ,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACsD,WAAW,EAAE,UAASC,OAAO,EAAC1C,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,KAAK,EAAC;MAACa,GAAG,EAACD,KAAK;MAACT,WAAW,EAAC;IAAqB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAqB,CAAC,EAAC,CAACH,EAAE,CAAC,GAAG,EAAC;MAACgB,KAAK,EAACsC,OAAO,CAACC;IAAI,CAAC,CAAC,CAAC,CAAC,EAACvD,EAAE,CAAC,IAAI,EAAC;MAACG,WAAW,EAAC;IAAsB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACkC,OAAO,CAACjC,KAAK,CAAC,CAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,GAAG,EAAC;MAACG,WAAW,EAAC;IAAqB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACkC,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAACxD,EAAE,CAAC,SAAS,EAAC;IAACG,WAAW,EAAC;EAA4C,CAAC,EAAC,CAACJ,GAAG,CAAC4C,EAAE,CAAC,CAAC,CAAC,EAAC3C,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAA0B,CAAC,EAACJ,GAAG,CAACU,EAAE,CAAEV,GAAG,CAACyF,kBAAkB,EAAE,UAASzB,GAAG,EAACnD,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,KAAK,EAAC;MAACa,GAAG,EAACD,KAAK;MAACT,WAAW,EAAC;IAAiB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;IAAkB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;MAACc,KAAK,EAAC;QAAC,KAAK,EAACiD,GAAG,CAACF,KAAK;QAAC,KAAK,EAACE,GAAG,CAAC1C;MAAK;IAAC,CAAC,CAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,IAAI,EAAC;MAACG,WAAW,EAAC;IAAkB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAAC2C,GAAG,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAACrB,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAwB,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;IAACG,WAAW,EAAC;EAAsB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,QAAQ,EAAC;IAACG,WAAW,EAAC,uBAAuB;IAACuB,EAAE,EAAC;MAAC,OAAO,EAAC3B,GAAG,CAACoE;IAAgB;EAAC,CAAC,EAAC,CAACpE,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,YAAY,EAAC;IAACc,KAAK,EAAC;MAAC,MAAM,EAAC;IAAa;EAAC,CAAC,EAAC,CAAEf,GAAG,CAACqE,gBAAgB,GAAEpE,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC,wBAAwB;IAACuB,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;QAAC,IAAGA,MAAM,CAACyC,MAAM,KAAKzC,MAAM,CAAC0C,aAAa,EAAC,OAAO,IAAI;QAAC,OAAOvE,GAAG,CAACwE,iBAAiB,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACzE,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAsB,CAAC,EAAC,CAACH,EAAE,CAAC,QAAQ,EAAC;IAACG,WAAW,EAAC,oBAAoB;IAACuB,EAAE,EAAC;MAAC,OAAO,EAAC3B,GAAG,CAACwE;IAAiB;EAAC,CAAC,EAAC,CAACxE,GAAG,CAACuB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAwB,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAqB,CAAC,EAAC,CAACH,EAAE,CAAC,GAAG,EAAC;IAACG,WAAW,EAAC;EAAc,CAAC,CAAC,EAACH,EAAE,CAAC,MAAM,EAAC,CAACD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC2E,WAAW,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC/C,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAqB,CAAC,EAAC,CAACH,EAAE,CAAC,GAAG,EAAC;IAACG,WAAW,EAAC;EAAe,CAAC,CAAC,EAACH,EAAE,CAAC,MAAM,EAAC,CAACD,GAAG,CAACuB,EAAE,CAACvB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAAC2E,WAAW,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC3E,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAqB,CAAC,EAAC,CAACH,EAAE,CAAC,GAAG,EAAC,CAACD,GAAG,CAACuB,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACvB,GAAG,CAACiC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAE,CAACjC,GAAG,CAACG,QAAQ,GAAEF,EAAE,CAAC,OAAO,CAAC,GAACD,GAAG,CAACiC,EAAE,EAAE,EAAChC,EAAE,CAAC,QAAQ,CAAC,EAACA,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC;AACtzb,CAAC;AACD,IAAIyF,eAAe,GAAG,CAAC,YAAW;EAAC,IAAI1F,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;IAACG,WAAW,EAAC;EAAe,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,GAAG,EAAC;IAACG,WAAW,EAAC;EAAqB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,yCAAyC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzQ,CAAC,EAAC,YAAW;EAAC,IAAIvB,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;IAACG,WAAW,EAAC;EAAe,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,GAAG,EAAC;IAACG,WAAW,EAAC;EAAqB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC;AACvO,CAAC,EAAC,YAAW;EAAC,IAAIvB,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;IAACG,WAAW,EAAC;EAAe,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,GAAG,EAAC;IAACG,WAAW,EAAC;EAAqB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3N,CAAC,EAAC,YAAW;EAAC,IAAIvB,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAuB,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;IAACG,WAAW,EAAC;EAAsB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,GAAG,EAAC;IAACG,WAAW,EAAC;EAA4B,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3P,CAAC,EAAC,YAAW;EAAC,IAAIvB,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAuB,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;IAACG,WAAW,EAAC;EAAsB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,GAAG,EAAC;IAACG,WAAW,EAAC;EAA4B,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;AAClP,CAAC,EAAC,YAAW;EAAC,IAAIvB,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAuB,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;IAACG,WAAW,EAAC;EAAsB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,GAAG,EAAC;IAACG,WAAW,EAAC;EAA4B,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3P,CAAC,EAAC,YAAW;EAAC,IAAIvB,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACG,WAAW,EAAC;EAAuB,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;IAACG,WAAW,EAAC;EAAsB,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACtB,EAAE,CAAC,GAAG,EAAC;IAACG,WAAW,EAAC;EAA4B,CAAC,EAAC,CAACJ,GAAG,CAACuB,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;AAChP,CAAC,CAAC;AAEF,SAASxB,MAAM,EAAE2F,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}