"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[964],{9484:function(t,e,s){s.d(e,{Z:function(){return A}});var i=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"chat-container"},[e("div",{staticClass:"question-carousel",on:{mouseenter:t.pauseCarousel,mouseleave:t.resumeCarousel}},[e("transition-group",{staticClass:"carousel-wrapper",attrs:{name:"slide",tag:"div"}},t._l(t.questions,(function(s,i){return e("div",{directives:[{name:"show",rawName:"v-show",value:t.currentQuestionIndex===i,expression:"currentQuestionIndex === index"}],key:s,staticClass:"question-item",on:{click:function(e){return t.sendCarouselQuestion(s)},mouseenter:function(e){return t.witde(i)}}},[t._v(" "+t._s(s)+" ")])})),0)],1),e("div",{staticClass:"chat-icon",class:{"chat-icon-active":t.showChat},on:{click:t.toggleChat}},[e("i",{staticClass:"fas fa-comment"})])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showChat,expression:"showChat"}],staticClass:"chat-window"},[e("div",{staticClass:"chat-header"},[t._m(0),e("div",{staticClass:"chat-controls"},[e("i",{staticClass:"fas fa-times",on:{click:t.toggleChat}})])]),e("div",{ref:"messagesContainer",staticClass:"chat-messages"},[t._l(t.messages,(function(s,i){return e("div",{key:i,class:["message",s.type]},["bot"===s.type?e("div",{staticClass:"avatar"},[e("i",{staticClass:"fas fa-robot"})]):t._e(),e("div",{staticClass:"message-content"},[e("div",{staticClass:"message-text",domProps:{innerHTML:t._s(t.formatMessage(s.text))}}),e("div",{staticClass:"message-time"},[t._v(t._s(t.formatTime(s.time)))])])])})),t.loading?e("div",{staticClass:"typing-indicator"},[e("div",{staticClass:"dot"}),e("div",{staticClass:"dot"}),e("div",{staticClass:"dot"})]):t._e()],2),e("div",{staticClass:"chat-input"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.userInput,expression:"userInput"}],attrs:{type:"text",placeholder:"请输入您的问题...",disabled:t.loading},domProps:{value:t.userInput},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.sendMessage.apply(null,arguments)},input:function(e){e.target.composing||(t.userInput=e.target.value)}}}),e("button",{attrs:{disabled:t.loading||!t.userInput.trim()},on:{click:t.sendMessage}},[e("i",{staticClass:"fas fa-paper-plane"})])])])])},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"chat-title"},[e("i",{staticClass:"fas fa-robot"}),e("span",[t._v("智能客服")])])}],n=(s(7658),{name:"chatAi",data(){return{showChat:!1,userInput:"",messages:[{type:"bot",text:"您好！我是智能客服助手，有什么可以帮您？",time:new Date}],loading:!1,historyMessages:[],questions:["如何租赁GPU算力？","支持哪些支付方式？","如何查看订单状态？"],currentQuestionIndex:0,carouselTimer:null,carouselInterval:3e3,isPaused:!1}},beforeDestroy(){this.clearCarousel()},mounted(){if(this.startCarousel(),!document.getElementById("font-awesome")){const t=document.createElement("link");t.id="font-awesome",t.rel="stylesheet",t.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(t)}},methods:{witde(t){this.currentQuestionIndex=t,this.pauseCarousel()},startCarousel(){let t=this;this.clearCarousel(),this.carouselTimer=setInterval((()=>{t.isPaused||(this.currentQuestionIndex=(this.currentQuestionIndex+1)%this.questions.length,console.log("数据",this.currentQuestionIndex),console.log("ispasued",t.isPaused))}),this.carouselInterval)},pauseCarousel(){this.isPaused=!0},resumeCarousel(){this.isPaused=!1},clearCarousel(){this.carouselTimer&&(clearInterval(this.carouselTimer),this.carouselTimer=null)},sendCarouselQuestion(t){this.userInput=t,this.sendMessage()},toggleChat(){this.showChat=!this.showChat,this.showChat&&this.$nextTick((()=>{this.scrollToBottom()}))},async sendMessage(){if(!this.userInput.trim()||this.loading)return;this.messages.push({type:"user",text:this.userInput,time:new Date});const t=this.userInput;this.userInput="",this.loading=!0,this.historyMessages.push({role:"user",content:t});const e={model:"Qwen/QwQ-32B",messages:[{role:"system",content:"你是天工开物算力租赁网站的客服，当我问到算力租赁相关问题的时候再进行回答，如果我的问题与算力无关则回答“小天也不知道怎么办了呢，不如我们来聊聊算力相关的话题吧！”,如果问题涉及到你是谁或者你好的这种礼貌性踊跃，可以进行礼貌性的回复"},...this.historyMessages],stream:!0,options:{presence_penalty:1.2,frequency_penalty:1.5,seed:12345}};this.$nextTick((()=>{this.scrollToBottom()}));try{const t=await fetch("https://api.siliconflow.cn/v1/chat/completions",{method:"POST",headers:{Authorization:"Bearer sk-yxjtnfarjndtcwbjwmscdejrgtlpaaebnfdxcfnhhuanbqty","Content-Type":"application/json"},body:JSON.stringify(e)}),i=t.body.getReader(),a=new TextDecoder,n=this.messages.push({type:"bot",text:"",time:new Date})-1;while(1){const{done:t,value:e}=await i.read();if(t)break;const o=a.decode(e),c=o.split("\n").filter((t=>t.trim()));for(const i of c)try{const t=i.slice(6).trim();if(""===t||"[DONE]"===t)continue;let e=JSON.parse(t);if(e.choices){if(null!=e.choices[0].delta.reasoning_content)continue;if("\n\n"==e.choices[0].delta.content)continue;this.messages[n].text+=e.choices[0].delta.content}}catch(s){}}this.historyMessages.push({role:"assistant",content:this.messages[n].text})}catch(i){this.messages.push({type:"bot",text:"抱歉，系统暂时无法响应，请稍后再试。",time:new Date})}finally{this.loading=!1,this.$nextTick((()=>{this.scrollToBottom()}))}},async callChatAPI(t){return await new Promise((t=>setTimeout(t,1e3))),`感谢您的提问: "${t}"。这是一个模拟回复，请替换为真实API调用。`},scrollToBottom(){const t=this.$refs.messagesContainer;t.scrollTop=t.scrollHeight},formatTime(t){return new Date(t).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},formatMessage(t){return t.replace(/\n/g,"<br>").replace(/(https?:\/\/[^\s]+)/g,'<a href="$1" target="_blank">$1</a>')}}}),o=n,c=s(1001),r=(0,c.Z)(o,i,a,!1,null,"46c63c47",null),A=r.exports},4670:function(t,e,s){s.r(e),s.d(e,{default:function(){return _}});var i=function(){var t=this,e=t._self._c;return e("div",[t.isMobile?e("div",{staticClass:"mobile-layout"},[e("div",{staticClass:"mobile-banner",on:{touchstart:t.handleTouchStart,touchmove:t.handleTouchMove,touchend:t.handleTouchEnd}},[e("div",{staticClass:"mobile-banner-slider",style:{transform:`translateX(${100*-t.mobileCurrentSlide}%)`}},t._l(t.bannerImages,(function(s,i){return e("div",{key:i,staticClass:"mobile-slide"},[e("div",{staticClass:"mobile-slide-inner"},[e("img",{staticClass:"mobile-slide-img",attrs:{src:s.img,alt:""}}),e("div",{staticClass:"mobile-banner-content",class:"pos-"+s.content.position},[e("h2",{staticClass:"mobile-banner-title",domProps:{innerHTML:t._s(s.content.title)}}),e("p",{staticClass:"mobile-banner-text"},[t._v(t._s(s.content.text))]),e("div",{staticClass:"mobile-banner-actions"},[!t.isLogin&&s.content.secondaryLink?e("a",{staticClass:"mobile-banner-button secondary-btn",attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.navigateTo(s.content.secondaryLink)}}},[t._v(" "+t._s(s.content.secondaryBtnText)+" ")]):t._e(),!t.isLogin&&s.content.primaryLink?e("a",{staticClass:"mobile-banner-button primary-btn",attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.navigateTo(s.content.primaryLink)}}},[t._v(" "+t._s(s.content.primaryBtnText)+" ")]):t._e(),s.content.thirdLink?e("a",{staticClass:"banner-button secondary-btn",attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.navigateTo(s.content.thirdLink)}}},[t._v(" "+t._s(s.content.thirdBtnText)+" ")]):t._e()])])])])})),0),e("div",{staticClass:"mobile-banner-pagination"},t._l(t.bannerImages,(function(s,i){return e("span",{key:i,class:{active:t.mobileCurrentSlide===i},on:{click:function(e){return t.goToSlide(i)}}})})),0)]),e("section",{staticClass:"mobile-section mobile-gpu-section"},[t._m(3),e("div",{staticClass:"mobile-gpu-list"},t._l(t.gpus,(function(s,i){return e("div",{key:i,staticClass:"mobile-gpu-card",class:{recommended:s.recommended},on:{click:function(e){return t.navigateTo("/product")}}},[e("div",{staticClass:"mobile-gpu-header"},[e("h3",{staticClass:"mobile-gpu-name"},[t._v(t._s(s.name))]),e("div",{staticClass:"mobile-gpu-tags"},[s.recommended?e("span",{staticClass:"mobile-recommend-tag"},[t._v("推荐")]):t._e(),s.isNew?e("span",{staticClass:"mobile-new-tag"},[t._v("NEW")]):t._e()])]),e("div",{staticClass:"mobile-gpu-specs"},[e("div",{staticClass:"mobile-spec-item"},[e("span",{staticClass:"mobile-spec-label"},[t._v("单精度:")]),e("span",{staticClass:"mobile-spec-value"},[t._v(t._s(s.singlePrecision)+" TFLOPS")])]),e("div",{staticClass:"mobile-spec-item"},[e("span",{staticClass:"mobile-spec-label"},[t._v("半精度:")]),e("span",{staticClass:"mobile-spec-value"},[t._v(t._s(s.halfPrecision)+" Tensor TFL")])])]),e("div",{staticClass:"mobile-gpu-price"},[s.originalPrice?e("span",{staticClass:"mobile-original-price"},[t._v("¥"+t._s(s.originalPrice)+"/时")]):t._e(),e("span",{staticClass:"mobile-current-price"},[t._v("¥"+t._s(s.price)+"/时")])])])})),0)]),e("section",{staticClass:"mobile-section mobile-comparison-section"},[t._m(4),e("div",{staticClass:"mobile-comparison-container"},[e("div",{staticClass:"mobile-comparison-scroll"},[e("table",{staticClass:"mobile-comparison-table"},[e("thead",[e("tr",[e("th",[t._v("GPU型号")]),t._l(t.comparisonGpus,(function(s){return e("th",{key:s.name},[t._v(t._s(s.name))])}))],2)]),e("tbody",[e("tr",[e("td",[t._v("架构")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.architecture))])}))],2),e("tr",[e("td",[t._v("FP16性能")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.fp16Performance))])}))],2),e("tr",[e("td",[t._v("FP32性能")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.fp32Performance))])}))],2),e("tr",[e("td",[t._v("显存")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.memory))])}))],2),e("tr",[e("td",[t._v("显存类型")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.memoryType))])}))],2),e("tr",[e("td",[t._v("带宽")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.bandwidth))])}))],2)])])])])]),e("section",{staticClass:"mobile-section mobile-services-section"},[t._m(5),e("div",{staticClass:"mobile-services-list"},t._l(t.serviceList,(function(s,i){return e("div",{key:i,staticClass:"mobile-service-card"},[e("div",{staticClass:"mobile-service-icon"},[e("i",{class:s.icon})]),e("h3",{staticClass:"mobile-service-title"},[t._v(t._s(s.title))]),e("p",{staticClass:"mobile-service-desc"},[t._v(t._s(s.desc))])])})),0)]),e("section",{staticClass:"mobile-section mobile-applications-section"},[t._m(6),e("div",{staticClass:"mobile-applications-grid"},t._l(t.mobileApplications,(function(s,i){return e("div",{key:i,staticClass:"mobile-app-item"},[e("div",{staticClass:"mobile-app-image"},[e("img",{attrs:{src:s.image,alt:s.title}})]),e("h3",{staticClass:"mobile-app-title"},[t._v(t._s(s.title))])])})),0)]),e("div",{staticClass:"mobile-consult-section"},[e("h3",{staticClass:"mobile-consult-title"},[t._v("为AI+千行百业，提供高性能算力服务")]),e("button",{staticClass:"mobile-consult-button",on:{click:t.openContactModal}},[t._v("立即咨询")])]),e("transition",{attrs:{name:"mobile-fade"}},[t.showContactModal?e("div",{staticClass:"mobile-contact-overlay",on:{click:function(e){return e.target!==e.currentTarget?null:t.closeContactModal.apply(null,arguments)}}},[e("div",{staticClass:"mobile-contact-modal"},[e("button",{staticClass:"mobile-close-modal",on:{click:t.closeContactModal}},[t._v(" × ")]),e("div",{staticClass:"mobile-contact-content"},[e("div",{staticClass:"mobile-contact-item"},[e("i",{staticClass:"am-icon-user"}),e("span",[t._v(t._s(t.contactInfo.name))])]),e("div",{staticClass:"mobile-contact-item"},[e("i",{staticClass:"am-icon-phone"}),e("span",[t._v(t._s(t.contactInfo.phone))])])]),e("div",{staticClass:"mobile-contact-note"},[e("p",[t._v("欢迎随时来电咨询")])])])]):t._e()])],1):e("div",{staticClass:"desktop-layout"},[e("div",{staticClass:"banner-section"},[e("div",{staticClass:"banner-container"},[e("div",{staticClass:"big-box"},[e("div",{staticClass:"img-box"},[e("div",{staticClass:"show-box",style:{transform:"translateX("+t.translate+")",transition:t.tsion?"all 0.5s":"none"}},t._l(t.bannerImages,(function(s,i){return e("div",{key:i,staticClass:"slide-item"},[e("img",{attrs:{src:s.img,alt:""}}),e("div",{staticClass:"banner-content",class:"pos-"+s.content.position},[e("h2",{staticClass:"banner-title",domProps:{innerHTML:t._s(s.content.title)}}),e("p",{staticClass:"banner-text"},[t._v(t._s(s.content.text))]),e("div",{staticClass:"banner-actions"},[!t.isLogin&&s.content.secondaryLink?e("a",{staticClass:"banner-button secondary-btn",attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.navigateTo(s.content.secondaryLink)}}},[t._v(" "+t._s(s.content.secondaryBtnText)+" ")]):t._e(),!t.isLogin&&s.content.primaryLink?e("a",{staticClass:"banner-button primary-btn",attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.navigateTo(s.content.primaryLink)}}},[t._v(" "+t._s(s.content.primaryBtnText)+" ")]):t._e(),s.content.thirdLink?e("a",{staticClass:"banner-button secondary-btn",attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.navigateTo(s.content.thirdLink)}}},[t._v(" "+t._s(s.content.thirdBtnText)+" ")]):t._e()])])])})),0)]),e("div",{staticClass:"arrowhead-box"},[e("span",{staticClass:"nav-arrow left",on:{click:t.last}},[e("img",{staticClass:"arrow-icon rotated",attrs:{src:s(2594),alt:""}})]),e("span",{staticClass:"nav-arrow right",on:{click:t.next}},[e("img",{staticClass:"arrow-icon",attrs:{src:s(2594),alt:""}})])]),e("div",{ref:"swiperPagination",staticClass:"swiper-pagination"},t._l(t.bannerImages,(function(s,i){return e("span",{key:i,class:{active:t.translateX===i}})})),0)])])]),e("section",{staticClass:"section gpu-section"},[e("div",{staticClass:"container"},[t._m(0),e("div",{staticClass:"gpu-card-grid"},t._l(t.gpus,(function(s,i){return e("div",{key:i,staticClass:"gpu-card",class:{recommended:s.recommended},on:{click:function(e){return t.navigateTo("/product")}}},[e("div",{staticClass:"gpu-card-header"},[e("h3",{staticClass:"gpu-name"},[t._v(t._s(s.name))]),s.recommended?e("span",{staticClass:"recommendation-tag"},[t._v("推荐")]):t._e(),s.isNew?e("span",{staticClass:"new-tag"},[t._v("NEW")]):t._e()]),e("div",{staticClass:"gpu-specs-pricing"},[e("div",{staticClass:"specs-section"},[e("div",{staticClass:"spec-item"},[e("span",{staticClass:"spec-label"},[t._v("单精度:")]),e("span",{staticClass:"spec-value"},[t._v(t._s(s.singlePrecision)+" TFLOPS")])]),e("div",{staticClass:"spec-item"},[e("span",{staticClass:"spec-label"},[t._v("半精度:")]),e("span",{staticClass:"spec-value"},[t._v(t._s(s.halfPrecision)+" Tensor TFL")])])]),e("div",{staticClass:"price-section"},[e("div",{staticClass:"gpu-pricing"},[s.originalPrice?e("span",{staticClass:"original-price"},[t._v("¥"+t._s(s.originalPrice)+"/时")]):t._e(),e("span",{staticClass:"current-price"},[t._v("¥"),e("span",{staticClass:"price-value"},[t._v(t._s(s.price))]),t._v("/时")])])])])])})),0)])]),e("GpuComparison"),e("section",{staticClass:"section services-section"},[e("div",{staticClass:"container"},[t._m(1),e("div",{staticClass:"services-grid"},t._l(t.serviceList,(function(s,i){return e("div",{key:i,staticClass:"service-item"},[e("div",{staticClass:"service-card"},[e("i",{staticClass:"service-icon",class:s.icon}),e("h3",{staticClass:"service-title"},[t._v(t._s(s.title))]),e("div",{staticClass:"service-text"},[e("p",[t._v(t._s(s.desc))])])])])})),0)])]),e("section",{staticClass:"appsec-section"},[t._m(2),e("div",{staticClass:"appsec-container"},[e("div",{staticClass:"appsec-grid"},[e("div",{staticClass:"appsec-item appsec-wide"},[e("div",{staticClass:"appsec-card",on:{mouseover:function(e){t.firstRowWide.hover=!0},mouseleave:function(e){t.firstRowWide.hover=!1}}},[e("div",{staticClass:"appsec-image",class:{"appsec-hover":t.firstRowWide.hover}},[e("img",{attrs:{src:t.firstRowWide.image,alt:t.firstRowWide.title}})]),e("div",{staticClass:"appsec-cardtitle",class:{"appsec-hover":t.firstRowWide.hover}},[t._v(t._s(t.firstRowWide.title))])])]),t._l(t.firstRowTallApps,(function(s,i){return e("div",{key:"tall-"+i,staticClass:"appsec-item appsec-tall"},[e("div",{staticClass:"appsec-card",on:{mouseover:function(t){s.hover=!0},mouseleave:function(t){s.hover=!1}}},[e("div",{staticClass:"appsec-image",class:{"appsec-hover":s.hover}},[e("img",{attrs:{src:s.image,alt:s.title}})]),e("div",{staticClass:"appsec-cardtitle",class:{"appsec-hover":s.hover}},[t._v(t._s(s.title))])])])})),t._l(t.secondRowApps,(function(s,i){return e("div",{key:"small-"+i,staticClass:"appsec-item appsec-small"},[e("div",{staticClass:"appsec-card",on:{mouseover:function(t){s.hover=!0},mouseleave:function(t){s.hover=!1}}},[e("div",{staticClass:"appsec-image",class:{"appsec-hover":s.hover}},[e("img",{attrs:{src:s.image,alt:s.title}})]),e("div",{staticClass:"appsec-cardtitle",class:{"appsec-hover":s.hover}},[t._v(t._s(s.title))])])])})),t._l(t.thirdRowSmallApps,(function(s,i){return e("div",{key:"third-small-"+i,staticClass:"appsec-item appsec-small"},[e("div",{staticClass:"appsec-card",on:{mouseover:function(t){s.hover=!0},mouseleave:function(t){s.hover=!1}}},[e("div",{staticClass:"appsec-image",class:{"appsec-hover":s.hover}},[e("img",{attrs:{src:s.image,alt:s.title}})]),e("div",{staticClass:"appsec-cardtitle",class:{"appsec-hover":s.hover}},[t._v(t._s(s.title))])])])})),e("div",{staticClass:"appsec-item appsec-wide"},[e("div",{staticClass:"appsec-card",on:{mouseover:function(e){t.thirdRowWide.hover=!0},mouseleave:function(e){t.thirdRowWide.hover=!1}}},[e("div",{staticClass:"appsec-image",class:{"appsec-hover":t.thirdRowWide.hover}},[e("img",{attrs:{src:t.thirdRowWide.image,alt:t.thirdRowWide.title}})]),e("div",{staticClass:"appsec-cardtitle",class:{"appsec-hover":t.thirdRowWide.hover}},[t._v(t._s(t.thirdRowWide.title))])])])],2),e("chat-ai")],1)]),e("div",{staticClass:"recommendation-tag1"},[e("div",{staticClass:"card1"},[e("h1",{staticClass:"banner-text1"},[t._v("为AI+千行百业，提供高性能算力服务")]),e("button",{staticClass:"consult-button1",on:{click:t.openContactModal}},[t._v("立即咨询")])])]),e("transition",{attrs:{name:"fade"}},[t.showContactModal?e("div",{staticClass:"contact-modal-overlay",on:{click:function(e){return e.target!==e.currentTarget?null:t.closeContactModal.apply(null,arguments)}}},[e("div",{staticClass:"contact-modal"},[e("button",{staticClass:"close-modal",on:{click:t.closeContactModal}},[t._v(" × ")]),e("div",{staticClass:"contact-content"},[e("div",{staticClass:"contact-item"},[e("i",{staticClass:"am-icon-user"}),e("span",[t._v(t._s(t.contactInfo.name))])]),e("div",{staticClass:"contact-item"},[e("i",{staticClass:"am-icon-phone"}),e("span",[t._v(t._s(t.contactInfo.phone))])])]),e("div",{staticClass:"contact-note"},[e("p",[t._v("欢迎随时来电咨询")])])])]):t._e()])],1),t.isMobile?t._e():e("Mider"),e("Footer"),e("chatAi")],1)},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"section-header"},[e("h2",{staticClass:"section-title"},[t._v("为您推荐")]),e("p",{staticClass:"section-description"},[t._v(" 专注于提供高性能、稳定可靠的 GPU 算力服务，兼具灵活配置与优质性价比。 ")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"section-header"},[e("h2",{staticClass:"section-title"},[t._v("核心优势")]),e("p",{staticClass:"section-description"},[t._v(" 专业铸造优秀,天工开物企业AI变革路上的好伙伴。 ")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"section-header"},[e("h2",{staticClass:"section-title"},[t._v("行业应用")]),e("p",{staticClass:"section-description"},[t._v(" Applications ")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"mobile-section-header"},[e("h2",{staticClass:"mobile-section-title"},[t._v("为您推荐")]),e("p",{staticClass:"mobile-section-description"},[t._v(" 专注于提供高性能、稳定可靠的 GPU 算力服务 ")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"mobile-section-header"},[e("h2",{staticClass:"mobile-section-title"},[t._v("GPU性能对比")]),e("p",{staticClass:"mobile-section-description"},[t._v(" 专业GPU性能详细对比 ")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"mobile-section-header"},[e("h2",{staticClass:"mobile-section-title"},[t._v("核心优势")]),e("p",{staticClass:"mobile-section-description"},[t._v(" 专业铸造优秀,天工开物企业AI变革路上的好伙伴 ")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"mobile-section-header"},[e("h2",{staticClass:"mobile-section-title"},[t._v("行业应用")]),e("p",{staticClass:"mobile-section-description"},[t._v(" Applications ")])])}],n=(s(7658),s(9891)),o=s(9484),c=s(3644),r=s(2711),A=function(){var t=this,e=t._self._c;return e("section",{staticClass:"section gpu-comparison-section"},[e("div",{staticClass:"container"},[t._m(0),e("div",{staticClass:"gpu-comparison-table"},[e("table",[e("thead",[e("tr",[e("th",[t._v("GPU型号")]),t._l(t.comparisonGpus,(function(s){return e("th",{key:s.name},[t._v(t._s(s.name))])}))],2)]),e("tbody",[e("tr",[e("td",[t._v("架构")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.architecture))])}))],2),e("tr",[e("td",[t._v("FP16性能")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.fp16Performance))])}))],2),e("tr",[e("td",[t._v("FP32性能")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.fp32Performance))])}))],2),e("tr",[e("td",[t._v("显存")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.memory))])}))],2),e("tr",[e("td",[t._v("显存类型")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.memoryType))])}))],2),e("tr",[e("td",[t._v("带宽")]),t._l(t.comparisonGpus,(function(s){return e("td",{key:s.name},[t._v(t._s(s.bandwidth))])}))],2)])])])])])},l=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"section-header"},[e("h2",{staticClass:"section-title"},[t._v("GPU性能对比")]),e("p",{staticClass:"section-description"},[t._v(" 专业GPU性能详细对比，助您选择最适合的计算资源 ")])])}],m=s(2223),d={name:"GpuComparison",data(){return{comparisonGpus:[{name:"A100",architecture:"Ampere",fp16Performance:"312 TFLOPS",fp32Performance:"19.5 TFLOPS",memory:"80 GB",memoryType:"HBM2",bandwidth:"2,039 GB/s"},{name:"V100",architecture:"Volta",fp16Performance:"125 TFLOPS",fp32Performance:"15.7 TFLOPS",memory:"32 GB",memoryType:"HBM2",bandwidth:"900 GB/s"},{name:"A6000",architecture:"Ampere",fp16Performance:"77.4 TFLOPS",fp32Performance:"38.7 TFLOPS",memory:"48 GB",memoryType:"GDDR6",bandwidth:"768 GB/s"},{name:"A5000",architecture:"Ampere",fp16Performance:"54.2 TFLOPS",fp32Performance:"27.8 TFLOPS",memory:"24 GB",memoryType:"GDDR6",bandwidth:"768 GB/s"},{name:"A4000",architecture:"Ampere",fp16Performance:"19.17 TFLOPS",fp32Performance:"19.17 TFLOPS",memory:"16 GB",memoryType:"GDDR6",bandwidth:"448 GB/s"}]}},created(){this.fetchComparison()},methods:{async fetchComparison(){try{(0,m.xf)("/system/comparison/list").then((t=>{this.comparisonGpus=t.data.rows.map((t=>({...t,fp16Performance:t.fp16performance,fp32Performance:t.fp32performance,memoryType:t.memorytype})))}))}catch(t){console.error("获取GPU推荐列表失败:",t)}}}},p=d,u=s(1001),h=(0,u.Z)(p,A,l,!1,null,"fed986aa",null),v=h.exports,C=s(1836),g={name:"IndexView",components:{Layout:n.Z,chatAi:o.Z,Footer:r.Z,Mider:c.Z,GpuComparison:v},computed:{translate(){return 100*-this.translateX+"%"},isLogin(){return!!document.cookie.includes("Admin-Token")},mobileTranslateX(){return 100*-this.mobileCurrentSlide},mobileApplications(){return[this.firstRowWide,...this.firstRowTallApps,...this.secondRowApps,...this.thirdRowSmallApps,this.thirdRowWide]}},data(){return{touchStartX:0,touchEndX:0,touchThreshold:50,isMobile:!1,mobileCurrentSlide:0,showContactModal:!1,contactInfo:{name:"王先生",phone:"13913283376"},tabIndex:0,firstRowWide:{title:"工业制造",image:s(5647),hover:!1},firstRowTallApps:[{title:"自动驾驶",image:s(3494),hover:!1},{title:"智能交通",image:s(2951),hover:!1}],secondRowApps:[{title:"智慧农业",image:s(241),hover:!1},{title:"影视渲染",image:s(3846),hover:!1}],thirdRowSmallApps:[{title:"医疗影像",image:s(472),hover:!1},{title:"金融风暴",image:s(8319),hover:!1}],thirdRowWide:{title:"能源科技",image:s(218),hover:!1},bannerImages:[{img:s(8067),content:{title:"天工开物",text:"构建AI应用周期服务的一站式算力云",position:"left",thirdLink:"/product",thirdBtnText:"立即购买"}},{img:s(4675),content:{title:"专业AI训练平台",text:"为AI+千行百业，提供高性能算力服务",position:"left",secondaryLink:"/login",secondaryBtnText:"立即登录",primaryLink:"/register",primaryBtnText:"立即注册"}},{img:s(9728),content:{title:"企业级GPU集群",text:"H100/H800/RTX 4090等高性能GPU随时可用，按需付费",position:"left"}}],translateX:0,tsion:!0,tabList:[],swiperOptions:{loop:!0,autoplay:{delay:5e3,disableOnInteraction:!1},pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}},serviceList:[{id:1,icon:"am-icon-shield",title:"数据安全",desc:"平台采取各种措施保证用户的数据安全，例如数据加密、防火墙、漏洞扫描和安全审计等措施，以防止数据泄露、篡改和丢失等风险。"},{id:2,icon:"am-icon-sliders",title:"部署灵活",desc:"租用GPU服务器具备比购买更高的灵活性，用户可以根据自己的需求随时调整租赁资源的配置及数量。"},{id:3,icon:"am-icon-server",title:"高可靠性",desc:"平台拥有完善的运维体系，采用丰富的数据备份、冗余技术以及定期检测等机制，保证租赁服务器的稳定性、易用性和安全性。"},{id:4,icon:"am-icon-rocket",title:"高处理性能",desc:"采用先进的GPU集群架构，平台能够大大提高计算速度和处理能力，可以在科学计算、深度学习等领域有更优秀的表现。"},{id:5,icon:"am-icon-credit-card",title:"低成本",desc:"购买GPU服务器需要投入较高的资金，而租赁可以让用户以较低的成本去获得相关基础设施，减轻了用户在预算上的负担。"},{id:6,icon:"am-icon-phone",title:"及时服务",desc:"提供365天 7*24小时技术支持及运维服务， 工程师现场及在线响应及电话支持。"}],comparisonGpus:[{name:"A100",architecture:"Ampere",fp16Performance:"312 TFLOPS",fp32Performance:"19.5 TFLOPS",memory:"80 GB",memoryType:"HBM2",bandwidth:"2,039 GB/s"},{name:"A100",architecture:"Ampere",fp16Performance:"312 TFLOPS",fp32Performance:"19.5 TFLOPS",memory:"80 GB",memoryType:"HBM2",bandwidth:"2,039 GB/s"},{name:"A100",architecture:"Ampere",fp16Performance:"312 TFLOPS",fp32Performance:"19.5 TFLOPS",memory:"80 GB",memoryType:"HBM2",bandwidth:"2,039 GB/s"},{name:"V100",architecture:"Volta",fp16Performance:"125 TFLOPS",fp32Performance:"15.7 TFLOPS",memory:"32 GB",memoryType:"HBM2",bandwidth:"900 GB/s"},{name:"A6000",architecture:"Ampere",fp16Performance:"77.4 TFLOPS",fp32Performance:"38.7 TFLOPS",memory:"48 GB",memoryType:"GDDR6",bandwidth:"768 GB/s"},{name:"A5000",architecture:"Ampere",fp16Performance:"54.2 TFLOPS",fp32Performance:"27.8 TFLOPS",memory:"24 GB",memoryType:"GDDR6",bandwidth:"768 GB/s"},{name:"A4000",architecture:"Ampere",fp16Performance:"19.17 TFLOPS",fp32Performance:"19.17 TFLOPS",memory:"16 GB",memoryType:"GDDR6",bandwidth:"448 GB/s"}],gpus:[{name:"A100 SXM4 / 80GB",singlePrecision:"156",halfPrecision:"19.5",originalPrice:"11.10",price:"6.66",recommended:!0,isNew:!1},{name:"RTX 3090 / 24GB",singlePrecision:"35.58",halfPrecision:"71.2",originalPrice:"2.30",price:"1.38",recommended:!0,isNew:!1},{name:"A100 PCIE / 80GB",singlePrecision:"156",halfPrecision:"19.5",originalPrice:"11.10",price:"6.66",recommended:!0,isNew:!1},{name:"RTX 4090 / 24GB",singlePrecision:"82.58",halfPrecision:"164.5",originalPrice:"2.97",price:"1.78",recommended:!1,isNew:!0},{name:"RTX 4090D / 24GB",singlePrecision:"73.54",halfPrecision:"147.1",originalPrice:"2.93",price:"1.76",recommended:!1,isNew:!1},{name:"RTX 3060 / 12GB",singlePrecision:"12.7",halfPrecision:"51.2",originalPrice:"1.00",price:"0.60",recommended:!1,isNew:!1},{name:"RTX A4000 / 16GB",singlePrecision:"19.17",halfPrecision:"76.7",originalPrice:"1.53",price:"0.92",recommended:!1,isNew:!1},{name:"Tesla P40 / 24GB",singlePrecision:"5.9",halfPrecision:"11.76",originalPrice:"1.35",price:"0.81",recommended:!1,isNew:!1}],activeIndex:0}},created(){this.fetchRecommendations();const t=new URL(window.location.href),e=t.searchParams.get("token");if(e){const s=localStorage.getItem("hasRefreshedWithToken");s?(window.location.href=t.origin+t.pathname,localStorage.setItem("hasRefreshedWithToken","false")):((0,C.o4)(e),localStorage.setItem("hasRefreshedWithToken","true"))}},mounted(){this.checkIsMobile(),window.addEventListener("resize",this.checkIsMobile),this.desktopInterval=setInterval((()=>{this.next()}),5e3),this.mobileInterval=setInterval((()=>{this.mobileNext()}),5e3)},beforeDestroy(){window.removeEventListener("resize",this.checkIsMobile),clearInterval(this.desktopInterval),clearInterval(this.mobileInterval)},methods:{async fetchRecommendations(){try{(0,m.xf)("/system/recommend/list").then((t=>{this.gpus=t.data.rows.map((t=>({...t,isNew:0===t.isnew,recommended:0===t.recommended})))}))}catch(t){console.error("获取GPU推荐列表失败:",t)}},handleTouchStart(t){this.touchStartX=t.touches[0].clientX},handleTouchMove(t){this.touchEndX=t.touches[0].clientX},handleTouchEnd(){if(!this.touchStartX||!this.touchEndX)return;const t=this.touchStartX-this.touchEndX;t>this.touchThreshold&&this.mobileNext(),t<-this.touchThreshold&&this.mobilePrev(),this.touchStartX=0,this.touchEndX=0},mobilePrev(){this.mobileCurrentSlide=(this.mobileCurrentSlide-1+this.bannerImages.length)%this.bannerImages.length,this.resetMobileInterval()},mobileNext(){this.mobileCurrentSlide=(this.mobileCurrentSlide+1)%this.bannerImages.length,this.resetMobileInterval()},resetMobileInterval(){clearInterval(this.mobileInterval),this.mobileInterval=setInterval((()=>{this.mobileNext()}),5e3)},checkIsMobile(){this.isMobile=window.innerWidth<=768},openContactModal(){this.showContactModal=!0},closeContactModal(){this.showContactModal=!1},handleBannerAction(t){t.content.link&&this.$router.push(t.content.link),this.$ga.event("Banner","click",t.content.title)},navigateTo(t){this.currentPath&&this.currentPath!==t?(this.previousActivePath=this.currentPath,this.$nextTick((()=>{const e=document.querySelectorAll(".nav-link, .btn-login");e.forEach((e=>{(e.classList.contains("active")||"/login"===t&&e.classList.contains("btn-login"))&&!e.classList.contains("active-exit")&&(e.classList.add("active-exit"),setTimeout((()=>{e.classList.remove("active-exit")}),300))})),this.currentPath=t}))):this.currentPath=t,this.$route.path===t?this.$nextTick((()=>{window.scrollTo({top:0,behavior:"instant"}),this.$router.go(0)})):(this.$router.push(t),window.scrollTo({top:0,behavior:"instant"}))},last(){this.translateX=(this.translateX-1+this.bannerImages.length)%this.bannerImages.length,this.tsion=!0},next(){this.translateX=(this.translateX+1)%this.bannerImages.length,this.tsion=!0},goToSlide(t){this.mobileCurrentSlide=t,clearInterval(this.mobileInterval),this.mobileInterval=setInterval((()=>{this.mobileNext()}),5e3)},changeTab(t){this.tabIndex=t},useGpu(t){console.log(`Selected GPU: ${t.name}`)},getCustomSolution(){console.log("Get Custom Solution")},contactUs(){console.log("Contact Us")},prevSlide(){this.activeIndex=this.activeIndex>0?this.activeIndex-1:this.slideshow.length-1},nextSlide(){this.activeIndex=this.activeIndex<this.slideshow.length-1?this.activeIndex+1:0},selectGpu(t){console.log(`Selected GPU: ${t.name}`)}},watch:{translateX(t){const e=this.$refs.swiperPagination?.querySelectorAll("span");e&&e.forEach(((e,s)=>{e.classList.toggle("active",s===t)}))}}},f=g,b=(0,u.Z)(f,i,a,!1,null,"5d515a4a",null),_=b.exports},8067:function(t,e,s){t.exports=s.p+"img/back1.46426188.webp"},4675:function(t,e,s){t.exports=s.p+"img/back2.cc7c82c6.webp"},9728:function(t,e,s){t.exports=s.p+"img/back3.bc8101d7.webp"},5647:function(t,e,s){t.exports=s.p+"img/gongyezhizao.edd12f1d.webp"},8319:function(t,e,s){t.exports=s.p+"img/jinrongfengbao.dabc07d5.webp"},218:function(t,e,s){t.exports=s.p+"img/nengyuankeji.0808ec17.webp"},2594:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAMAAADDpiTIAAAAA3NCSVQICAjb4U/gAAAACXBIWXMAAA5nAAAOZwGPiYJxAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAAfhQTFRF////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAe7yhUAAAAKd0Uk5TAAECAwQGBwgJCgsMDg8QERIUFRYXGRobHh8gISQlJygpKissLS8wMjQ2Nzg7PD4/QEFFRkdISUxOUVVWV1hZW1xdYGFlZmdoa2xtbm9yd3t/gIKDhoeIjI6Pk5WWmJqbnZ+gpaiqq6ytr7Cys7W2t7i5uru8vb6/wMPGysvMzdDR0tPU1tfY2dvf4eLj5OXm5+jp6uvs7e7v8PLz9PX29/j5+vv8/f6Ucmt/AAAG8ElEQVR42u3c+ZfNBRzG8RlEYdCeZRqhReuEFKVEm0pRTLvKUkYbI1pQKaWNLGmSlJmGz7/Zr3XOMHNOJ2fm+7yeP+Hzfp1zz733e29Ly8jahHufe/3dfYf7jn2+/c11y9paLGlXrOw+Xf9c/84npztLyuZsO1uD7JM7nSZh0zcOmr+q6r0bnafpm/TSmTr/zm26xokavZlf1oX303xHavDu+rmG2p8POVNj91h/DWNdY1yqmVtXw9uWcW7VxK2v4e4tAqL7E5Den4D0/gSk9ycgvT8B6f0JSO9PQHp/AtL7E5Den4D0/gSk9ycgvT8B6f0JSO9PwGjd01UEBG/BQBEQvPbeKgJy1/Z1FQHB21xFQPBuqSIgeT1FQPI6qwhI3qdFQPJuryIgeV1FQPS+KgKSN6OKgOQ9UQREb2sREL09RUD0vi8CotdXBCRvahUByesoAqJ3XREQvfFFQPZ6CcjegSIgeruLgOi9XARE754iIPttwO8EZG9bERC9R4qA6F15moDsdRUB0Zv6KwHZe7YIiN7EowRkb2kRkL3nCcheazcB2Zu0n4DszTxCQPY6CCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIaOhmE0AAAQQQQAABBBBAAAGZAo4SQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAaN1NxBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAHNFXCMAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIICAZm4OAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEDB6BRwngAACCCCAAAIIIIAAAhI3lwACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAAC/ts2uHm4gKfcPFvAwAI3zxbQ2+7m2QL2j3XzbAErnTxbwA/jnTxbwGoXzxZwYrKLZwtY5eDZAra7d7aAM5PcO1vAEufOFvCGa2cL+M6xR+TmXSwB/a2OnS3gcrfOFjDXqbMFeCpg5Ar45WIAeNihR+ru67sYAB506Oj+1enS0f2rw6mj+1ebW0f3/8Oto/vXAceO7l8vunZ0/7rbuaP7n/Qb0ej+tdm9o/vXMgeP7n/QK0B0/7rfxaP7f+ZxoOj+tcjJo/u/7+TR/b+d6ubJ/U/6Iji6/9nFbp7c318DhPdf6+b6m/6mv+lv+pv+pr/pb/qb/qa/6W/6m/6mv+lv+pv+pr/pb/qb/qa/6W/6m/6mv+lv+pv+pr/pb/qb/qa/6a+//vrrr7/++uuvv/7666+//vrrr7/++uuvv/7666+//vrrr7/++uuvv/7666+//vrrr7/++uuvv/7666+//vrrr7/++uuvv+lv+pv+pr/pb/qb/qa/6W/6m/6mv+lv+pv+pr/pb/qb/qa/6W/6m/6mv+lv+pv+pr/pb/qb/qa/6W/6m/7666+//vrrr7/++uuvv/7666+//vrrr7/++uuvv/7666+//vrrr7/++uuvv/7666+//vrrr7/++uuvv/7666+//vrrr7/++ru5/qa/6W/6m/6mv+lv+pv+pr/pb/qb/qa/6W/6m/6mv+lv+pv+pr/pb/qb/qa/6W/6m/6mv+lv+pv+pr/pb/rrr7/++uuvv/76669/zhbqH73rT+qfvCkH9U/e2B79o/eC/tGbPaB/9Lr1j96t+mfvI/2j16l/9l7VP3s/6h+9m/XP3lr9s7dP/+yd0j96E/XP3iz9s3eH/tlbqn/2VuifvcX6Z+8m/bN3lf7ZGzOgf/YO6Z+9Tfpn7wH9sze5X//s9eifvVX6Z29Kr/7Ze0b/7F16WP/sPap/9sZ9o3/25p3SP3tLzumfvTX6Z6/1bf2zd9ku/cM/Ddilf7iAD/UnQH8C9A8WsFN/AvQnQP9gAT36Z29Cj/4E6B8tYIf+BOhPgP7BArbrT4D+0QI+GGb/NW7VzI17ZTj5z6xwqcbu8aF/N3zkNmdq8DpPDNF/77WO1OjN2Hqh/KfXT3Cipm/+7vPl/+u1q50nYQv3DJZ/YHO706Rs1uqP//1fcr9tWT7NWaI2bXnXxh1fHO87tPedDWsXXeIg//f+BmieZkzoaEKrAAAAAElFTkSuQmCC"},472:function(t,e,s){t.exports=s.p+"img/yiliaoyingxiang.c263d939.webp"},3846:function(t,e,s){t.exports=s.p+"img/yingshixuanran.4c630655.webp"},241:function(t,e,s){t.exports=s.p+"img/zhihuinongye.96987a71.webp"},2951:function(t,e,s){t.exports=s.p+"img/zhinengjiaotong.9cfabb6d.webp"},3494:function(t,e,s){t.exports=s.p+"img/zidongjiashi.4f0df4fb.webp"}}]);
//# sourceMappingURL=964.dd19bcfb.js.map