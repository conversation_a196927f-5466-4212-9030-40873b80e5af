{"ast": null, "code": "import { getNotAuth } from \"@/api/login\";\nexport default {\n  name: 'GpuComparison',\n  data() {\n    return {\n      comparisonGpus: [{\n        name: 'A100',\n        architecture: 'Ampere',\n        fp16Performance: '312 TFLOPS',\n        fp32Performance: '19.5 TFLOPS',\n        memory: '80 GB',\n        memoryType: 'HBM2',\n        bandwidth: '2,039 GB/s'\n      }, {\n        name: 'V100',\n        architecture: 'Volta',\n        fp16Performance: '125 TFLOPS',\n        fp32Performance: '15.7 TFLOPS',\n        memory: '32 GB',\n        memoryType: 'HBM2',\n        bandwidth: '900 GB/s'\n      }, {\n        name: 'A6000',\n        architecture: 'Ampere',\n        fp16Performance: '77.4 TFLOPS',\n        fp32Performance: '38.7 TFLOPS',\n        memory: '48 GB',\n        memoryType: 'GDDR6',\n        bandwidth: '768 GB/s'\n      }, {\n        name: 'A5000',\n        architecture: 'Ampere',\n        fp16Performance: '54.2 TFLOPS',\n        fp32Performance: '27.8 TFLOPS',\n        memory: '24 GB',\n        memoryType: 'GDDR6',\n        bandwidth: '768 GB/s'\n      }, {\n        name: 'A4000',\n        architecture: 'Ampere',\n        fp16Performance: '19.17 TFLOPS',\n        fp32Performance: '19.17 TFLOPS',\n        memory: '16 GB',\n        memoryType: 'GDDR6',\n        bandwidth: '448 GB/s'\n      }]\n    };\n  },\n  created() {\n    this.fetchComparison();\n  },\n  methods: {\n    async fetchComparison() {\n      try {\n        // console.log(\"开始获取对比数据\")\n        getNotAuth(\"/system/comparison/list\").then(req => {\n          // console.log(\"原始数据\",req.data.rows)\n          this.comparisonGpus = req.data.rows.map(item => ({\n            ...item,\n            fp16Performance: item.fp16performance,\n            fp32Performance: item.fp32performance,\n            memoryType: item.memorytype\n          }));\n          // this.gpus = req.data.rows\n          // console.log(\"数据\",this.gpus)\n        });\n      } catch (error) {\n        console.error('获取GPU推荐列表失败:', error);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["getNotAuth", "name", "data", "comparisonGpus", "architecture", "fp16Performance", "fp32Performance", "memory", "memoryType", "bandwidth", "created", "fetchComparison", "methods", "then", "req", "rows", "map", "item", "fp16performance", "fp32performance", "memorytype", "error", "console"], "sources": ["src/views/Index/GpuComparison.vue"], "sourcesContent": ["<template>\r\n  <section class=\"section gpu-comparison-section\">\r\n    <div class=\"container\">\r\n      <div class=\"section-header\">\r\n        <h2 class=\"section-title\">GPU性能对比</h2>\r\n        <p class=\"section-description\">\r\n          专业GPU性能详细对比，助您选择最适合的计算资源\r\n        </p>\r\n      </div>\r\n\r\n      <div class=\"gpu-comparison-table\">\r\n        <table>\r\n          <thead>\r\n          <tr>\r\n            <th>GPU型号</th>\r\n            <th v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.name }}</th>\r\n          </tr>\r\n          </thead>\r\n          <tbody>\r\n          <tr>\r\n            <td>架构</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.architecture }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>FP16性能</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp16Performance }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>FP32性能</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp32Performance }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>显存</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memory }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>显存类型</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memoryType }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>带宽</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.bandwidth }}</td>\r\n          </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </div>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport {getNotAuth} from \"@/api/login\";\r\n\r\nexport default {\r\n  name: 'GpuComparison',\r\n  data() {\r\n    return {\r\n      comparisonGpus: [\r\n        {\r\n          name: 'A100',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '312 TFLOPS',\r\n          fp32Performance: '19.5 TFLOPS',\r\n          memory: '80 GB',\r\n          memoryType: 'HBM2',\r\n          bandwidth: '2,039 GB/s'\r\n        },\r\n        {\r\n          name: 'V100',\r\n          architecture: 'Volta',\r\n          fp16Performance: '125 TFLOPS',\r\n          fp32Performance: '15.7 TFLOPS',\r\n          memory: '32 GB',\r\n          memoryType: 'HBM2',\r\n          bandwidth: '900 GB/s'\r\n        },\r\n        {\r\n          name: 'A6000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '77.4 TFLOPS',\r\n          fp32Performance: '38.7 TFLOPS',\r\n          memory: '48 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '768 GB/s'\r\n        },\r\n        {\r\n          name: 'A5000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '54.2 TFLOPS',\r\n          fp32Performance: '27.8 TFLOPS',\r\n          memory: '24 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '768 GB/s'\r\n        },\r\n        {\r\n          name: 'A4000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '19.17 TFLOPS',\r\n          fp32Performance: '19.17 TFLOPS',\r\n          memory: '16 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '448 GB/s'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchComparison();\r\n  },\r\n  methods: {\r\n    async fetchComparison() {\r\n      try {\r\n        // console.log(\"开始获取对比数据\")\r\n        getNotAuth(\"/system/comparison/list\").then(req =>{\r\n          // console.log(\"原始数据\",req.data.rows)\r\n          this.comparisonGpus = req.data.rows.map(item => ({\r\n            ...item,\r\n            fp16Performance: item.fp16performance,\r\n            fp32Performance: item.fp32performance,\r\n            memoryType:item.memorytype\r\n          }))\r\n          // this.gpus = req.data.rows\r\n          // console.log(\"数据\",this.gpus)\r\n        })\r\n      } catch (error) {\r\n        console.error('获取GPU推荐列表失败:', error);\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.gpu-comparison-section {\r\n  /*padding-top: 10vh;*/\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n  padding-bottom: 15vh;\r\n}\r\n\r\n.container {\r\n  width: 93%;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.section-header {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 28px;\r\n  color: #333;\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.section-title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -5px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 50px;\r\n  height: 3px;\r\n  background-color: #2196f3;\r\n}\r\n\r\n.section-description {\r\n  color: #666;\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n.gpu-comparison-table {\r\n  width: 100%;\r\n  padding: 0;\r\n  border-collapse: collapse;\r\n  margin: 0;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  overflow-x: auto;\r\n}\r\n\r\n.gpu-comparison-table table {\r\n  width: 100%;\r\n  min-width: 1000px;\r\n}\r\n\r\n.gpu-comparison-table thead {\r\n  background-color: #cddfec;\r\n}\r\n\r\n.gpu-comparison-table thead tr th,\r\n.gpu-comparison-table tbody tr td {\r\n  padding: 18px 25px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  min-width: 180px;\r\n  border-bottom: 1px solid #e0e5eb;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.gpu-comparison-table thead tr th {\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.gpu-comparison-table tbody tr td {\r\n  color: #333;\r\n  font-weight: normal;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:nth-child(even) {\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:hover {\r\n  background-color: #f1f6fd;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .container {\r\n    width: 98%;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .section-title {\r\n    font-size: 26px;\r\n    color: #333;\r\n  }\r\n\r\n  .section-description {\r\n    font-size: 16px;\r\n  }\r\n}\r\n</style>"], "mappings": "AAmDA,SAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,cAAA,GACA;QACAF,IAAA;QACAG,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA,GACA;QACAR,IAAA;QACAG,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA,GACA;QACAR,IAAA;QACAG,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA,GACA;QACAR,IAAA;QACAG,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA,GACA;QACAR,IAAA;QACAG,YAAA;QACAC,eAAA;QACAC,eAAA;QACAC,MAAA;QACAC,UAAA;QACAC,SAAA;MACA;IAEA;EACA;EACAC,QAAA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IACA,MAAAD,gBAAA;MACA;QACA;QACAX,UAAA,4BAAAa,IAAA,CAAAC,GAAA;UACA;UACA,KAAAX,cAAA,GAAAW,GAAA,CAAAZ,IAAA,CAAAa,IAAA,CAAAC,GAAA,CAAAC,IAAA;YACA,GAAAA,IAAA;YACAZ,eAAA,EAAAY,IAAA,CAAAC,eAAA;YACAZ,eAAA,EAAAW,IAAA,CAAAE,eAAA;YACAX,UAAA,EAAAS,IAAA,CAAAG;UACA;UACA;UACA;QACA;MACA,SAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,iBAAAA,KAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}