{"ast": null, "code": "export function formatDateTime(inputStr) {\n  // 1. 将 \"+\" 替换为空格（如果输入是URL编码后的格式）\n  const normalizedStr = inputStr.replace('+', ' ');\n  // 2. 解析为Date对象（注意时区问题）\n  const date = new Date(normalizedStr);\n  // 3. 格式化为 yyyy-MM-dd HH:mm:ss\n  const pad = num => num.toString().padStart(2, '0');\n  const year = date.getFullYear();\n  const month = pad(date.getMonth() + 1); // 月份从0开始\n  const day = pad(date.getDate());\n  const hours = pad(date.getHours());\n  const minutes = pad(date.getMinutes());\n  const seconds = pad(date.getSeconds());\n  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n}", "map": {"version": 3, "names": ["formatDateTime", "inputStr", "normalizedStr", "replace", "date", "Date", "pad", "num", "toString", "padStart", "year", "getFullYear", "month", "getMonth", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/utils/component.js"], "sourcesContent": ["export function formatDateTime(inputStr) {\r\n    // 1. 将 \"+\" 替换为空格（如果输入是URL编码后的格式）\r\n    const normalizedStr = inputStr.replace('+', ' ');\r\n    // 2. 解析为Date对象（注意时区问题）\r\n    const date = new Date(normalizedStr);\r\n    // 3. 格式化为 yyyy-MM-dd HH:mm:ss\r\n    const pad = (num) => num.toString().padStart(2, '0');\r\n    const year = date.getFullYear();\r\n    const month = pad(date.getMonth() + 1); // 月份从0开始\r\n    const day = pad(date.getDate());\r\n    const hours = pad(date.getHours());\r\n    const minutes = pad(date.getMinutes());\r\n    const seconds = pad(date.getSeconds());\r\n    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n}"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAACC,QAAQ,EAAE;EACrC;EACA,MAAMC,aAAa,GAAGD,QAAQ,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAChD;EACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACH,aAAa,CAAC;EACpC;EACA,MAAMI,GAAG,GAAIC,GAAG,IAAKA,GAAG,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACpD,MAAMC,IAAI,GAAGN,IAAI,CAACO,WAAW,EAAE;EAC/B,MAAMC,KAAK,GAAGN,GAAG,CAACF,IAAI,CAACS,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMC,GAAG,GAAGR,GAAG,CAACF,IAAI,CAACW,OAAO,EAAE,CAAC;EAC/B,MAAMC,KAAK,GAAGV,GAAG,CAACF,IAAI,CAACa,QAAQ,EAAE,CAAC;EAClC,MAAMC,OAAO,GAAGZ,GAAG,CAACF,IAAI,CAACe,UAAU,EAAE,CAAC;EACtC,MAAMC,OAAO,GAAGd,GAAG,CAACF,IAAI,CAACiB,UAAU,EAAE,CAAC;EACtC,OAAQ,GAAEX,IAAK,IAAGE,KAAM,IAAGE,GAAI,IAAGE,KAAM,IAAGE,OAAQ,IAAGE,OAAQ,EAAC;AACnE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}