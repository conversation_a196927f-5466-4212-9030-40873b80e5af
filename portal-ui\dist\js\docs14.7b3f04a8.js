"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[370],{1273:function(o,t,n){n.r(t);var l=new URL(n(6097),n.b),r=new URL(n(9705),n.b),f=new URL(n(2155),n.b),s=new URL(n(7890),n.b),e=new URL(n(1052),n.b),p=new URL(n(6604),n.b),c=new URL(n(7254),n.b),g=new URL(n(6649),n.b),i=new URL(n(4869),n.b),y=new URL(n(4716),n.b),a=new URL(n(7705),n.b),u=new URL(n(5301),n.b),b=new URL(n(1466),n.b),h=new URL(n(6850),n.b),d=new URL(n(5041),n.b),m='<h1 id="快速开始">快速开始</h1> <blockquote> <p><font style="color:#020817">前提条件：请先在<a href="https://tiangongkaiwu.top/#/login">https://tiangongkaiwu.top/#/login</a>完成注册与登录</font></p> </blockquote> <hr> <blockquote> <p><font style="color:#020817">一共有两种方法：利用我们预制的镜像快速发布任务和使用用户自定义打包的镜像发布任务</font></p> </blockquote> <h2 id="1、用预制镜像发布任务">1、用预制镜像发布任务</h2> <h3 id="11第一步：新增部署任务">1.1第一步：新增部署任务<font style="background-color:#cef5f7"></font></h3> <p>进入<font style="background-color:#e7e9e8"> </font><font style="color:#2f8ef4;background-color:#e7e9e8">新增部署任务 </font>界面：<a href="https://tiangongkaiwu.top/#/console">https://tiangongkaiwu.top/#/console</a></p> <p><img src="'+l+'" alt=""></p> <h3 id="12第二步：填写任务信息">1.2第二步：填写任务信息</h3> <h4 id="121选择资源">1.2.1选择资源</h4> <p><font style="color:#020817">根据您的需要筛选 GPU，包括所在区域、显存要求、CPU 核心数、内存大小等。</font></p> <p><img src="'+r+'" alt=""></p> <p><font style="color:#020817">选择 GPU 型号：</font><strong><font style="color:#020817">推荐选择不限区域——享受全国闲置算力资源 随取随用</font></strong></p> <p><font style="color:#020817">GPU 型号推荐配置：随便选</font></p> <ol> <li><strong><font style="color:#020817">显存完全达标</font></strong><font style="color:#020817">：</font></li> </ol> <ul> <li><font style="color:#020817">图片中所有型号单卡均提供</font><strong><font style="color:#020817"> 24GB 以上显存</font></strong><font style="color:#020817">，远超服务部署启动的要求，无需担心显存不足。</font></li> </ul> <ol start="2"> <li><strong><font style="color:#020817">性价比与灵活性</font></strong><font style="color:#020817">：</font></li> </ol> <ul> <li><strong><font style="color:#020817">1 卡配置</font></strong><font style="color:#020817">：适合短期测试或常规任务。</font></li> <li><strong><font style="color:#020817">库存充足</font></strong><font style="color:#020817">：1 卡版本库存多台，无需等待，快速部署。</font></li> <li><strong><font style="color:#020817">多卡选项备用</font></strong><font style="color:#020817">：若未来需要分布式训练或高并发任务，可随时切换 2 卡/4 卡。</font></li> </ul> <ol start="3"> <li><strong><font style="color:#020817">简化选择逻辑</font></strong><font style="color:#020817">：</font></li> </ol> <ul> <li><strong><font style="color:#020817">无特殊需求</font></strong><font style="color:#020817">：如果只是运行文档镜像中的常规任务（如模型推理、数据处理），单卡性能完全足够。</font></li> <li><strong><font style="color:#020817">兼容性无忧</font></strong><font style="color:#020817">：镜像仓库默认适配主流 GPU 型号（如 4090），无需额外调试。</font></li> </ul> <p><font style="color:#020817">操作建议</font></p> <ul> <li><strong><font style="color:#020817">随便选 直接勾选「4090（1 卡）」+「1 个节点」</font></strong><font style="color:#020817">，填写服务名称，快速完成部署。</font></li> <li><strong><font style="color:#020817">进阶场景</font></strong><font style="color:#020817">：仅当需要训练大模型（如 LLM、多模态）时，再考虑 2 卡/4 卡配置。</font></li> </ul> <p><strong><font style="color:#020817">图片中的配置均为“无坑选项”，闭眼选 1 卡即可开箱即用，成本可控，资源立即可用。</font></strong></p> <p><font style="color:#020817">点击选择某一型号 GPU 后，可配置任务需要的计算节点数量。至少选择 1 个节点，最多可选 20 个节点，建议先启动 1 个节点进行验证，运行稳定后可横向扩展至所需规模。</font></p> <p><font style="color:#020817">选择 GPU 和节点数量后，继续填写服务配置信息</font></p> <h4 id="122-定义服务配置信息"><font style="color:#020817">1.2.2 定义服务配置信息</font></h4> <p><font style="color:#020817">按照需求选择我们预制好的镜像，同时可以通过文档链接快速了解容器化部署对应服务的步骤。</font></p> <p><img src="'+f+'" alt=""></p> <p><font style="color:#020817">这里我们选择了Jupyter Lab的 CPU 服务信息作为示例</font></p> <p><img src="'+s+'" alt=""></p> <p><font style="color:#020817">选择完成后，点击【部署服务】按钮，发布任务。</font></p> <h3 id="13第三步：查看运行状态"><font style="color:#020817">1.3第三步：查看运行状态</font></h3> <p><font style="color:#020817">发布任务后会自动跳转至任务详情界面，等待节点分配。</font></p> <p><img src="'+e+'" alt=""></p> <p><font style="color:#020817">节点分配完成后，点击【公开访问】中的任一链接均可正常访问该服务</font></p> <p><strong><font style="color:#020817">JupyterLab 建议只用一个节点</font></strong><font style="color:#020817">，不然可能导致异常</font></p> <p><font style="color:#020817">节点分配完成后，可以通过点击回传链接访问服务：</font></p> <p><img src="'+p+'" alt=""></p> <p><font style="color:#020817">进入 JupyterLab 服务：</font></p> <p><img src="'+c+'" alt=""></p> <h2 id="2、使用用户自定义打包的镜像发布任务"><font style="color:#020817">2、使用用户自定义打包的镜像发布任务</font></h2> <h3 id="21第一步：新增部署任务">2.1第一步：新增部署任务</h3> <p>进入<font style="background-color:#e7e9e8"> </font><font style="color:#2f8ef4;background-color:#e7e9e8">新增部署任务 </font>界面：<a href="https://tiangongkaiwu.top/#/console">https://tiangongkaiwu.top/#/console</a></p> <p><img src="'+g+'" alt=""></p> <h3 id="22第二步：填写任务信息">2.2第二步：填写任务信息</h3> <h4 id="221选择资源">2.2.1选择资源</h4> <p><font style="color:#020817">根据您的需要筛选 GPU，包括所在区域、显存要求、CPU 核心数、内存大小等。</font></p> <p><img src="'+i+'" alt=""></p> <p><font style="color:#020817">选择 GPU 型号：</font><strong><font style="color:#020817">推荐选择不限区域——享受全国闲置算力资源 随取随用</font></strong></p> <p><font style="color:#020817">GPU 型号推荐配置：随便选</font></p> <ol> <li><strong><font style="color:#020817">显存完全达标</font></strong><font style="color:#020817">：</font></li> </ol> <ul> <li><font style="color:#020817">图片中所有型号单卡均提供</font><strong><font style="color:#020817"> 24GB 以上显存</font></strong><font style="color:#020817">，远超服务部署启动的要求，无需担心显存不足。</font></li> </ul> <ol start="2"> <li><strong><font style="color:#020817">性价比与灵活性</font></strong><font style="color:#020817">：</font></li> </ol> <ul> <li><strong><font style="color:#020817">1 卡配置</font></strong><font style="color:#020817">：适合短期测试或常规任务。</font></li> <li><strong><font style="color:#020817">库存充足</font></strong><font style="color:#020817">：1 卡版本库存多台，无需等待，快速部署。</font></li> <li><strong><font style="color:#020817">多卡选项备用</font></strong><font style="color:#020817">：若未来需要分布式训练或高并发任务，可随时切换 2 卡/4 卡。</font></li> </ul> <ol start="3"> <li><strong><font style="color:#020817">简化选择逻辑</font></strong><font style="color:#020817">：</font></li> </ol> <ul> <li><strong><font style="color:#020817">无特殊需求</font></strong><font style="color:#020817">：如果只是运行文档镜像中的常规任务（如模型推理、数据处理），单卡性能完全足够。</font></li> <li><strong><font style="color:#020817">兼容性无忧</font></strong><font style="color:#020817">：镜像仓库默认适配主流 GPU 型号（如 4090），无需额外调试。</font></li> </ul> <p><font style="color:#020817">操作建议</font></p> <ul> <li><strong><font style="color:#020817">随便选 直接勾选「4090（1 卡）」+「1 个节点」</font></strong><font style="color:#020817">，填写服务名称，快速完成部署。</font></li> <li><strong><font style="color:#020817">进阶场景</font></strong><font style="color:#020817">：仅当需要训练大模型（如 LLM、多模态）时，再考虑 2 卡/4 卡配置。</font></li> </ul> <p><strong><font style="color:#020817">图片中的配置均为“无坑选项”，闭眼选 1 卡即可开箱即用，成本可控，资源立即可用。</font></strong></p> <p><font style="color:#020817">点击选择某一型号 GPU 后，可配置任务需要的计算节点数量。至少选择 1 个节点，最多可选 20 个节点，建议先启动 1 个节点进行验证，运行稳定后可横向扩展至所需规模。</font></p> <p><font style="color:#020817">选择 GPU 和节点数量后，继续填写服务配置信息</font></p> <h4 id="222-定义服务配置信息"><font style="color:#020817">2.2.2 定义服务配置信息</font></h4> <p><font style="color:#020817">选择</font><strong><font style="color:#020817">服务配置——我的镜像</font></strong></p> <p><img src="'+y+'" alt=""></p> <p><font style="color:#020817">在镜像URL字段中填写完整的镜像地址及标签。支持以下两种方式：</font></p> <p><strong><font style="color:#020817">第一种：第三方公共镜像库</font></strong><font style="color:#020817">（如 Docker Hub、阿里云镜像库等）： 直接使用公开镜像地址，例如：</font></p> <pre><code>swr.cn-north-4.myhuaweicloud.com/ddn-k8s/quay.io/jupyter/pytorch-notebook:cuda12-python-3.11.8\n</code></pre> <p><strong><font style="color:#020817">第二种：我们提供的私有镜像库</font></strong><font style="color:#020817">（需预先上传镜像）： 若需使用内部私有镜像仓库，请参考镜像仓库完成镜像推送后，刷新后在界面中选择内部仓库地址。</font></p> <p><font style="color:#020817">示例私有镜像地址库：</font><font style="color:#2f8ef4;background-color:#e7e9e8">harbor.suanleme.cn/xiditgkw/qwen3:30b-a3b</font></p> <p><img src="'+a+'" alt=""></p> <p><font style="color:#020817">完成镜像 URL 填写后进行端口配置（这里以qwen3为例 端口号填写11434）</font></p> <p><img src="'+u+'" alt=""></p> <p><font style="color:#020817">完成上面的配置后点击部署服务发布任务</font></p> <p><img src="'+b+'" alt=""></p> <h3 id="23-第三步：查看运行状态"><font style="color:#020817">2.3 第三步：查看运行状态</font></h3> <p><font style="color:#020817">发布任务后会自动跳转至任务详情界面，等待节点分配。</font></p> <p><img src="'+h+'" alt=""></p> <p><font style="color:#020817">节点分配完成后，点击【公开访问】中的任一链接均可正常访问该服务</font></p> <p><font style="color:#020817">节点分配完成后，可以通过点击回传链接访问服务：</font></p> <p><img src="'+d+'" alt=""></p> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/6 17:07</font></p> ';t["default"]=m},6097:function(o,t,n){o.exports=n.p+"img/start1.42930b1d.png"},4716:function(o,t,n){o.exports=n.p+"img/start10.88c3feac.png"},7705:function(o,t,n){o.exports=n.p+"img/start11.6f482065.png"},5301:function(o,t,n){o.exports=n.p+"img/start12.c04cb57d.png"},1466:function(o,t,n){o.exports=n.p+"img/start13.e06ce642.png"},6850:function(o,t,n){o.exports=n.p+"img/start14.93713103.png"},5041:function(o,t,n){o.exports=n.p+"img/start15.de859996.png"},9705:function(o,t,n){o.exports=n.p+"img/start2.4a5de580.png"},2155:function(o,t,n){o.exports=n.p+"img/start3.58d32a73.png"},7890:function(o,t,n){o.exports=n.p+"img/start4.04cb0c83.png"},1052:function(o,t,n){o.exports=n.p+"img/start5.3fcc7a59.png"},6604:function(o,t,n){o.exports=n.p+"img/start6.621cbe35.png"},7254:function(o,t,n){o.exports=n.p+"img/start7.a2facd50.png"},6649:function(o,t,n){o.exports=n.p+"img/start8.42930b1d.png"},4869:function(o,t,n){o.exports=n.p+"img/start9.4a5de580.png"}}]);
//# sourceMappingURL=docs14.7b3f04a8.js.map