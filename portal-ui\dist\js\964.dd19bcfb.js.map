{"version": 3, "file": "js/964.dd19bcfb.js", "mappings": "iJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoBC,GAAG,CAAC,WAAaL,EAAIM,cAAc,WAAaN,EAAIO,iBAAiB,CAACL,EAAG,mBAAmB,CAACE,YAAY,mBAAmBI,MAAM,CAAC,KAAO,QAAQ,IAAM,QAAQR,EAAIS,GAAIT,EAAIU,WAAW,SAASC,EAASC,GAAO,OAAOV,EAAG,MAAM,CAACW,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOhB,EAAIiB,uBAAyBL,EAAOM,WAAW,mCAAmCC,IAAIR,EAASP,YAAY,gBAAgBC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOpB,EAAIqB,qBAAqBV,EAAS,EAAE,WAAa,SAASS,GAAQ,OAAOpB,EAAIsB,MAAMV,EAAM,IAAI,CAACZ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGb,GAAU,MAAM,IAAG,IAAI,GAAGT,EAAG,MAAM,CAACE,YAAY,YAAYqB,MAAM,CAAE,mBAAoBzB,EAAI0B,UAAWrB,GAAG,CAAC,MAAQL,EAAI2B,aAAa,CAACzB,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,MAAM,CAACW,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOhB,EAAI0B,SAAUR,WAAW,aAAad,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,IAAI,CAACE,YAAY,eAAeC,GAAG,CAAC,MAAQL,EAAI2B,kBAAkBzB,EAAG,MAAM,CAAC2B,IAAI,oBAAoBzB,YAAY,iBAAiB,CAACJ,EAAIS,GAAIT,EAAI8B,UAAU,SAASC,EAAQnB,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAIP,EAAMa,MAAM,CAAC,UAAWM,EAAQC,OAAO,CAAmB,QAAjBD,EAAQC,KAAgB9B,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,IAAI,CAACE,YAAY,mBAAmBJ,EAAIiC,KAAK/B,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe8B,SAAS,CAAC,UAAYlC,EAAIwB,GAAGxB,EAAImC,cAAcJ,EAAQK,UAAUlC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAIqC,WAAWN,EAAQO,aAAa,IAAItC,EAAIuC,QAASrC,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQF,EAAG,MAAM,CAACE,YAAY,QAAQF,EAAG,MAAM,CAACE,YAAY,UAAUJ,EAAIiC,MAAM,GAAG/B,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACW,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOhB,EAAIwC,UAAWtB,WAAW,cAAcV,MAAM,CAAC,KAAO,OAAO,YAAc,aAAa,SAAWR,EAAIuC,SAASL,SAAS,CAAC,MAASlC,EAAIwC,WAAYnC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAIA,EAAOY,KAAKS,QAAQ,QAAQzC,EAAI0C,GAAGtB,EAAOuB,QAAQ,QAAQ,GAAGvB,EAAOD,IAAI,SAAgB,KAAYnB,EAAI4C,YAAYC,MAAM,KAAMC,UAAU,EAAE,MAAQ,SAAS1B,GAAWA,EAAO2B,OAAOC,YAAiBhD,EAAIwC,UAAUpB,EAAO2B,OAAO/B,MAAK,KAAKd,EAAG,SAAS,CAACM,MAAM,CAAC,SAAWR,EAAIuC,UAAYvC,EAAIwC,UAAUS,QAAQ5C,GAAG,CAAC,MAAQL,EAAI4C,cAAc,CAAC1C,EAAG,IAAI,CAACE,YAAY,8BAC36E,EACI8C,EAAkB,CAAC,WAAY,IAAIlD,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIuB,GAAG,WACnK,GC6EA,G,QAAA,CACAT,KAAA,SAEAqC,OACA,OACAzB,UAAA,EACAc,UAAA,GACAV,SAAA,CACA,CACAE,KAAA,MACAI,KAAA,uBACAE,KAAA,IAAAc,OAGAb,SAAA,EACAc,gBAAA,GACA3C,UAAA,CACA,aACA,YACA,aAEAO,qBAAA,EACAqC,cAAA,KACAC,iBAAA,IACAC,UAAA,EAEA,EACAC,gBACA,KAAAC,eACA,EACAC,UAGA,GAFA,KAAAC,iBAEAC,SAAAC,eAAA,iBACA,MAAAC,EAAAF,SAAAG,cAAA,QACAD,EAAAE,GAAA,eACAF,EAAAG,IAAA,aACAH,EAAAI,KAAA,6EACAN,SAAAO,KAAAC,YAAAN,EACA,CACA,EAEAO,QAAA,CACAhD,MAAAV,GACA,KAAAK,qBAAAL,EACA,KAAAN,eACA,EACAsD,gBACA,IAAAW,EAAA,KACA,KAAAb,gBACA,KAAAJ,cAAAkB,aAAA,KACAD,EAAAf,WACA,KAAAvC,sBACA,KAAAA,qBAAA,QAAAP,UAAA+D,OACAC,QAAAC,IAAA,UAAA1D,sBACAyD,QAAAC,IAAA,WAAAJ,EAAAf,UACA,GACA,KAAAD,iBACA,EACAjD,gBACA,KAAAkD,UAAA,CACA,EACAjD,iBACA,KAAAiD,UAAA,CACA,EACAE,gBACA,KAAAJ,gBACAsB,cAAA,KAAAtB,eACA,KAAAA,cAAA,KAEA,EAEAjC,qBAAAV,GACA,KAAA6B,UAAA7B,EACA,KAAAiC,aACA,EACAjB,aACA,KAAAD,UAAA,KAAAA,SAEA,KAAAA,UACA,KAAAmD,WAAA,KACA,KAAAC,gBAAA,GAGA,EAEA,oBACA,SAAAtC,UAAAS,QAAA,KAAAV,QAAA,OAGA,KAAAT,SAAAiD,KAAA,CACA/C,KAAA,OACAI,KAAA,KAAAI,UACAF,KAAA,IAAAc,OAGA,MAAA4B,EAAA,KAAAxC,UACA,KAAAA,UAAA,GACA,KAAAD,SAAA,EAEA,KAAAc,gBAAA0B,KAAA,CACAE,KAAA,OACAC,QAAAF,IAIA,MAAAG,EAAA,CACAC,MAAA,eACAtD,SAAA,EAAAmD,KAAA,SAAAC,QAAA,+HAAA7B,iBACAgC,QAAA,EACAC,QAAA,CACAC,iBAAA,IACAC,kBAAA,IAEAC,KAAA,QAIA,KAAAZ,WAAA,KACA,KAAAC,gBAAA,IAGA,IAGA,MAAAY,QAAAC,MAAA,kDACAC,OAAA,OACAC,QAAA,CACAC,cAAA,6DACA,mCAEAC,KAAAC,KAAAC,UAAAd,KAIAe,EAAAR,EAAAK,KAAAI,YACAC,EAAA,IAAAC,YACAC,EAAA,KAAAxE,SAAAiD,KAAA,CACA/C,KAAA,MACAI,KAAA,GACAE,KAAA,IAAAc,OACA,EACA,SACA,WAAAmD,EAAA,MAAAvF,SAAAkF,EAAAM,OACA,GAAAD,EAAA,MAGA,MAAAE,EAAAL,EAAAM,OAAA1F,GACA2F,EAAAF,EAAAG,MAAA,MAAAC,QAAAC,GAAAA,EAAA7D,SAEA,UAAA6D,KAAAH,EACA,IACA,MAAAI,EAAAD,EAAAE,MAAA,GAAA/D,OACA,QAAA8D,GAAA,WAAAA,EAAA,SAEA,IAAA5D,EAAA6C,KAAAiB,MAAAF,GACA,GAAA5D,EAAA+D,QAAA,CACA,SAAA/D,EAAA+D,QAAA,GAAAC,MAAAC,kBACA,SAEA,WAAAjE,EAAA+D,QAAA,GAAAC,MAAAjC,QACA,SAEA,KAAApD,SAAAwE,GAAAlE,MAAAe,EAAA+D,QAAA,GAAAC,MAAAjC,OACA,CACA,OAAAmC,GACA,CAEA,CACA,KAAAhE,gBAAA0B,KAAA,CACAE,KAAA,YACAC,QAAA,KAAApD,SAAAwE,GAAAlE,MASA,OAAAkF,GAGA,KAAAxF,SAAAiD,KAAA,CACA/C,KAAA,MACAI,KAAA,qBACAE,KAAA,IAAAc,MAEA,SACA,KAAAb,SAAA,EAGA,KAAAsC,WAAA,KACA,KAAAC,gBAAA,GAEA,CACA,EAGA,kBAAA/C,GAeA,aAbA,IAAAwF,SAAAC,GAAAC,WAAAD,EAAA,OAaA,YAAAzF,0BACA,EAEA+C,iBACA,MAAA4C,EAAA,KAAAC,MAAAC,kBACAF,EAAAG,UAAAH,EAAAI,YACA,EAEAzF,WAAA0F,GACA,WAAA3E,KAAA2E,GAAAC,mBAAA,IAAAC,KAAA,UAAAC,OAAA,WACA,EAEA/F,cAAAC,GAEA,OAAAA,EACA+F,QAAA,cACAA,QAAA,6DACA,KCvTwQ,I,UCQpQC,GAAY,OACd,EACArI,EACAmD,GACA,EACA,KACA,WACA,MAIF,EAAekF,EAAiB,O,oECnBhC,IAAIrI,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAGF,EAAIqI,SAGmyOnI,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgBC,GAAG,CAAC,WAAaL,EAAIsI,iBAAiB,UAAYtI,EAAIuI,gBAAgB,SAAWvI,EAAIwI,iBAAiB,CAACtI,EAAG,MAAM,CAACE,YAAY,uBAAuBqI,MAAO,CAAEC,UAAY,cAAuC,KAAzB1I,EAAI2I,yBAAiC3I,EAAIS,GAAIT,EAAI4I,cAAc,SAASC,EAAKjI,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAIP,EAAMR,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmBI,MAAM,CAAC,IAAMqI,EAAKC,IAAI,IAAM,MAAM5I,EAAG,MAAM,CAACE,YAAY,wBAAwBqB,MAAM,OAASoH,EAAK3D,QAAQ6D,UAAU,CAAC7I,EAAG,KAAK,CAACE,YAAY,sBAAsB8B,SAAS,CAAC,UAAYlC,EAAIwB,GAAGqH,EAAK3D,QAAQ8D,UAAU9I,EAAG,IAAI,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGqH,EAAK3D,QAAQ9C,SAASlC,EAAG,MAAM,CAACE,YAAY,yBAAyB,EAAGJ,EAAIiJ,SAAWJ,EAAK3D,QAAQgE,cAAehJ,EAAG,IAAI,CAACE,YAAY,qCAAqCI,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASe,GAAgC,OAAxBA,EAAO+H,iBAAwBnJ,EAAIoJ,WAAWP,EAAK3D,QAAQgE,cAAc,IAAI,CAAClJ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGqH,EAAK3D,QAAQmE,kBAAkB,OAAOrJ,EAAIiC,MAAOjC,EAAIiJ,SAAWJ,EAAK3D,QAAQoE,YAAapJ,EAAG,IAAI,CAACE,YAAY,mCAAmCI,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASe,GAAgC,OAAxBA,EAAO+H,iBAAwBnJ,EAAIoJ,WAAWP,EAAK3D,QAAQoE,YAAY,IAAI,CAACtJ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGqH,EAAK3D,QAAQqE,gBAAgB,OAAOvJ,EAAIiC,KAAM4G,EAAK3D,QAAQsE,UAAWtJ,EAAG,IAAI,CAACE,YAAY,8BAA8BI,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASe,GAAgC,OAAxBA,EAAO+H,iBAAwBnJ,EAAIoJ,WAAWP,EAAK3D,QAAQsE,UAAU,IAAI,CAACxJ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGqH,EAAK3D,QAAQuE,cAAc,OAAOzJ,EAAIiC,YAAY,IAAG,GAAG/B,EAAG,MAAM,CAACE,YAAY,4BAA4BJ,EAAIS,GAAIT,EAAI4I,cAAc,SAASC,EAAKjI,GAAO,OAAOV,EAAG,OAAO,CAACiB,IAAIP,EAAMa,MAAM,CAAEiI,OAAQ1J,EAAI2I,qBAAuB/H,GAAQP,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOpB,EAAI2J,UAAU/I,EAAM,IAAI,IAAG,KAAKV,EAAG,UAAU,CAACE,YAAY,qCAAqC,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,mBAAmBJ,EAAIS,GAAIT,EAAI4J,MAAM,SAASC,EAAIjJ,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAIP,EAAMR,YAAY,kBAAkBqB,MAAM,CAAE,YAAeoI,EAAIC,aAAczJ,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOpB,EAAIoJ,WAAW,WAAW,IAAI,CAAClJ,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,KAAK,CAACE,YAAY,mBAAmB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAI/I,SAASZ,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAAEyJ,EAAIC,YAAa5J,EAAG,OAAO,CAACE,YAAY,wBAAwB,CAACJ,EAAIuB,GAAG,QAAQvB,EAAIiC,KAAM4H,EAAIE,MAAO7J,EAAG,OAAO,CAACE,YAAY,kBAAkB,CAACJ,EAAIuB,GAAG,SAASvB,EAAIiC,SAAS/B,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,OAAO,CAACE,YAAY,qBAAqB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,OAAO,CAACE,YAAY,qBAAqB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIG,iBAAiB,eAAe9J,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,OAAO,CAACE,YAAY,qBAAqB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,OAAO,CAACE,YAAY,qBAAqB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAII,eAAe,qBAAqB/J,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAAEyJ,EAAIK,cAAehK,EAAG,OAAO,CAACE,YAAY,yBAAyB,CAACJ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGqI,EAAIK,eAAe,QAAQlK,EAAIiC,KAAK/B,EAAG,OAAO,CAACE,YAAY,wBAAwB,CAACJ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGqI,EAAIM,OAAO,WAAW,IAAG,KAAKjK,EAAG,UAAU,CAACE,YAAY,4CAA4C,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,+BAA+B,CAACF,EAAG,MAAM,CAACE,YAAY,4BAA4B,CAACF,EAAG,QAAQ,CAACE,YAAY,2BAA2B,CAACF,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,WAAWvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAI/I,QAAQ,KAAI,KAAKZ,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,QAAQvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIQ,gBAAgB,KAAI,GAAGnK,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,YAAYvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIS,mBAAmB,KAAI,GAAGpK,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,YAAYvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIU,mBAAmB,KAAI,GAAGrK,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,QAAQvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIW,UAAU,KAAI,GAAGtK,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,UAAUvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIY,cAAc,KAAI,GAAGvK,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,QAAQvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIa,aAAa,KAAI,aAAaxK,EAAG,UAAU,CAACE,YAAY,0CAA0C,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,wBAAwBJ,EAAIS,GAAIT,EAAI2K,aAAa,SAASC,EAAQhK,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAIP,EAAMR,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACuB,MAAMmJ,EAAQC,SAAS3K,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGoJ,EAAQ5B,UAAU9I,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGoJ,EAAQE,UAAU,IAAG,KAAK5K,EAAG,UAAU,CAACE,YAAY,8CAA8C,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,4BAA4BJ,EAAIS,GAAIT,EAAI+K,oBAAoB,SAASC,EAAIpK,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAIP,EAAMR,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACM,MAAM,CAAC,IAAMwK,EAAIC,MAAM,IAAMD,EAAIhC,WAAW9I,EAAG,KAAK,CAACE,YAAY,oBAAoB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGwJ,EAAIhC,WAAW,IAAG,KAAK9I,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIuB,GAAG,wBAAwBrB,EAAG,SAAS,CAACE,YAAY,wBAAwBC,GAAG,CAAC,MAAQL,EAAIkL,mBAAmB,CAAClL,EAAIuB,GAAG,YAAYrB,EAAG,aAAa,CAACM,MAAM,CAAC,KAAO,gBAAgB,CAAER,EAAImL,iBAAkBjL,EAAG,MAAM,CAACE,YAAY,yBAAyBC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAGA,EAAO2B,SAAW3B,EAAOgK,cAAqB,KAAYpL,EAAIqL,kBAAkBxI,MAAM,KAAMC,UAAU,IAAI,CAAC5C,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,SAAS,CAACE,YAAY,qBAAqBC,GAAG,CAAC,MAAQL,EAAIqL,oBAAoB,CAACrL,EAAIuB,GAAG,SAASrB,EAAG,MAAM,CAACE,YAAY,0BAA0B,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAIsL,YAAYxK,WAAWZ,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACF,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAIsL,YAAYC,cAAcrL,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACF,EAAIuB,GAAG,oBAAoBvB,EAAIiC,QAAQ,GAHppb/B,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,WAAWqI,MAAO,CACpTC,UAAW,cAAgB1I,EAAIwL,UAAY,IAC3CC,WAAYzL,EAAI0L,MAAQ,WAAa,SACnC1L,EAAIS,GAAIT,EAAI4I,cAAc,SAASC,EAAKjI,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAIP,EAAMR,YAAY,cAAc,CAACF,EAAG,MAAM,CAACM,MAAM,CAAC,IAAMqI,EAAKC,IAAI,IAAM,MAAM5I,EAAG,MAAM,CAACE,YAAY,iBAAiBqB,MAAM,OAASoH,EAAK3D,QAAQ6D,UAAU,CAAC7I,EAAG,KAAK,CAACE,YAAY,eAAe8B,SAAS,CAAC,UAAYlC,EAAIwB,GAAGqH,EAAK3D,QAAQ8D,UAAU9I,EAAG,IAAI,CAACE,YAAY,eAAe,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGqH,EAAK3D,QAAQ9C,SAASlC,EAAG,MAAM,CAACE,YAAY,kBAAkB,EAAGJ,EAAIiJ,SAAWJ,EAAK3D,QAAQgE,cAAehJ,EAAG,IAAI,CAACE,YAAY,8BAA8BI,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASe,GAAgC,OAAxBA,EAAO+H,iBAAwBnJ,EAAIoJ,WAAWP,EAAK3D,QAAQgE,cAAc,IAAI,CAAClJ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGqH,EAAK3D,QAAQmE,kBAAkB,OAAOrJ,EAAIiC,MAAOjC,EAAIiJ,SAAWJ,EAAK3D,QAAQoE,YAAapJ,EAAG,IAAI,CAACE,YAAY,4BAA4BI,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASe,GAAgC,OAAxBA,EAAO+H,iBAAwBnJ,EAAIoJ,WAAWP,EAAK3D,QAAQoE,YAAY,IAAI,CAACtJ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGqH,EAAK3D,QAAQqE,gBAAgB,OAAOvJ,EAAIiC,KAAM4G,EAAK3D,QAAQsE,UAAWtJ,EAAG,IAAI,CAACE,YAAY,8BAA8BI,MAAM,CAAC,KAAO,KAAKH,GAAG,CAAC,MAAQ,SAASe,GAAgC,OAAxBA,EAAO+H,iBAAwBnJ,EAAIoJ,WAAWP,EAAK3D,QAAQsE,UAAU,IAAI,CAACxJ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGqH,EAAK3D,QAAQuE,cAAc,OAAOzJ,EAAIiC,UAAU,IAAG,KAAK/B,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACE,YAAY,iBAAiBC,GAAG,CAAC,MAAQL,EAAI2L,OAAO,CAACzL,EAAG,MAAM,CAACE,YAAY,qBAAqBI,MAAM,CAAC,IAAMoL,EAAQ,MAA6C,IAAM,QAAQ1L,EAAG,OAAO,CAACE,YAAY,kBAAkBC,GAAG,CAAC,MAAQL,EAAI6L,OAAO,CAAC3L,EAAG,MAAM,CAACE,YAAY,aAAaI,MAAM,CAAC,IAAMoL,EAAQ,MAA6C,IAAM,UAAU1L,EAAG,MAAM,CAAC2B,IAAI,mBAAmBzB,YAAY,qBAAqBJ,EAAIS,GAAIT,EAAI4I,cAAc,SAASC,EAAKjI,GAAO,OAAOV,EAAG,OAAO,CAACiB,IAAIP,EAAMa,MAAM,CAAEiI,OAAQ1J,EAAI8L,aAAelL,IAAS,IAAG,SAASV,EAAG,UAAU,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,iBAAiBJ,EAAIS,GAAIT,EAAI4J,MAAM,SAASC,EAAIjJ,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAIP,EAAMR,YAAY,WAAWqB,MAAM,CAAE,YAAeoI,EAAIC,aAAczJ,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAOpB,EAAIoJ,WAAW,WAAW,IAAI,CAAClJ,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACE,YAAY,YAAY,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAI/I,SAAU+I,EAAIC,YAAa5J,EAAG,OAAO,CAACE,YAAY,sBAAsB,CAACJ,EAAIuB,GAAG,QAAQvB,EAAIiC,KAAM4H,EAAIE,MAAO7J,EAAG,OAAO,CAACE,YAAY,WAAW,CAACJ,EAAIuB,GAAG,SAASvB,EAAIiC,OAAO/B,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIG,iBAAiB,eAAe9J,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,OAAO,CAACE,YAAY,cAAc,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAII,eAAe,qBAAqB/J,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAAEyJ,EAAIK,cAAehK,EAAG,OAAO,CAACE,YAAY,kBAAkB,CAACJ,EAAIuB,GAAG,IAAIvB,EAAIwB,GAAGqI,EAAIK,eAAe,QAAQlK,EAAIiC,KAAK/B,EAAG,OAAO,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAG,KAAKrB,EAAG,OAAO,CAACE,YAAY,eAAe,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIM,UAAUnK,EAAIuB,GAAG,eAAe,IAAG,OAAOrB,EAAG,iBAAiBA,EAAG,UAAU,CAACE,YAAY,4BAA4B,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,iBAAiBJ,EAAIS,GAAIT,EAAI2K,aAAa,SAASC,EAAQhK,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAIP,EAAMR,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,eAAeqB,MAAMmJ,EAAQC,OAAO3K,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAGvB,EAAIwB,GAAGoJ,EAAQ5B,UAAU9I,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACF,EAAIuB,GAAGvB,EAAIwB,GAAGoJ,EAAQE,cAAc,IAAG,OAAO5K,EAAG,UAAU,CAACE,YAAY,kBAAkB,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcC,GAAG,CAAC,UAAY,SAASe,GAAQpB,EAAI+L,aAAaC,OAAQ,CAAI,EAAE,WAAa,SAAS5K,GAAQpB,EAAI+L,aAAaC,OAAQ,CAAK,IAAI,CAAC9L,EAAG,MAAM,CAACE,YAAY,eAAeqB,MAAM,CAAE,eAAgBzB,EAAI+L,aAAaC,QAAS,CAAC9L,EAAG,MAAM,CAACM,MAAM,CAAC,IAAMR,EAAI+L,aAAad,MAAM,IAAMjL,EAAI+L,aAAa/C,WAAW9I,EAAG,MAAM,CAACE,YAAY,mBAAmBqB,MAAM,CAAE,eAAgBzB,EAAI+L,aAAaC,QAAS,CAAChM,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAI+L,aAAa/C,cAAchJ,EAAIS,GAAIT,EAAIiM,kBAAkB,SAASjB,EAAIpK,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAI,QAAQP,EAAMR,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcC,GAAG,CAAC,UAAY,SAASe,GAAQ4J,EAAIgB,OAAQ,CAAI,EAAE,WAAa,SAAS5K,GAAQ4J,EAAIgB,OAAQ,CAAK,IAAI,CAAC9L,EAAG,MAAM,CAACE,YAAY,eAAeqB,MAAM,CAAE,eAAgBuJ,EAAIgB,QAAS,CAAC9L,EAAG,MAAM,CAACM,MAAM,CAAC,IAAMwK,EAAIC,MAAM,IAAMD,EAAIhC,WAAW9I,EAAG,MAAM,CAACE,YAAY,mBAAmBqB,MAAM,CAAE,eAAgBuJ,EAAIgB,QAAS,CAAChM,EAAIuB,GAAGvB,EAAIwB,GAAGwJ,EAAIhC,aAAa,IAAGhJ,EAAIS,GAAIT,EAAIkM,eAAe,SAASlB,EAAIpK,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAI,SAASP,EAAMR,YAAY,4BAA4B,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcC,GAAG,CAAC,UAAY,SAASe,GAAQ4J,EAAIgB,OAAQ,CAAI,EAAE,WAAa,SAAS5K,GAAQ4J,EAAIgB,OAAQ,CAAK,IAAI,CAAC9L,EAAG,MAAM,CAACE,YAAY,eAAeqB,MAAM,CAAE,eAAgBuJ,EAAIgB,QAAS,CAAC9L,EAAG,MAAM,CAACM,MAAM,CAAC,IAAMwK,EAAIC,MAAM,IAAMD,EAAIhC,WAAW9I,EAAG,MAAM,CAACE,YAAY,mBAAmBqB,MAAM,CAAE,eAAgBuJ,EAAIgB,QAAS,CAAChM,EAAIuB,GAAGvB,EAAIwB,GAAGwJ,EAAIhC,aAAa,IAAGhJ,EAAIS,GAAIT,EAAImM,mBAAmB,SAASnB,EAAIpK,GAAO,OAAOV,EAAG,MAAM,CAACiB,IAAI,eAAeP,EAAMR,YAAY,4BAA4B,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcC,GAAG,CAAC,UAAY,SAASe,GAAQ4J,EAAIgB,OAAQ,CAAI,EAAE,WAAa,SAAS5K,GAAQ4J,EAAIgB,OAAQ,CAAK,IAAI,CAAC9L,EAAG,MAAM,CAACE,YAAY,eAAeqB,MAAM,CAAE,eAAgBuJ,EAAIgB,QAAS,CAAC9L,EAAG,MAAM,CAACM,MAAM,CAAC,IAAMwK,EAAIC,MAAM,IAAMD,EAAIhC,WAAW9I,EAAG,MAAM,CAACE,YAAY,mBAAmBqB,MAAM,CAAE,eAAgBuJ,EAAIgB,QAAS,CAAChM,EAAIuB,GAAGvB,EAAIwB,GAAGwJ,EAAIhC,aAAa,IAAG9I,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcC,GAAG,CAAC,UAAY,SAASe,GAAQpB,EAAIoM,aAAaJ,OAAQ,CAAI,EAAE,WAAa,SAAS5K,GAAQpB,EAAIoM,aAAaJ,OAAQ,CAAK,IAAI,CAAC9L,EAAG,MAAM,CAACE,YAAY,eAAeqB,MAAM,CAAE,eAAgBzB,EAAIoM,aAAaJ,QAAS,CAAC9L,EAAG,MAAM,CAACM,MAAM,CAAC,IAAMR,EAAIoM,aAAanB,MAAM,IAAMjL,EAAIoM,aAAapD,WAAW9I,EAAG,MAAM,CAACE,YAAY,mBAAmBqB,MAAM,CAAE,eAAgBzB,EAAIoM,aAAaJ,QAAS,CAAChM,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAIoM,aAAapD,eAAe,GAAG9I,EAAG,YAAY,KAAKA,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,KAAK,CAACE,YAAY,gBAAgB,CAACJ,EAAIuB,GAAG,wBAAwBrB,EAAG,SAAS,CAACE,YAAY,kBAAkBC,GAAG,CAAC,MAAQL,EAAIkL,mBAAmB,CAAClL,EAAIuB,GAAG,cAAcrB,EAAG,aAAa,CAACM,MAAM,CAAC,KAAO,SAAS,CAAER,EAAImL,iBAAkBjL,EAAG,MAAM,CAACE,YAAY,wBAAwBC,GAAG,CAAC,MAAQ,SAASe,GAAQ,OAAGA,EAAO2B,SAAW3B,EAAOgK,cAAqB,KAAYpL,EAAIqL,kBAAkBxI,MAAM,KAAMC,UAAU,IAAI,CAAC5C,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,SAAS,CAACE,YAAY,cAAcC,GAAG,CAAC,MAAQL,EAAIqL,oBAAoB,CAACrL,EAAIuB,GAAG,SAASrB,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,iBAAiBF,EAAG,OAAO,CAACF,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAIsL,YAAYxK,WAAWZ,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACF,EAAIuB,GAAGvB,EAAIwB,GAAGxB,EAAIsL,YAAYC,cAAcrL,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACF,EAAIuB,GAAG,oBAAoBvB,EAAIiC,QAAQ,GAAm4MjC,EAAIqI,SAAsBrI,EAAIiC,KAAhB/B,EAAG,SAAkBA,EAAG,UAAUA,EAAG,WAAW,EACpzb,EACIgD,EAAkB,CAAC,WAAY,IAAIlD,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACJ,EAAIuB,GAAG,8CAC3N,EAAE,WAAY,IAAIvB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACJ,EAAIuB,GAAG,iCACtM,EAAE,WAAY,IAAIvB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACJ,EAAIuB,GAAG,qBACtM,EAAE,WAAY,IAAIvB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,IAAI,CAACE,YAAY,8BAA8B,CAACJ,EAAIuB,GAAG,gCAC3N,EAAE,WAAY,IAAIvB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIuB,GAAG,aAAarB,EAAG,IAAI,CAACE,YAAY,8BAA8B,CAACJ,EAAIuB,GAAG,oBAC9N,EAAE,WAAY,IAAIvB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,IAAI,CAACE,YAAY,8BAA8B,CAACJ,EAAIuB,GAAG,gCAC3N,EAAE,WAAY,IAAIvB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACF,EAAG,KAAK,CAACE,YAAY,wBAAwB,CAACJ,EAAIuB,GAAG,UAAUrB,EAAG,IAAI,CAACE,YAAY,8BAA8B,CAACJ,EAAIuB,GAAG,qBAC3N,G,kDCZIxB,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACE,YAAY,kCAAkC,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAI4B,GAAG,GAAG1B,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,QAAQ,CAACA,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,WAAWvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAI/I,QAAQ,KAAI,KAAKZ,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,QAAQvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIQ,gBAAgB,KAAI,GAAGnK,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,YAAYvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIS,mBAAmB,KAAI,GAAGpK,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,YAAYvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIU,mBAAmB,KAAI,GAAGrK,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,QAAQvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIW,UAAU,KAAI,GAAGtK,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,UAAUvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIY,cAAc,KAAI,GAAGvK,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIuB,GAAG,QAAQvB,EAAIS,GAAIT,EAAIoK,gBAAgB,SAASP,GAAK,OAAO3J,EAAG,KAAK,CAACiB,IAAI0I,EAAI/I,MAAM,CAACd,EAAIuB,GAAGvB,EAAIwB,GAAGqI,EAAIa,aAAa,KAAI,YACrwC,EACIxH,EAAkB,CAAC,WAAY,IAAIlD,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIuB,GAAG,aAAarB,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACJ,EAAIuB,GAAG,iCAC9N,G,UCkDA,GACAT,KAAA,gBACAqC,OACA,OACAiH,eAAA,CACA,CACAtJ,KAAA,OACAuJ,aAAA,SACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,cAEA,CACA5J,KAAA,OACAuJ,aAAA,QACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,YAEA,CACA5J,KAAA,QACAuJ,aAAA,SACAC,gBAAA,cACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,QACAC,UAAA,YAEA,CACA5J,KAAA,QACAuJ,aAAA,SACAC,gBAAA,cACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,QACAC,UAAA,YAEA,CACA5J,KAAA,QACAuJ,aAAA,SACAC,gBAAA,eACAC,gBAAA,eACAC,OAAA,QACAC,WAAA,QACAC,UAAA,aAIA,EACA2B,UACA,KAAAC,iBACA,EACAhI,QAAA,CACA,wBACA,KAEAiI,EAAAA,EAAAA,IAAA,2BAAAC,MAAAC,IAEA,KAAArC,eAAAqC,EAAAtJ,KAAAuJ,KAAAC,KAAA9D,IAAA,IACAA,EACAyB,gBAAAzB,EAAA+D,gBACArC,gBAAA1B,EAAAgE,gBACApC,WAAA5B,EAAAiE,cACA,GAIA,OAAAxF,GACA5C,QAAA4C,MAAA,eAAAA,EACA,CACA,IC/HsQ,I,UCQlQc,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIF,EAAeA,EAAiB,Q,UCkbhC,GACAtH,KAAA,YACAiM,WAAA,CAAAC,OAAA,IAAAC,OAAA,IAAAC,OAAA,IAAAC,MAAA,IAAAC,cAAAA,GACAC,SAAA,CACA7B,YACA,iBAAAM,WAAA,GACA,EACA7C,UACA,QAAApF,SAAAyJ,OAAAC,SAAA,cACA,EACAC,mBACA,iBAAA7E,kBACA,EACAoC,qBACA,OACA,KAAAgB,gBACA,KAAAE,oBACA,KAAAC,iBACA,KAAAC,kBACA,KAAAC,aAEA,GAEAjJ,OACA,OACAsK,YAAA,EACAC,UAAA,EACAC,eAAA,GACAtF,UAAA,EACAM,mBAAA,EACAwC,kBAAA,EACAG,YAAA,CACAxK,KAAA,MACAyK,MAAA,eAEAqC,SAAA,EACA7B,aAAA,CACA/C,MAAA,OACAiC,MAAAW,EAAA,MACAI,OAAA,GAEAC,iBAAA,CACA,CACAjD,MAAA,OACAiC,MAAAW,EAAA,MACAI,OAAA,GAEA,CACAhD,MAAA,OACAiC,MAAAW,EAAA,MACAI,OAAA,IAGAE,cAAA,CACA,CACAlD,MAAA,OACAiC,MAAAW,EAAA,KACAI,OAAA,GAEA,CACAhD,MAAA,OACAiC,MAAAW,EAAA,MACAI,OAAA,IAGAG,kBAAA,CACA,CACAnD,MAAA,OACAiC,MAAAW,EAAA,KACAI,OAAA,GAEA,CACAhD,MAAA,OACAiC,MAAAW,EAAA,MACAI,OAAA,IAGAI,aAAA,CACApD,MAAA,OACAiC,MAAAW,EAAA,KACAI,OAAA,GAEApD,aAAA,CACA,CACAE,IAAA8C,EAAA,MACA1G,QAAA,CACA8D,MAAA,OACA5G,KAAA,oBACA2G,SAAA,OACAS,UAAA,WACAC,aAAA,SAGA,CACAX,IAAA8C,EAAA,MACA1G,QAAA,CACA8D,MAAA,WACA5G,KAAA,qBACA2G,SAAA,OACAG,cAAA,SACAG,iBAAA,OACAC,YAAA,YACAC,eAAA,SAGA,CACAT,IAAA8C,EAAA,MACA1G,QAAA,CACA8D,MAAA,WACA5G,KAAA,qCACA2G,SAAA,UAIA+C,WAAA,EACAJ,OAAA,EACAmC,QAAA,GACAC,cAAA,CACAC,MAAA,EACAC,SAAA,CACAC,MAAA,IACAC,sBAAA,GAEAC,WAAA,CACAC,GAAA,qBACAC,WAAA,GAEAC,WAAA,CACAC,OAAA,sBACAC,OAAA,wBAGA7D,YAAA,CACA,CAAA1G,GAAA,EAAA4G,KAAA,iBAAA7B,MAAA,OAAA8B,KAAA,+DACA,CAAA7G,GAAA,EAAA4G,KAAA,kBAAA7B,MAAA,OAAA8B,KAAA,kDACA,CAAA7G,GAAA,EAAA4G,KAAA,iBAAA7B,MAAA,OAAA8B,KAAA,4DACA,CAAA7G,GAAA,EAAA4G,KAAA,iBAAA7B,MAAA,QAAA8B,KAAA,0DACA,CAAA7G,GAAA,EAAA4G,KAAA,sBAAA7B,MAAA,MAAA8B,KAAA,2DACA,CAAA7G,GAAA,EAAA4G,KAAA,gBAAA7B,MAAA,OAAA8B,KAAA,6CAEAV,eAAA,CACA,CACAtJ,KAAA,OACAuJ,aAAA,SACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,cAEA,CACA5J,KAAA,OACAuJ,aAAA,SACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,cAEA,CACA5J,KAAA,OACAuJ,aAAA,SACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,cAEA,CACA5J,KAAA,OACAuJ,aAAA,QACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,YAEA,CACA5J,KAAA,QACAuJ,aAAA,SACAC,gBAAA,cACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,QACAC,UAAA,YAEA,CACA5J,KAAA,QACAuJ,aAAA,SACAC,gBAAA,cACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,QACAC,UAAA,YAEA,CACA5J,KAAA,QACAuJ,aAAA,SACAC,gBAAA,eACAC,gBAAA,eACAC,OAAA,QACAC,WAAA,QACAC,UAAA,aAGAd,KAAA,CACA,CACA9I,KAAA,mBACAkJ,gBAAA,MACAC,cAAA,OACAC,cAAA,QACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACAjJ,KAAA,kBACAkJ,gBAAA,QACAC,cAAA,OACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACAjJ,KAAA,mBACAkJ,gBAAA,MACAC,cAAA,OACAC,cAAA,QACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACAjJ,KAAA,kBACAkJ,gBAAA,QACAC,cAAA,QACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACAjJ,KAAA,mBACAkJ,gBAAA,QACAC,cAAA,QACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACAjJ,KAAA,kBACAkJ,gBAAA,OACAC,cAAA,OACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACAjJ,KAAA,mBACAkJ,gBAAA,QACAC,cAAA,OACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,GAEA,CACAjJ,KAAA,mBACAkJ,gBAAA,MACAC,cAAA,QACAC,cAAA,OACAC,MAAA,OACAL,aAAA,EACAC,OAAA,IAIA0E,YAAA,EAEA,EACApC,UACA,KAAAqC,uBACA,MAAAC,EAAA,IAAAC,IAAAC,OAAAC,SAAA3K,MACA4K,EAAAJ,EAAAK,aAAAC,IAAA,SAEA,GAAAF,EAAA,CACA,MAAAG,EAAAC,aAAAC,QAAA,yBAEAF,GAOAL,OAAAC,SAAA3K,KAAAwK,EAAAU,OAAAV,EAAAW,SACAH,aAAAI,QAAA,oCAPAC,EAAAA,EAAAA,IAAAT,GACAI,aAAAI,QAAA,gCAQA,CACA,EACA5L,UACA,KAAA8L,gBACAZ,OAAAa,iBAAA,cAAAD,eAGA,KAAAE,gBAAAnL,aAAA,KACA,KAAAqH,MAAA,GACA,KAEA,KAAA+D,eAAApL,aAAA,KACA,KAAAqL,YAAA,GACA,IACA,EACApM,gBACAoL,OAAAiB,oBAAA,cAAAL,eACA7K,cAAA,KAAA+K,iBACA/K,cAAA,KAAAgL,eAEA,EACAtL,QAAA,CAEA,6BACA,KACAiI,EAAAA,EAAAA,IAAA,0BAAAC,MAAAC,IACA,KAAA7C,KAAA6C,EAAAtJ,KAAAuJ,KAAAC,KAAA9D,IAAA,IACAA,EACAkB,MAAA,IAAAlB,EAAAkH,MACAjG,YAAA,IAAAjB,EAAAiB,eACA,GAGA,OAAAxC,GACA5C,QAAA4C,MAAA,eAAAA,EACA,CACA,EAEAgB,iBAAAjB,GACA,KAAAoG,YAAApG,EAAA2I,QAAA,GAAAC,OACA,EAEA1H,gBAAAlB,GACA,KAAAqG,UAAArG,EAAA2I,QAAA,GAAAC,OACA,EAEAzH,iBACA,SAAAiF,cAAA,KAAAC,UAAA,OAEA,MAAAwC,EAAA,KAAAzC,YAAA,KAAAC,UAGAwC,EAAA,KAAAvC,gBACA,KAAAkC,aAIAK,GAAA,KAAAvC,gBACA,KAAAwC,aAIA,KAAA1C,YAAA,EACA,KAAAC,UAAA,CACA,EAEAyC,aACA,KAAAxH,oBAAA,KAAAA,mBAAA,OAAAC,aAAAnE,QAAA,KAAAmE,aAAAnE,OACA,KAAA2L,qBACA,EAEAP,aACA,KAAAlH,oBAAA,KAAAA,mBAAA,QAAAC,aAAAnE,OACA,KAAA2L,qBACA,EAEAA,sBACAxL,cAAA,KAAAgL,gBACA,KAAAA,eAAApL,aAAA,KACA,KAAAqL,YAAA,GACA,IACA,EACAJ,gBACA,KAAApH,SAAAwG,OAAAwB,YAAA,GACA,EACAnF,mBACA,KAAAC,kBAAA,CACA,EACAE,oBACA,KAAAF,kBAAA,CACA,EACAmF,mBAAAzH,GACAA,EAAA3D,QAAAnB,MACA,KAAAwM,QAAAxL,KAAA8D,EAAA3D,QAAAnB,MAEA,KAAAyM,IAAAC,MAAA,iBAAA5H,EAAA3D,QAAA8D,MACA,EACAI,WAAAsH,GACA,KAAAC,aAAA,KAAAA,cAAAD,GACA,KAAAE,mBAAA,KAAAD,YAEA,KAAA9L,WAAA,KACA,MAAAgM,EAAAhN,SAAAiN,iBAAA,yBACAD,EAAAE,SAAAhN,KACAA,EAAAiN,UAAAC,SAAA,WACA,WAAAP,GAAA3M,EAAAiN,UAAAC,SAAA,gBACAlN,EAAAiN,UAAAC,SAAA,iBACAlN,EAAAiN,UAAAE,IAAA,eAEAzJ,YAAA,KACA1D,EAAAiN,UAAAG,OAAA,iBACA,KACA,IAGA,KAAAR,YAAAD,CAAA,KAGA,KAAAC,YAAAD,EAGA,KAAAU,OAAAV,OAAAA,EACA,KAAA7L,WAAA,KACAgK,OAAAwC,SAAA,CACAC,IAAA,EACAC,SAAA,YAEA,KAAAhB,QAAAiB,GAAA,OAGA,KAAAjB,QAAAxL,KAAA2L,GACA7B,OAAAwC,SAAA,CACAC,IAAA,EACAC,SAAA,YAGA,EACA5F,OACA,KAAAG,YAAA,KAAAA,WAAA,OAAAlD,aAAAnE,QAAA,KAAAmE,aAAAnE,OACA,KAAAiH,OAAA,CACA,EACAG,OACA,KAAAC,YAAA,KAAAA,WAAA,QAAAlD,aAAAnE,OACA,KAAAiH,OAAA,CACA,EAEA/B,UAAA/I,GACA,KAAA+H,mBAAA/H,EAEAgE,cAAA,KAAAgL,gBACA,KAAAA,eAAApL,aAAA,KACA,KAAAqL,YAAA,GACA,IACA,EACA4B,UAAA7Q,GACA,KAAAgN,SAAAhN,CACA,EACA8Q,OAAA7H,GACAnF,QAAAC,IAAA,iBAAAkF,EAAA/I,OACA,EACA6Q,oBACAjN,QAAAC,IAAA,sBACA,EACAiN,YACAlN,QAAAC,IAAA,aACA,EACAkN,YACA,KAAApD,YAAA,KAAAA,YAAA,OAAAA,YAAA,OAAAqD,UAAArN,OAAA,CACA,EACAsN,YACA,KAAAtD,YAAA,KAAAA,YAAA,KAAAqD,UAAArN,OAAA,OAAAgK,YAAA,GACA,EACAuD,UAAAnI,GACAnF,QAAAC,IAAA,iBAAAkF,EAAA/I,OACA,GAEAmR,MAAA,CACAnG,WAAAoG,GACA,MAAAC,EAAA,KAAAxK,MAAAyK,kBAAAtB,iBAAA,QACAqB,GACAA,EAAApB,SAAA,CAAAsB,EAAAzR,KACAyR,EAAArB,UAAAsB,OAAA,SAAA1R,IAAAsR,EAAA,GAGA,IC16BkQ,ICQ9P,GAAY,OACd,EACAnS,EACAmD,GACA,EACA,KACA,WACA,MAIF,EAAe,EAAiB,O", "sources": ["webpack://portal-ui/./src/components/common/mider/chatAi.vue", "webpack://portal-ui/src/components/common/mider/chatAi.vue", "webpack://portal-ui/./src/components/common/mider/chatAi.vue?09db", "webpack://portal-ui/./src/components/common/mider/chatAi.vue?3a6e", "webpack://portal-ui/./src/views/Index/IndexView.vue", "webpack://portal-ui/./src/views/Index/GpuComparison.vue", "webpack://portal-ui/src/views/Index/GpuComparison.vue", "webpack://portal-ui/./src/views/Index/GpuComparison.vue?25ec", "webpack://portal-ui/./src/views/Index/GpuComparison.vue?69c5", "webpack://portal-ui/src/views/Index/IndexView.vue", "webpack://portal-ui/./src/views/Index/IndexView.vue?6f88", "webpack://portal-ui/./src/views/Index/IndexView.vue?981c"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('div',{staticClass:\"chat-container\"},[_c('div',{staticClass:\"question-carousel\",on:{\"mouseenter\":_vm.pauseCarousel,\"mouseleave\":_vm.resumeCarousel}},[_c('transition-group',{staticClass:\"carousel-wrapper\",attrs:{\"name\":\"slide\",\"tag\":\"div\"}},_vm._l((_vm.questions),function(question,index){return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.currentQuestionIndex === index),expression:\"currentQuestionIndex === index\"}],key:question,staticClass:\"question-item\",on:{\"click\":function($event){return _vm.sendCarouselQuestion(question)},\"mouseenter\":function($event){return _vm.witde(index)}}},[_vm._v(\" \"+_vm._s(question)+\" \")])}),0)],1),_c('div',{staticClass:\"chat-icon\",class:{ 'chat-icon-active': _vm.showChat },on:{\"click\":_vm.toggleChat}},[_c('i',{staticClass:\"fas fa-comment\"})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showChat),expression:\"showChat\"}],staticClass:\"chat-window\"},[_c('div',{staticClass:\"chat-header\"},[_vm._m(0),_c('div',{staticClass:\"chat-controls\"},[_c('i',{staticClass:\"fas fa-times\",on:{\"click\":_vm.toggleChat}})])]),_c('div',{ref:\"messagesContainer\",staticClass:\"chat-messages\"},[_vm._l((_vm.messages),function(message,index){return _c('div',{key:index,class:['message', message.type]},[(message.type === 'bot')?_c('div',{staticClass:\"avatar\"},[_c('i',{staticClass:\"fas fa-robot\"})]):_vm._e(),_c('div',{staticClass:\"message-content\"},[_c('div',{staticClass:\"message-text\",domProps:{\"innerHTML\":_vm._s(_vm.formatMessage(message.text))}}),_c('div',{staticClass:\"message-time\"},[_vm._v(_vm._s(_vm.formatTime(message.time)))])])])}),(_vm.loading)?_c('div',{staticClass:\"typing-indicator\"},[_c('div',{staticClass:\"dot\"}),_c('div',{staticClass:\"dot\"}),_c('div',{staticClass:\"dot\"})]):_vm._e()],2),_c('div',{staticClass:\"chat-input\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.userInput),expression:\"userInput\"}],attrs:{\"type\":\"text\",\"placeholder\":\"请输入您的问题...\",\"disabled\":_vm.loading},domProps:{\"value\":(_vm.userInput)},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.sendMessage.apply(null, arguments)},\"input\":function($event){if($event.target.composing)return;_vm.userInput=$event.target.value}}}),_c('button',{attrs:{\"disabled\":_vm.loading || !_vm.userInput.trim()},on:{\"click\":_vm.sendMessage}},[_c('i',{staticClass:\"fas fa-paper-plane\"})])])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"chat-title\"},[_c('i',{staticClass:\"fas fa-robot\"}),_c('span',[_vm._v(\"智能客服\")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n    <div >\r\n<!--        问题轮播-->\r\n        <!-- 悬浮客服容器 -->\r\n        <div class=\"chat-container\">\r\n            <!-- 问题轮播区 -->\r\n            <div class=\"question-carousel\"\r\n                 @mouseenter=\"pauseCarousel\"\r\n                 @mouseleave=\"resumeCarousel\">\r\n                <transition-group name=\"slide\" tag=\"div\" class=\"carousel-wrapper\">\r\n                    <div v-for=\"(question, index) in questions\"\r\n                         :key=\"question\"\r\n                         class=\"question-item\"\r\n                         v-show=\"currentQuestionIndex === index\"\r\n                         @click=\"sendCarouselQuestion(question)\"\r\n                         @mouseenter=\"witde(index)\"\r\n                    >\r\n                        {{ question }}\r\n                    </div>\r\n                </transition-group>\r\n            </div>\r\n\r\n            <!-- 原有悬浮按钮 -->\r\n            <div class=\"chat-icon\"\r\n                 :class=\"{ 'chat-icon-active': showChat }\"\r\n                 @click=\"toggleChat\">\r\n                <i class=\"fas fa-comment\"></i>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 聊天窗口 -->\r\n        <div class=\"chat-window\" v-show=\"showChat\">\r\n            <div class=\"chat-header\">\r\n                <div class=\"chat-title\">\r\n                    <i class=\"fas fa-robot\"></i>\r\n                    <span>智能客服</span>\r\n                </div>\r\n                <div class=\"chat-controls\">\r\n                    <i class=\"fas fa-times\" @click=\"toggleChat\"></i>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-messages\" ref=\"messagesContainer\">\r\n                <div\r\n                        v-for=\"(message, index) in messages\"\r\n                        :key=\"index\"\r\n                        :class=\"['message', message.type]\"\r\n                >\r\n                    <div class=\"avatar\" v-if=\"message.type === 'bot'\">\r\n                        <i class=\"fas fa-robot\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"message-text\" v-html=\"formatMessage(message.text)\"></div>\r\n                        <div class=\"message-time\">{{ formatTime(message.time) }}</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"typing-indicator\" v-if=\"loading\">\r\n                    <div class=\"dot\"></div>\r\n                    <div class=\"dot\"></div>\r\n                    <div class=\"dot\"></div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-input\">\r\n                <input\r\n                        type=\"text\"\r\n                        v-model=\"userInput\"\r\n                        placeholder=\"请输入您的问题...\"\r\n                        @keyup.enter=\"sendMessage\"\r\n                        :disabled=\"loading\"\r\n                />\r\n                <button @click=\"sendMessage\" :disabled=\"loading || !userInput.trim()\">\r\n                    <i class=\"fas fa-paper-plane\"></i>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        name: 'chatAi',\r\n\r\n        data() {\r\n            return {\r\n                showChat: false,\r\n                userInput: '',\r\n                messages: [\r\n                    {\r\n                        type: 'bot',\r\n                        text: '您好！我是智能客服助手，有什么可以帮您？',\r\n                        time: new Date()\r\n                    }\r\n                ],\r\n                loading: false,\r\n                historyMessages:[],\r\n                questions: [\r\n                    \"如何租赁GPU算力？\",\r\n                    \"支持哪些支付方式？\",\r\n                    \"如何查看订单状态？\"\r\n                ],\r\n                currentQuestionIndex: 0,\r\n                carouselTimer: null,\r\n                carouselInterval: 3000,\r\n                isPaused: false\r\n            }\r\n        },\r\n        beforeDestroy() {\r\n            this.clearCarousel()\r\n        },\r\n        mounted() {\r\n            this.startCarousel();\r\n            // 导入 Font Awesome 图标库\r\n            if (!document.getElementById('font-awesome')) {\r\n                const link = document.createElement('link');\r\n                link.id = 'font-awesome';\r\n                link.rel = 'stylesheet';\r\n                link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';\r\n                document.head.appendChild(link);\r\n            }\r\n        },\r\n\r\n        methods: {\r\n            witde(index){\r\n                this.currentQuestionIndex = index;\r\n                this.pauseCarousel();\r\n            },\r\n            startCarousel() {\r\n                let that = this\r\n                this.clearCarousel();\r\n                this.carouselTimer = setInterval(() => {\r\n                    if (!that.isPaused) {\r\n                        this.currentQuestionIndex =\r\n                            (this.currentQuestionIndex + 1) % this.questions.length\r\n                        console.log(\"数据\", this.currentQuestionIndex)\r\n                        console.log(\"ispasued\",that.isPaused)\r\n                    }\r\n                }, this.carouselInterval)\r\n            },\r\n            pauseCarousel() {\r\n                this.isPaused = true;\r\n            },\r\n            resumeCarousel() {\r\n                this.isPaused = false;\r\n            },\r\n            clearCarousel() {\r\n                if (this.carouselTimer) {\r\n                    clearInterval(this.carouselTimer);\r\n                    this.carouselTimer = null;\r\n                }\r\n            },\r\n            // 点击轮播问题自动提问\r\n            sendCarouselQuestion(question) {\r\n                this.userInput = question;\r\n                this.sendMessage();\r\n            },\r\n            toggleChat() {\r\n                this.showChat = !this.showChat;\r\n\r\n                if (this.showChat) {\r\n                    this.$nextTick(() => {\r\n                        this.scrollToBottom();\r\n                    });\r\n                }\r\n            },\r\n\r\n            async sendMessage() {\r\n                if (!this.userInput.trim() || this.loading) return;\r\n\r\n                // 添加用户消息\r\n                this.messages.push({\r\n                    type: 'user',\r\n                    text: this.userInput,\r\n                    time: new Date()\r\n                });\r\n\r\n                const userQuestion = this.userInput;\r\n                this.userInput = '';\r\n                this.loading = true;\r\n                //添加历史记录\r\n                this.historyMessages.push({\r\n                    role:'user',\r\n                    content:userQuestion,\r\n                })\r\n\r\n                // 构造请求体\r\n                const requestBody = {\r\n                    model: 'Qwen/QwQ-32B',\r\n                    messages: [{role:\"system\",content: \"你是天工开物算力租赁网站的客服，当我问到算力租赁相关问题的时候再进行回答，如果我的问题与算力无关则回答“小天也不知道怎么办了呢，不如我们来聊聊算力相关的话题吧！”,如果问题涉及到你是谁或者你好的这种礼貌性踊跃，可以进行礼貌性的回复\"},...this.historyMessages], // 携带上下文历史\r\n                    stream: true,\r\n                    options: {\r\n                        presence_penalty: 1.2,  // 重复内容惩罚（0-2）\r\n                        frequency_penalty: 1.5, // 高频词惩罚（0-2）\r\n                        // repeat_last_n: 64,      // 检查重复的上下文长度\r\n                        seed: 12345             // 固定随机种子\r\n                    }\r\n                };\r\n                // 滚动到底部\r\n                this.$nextTick(() => {\r\n                    this.scrollToBottom();\r\n                });\r\n\r\n                try {\r\n                    // 调用后端API获取回复\r\n                    // 替换为你的实际API\r\n                    const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {\r\n                        method: 'POST',\r\n                        headers: {\r\n                            Authorization: 'Bearer sk-yxjtnfarjndtcwbjwmscdejrgtlpaaebnfdxcfnhhuanbqty',\r\n                            'Content-Type': 'application/json',\r\n                        },\r\n                        body: JSON.stringify(requestBody)\r\n                    });\r\n\r\n                    // 处理流式数据\r\n                    const reader = response.body.getReader();\r\n                    const decoder = new TextDecoder();\r\n                    const aiResponseIndex = this.messages.push({\r\n                        type: 'bot',\r\n                        text: '',\r\n                        time:new Date()\r\n                    }) - 1;\r\n                    while (true) {\r\n                        const { done, value } = await reader.read();\r\n                        if (done) break;\r\n\r\n                        // 解析流式数据块（可能包含多个JSON对象）\r\n                        const chunk = decoder.decode(value);\r\n                        const lines = chunk.split('\\n').filter(line => line.trim());\r\n\r\n                        for (const line of lines) {\r\n                            try {\r\n                                const jsonString = line.slice(6).trim();\r\n                                if (jsonString === \"\" || jsonString === \"[DONE]\") continue;\r\n\r\n                                let data = JSON.parse(jsonString)\r\n                                if (data.choices) {\r\n                                    if (data.choices[0].delta.reasoning_content!=null){\r\n                                        continue\r\n                                    }\r\n                                    if (data.choices[0].delta.content == '\\n\\n'){\r\n                                        continue\r\n                                    }\r\n                                    this.messages[aiResponseIndex].text += data.choices[0].delta.content;\r\n                                }\r\n                            } catch (e) {\r\n                            }\r\n                        }\r\n                    }\r\n                    this.historyMessages.push({\r\n                        role:\"assistant\",\r\n                        content:this.messages[aiResponseIndex].text\r\n                    })\r\n\r\n                    // 添加机器人回复\r\n                    // this.messages.push({\r\n                    //     type: 'bot',\r\n                    //     text: response,\r\n                    //     time: new Date()\r\n                    // });\r\n                } catch (error) {\r\n\r\n                    // 添加错误消息\r\n                    this.messages.push({\r\n                        type: 'bot',\r\n                        text: '抱歉，系统暂时无法响应，请稍后再试。',\r\n                        time: new Date()\r\n                    });\r\n                } finally {\r\n                    this.loading = false;\r\n\r\n                    // 滚动到底部\r\n                    this.$nextTick(() => {\r\n                        this.scrollToBottom();\r\n                    });\r\n                }\r\n            },\r\n\r\n            // 模拟API调用，实际使用时替换为真实API\r\n            async callChatAPI(message) {\r\n                // 模拟网络延迟\r\n                await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n                // TODO: 替换为实际的API调用\r\n                // const response = await fetch('YOUR_API_ENDPOINT', {\r\n                //   method: 'POST',\r\n                //   headers: {\r\n                //     'Content-Type': 'application/json',\r\n                //   },\r\n                //   body: JSON.stringify({ message }),\r\n                // });\r\n                // return await response.json();\r\n\r\n                // 模拟返回数据\r\n                return `感谢您的提问: \"${message}\"。这是一个模拟回复，请替换为真实API调用。`;\r\n            },\r\n\r\n            scrollToBottom() {\r\n                const container = this.$refs.messagesContainer;\r\n                container.scrollTop = container.scrollHeight;\r\n            },\r\n\r\n            formatTime(date) {\r\n                return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n            },\r\n\r\n            formatMessage(text) {\r\n                // 处理文本中的链接、表情等\r\n                return text\r\n                    .replace(/\\n/g, '<br>')\r\n                    .replace(/(https?:\\/\\/[^\\s]+)/g, '<a href=\"$1\" target=\"_blank\">$1</a>');\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n    /* 修正后的轮播样式 */\r\n    .chat-container {\r\n        position: fixed;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n        gap: 10px;\r\n        z-index: 1001;\r\n    }\r\n\r\n    .question-carousel {\r\n        left: 20px;\r\n        padding-bottom: 85px;\r\n        padding-top: 20px;\r\n        color: white;\r\n        min-width: 150px;\r\n        text-align: left;\r\n        cursor: pointer;\r\n        overflow: hidden;\r\n        position: relative;\r\n        height: 60px; /* 固定高度避免跳动 */\r\n    }\r\n\r\n    .carousel-wrapper {\r\n        position: relative;\r\n        height: 100%;\r\n    }\r\n\r\n    .question-item {\r\n        position: absolute;\r\n        border-radius: 20px 20px 20px 20px;\r\n        background-color: black;\r\n        width: 100%;\r\n        left: 0;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        padding: 10px 10px;\r\n        font-size: 14px;\r\n        opacity: 1;\r\n        transition: all 0.5s ease;\r\n    }\r\n\r\n    .question-item:hover {\r\n        color: #4286f4;\r\n    }\r\n\r\n    /* 过渡动画修正 */\r\n    .slide-enter-active,\r\n    .slide-leave-active {\r\n        transition: all 0.5s ease;\r\n    }\r\n    .slide-enter-from {\r\n        opacity: 0;\r\n        transform: translateY(20px) translateY(-50%);\r\n    }\r\n    .slide-leave-to {\r\n        opacity: 0;\r\n        transform: translateY(-20px) translateY(-50%);\r\n    }\r\n    .slide-enter-to,\r\n    .slide-leave-from {\r\n        opacity: 1;\r\n        transform: translateY(0) translateY(-50%);\r\n    }\r\n    .chat-icon {\r\n        position: fixed;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        width: 60px;\r\n        height: 60px;\r\n        background-color: blue;\r\n        border-radius: 50%;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n        transition: all 0.3s ease;\r\n        z-index: 1001;\r\n    }\r\n\r\n    .chat-icon i {\r\n        color: white;\r\n        font-size: 24px;\r\n    }\r\n\r\n    .chat-icon:hover, .chat-icon-active {\r\n        background-color: #3367d6;\r\n        transform: scale(1.05);\r\n    }\r\n\r\n    .chat-window {\r\n        position: fixed;\r\n        bottom: 90px;\r\n        right: 20px;\r\n        width: 350px;\r\n        height: 500px;\r\n        background-color: #fff;\r\n        border-radius: 10px;\r\n        box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16);\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: hidden;\r\n        z-index: 1002;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 15px;\r\n        background-color: #4286f4;\r\n        color: white;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n    }\r\n\r\n    .chat-title {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 10px;\r\n        font-weight: bold;\r\n    }\r\n\r\n    .chat-controls i {\r\n        cursor: pointer;\r\n        font-size: 18px;\r\n    }\r\n\r\n    .chat-messages {\r\n        flex: 1;\r\n        padding: 15px;\r\n        overflow-y: auto;\r\n        background-color: #f5f5f5;\r\n    }\r\n\r\n    .message {\r\n        display: flex;\r\n        margin-bottom: 15px;\r\n        align-items: flex-start;\r\n    }\r\n\r\n    .message.user {\r\n        flex-direction: row-reverse;\r\n    }\r\n\r\n    .avatar {\r\n        width: 36px;\r\n        height: 36px;\r\n        border-radius: 50%;\r\n        background-color: #4286f4;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        color: white;\r\n        margin-right: 10px;\r\n    }\r\n\r\n    .message-content {\r\n        max-width: 70%;\r\n    }\r\n\r\n    .message-text {\r\n        padding: 10px 15px;\r\n        border-radius: 18px;\r\n        margin-bottom: 5px;\r\n        word-break: break-word;\r\n    }\r\n\r\n    .message.bot .message-text {\r\n        background-color: white;\r\n        border: 1px solid #e0e0e0;\r\n    }\r\n\r\n    .message.user .message-text {\r\n        background-color: #4286f4;\r\n        color: white;\r\n        text-align: right;\r\n    }\r\n\r\n    .message-time {\r\n        font-size: 12px;\r\n        color: #999;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .message.user .message-time {\r\n        text-align: right;\r\n    }\r\n\r\n    .typing-indicator {\r\n        display: flex;\r\n        padding: 10px 15px;\r\n        background-color: white;\r\n        border-radius: 18px;\r\n        border: 1px solid #e0e0e0;\r\n        width: fit-content;\r\n        margin-bottom: 15px;\r\n    }\r\n\r\n    .dot {\r\n        width: 8px;\r\n        height: 8px;\r\n        background-color: #999;\r\n        border-radius: 50%;\r\n        margin: 0 2px;\r\n        animation: bounce 1.5s infinite;\r\n    }\r\n\r\n    .dot:nth-child(2) {\r\n        animation-delay: 0.2s;\r\n    }\r\n\r\n    .dot:nth-child(3) {\r\n        animation-delay: 0.4s;\r\n    }\r\n\r\n    @keyframes bounce {\r\n        0%, 60%, 100% {\r\n            transform: translateY(0);\r\n        }\r\n        30% {\r\n            transform: translateY(-4px);\r\n        }\r\n    }\r\n\r\n    .chat-input {\r\n        padding: 15px;\r\n        display: flex;\r\n        border-top: 1px solid #e0e0e0;\r\n        background-color: white;\r\n    }\r\n\r\n    .chat-input input {\r\n        flex: 1;\r\n        padding: 10px 15px;\r\n        border: 1px solid #e0e0e0;\r\n        border-radius: 20px;\r\n        font-size: 14px;\r\n        outline: none;\r\n    }\r\n\r\n    .chat-input button {\r\n        margin-left: 10px;\r\n        width: 40px;\r\n        height: 40px;\r\n        border-radius: 50%;\r\n        background-color: #4286f4;\r\n        color: white;\r\n        border: none;\r\n        cursor: pointer;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n    }\r\n\r\n    .chat-input button:disabled {\r\n        background-color: #b3c9f4;\r\n        cursor: not-allowed;\r\n    }\r\n\r\n    .chat-input button i {\r\n        font-size: 16px;\r\n    }\r\n\r\n    /* 移动端适配 */\r\n    @media (max-width: 480px) {\r\n        .chat-window {\r\n            width: 100%;\r\n            height: 100%;\r\n            bottom: 0;\r\n            right: 0;\r\n            border-radius: 0;\r\n        }\r\n\r\n        .chat-icon {\r\n            bottom: 15px;\r\n            right: 15px;\r\n        }\r\n    }\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./chatAi.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./chatAi.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./chatAi.vue?vue&type=template&id=46c63c47&scoped=true&\"\nimport script from \"./chatAi.vue?vue&type=script&lang=js&\"\nexport * from \"./chatAi.vue?vue&type=script&lang=js&\"\nimport style0 from \"./chatAi.vue?vue&type=style&index=0&id=46c63c47&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"46c63c47\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(!_vm.isMobile)?_c('div',{staticClass:\"desktop-layout\"},[_c('div',{staticClass:\"banner-section\"},[_c('div',{staticClass:\"banner-container\"},[_c('div',{staticClass:\"big-box\"},[_c('div',{staticClass:\"img-box\"},[_c('div',{staticClass:\"show-box\",style:({\n                  transform: 'translateX(' + _vm.translate + ')',\n                  transition: _vm.tsion ? 'all 0.5s' : 'none',\n                })},_vm._l((_vm.bannerImages),function(item,index){return _c('div',{key:index,staticClass:\"slide-item\"},[_c('img',{attrs:{\"src\":item.img,\"alt\":\"\"}}),_c('div',{staticClass:\"banner-content\",class:'pos-' + item.content.position},[_c('h2',{staticClass:\"banner-title\",domProps:{\"innerHTML\":_vm._s(item.content.title)}}),_c('p',{staticClass:\"banner-text\"},[_vm._v(_vm._s(item.content.text))]),_c('div',{staticClass:\"banner-actions\"},[(!_vm.isLogin && item.content.secondaryLink)?_c('a',{staticClass:\"banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.secondaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.secondaryBtnText)+\" \")]):_vm._e(),(!_vm.isLogin && item.content.primaryLink)?_c('a',{staticClass:\"banner-button primary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.primaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.primaryBtnText)+\" \")]):_vm._e(),(item.content.thirdLink)?_c('a',{staticClass:\"banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.thirdLink)}}},[_vm._v(\" \"+_vm._s(item.content.thirdBtnText)+\" \")]):_vm._e()])])])}),0)]),_c('div',{staticClass:\"arrowhead-box\"},[_c('span',{staticClass:\"nav-arrow left\",on:{\"click\":_vm.last}},[_c('img',{staticClass:\"arrow-icon rotated\",attrs:{\"src\":require(\"../../assets/images/index/right-arrow.png\"),\"alt\":\"\"}})]),_c('span',{staticClass:\"nav-arrow right\",on:{\"click\":_vm.next}},[_c('img',{staticClass:\"arrow-icon\",attrs:{\"src\":require(\"../../assets/images/index/right-arrow.png\"),\"alt\":\"\"}})])]),_c('div',{ref:\"swiperPagination\",staticClass:\"swiper-pagination\"},_vm._l((_vm.bannerImages),function(item,index){return _c('span',{key:index,class:{ active: _vm.translateX === index }})}),0)])])]),_c('section',{staticClass:\"section gpu-section\"},[_c('div',{staticClass:\"container\"},[_vm._m(0),_c('div',{staticClass:\"gpu-card-grid\"},_vm._l((_vm.gpus),function(gpu,index){return _c('div',{key:index,staticClass:\"gpu-card\",class:{ 'recommended': gpu.recommended },on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('div',{staticClass:\"gpu-card-header\"},[_c('h3',{staticClass:\"gpu-name\"},[_vm._v(_vm._s(gpu.name))]),(gpu.recommended)?_c('span',{staticClass:\"recommendation-tag\"},[_vm._v(\"推荐\")]):_vm._e(),(gpu.isNew)?_c('span',{staticClass:\"new-tag\"},[_vm._v(\"NEW\")]):_vm._e()]),_c('div',{staticClass:\"gpu-specs-pricing\"},[_c('div',{staticClass:\"specs-section\"},[_c('div',{staticClass:\"spec-item\"},[_c('span',{staticClass:\"spec-label\"},[_vm._v(\"单精度:\")]),_c('span',{staticClass:\"spec-value\"},[_vm._v(_vm._s(gpu.singlePrecision)+\" TFLOPS\")])]),_c('div',{staticClass:\"spec-item\"},[_c('span',{staticClass:\"spec-label\"},[_vm._v(\"半精度:\")]),_c('span',{staticClass:\"spec-value\"},[_vm._v(_vm._s(gpu.halfPrecision)+\" Tensor TFL\")])])]),_c('div',{staticClass:\"price-section\"},[_c('div',{staticClass:\"gpu-pricing\"},[(gpu.originalPrice)?_c('span',{staticClass:\"original-price\"},[_vm._v(\"¥\"+_vm._s(gpu.originalPrice)+\"/时\")]):_vm._e(),_c('span',{staticClass:\"current-price\"},[_vm._v(\"¥\"),_c('span',{staticClass:\"price-value\"},[_vm._v(_vm._s(gpu.price))]),_vm._v(\"/时\")])])])])])}),0)])]),_c('GpuComparison'),_c('section',{staticClass:\"section services-section\"},[_c('div',{staticClass:\"container\"},[_vm._m(1),_c('div',{staticClass:\"services-grid\"},_vm._l((_vm.serviceList),function(service,index){return _c('div',{key:index,staticClass:\"service-item\"},[_c('div',{staticClass:\"service-card\"},[_c('i',{staticClass:\"service-icon\",class:service.icon}),_c('h3',{staticClass:\"service-title\"},[_vm._v(_vm._s(service.title))]),_c('div',{staticClass:\"service-text\"},[_c('p',[_vm._v(_vm._s(service.desc))])])])])}),0)])]),_c('section',{staticClass:\"appsec-section\"},[_vm._m(2),_c('div',{staticClass:\"appsec-container\"},[_c('div',{staticClass:\"appsec-grid\"},[_c('div',{staticClass:\"appsec-item appsec-wide\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){_vm.firstRowWide.hover = true},\"mouseleave\":function($event){_vm.firstRowWide.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': _vm.firstRowWide.hover }},[_c('img',{attrs:{\"src\":_vm.firstRowWide.image,\"alt\":_vm.firstRowWide.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': _vm.firstRowWide.hover }},[_vm._v(_vm._s(_vm.firstRowWide.title))])])]),_vm._l((_vm.firstRowTallApps),function(app,index){return _c('div',{key:'tall-'+index,staticClass:\"appsec-item appsec-tall\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){app.hover = true},\"mouseleave\":function($event){app.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': app.hover }},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': app.hover }},[_vm._v(_vm._s(app.title))])])])}),_vm._l((_vm.secondRowApps),function(app,index){return _c('div',{key:'small-'+index,staticClass:\"appsec-item appsec-small\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){app.hover = true},\"mouseleave\":function($event){app.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': app.hover }},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': app.hover }},[_vm._v(_vm._s(app.title))])])])}),_vm._l((_vm.thirdRowSmallApps),function(app,index){return _c('div',{key:'third-small-'+index,staticClass:\"appsec-item appsec-small\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){app.hover = true},\"mouseleave\":function($event){app.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': app.hover }},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': app.hover }},[_vm._v(_vm._s(app.title))])])])}),_c('div',{staticClass:\"appsec-item appsec-wide\"},[_c('div',{staticClass:\"appsec-card\",on:{\"mouseover\":function($event){_vm.thirdRowWide.hover = true},\"mouseleave\":function($event){_vm.thirdRowWide.hover = false}}},[_c('div',{staticClass:\"appsec-image\",class:{ 'appsec-hover': _vm.thirdRowWide.hover }},[_c('img',{attrs:{\"src\":_vm.thirdRowWide.image,\"alt\":_vm.thirdRowWide.title}})]),_c('div',{staticClass:\"appsec-cardtitle\",class:{ 'appsec-hover': _vm.thirdRowWide.hover }},[_vm._v(_vm._s(_vm.thirdRowWide.title))])])])],2),_c('chat-ai')],1)]),_c('div',{staticClass:\"recommendation-tag1\"},[_c('div',{staticClass:\"card1\"},[_c('h1',{staticClass:\"banner-text1\"},[_vm._v(\"为AI+千行百业，提供高性能算力服务\")]),_c('button',{staticClass:\"consult-button1\",on:{\"click\":_vm.openContactModal}},[_vm._v(\"立即咨询\")])])]),_c('transition',{attrs:{\"name\":\"fade\"}},[(_vm.showContactModal)?_c('div',{staticClass:\"contact-modal-overlay\",on:{\"click\":function($event){if($event.target !== $event.currentTarget)return null;return _vm.closeContactModal.apply(null, arguments)}}},[_c('div',{staticClass:\"contact-modal\"},[_c('button',{staticClass:\"close-modal\",on:{\"click\":_vm.closeContactModal}},[_vm._v(\" × \")]),_c('div',{staticClass:\"contact-content\"},[_c('div',{staticClass:\"contact-item\"},[_c('i',{staticClass:\"am-icon-user\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.name))])]),_c('div',{staticClass:\"contact-item\"},[_c('i',{staticClass:\"am-icon-phone\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.phone))])])]),_c('div',{staticClass:\"contact-note\"},[_c('p',[_vm._v(\"欢迎随时来电咨询\")])])])]):_vm._e()])],1):_c('div',{staticClass:\"mobile-layout\"},[_c('div',{staticClass:\"mobile-banner\",on:{\"touchstart\":_vm.handleTouchStart,\"touchmove\":_vm.handleTouchMove,\"touchend\":_vm.handleTouchEnd}},[_c('div',{staticClass:\"mobile-banner-slider\",style:({ transform: `translateX(${-_vm.mobileCurrentSlide * 100}%)` })},_vm._l((_vm.bannerImages),function(item,index){return _c('div',{key:index,staticClass:\"mobile-slide\"},[_c('div',{staticClass:\"mobile-slide-inner\"},[_c('img',{staticClass:\"mobile-slide-img\",attrs:{\"src\":item.img,\"alt\":\"\"}}),_c('div',{staticClass:\"mobile-banner-content\",class:'pos-' + item.content.position},[_c('h2',{staticClass:\"mobile-banner-title\",domProps:{\"innerHTML\":_vm._s(item.content.title)}}),_c('p',{staticClass:\"mobile-banner-text\"},[_vm._v(_vm._s(item.content.text))]),_c('div',{staticClass:\"mobile-banner-actions\"},[(!_vm.isLogin && item.content.secondaryLink)?_c('a',{staticClass:\"mobile-banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.secondaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.secondaryBtnText)+\" \")]):_vm._e(),(!_vm.isLogin && item.content.primaryLink)?_c('a',{staticClass:\"mobile-banner-button primary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.primaryLink)}}},[_vm._v(\" \"+_vm._s(item.content.primaryBtnText)+\" \")]):_vm._e(),(item.content.thirdLink)?_c('a',{staticClass:\"banner-button secondary-btn\",attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.navigateTo(item.content.thirdLink)}}},[_vm._v(\" \"+_vm._s(item.content.thirdBtnText)+\" \")]):_vm._e()])])])])}),0),_c('div',{staticClass:\"mobile-banner-pagination\"},_vm._l((_vm.bannerImages),function(item,index){return _c('span',{key:index,class:{ active: _vm.mobileCurrentSlide === index },on:{\"click\":function($event){return _vm.goToSlide(index)}}})}),0)]),_c('section',{staticClass:\"mobile-section mobile-gpu-section\"},[_vm._m(3),_c('div',{staticClass:\"mobile-gpu-list\"},_vm._l((_vm.gpus),function(gpu,index){return _c('div',{key:index,staticClass:\"mobile-gpu-card\",class:{ 'recommended': gpu.recommended },on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('div',{staticClass:\"mobile-gpu-header\"},[_c('h3',{staticClass:\"mobile-gpu-name\"},[_vm._v(_vm._s(gpu.name))]),_c('div',{staticClass:\"mobile-gpu-tags\"},[(gpu.recommended)?_c('span',{staticClass:\"mobile-recommend-tag\"},[_vm._v(\"推荐\")]):_vm._e(),(gpu.isNew)?_c('span',{staticClass:\"mobile-new-tag\"},[_vm._v(\"NEW\")]):_vm._e()])]),_c('div',{staticClass:\"mobile-gpu-specs\"},[_c('div',{staticClass:\"mobile-spec-item\"},[_c('span',{staticClass:\"mobile-spec-label\"},[_vm._v(\"单精度:\")]),_c('span',{staticClass:\"mobile-spec-value\"},[_vm._v(_vm._s(gpu.singlePrecision)+\" TFLOPS\")])]),_c('div',{staticClass:\"mobile-spec-item\"},[_c('span',{staticClass:\"mobile-spec-label\"},[_vm._v(\"半精度:\")]),_c('span',{staticClass:\"mobile-spec-value\"},[_vm._v(_vm._s(gpu.halfPrecision)+\" Tensor TFL\")])])]),_c('div',{staticClass:\"mobile-gpu-price\"},[(gpu.originalPrice)?_c('span',{staticClass:\"mobile-original-price\"},[_vm._v(\"¥\"+_vm._s(gpu.originalPrice)+\"/时\")]):_vm._e(),_c('span',{staticClass:\"mobile-current-price\"},[_vm._v(\"¥\"+_vm._s(gpu.price)+\"/时\")])])])}),0)]),_c('section',{staticClass:\"mobile-section mobile-comparison-section\"},[_vm._m(4),_c('div',{staticClass:\"mobile-comparison-container\"},[_c('div',{staticClass:\"mobile-comparison-scroll\"},[_c('table',{staticClass:\"mobile-comparison-table\"},[_c('thead',[_c('tr',[_c('th',[_vm._v(\"GPU型号\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('th',{key:gpu.name},[_vm._v(_vm._s(gpu.name))])})],2)]),_c('tbody',[_c('tr',[_c('td',[_vm._v(\"架构\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.architecture))])})],2),_c('tr',[_c('td',[_vm._v(\"FP16性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp16Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"FP32性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp32Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"显存\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memory))])})],2),_c('tr',[_c('td',[_vm._v(\"显存类型\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memoryType))])})],2),_c('tr',[_c('td',[_vm._v(\"带宽\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.bandwidth))])})],2)])])])])]),_c('section',{staticClass:\"mobile-section mobile-services-section\"},[_vm._m(5),_c('div',{staticClass:\"mobile-services-list\"},_vm._l((_vm.serviceList),function(service,index){return _c('div',{key:index,staticClass:\"mobile-service-card\"},[_c('div',{staticClass:\"mobile-service-icon\"},[_c('i',{class:service.icon})]),_c('h3',{staticClass:\"mobile-service-title\"},[_vm._v(_vm._s(service.title))]),_c('p',{staticClass:\"mobile-service-desc\"},[_vm._v(_vm._s(service.desc))])])}),0)]),_c('section',{staticClass:\"mobile-section mobile-applications-section\"},[_vm._m(6),_c('div',{staticClass:\"mobile-applications-grid\"},_vm._l((_vm.mobileApplications),function(app,index){return _c('div',{key:index,staticClass:\"mobile-app-item\"},[_c('div',{staticClass:\"mobile-app-image\"},[_c('img',{attrs:{\"src\":app.image,\"alt\":app.title}})]),_c('h3',{staticClass:\"mobile-app-title\"},[_vm._v(_vm._s(app.title))])])}),0)]),_c('div',{staticClass:\"mobile-consult-section\"},[_c('h3',{staticClass:\"mobile-consult-title\"},[_vm._v(\"为AI+千行百业，提供高性能算力服务\")]),_c('button',{staticClass:\"mobile-consult-button\",on:{\"click\":_vm.openContactModal}},[_vm._v(\"立即咨询\")])]),_c('transition',{attrs:{\"name\":\"mobile-fade\"}},[(_vm.showContactModal)?_c('div',{staticClass:\"mobile-contact-overlay\",on:{\"click\":function($event){if($event.target !== $event.currentTarget)return null;return _vm.closeContactModal.apply(null, arguments)}}},[_c('div',{staticClass:\"mobile-contact-modal\"},[_c('button',{staticClass:\"mobile-close-modal\",on:{\"click\":_vm.closeContactModal}},[_vm._v(\" × \")]),_c('div',{staticClass:\"mobile-contact-content\"},[_c('div',{staticClass:\"mobile-contact-item\"},[_c('i',{staticClass:\"am-icon-user\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.name))])]),_c('div',{staticClass:\"mobile-contact-item\"},[_c('i',{staticClass:\"am-icon-phone\"}),_c('span',[_vm._v(_vm._s(_vm.contactInfo.phone))])])]),_c('div',{staticClass:\"mobile-contact-note\"},[_c('p',[_vm._v(\"欢迎随时来电咨询\")])])])]):_vm._e()])],1),(!_vm.isMobile)?_c('Mider'):_vm._e(),_c('Footer'),_c('chatAi')],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"为您推荐\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" 专注于提供高性能、稳定可靠的 GPU 算力服务，兼具灵活配置与优质性价比。 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"核心优势\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴。 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"行业应用\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" Applications \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"为您推荐\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" 专注于提供高性能、稳定可靠的 GPU 算力服务 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"GPU性能对比\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" 专业GPU性能详细对比 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"核心优势\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" 专业铸造优秀,天工开物企业AI变革路上的好伙伴 \")])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mobile-section-header\"},[_c('h2',{staticClass:\"mobile-section-title\"},[_vm._v(\"行业应用\")]),_c('p',{staticClass:\"mobile-section-description\"},[_vm._v(\" Applications \")])])\n}]\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('section',{staticClass:\"section gpu-comparison-section\"},[_c('div',{staticClass:\"container\"},[_vm._m(0),_c('div',{staticClass:\"gpu-comparison-table\"},[_c('table',[_c('thead',[_c('tr',[_c('th',[_vm._v(\"GPU型号\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('th',{key:gpu.name},[_vm._v(_vm._s(gpu.name))])})],2)]),_c('tbody',[_c('tr',[_c('td',[_vm._v(\"架构\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.architecture))])})],2),_c('tr',[_c('td',[_vm._v(\"FP16性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp16Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"FP32性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp32Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"显存\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memory))])})],2),_c('tr',[_c('td',[_vm._v(\"显存类型\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memoryType))])})],2),_c('tr',[_c('td',[_vm._v(\"带宽\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.bandwidth))])})],2)])])])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"GPU性能对比\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" 专业GPU性能详细对比，助您选择最适合的计算资源 \")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <section class=\"section gpu-comparison-section\">\r\n    <div class=\"container\">\r\n      <div class=\"section-header\">\r\n        <h2 class=\"section-title\">GPU性能对比</h2>\r\n        <p class=\"section-description\">\r\n          专业GPU性能详细对比，助您选择最适合的计算资源\r\n        </p>\r\n      </div>\r\n\r\n      <div class=\"gpu-comparison-table\">\r\n        <table>\r\n          <thead>\r\n          <tr>\r\n            <th>GPU型号</th>\r\n            <th v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.name }}</th>\r\n          </tr>\r\n          </thead>\r\n          <tbody>\r\n          <tr>\r\n            <td>架构</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.architecture }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>FP16性能</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp16Performance }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>FP32性能</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp32Performance }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>显存</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memory }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>显存类型</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memoryType }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>带宽</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.bandwidth }}</td>\r\n          </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </div>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport {getNotAuth} from \"@/api/login\";\r\n\r\nexport default {\r\n  name: 'GpuComparison',\r\n  data() {\r\n    return {\r\n      comparisonGpus: [\r\n        {\r\n          name: 'A100',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '312 TFLOPS',\r\n          fp32Performance: '19.5 TFLOPS',\r\n          memory: '80 GB',\r\n          memoryType: 'HBM2',\r\n          bandwidth: '2,039 GB/s'\r\n        },\r\n        {\r\n          name: 'V100',\r\n          architecture: 'Volta',\r\n          fp16Performance: '125 TFLOPS',\r\n          fp32Performance: '15.7 TFLOPS',\r\n          memory: '32 GB',\r\n          memoryType: 'HBM2',\r\n          bandwidth: '900 GB/s'\r\n        },\r\n        {\r\n          name: 'A6000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '77.4 TFLOPS',\r\n          fp32Performance: '38.7 TFLOPS',\r\n          memory: '48 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '768 GB/s'\r\n        },\r\n        {\r\n          name: 'A5000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '54.2 TFLOPS',\r\n          fp32Performance: '27.8 TFLOPS',\r\n          memory: '24 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '768 GB/s'\r\n        },\r\n        {\r\n          name: 'A4000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '19.17 TFLOPS',\r\n          fp32Performance: '19.17 TFLOPS',\r\n          memory: '16 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '448 GB/s'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchComparison();\r\n  },\r\n  methods: {\r\n    async fetchComparison() {\r\n      try {\r\n        // console.log(\"开始获取对比数据\")\r\n        getNotAuth(\"/system/comparison/list\").then(req =>{\r\n          // console.log(\"原始数据\",req.data.rows)\r\n          this.comparisonGpus = req.data.rows.map(item => ({\r\n            ...item,\r\n            fp16Performance: item.fp16performance,\r\n            fp32Performance: item.fp32performance,\r\n            memoryType:item.memorytype\r\n          }))\r\n          // this.gpus = req.data.rows\r\n          // console.log(\"数据\",this.gpus)\r\n        })\r\n      } catch (error) {\r\n        console.error('获取GPU推荐列表失败:', error);\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.gpu-comparison-section {\r\n  /*padding-top: 10vh;*/\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n  padding-bottom: 15vh;\r\n}\r\n\r\n.container {\r\n  width: 93%;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.section-header {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 28px;\r\n  color: #333;\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.section-title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -5px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 50px;\r\n  height: 3px;\r\n  background-color: #2196f3;\r\n}\r\n\r\n.section-description {\r\n  color: #666;\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n.gpu-comparison-table {\r\n  width: 100%;\r\n  padding: 0;\r\n  border-collapse: collapse;\r\n  margin: 0;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  overflow-x: auto;\r\n}\r\n\r\n.gpu-comparison-table table {\r\n  width: 100%;\r\n  min-width: 1000px;\r\n}\r\n\r\n.gpu-comparison-table thead {\r\n  background-color: #cddfec;\r\n}\r\n\r\n.gpu-comparison-table thead tr th,\r\n.gpu-comparison-table tbody tr td {\r\n  padding: 18px 25px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  min-width: 180px;\r\n  border-bottom: 1px solid #e0e5eb;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.gpu-comparison-table thead tr th {\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.gpu-comparison-table tbody tr td {\r\n  color: #333;\r\n  font-weight: normal;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:nth-child(even) {\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:hover {\r\n  background-color: #f1f6fd;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .container {\r\n    width: 98%;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .section-title {\r\n    font-size: 26px;\r\n    color: #333;\r\n  }\r\n\r\n  .section-description {\r\n    font-size: 16px;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./GpuComparison.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./GpuComparison.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./GpuComparison.vue?vue&type=template&id=fed986aa&scoped=true&\"\nimport script from \"./GpuComparison.vue?vue&type=script&lang=js&\"\nexport * from \"./GpuComparison.vue?vue&type=script&lang=js&\"\nimport style0 from \"./GpuComparison.vue?vue&type=style&index=0&id=fed986aa&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fed986aa\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\r\n  <div>\r\n\r\n    <!-- 电脑端布局 -->\r\n    <div v-if=\"!isMobile\" class=\"desktop-layout\">\r\n      <!-- Banner Section -->\r\n      <div class=\"banner-section\">\r\n        <div class=\"banner-container\">\r\n          <div class=\"big-box\">\r\n            <div class=\"img-box\">\r\n              <div class=\"show-box\"\r\n                   :style=\"{\r\n                    transform: 'translateX(' + translate + ')',\r\n                    transition: tsion ? 'all 0.5s' : 'none',\r\n                  }\">\r\n                <div class=\"slide-item\" v-for=\"(item, index) in bannerImages\" :key=\"index\">\r\n                  <img :src=\"item.img\" alt=\"\" />\r\n                  <div class=\"banner-content\" :class=\"'pos-' + item.content.position\">\r\n                    <h2 class=\"banner-title\" v-html=\"item.content.title\"></h2>\r\n                    <p class=\"banner-text\">{{ item.content.text }}</p>\r\n                    <div class=\"banner-actions\">\r\n                      <a v-if=\"!isLogin && item.content.secondaryLink\"\r\n                         href=\"#\"\r\n                         @click.prevent=\"navigateTo(item.content.secondaryLink)\"\r\n                         class=\"banner-button secondary-btn\">\r\n                        {{ item.content.secondaryBtnText }}\r\n                      </a>\r\n                      <a v-if=\"!isLogin && item.content.primaryLink\"\r\n                         href=\"#\"\r\n                         @click.prevent=\"navigateTo(item.content.primaryLink)\"\r\n                         class=\"banner-button primary-btn\">\r\n                        {{ item.content.primaryBtnText }}\r\n                      </a>\r\n                      <a v-if=\"item.content.thirdLink\"\r\n                         href=\"#\"\r\n                         @click.prevent=\"navigateTo(item.content.thirdLink)\"\r\n                         class=\"banner-button secondary-btn\">\r\n                        {{ item.content.thirdBtnText }}\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"arrowhead-box\">\r\n              <span @click=\"last\" class=\"nav-arrow left\">\r\n                <img src=\"../../assets/images/index/right-arrow.png\" alt=\"\" class=\"arrow-icon rotated\">\r\n              </span>\r\n              <span @click=\"next\" class=\"nav-arrow right\">\r\n                <img src=\"../../assets/images/index/right-arrow.png\" alt=\"\" class=\"arrow-icon\">\r\n              </span>\r\n            </div>\r\n            <div class=\"swiper-pagination\" ref=\"swiperPagination\">\r\n              <span v-for=\"(item, index) in bannerImages\" :key=\"index\" :class=\"{ active: translateX === index }\"></span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 为您推荐 Section  -->\r\n      <section class=\"section gpu-section\">\r\n        <div class=\"container\">\r\n          <div class=\"section-header\">\r\n            <h2 class=\"section-title\">为您推荐</h2>\r\n            <p class=\"section-description\">\r\n              专注于提供高性能、稳定可靠的 GPU 算力服务，兼具灵活配置与优质性价比。\r\n            </p>\r\n          </div>\r\n\r\n          <div class=\"gpu-card-grid\">\r\n            <div\r\n                v-for=\"(gpu, index) in gpus\"\r\n                :key=\"index\"\r\n                class=\"gpu-card\"\r\n                :class=\"{ 'recommended': gpu.recommended }\"\r\n                @click=\"navigateTo('/product')\"\r\n            >\r\n              <div class=\"gpu-card-header\">\r\n                <h3 class=\"gpu-name\">{{ gpu.name }}</h3>\r\n                <span v-if=\"gpu.recommended\" class=\"recommendation-tag\">推荐</span>\r\n                <span v-if=\"gpu.isNew\" class=\"new-tag\">NEW</span>\r\n              </div>\r\n\r\n              <div class=\"gpu-specs-pricing\">\r\n                <div class=\"specs-section\">\r\n                  <div class=\"spec-item\">\r\n                    <span class=\"spec-label\">单精度:</span>\r\n                    <span class=\"spec-value\">{{ gpu.singlePrecision }} TFLOPS</span>\r\n                  </div>\r\n                  <div class=\"spec-item\">\r\n                    <span class=\"spec-label\">半精度:</span>\r\n                    <span class=\"spec-value\">{{ gpu.halfPrecision }} Tensor TFL</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"price-section\">\r\n                  <div class=\"gpu-pricing\">\r\n                    <span class=\"original-price\" v-if=\"gpu.originalPrice\">¥{{ gpu.originalPrice }}/时</span>\r\n                    <span class=\"current-price\">¥<span class=\"price-value\">{{ gpu.price }}</span>/时</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <GpuComparison></GpuComparison>\r\n\r\n      <!-- 核心优势 Section -->\r\n      <section class=\"section services-section\">\r\n        <div class=\"container\">\r\n          <div class=\"section-header\">\r\n            <h2 class=\"section-title\">核心优势</h2>\r\n            <p class=\"section-description\">\r\n              专业铸造优秀,天工开物企业AI变革路上的好伙伴。\r\n            </p>\r\n          </div>\r\n\r\n          <div class=\"services-grid\">\r\n            <div class=\"service-item\" v-for=\"(service,index) in serviceList\" :key=\"index\">\r\n              <div class=\"service-card\">\r\n                <i class=\"service-icon\" :class=\"service.icon\"></i>\r\n                <h3 class=\"service-title\">{{ service.title }}</h3>\r\n                <div class=\"service-text\"><p>{{service.desc}}</p></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- Application Areas Section -->\r\n      <section class=\"appsec-section\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">行业应用</h2>\r\n          <p class=\"section-description\">\r\n            Applications\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"appsec-container\">\r\n          <div class=\"appsec-grid\">\r\n            <!-- 第一行 -->\r\n            <!-- 宽幅项目 (2x1) -->\r\n            <div class=\"appsec-item appsec-wide\">\r\n              <div class=\"appsec-card\" @mouseover=\"firstRowWide.hover = true\" @mouseleave=\"firstRowWide.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': firstRowWide.hover }\">\r\n                  <img :src=\"firstRowWide.image\" :alt=\"firstRowWide.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': firstRowWide.hover }\">{{ firstRowWide.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 两个高竖幅项目 (1x2) -->\r\n            <div class=\"appsec-item appsec-tall\" v-for=\"(app, index) in firstRowTallApps\" :key=\"'tall-'+index\">\r\n              <div class=\"appsec-card\" @mouseover=\"app.hover = true\" @mouseleave=\"app.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': app.hover }\">\r\n                  <img :src=\"app.image\" :alt=\"app.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': app.hover }\">{{ app.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 第二行 -->\r\n            <!-- 两个小方形 (1x1) 放在第一行宽幅图片下方 -->\r\n            <div class=\"appsec-item appsec-small\" v-for=\"(app, index) in secondRowApps\" :key=\"'small-'+index\">\r\n              <div class=\"appsec-card\" @mouseover=\"app.hover = true\" @mouseleave=\"app.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': app.hover }\">\r\n                  <img :src=\"app.image\" :alt=\"app.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': app.hover }\">{{ app.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 第三行 -->\r\n            <!-- 两个小方形 (1x1) -->\r\n            <div class=\"appsec-item appsec-small\" v-for=\"(app, index) in thirdRowSmallApps\" :key=\"'third-small-'+index\">\r\n              <div class=\"appsec-card\" @mouseover=\"app.hover = true\" @mouseleave=\"app.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': app.hover }\">\r\n                  <img :src=\"app.image\" :alt=\"app.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': app.hover }\">{{ app.title }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 一个宽幅项目 (2x1) -->\r\n            <div class=\"appsec-item appsec-wide\">\r\n              <div class=\"appsec-card\" @mouseover=\"thirdRowWide.hover = true\" @mouseleave=\"thirdRowWide.hover = false\">\r\n                <div class=\"appsec-image\" :class=\"{ 'appsec-hover': thirdRowWide.hover }\">\r\n                  <img :src=\"thirdRowWide.image\" :alt=\"thirdRowWide.title\">\r\n                </div>\r\n                <div class=\"appsec-cardtitle\" :class=\"{ 'appsec-hover': thirdRowWide.hover }\">{{ thirdRowWide.title }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <chat-ai/>\r\n        </div>\r\n      </section>\r\n\r\n      <div class=\"recommendation-tag1\">\r\n        <div class=\"card1\">\r\n          <h1 class=\"banner-text1\">为AI+千行百业，提供高性能算力服务</h1>\r\n          <button class=\"consult-button1\" @click=\"openContactModal\">立即咨询</button>\r\n        </div>\r\n      </div>\r\n\r\n      <transition name=\"fade\">\r\n        <div v-if=\"showContactModal\" class=\"contact-modal-overlay\" @click.self=\"closeContactModal\">\r\n          <div class=\"contact-modal\">\r\n            <button class=\"close-modal\" @click=\"closeContactModal\">\r\n              &times;\r\n            </button>\r\n            <div class=\"contact-content\">\r\n              <div class=\"contact-item\">\r\n                <i class=\"am-icon-user\"></i>\r\n                <span>{{ contactInfo.name }}</span>\r\n              </div>\r\n              <div class=\"contact-item\">\r\n                <i class=\"am-icon-phone\"></i>\r\n                <span>{{ contactInfo.phone }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"contact-note\">\r\n              <p>欢迎随时来电咨询</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </transition>\r\n    </div>\r\n\r\n    <!-- 移动端布局 -->\r\n    <div v-else class=\"mobile-layout\">\r\n\r\n      <div class=\"mobile-banner\"\r\n           @touchstart=\"handleTouchStart\"\r\n           @touchmove=\"handleTouchMove\"\r\n           @touchend=\"handleTouchEnd\">\r\n        <div class=\"mobile-banner-slider\" :style=\"{ transform: `translateX(${-mobileCurrentSlide * 100}%)` }\">\r\n          <div class=\"mobile-slide\" v-for=\"(item, index) in bannerImages\" :key=\"index\">\r\n            <div class=\"mobile-slide-inner\">\r\n              <img :src=\"item.img\" alt=\"\" class=\"mobile-slide-img\">\r\n              <div class=\"mobile-banner-content\" :class=\"'pos-' + item.content.position\">\r\n                <h2 class=\"mobile-banner-title\" v-html=\"item.content.title\"></h2>\r\n                <p class=\"mobile-banner-text\">{{ item.content.text }}</p>\r\n                <div class=\"mobile-banner-actions\">\r\n                  <a v-if=\"!isLogin && item.content.secondaryLink\"\r\n                     href=\"#\"\r\n                     @click.prevent=\"navigateTo(item.content.secondaryLink)\"\r\n                     class=\"mobile-banner-button secondary-btn\">\r\n                    {{ item.content.secondaryBtnText }}\r\n                  </a>\r\n                  <a v-if=\"!isLogin && item.content.primaryLink\"\r\n                     href=\"#\"\r\n                     @click.prevent=\"navigateTo(item.content.primaryLink)\"\r\n                     class=\"mobile-banner-button primary-btn\">\r\n                    {{ item.content.primaryBtnText }}\r\n                  </a>\r\n                  <a v-if=\"item.content.thirdLink\"\r\n                     href=\"#\"\r\n                     @click.prevent=\"navigateTo(item.content.thirdLink)\"\r\n                     class=\"banner-button secondary-btn\">\r\n                    {{ item.content.thirdBtnText }}\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"mobile-banner-pagination\">\r\n    <span v-for=\"(item, index) in bannerImages\"\r\n          :key=\"index\"\r\n          :class=\"{ active: mobileCurrentSlide === index }\"\r\n          @click=\"goToSlide(index)\"></span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 移动端 GPU 推荐 -->\r\n      <section class=\"mobile-section mobile-gpu-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">为您推荐</h2>\r\n          <p class=\"mobile-section-description\">\r\n            专注于提供高性能、稳定可靠的 GPU 算力服务\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-gpu-list\">\r\n          <div class=\"mobile-gpu-card\"\r\n               v-for=\"(gpu, index) in gpus\"\r\n               :key=\"index\"\r\n               :class=\"{ 'recommended': gpu.recommended }\"\r\n               @click=\"navigateTo('/product')\">\r\n            <div class=\"mobile-gpu-header\">\r\n              <h3 class=\"mobile-gpu-name\">{{ gpu.name }}</h3>\r\n              <div class=\"mobile-gpu-tags\">\r\n                <span v-if=\"gpu.recommended\" class=\"mobile-recommend-tag\">推荐</span>\r\n                <span v-if=\"gpu.isNew\" class=\"mobile-new-tag\">NEW</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"mobile-gpu-specs\">\r\n              <div class=\"mobile-spec-item\">\r\n                <span class=\"mobile-spec-label\">单精度:</span>\r\n                <span class=\"mobile-spec-value\">{{ gpu.singlePrecision }} TFLOPS</span>\r\n              </div>\r\n              <div class=\"mobile-spec-item\">\r\n                <span class=\"mobile-spec-label\">半精度:</span>\r\n                <span class=\"mobile-spec-value\">{{ gpu.halfPrecision }} Tensor TFL</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"mobile-gpu-price\">\r\n              <span class=\"mobile-original-price\" v-if=\"gpu.originalPrice\">¥{{ gpu.originalPrice }}/时</span>\r\n              <span class=\"mobile-current-price\">¥{{ gpu.price }}/时</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 GPU 对比 -->\r\n      <section class=\"mobile-section mobile-comparison-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">GPU性能对比</h2>\r\n          <p class=\"mobile-section-description\">\r\n            专业GPU性能详细对比\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-comparison-container\">\r\n          <div class=\"mobile-comparison-scroll\">\r\n            <table class=\"mobile-comparison-table\">\r\n              <thead>\r\n              <tr>\r\n                <th>GPU型号</th>\r\n                <th v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.name }}</th>\r\n              </tr>\r\n              </thead>\r\n              <tbody>\r\n              <tr>\r\n                <td>架构</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.architecture }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>FP16性能</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp16Performance }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>FP32性能</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp32Performance }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>显存</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memory }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>显存类型</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memoryType }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>带宽</td>\r\n                <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.bandwidth }}</td>\r\n              </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 核心优势 -->\r\n      <section class=\"mobile-section mobile-services-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">核心优势</h2>\r\n          <p class=\"mobile-section-description\">\r\n            专业铸造优秀,天工开物企业AI变革路上的好伙伴\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-services-list\">\r\n          <div class=\"mobile-service-card\" v-for=\"(service, index) in serviceList\" :key=\"index\">\r\n            <div class=\"mobile-service-icon\">\r\n              <i :class=\"service.icon\"></i>\r\n            </div>\r\n            <h3 class=\"mobile-service-title\">{{ service.title }}</h3>\r\n            <p class=\"mobile-service-desc\">{{ service.desc }}</p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 行业应用 -->\r\n      <section class=\"mobile-section mobile-applications-section\">\r\n        <div class=\"mobile-section-header\">\r\n          <h2 class=\"mobile-section-title\">行业应用</h2>\r\n          <p class=\"mobile-section-description\">\r\n            Applications\r\n          </p>\r\n        </div>\r\n\r\n        <div class=\"mobile-applications-grid\">\r\n          <div class=\"mobile-app-item\" v-for=\"(app, index) in mobileApplications\" :key=\"index\">\r\n            <div class=\"mobile-app-image\">\r\n              <img :src=\"app.image\" :alt=\"app.title\">\r\n            </div>\r\n            <h3 class=\"mobile-app-title\">{{ app.title }}</h3>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 移动端 咨询按钮 -->\r\n      <div class=\"mobile-consult-section\">\r\n        <h3 class=\"mobile-consult-title\">为AI+千行百业，提供高性能算力服务</h3>\r\n        <button class=\"mobile-consult-button\" @click=\"openContactModal\">立即咨询</button>\r\n      </div>\r\n\r\n      <!-- 移动端 联系弹窗 -->\r\n      <transition name=\"mobile-fade\">\r\n        <div v-if=\"showContactModal\" class=\"mobile-contact-overlay\" @click.self=\"closeContactModal\">\r\n          <div class=\"mobile-contact-modal\">\r\n            <button class=\"mobile-close-modal\" @click=\"closeContactModal\">\r\n              &times;\r\n            </button>\r\n            <div class=\"mobile-contact-content\">\r\n              <div class=\"mobile-contact-item\">\r\n                <i class=\"am-icon-user\"></i>\r\n                <span>{{ contactInfo.name }}</span>\r\n              </div>\r\n              <div class=\"mobile-contact-item\">\r\n                <i class=\"am-icon-phone\"></i>\r\n                <span>{{ contactInfo.phone }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"mobile-contact-note\">\r\n              <p>欢迎随时来电咨询</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </transition>\r\n    </div>\r\n    <Mider v-if=\"!isMobile\"></Mider>\r\n    <Footer></Footer>\r\n    <chatAi></chatAi>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport chatAi from \"@/components/common/mider/chatAi\";\r\nimport Mider from \"@/components/common/mider/Mider\";\r\nimport Footer from \"@/components/common/footer/Footer\";\r\nimport GpuComparison from \"@/views/Index/GpuComparison\";\r\nimport {postAnyData, getNotAuth, postNotAuth,getAnyData,postJsonData} from \"@/api/login\";\r\n\r\n\r\nimport {setToken} from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"IndexView\",\r\n  components: { Layout, chatAi,Footer, Mider, GpuComparison },\r\n  computed: {\r\n    translate() {\r\n      return -this.translateX * 100 + \"%\";\r\n    },\r\n    isLogin() {\r\n      return !! document.cookie.includes('Admin-Token');\r\n    },\r\n    mobileTranslateX() {\r\n      return -this.mobileCurrentSlide * 100;\r\n    },\r\n    mobileApplications() {\r\n      return [\r\n        this.firstRowWide,\r\n        ...this.firstRowTallApps,\r\n        ...this.secondRowApps,\r\n        ...this.thirdRowSmallApps,\r\n        this.thirdRowWide\r\n      ];\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      touchStartX: 0,\r\n      touchEndX: 0,\r\n      touchThreshold: 50, // 滑动阈值，单位px\r\n      isMobile: false,\r\n      mobileCurrentSlide: 0,\r\n      showContactModal: false,\r\n      contactInfo: {\r\n        name: '王先生',\r\n        phone: '13913283376'\r\n      },\r\n      tabIndex: 0,\r\n      firstRowWide: {\r\n        title: '工业制造',\r\n        image: require(\"../../assets/images/index/gongyezhizao.webp\"),\r\n        hover: false,\r\n      },\r\n      firstRowTallApps: [\r\n        {\r\n          title: '自动驾驶',\r\n          image: require(\"../../assets/images/index/zidongjiashi.webp\"),\r\n          hover: false,\r\n        },\r\n        {\r\n          title: '智能交通',\r\n          image: require(\"../../assets/images/index/zhinengjiaotong.webp\"),\r\n          hover: false,\r\n        }\r\n      ],\r\n      secondRowApps: [\r\n        {\r\n          title: '智慧农业',\r\n          image: require(\"../../assets/images/index/zhihuinongye.webp\"),\r\n          hover: false,\r\n        },\r\n        {\r\n          title: '影视渲染',\r\n          image: require(\"../../assets/images/index/yingshixuanran.webp\"),\r\n          hover: false,\r\n        }\r\n      ],\r\n      thirdRowSmallApps: [\r\n        {\r\n          title: '医疗影像',\r\n          image: require(\"../../assets/images/index/yiliaoyingxiang.webp\"),\r\n          hover: false,\r\n        },\r\n        {\r\n          title: '金融风暴',\r\n          image: require(\"../../assets/images/index/jinrongfengbao.webp\"),\r\n          hover: false,\r\n        }\r\n      ],\r\n      thirdRowWide: {\r\n        title: '能源科技',\r\n        image: require(\"../../assets/images/index/nengyuankeji.webp\"),\r\n        hover: false,\r\n      },\r\n      bannerImages: [\r\n        {\r\n          img: require(\"/public/images/back1.webp\"),\r\n          content: {\r\n            title: \"天工开物\",\r\n            text: \"构建AI应用周期服务的一站式算力云\",\r\n            position: \"left\",\r\n            thirdLink: \"/product\",\r\n            thirdBtnText: \"立即购买\"\r\n          }\r\n        },\r\n        {\r\n          img: require(\"/public/images/back2.webp\"),\r\n          content: {\r\n            title: \"专业AI训练平台\",\r\n            text: \"为AI+千行百业，提供高性能算力服务\",\r\n            position: \"left\",\r\n            secondaryLink: \"/login\",\r\n            secondaryBtnText: \"立即登录\",\r\n            primaryLink: '/register',\r\n            primaryBtnText: \"立即注册\"\r\n          }\r\n        },\r\n        {\r\n          img: require(\"/public/images/back3.webp\"),\r\n          content: {\r\n            title: \"企业级GPU集群\",\r\n            text: \"H100/H800/RTX 4090等高性能GPU随时可用，按需付费\",\r\n            position: \"left\",\r\n          }\r\n        }\r\n      ],\r\n      translateX: 0,\r\n      tsion: true,\r\n      tabList: [],\r\n      swiperOptions: {\r\n        loop: true,\r\n        autoplay: {\r\n          delay: 5000,\r\n          disableOnInteraction: false,\r\n        },\r\n        pagination: {\r\n          el: '.swiper-pagination',\r\n          clickable: true\r\n        },\r\n        navigation: {\r\n          nextEl: '.swiper-button-next',\r\n          prevEl: '.swiper-button-prev'\r\n        }\r\n      },\r\n      serviceList: [\r\n        { id: 1, icon: 'am-icon-shield', title: '数据安全', desc: '平台采取各种措施保证用户的数据安全，例如数据加密、防火墙、漏洞扫描和安全审计等措施，以防止数据泄露、篡改和丢失等风险。' },\r\n        { id: 2, icon: 'am-icon-sliders', title: '部署灵活', desc: '租用GPU服务器具备比购买更高的灵活性，用户可以根据自己的需求随时调整租赁资源的配置及数量。' },\r\n        { id: 3, icon: 'am-icon-server', title: '高可靠性', desc: '平台拥有完善的运维体系，采用丰富的数据备份、冗余技术以及定期检测等机制，保证租赁服务器的稳定性、易用性和安全性。' },\r\n        { id: 4, icon: 'am-icon-rocket', title: '高处理性能', desc: '采用先进的GPU集群架构，平台能够大大提高计算速度和处理能力，可以在科学计算、深度学习等领域有更优秀的表现。' },\r\n        { id: 5, icon: 'am-icon-credit-card', title: '低成本', desc: '购买GPU服务器需要投入较高的资金，而租赁可以让用户以较低的成本去获得相关基础设施，减轻了用户在预算上的负担。' },\r\n        { id: 6, icon: 'am-icon-phone', title: '及时服务', desc: '提供365天 7*24小时技术支持及运维服务， 工程师现场及在线响应及电话支持。' }\r\n      ],\r\n      comparisonGpus: [\r\n        {\r\n          name: \"A100\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"312 TFLOPS\",\r\n          fp32Performance: \"19.5 TFLOPS\",\r\n          memory: \"80 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"2,039 GB/s\"\r\n        },\r\n        {\r\n          name: \"A100\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"312 TFLOPS\",\r\n          fp32Performance: \"19.5 TFLOPS\",\r\n          memory: \"80 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"2,039 GB/s\"\r\n        },\r\n        {\r\n          name: \"A100\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"312 TFLOPS\",\r\n          fp32Performance: \"19.5 TFLOPS\",\r\n          memory: \"80 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"2,039 GB/s\"\r\n        },\r\n        {\r\n          name: \"V100\",\r\n          architecture: \"Volta\",\r\n          fp16Performance: \"125 TFLOPS\",\r\n          fp32Performance: \"15.7 TFLOPS\",\r\n          memory: \"32 GB\",\r\n          memoryType: \"HBM2\",\r\n          bandwidth: \"900 GB/s\"\r\n        },\r\n        {\r\n          name: \"A6000\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"77.4 TFLOPS\",\r\n          fp32Performance: \"38.7 TFLOPS\",\r\n          memory: \"48 GB\",\r\n          memoryType: \"GDDR6\",\r\n          bandwidth: \"768 GB/s\"\r\n        },\r\n        {\r\n          name: \"A5000\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"54.2 TFLOPS\",\r\n          fp32Performance: \"27.8 TFLOPS\",\r\n          memory: \"24 GB\",\r\n          memoryType: \"GDDR6\",\r\n          bandwidth: \"768 GB/s\"\r\n        },\r\n        {\r\n          name: \"A4000\",\r\n          architecture: \"Ampere\",\r\n          fp16Performance: \"19.17 TFLOPS\",\r\n          fp32Performance: \"19.17 TFLOPS\",\r\n          memory: \"16 GB\",\r\n          memoryType: \"GDDR6\",\r\n          bandwidth: \"448 GB/s\"\r\n        }\r\n      ],\r\n      gpus: [\r\n        {\r\n          name: \"A100 SXM4 / 80GB\",\r\n          singlePrecision: \"156\",\r\n          halfPrecision: \"19.5\",\r\n          originalPrice: \"11.10\",\r\n          price: \"6.66\",\r\n          recommended: true,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX 3090 / 24GB\",\r\n          singlePrecision: \"35.58\",\r\n          halfPrecision: \"71.2\",\r\n          originalPrice: \"2.30\",\r\n          price: \"1.38\",\r\n          recommended: true,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"A100 PCIE / 80GB\",\r\n          singlePrecision: \"156\",\r\n          halfPrecision: \"19.5\",\r\n          originalPrice: \"11.10\",\r\n          price: \"6.66\",\r\n          recommended: true,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX 4090 / 24GB\",\r\n          singlePrecision: \"82.58\",\r\n          halfPrecision: \"164.5\",\r\n          originalPrice: \"2.97\",\r\n          price: \"1.78\",\r\n          recommended: false,\r\n          isNew: true\r\n        },\r\n        {\r\n          name: \"RTX 4090D / 24GB\",\r\n          singlePrecision: \"73.54\",\r\n          halfPrecision: \"147.1\",\r\n          originalPrice: \"2.93\",\r\n          price: \"1.76\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX 3060 / 12GB\",\r\n          singlePrecision: \"12.7\",\r\n          halfPrecision: \"51.2\",\r\n          originalPrice: \"1.00\",\r\n          price: \"0.60\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"RTX A4000 / 16GB\",\r\n          singlePrecision: \"19.17\",\r\n          halfPrecision: \"76.7\",\r\n          originalPrice: \"1.53\",\r\n          price: \"0.92\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n        {\r\n          name: \"Tesla P40 / 24GB\",\r\n          singlePrecision: \"5.9\",\r\n          halfPrecision: \"11.76\",\r\n          originalPrice: \"1.35\",\r\n          price: \"0.81\",\r\n          recommended: false,\r\n          isNew: false\r\n        },\r\n\r\n      ],\r\n      activeIndex: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchRecommendations();\r\n    const url = new URL(window.location.href);\r\n    const token = url.searchParams.get('token');\r\n\r\n    if (token) {\r\n      const hasRefreshed = localStorage.getItem('hasRefreshedWithToken');\r\n      // console.log('需要修改的参数为', url.origin + url.pathname)\r\n      if (!hasRefreshed) {\r\n        setToken(token);\r\n        localStorage.setItem('hasRefreshedWithToken', 'true');\r\n        // 直接修改 window.location（移除所有查询参数）\r\n        // console.log('需要修改的参数为', url.origin + url.pathname)\r\n\r\n      } else {\r\n        window.location.href = url.origin + url.pathname;\r\n        localStorage.setItem('hasRefreshedWithToken', 'false');\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.checkIsMobile();\r\n    window.addEventListener('resize', this.checkIsMobile);\r\n\r\n    // Banner auto-play\r\n    this.desktopInterval = setInterval(() => {\r\n      this.next();\r\n    }, 5000);\r\n\r\n    this.mobileInterval = setInterval(() => {\r\n      this.mobileNext();\r\n    }, 5000);\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.checkIsMobile);\r\n    clearInterval(this.desktopInterval);\r\n    clearInterval(this.mobileInterval);\r\n\r\n  },\r\n  methods: {\r\n\r\n    async fetchRecommendations() {\r\n      try {\r\n        getNotAuth(\"/system/recommend/list\").then(req =>{\r\n          this.gpus = req.data.rows.map(item => ({\r\n            ...item,\r\n            isNew: item.isnew === 0,\r\n            recommended: item.recommended === 0\r\n          }))\r\n          // this.gpus = req.data.rows\r\n        })\r\n      } catch (error) {\r\n        console.error('获取GPU推荐列表失败:', error);\r\n      }\r\n    },\r\n\r\n    handleTouchStart(e) {\r\n      this.touchStartX = e.touches[0].clientX;\r\n    },\r\n\r\n    handleTouchMove(e) {\r\n      this.touchEndX = e.touches[0].clientX;\r\n    },\r\n\r\n    handleTouchEnd() {\r\n      if (!this.touchStartX || !this.touchEndX) return;\r\n\r\n      const diff = this.touchStartX - this.touchEndX;\r\n\r\n      // 向右滑动\r\n      if (diff > this.touchThreshold) {\r\n        this.mobileNext();\r\n      }\r\n\r\n      // 向左滑动\r\n      if (diff < -this.touchThreshold) {\r\n        this.mobilePrev();\r\n      }\r\n\r\n      // 重置触摸位置\r\n      this.touchStartX = 0;\r\n      this.touchEndX = 0;\r\n    },\r\n\r\n    mobilePrev() {\r\n      this.mobileCurrentSlide = (this.mobileCurrentSlide - 1 + this.bannerImages.length) % this.bannerImages.length;\r\n      this.resetMobileInterval();\r\n    },\r\n\r\n    mobileNext() {\r\n      this.mobileCurrentSlide = (this.mobileCurrentSlide + 1) % this.bannerImages.length;\r\n      this.resetMobileInterval();\r\n    },\r\n\r\n    resetMobileInterval() {\r\n      clearInterval(this.mobileInterval);\r\n      this.mobileInterval = setInterval(() => {\r\n        this.mobileNext();\r\n      }, 5000);\r\n    },\r\n    checkIsMobile() {\r\n      this.isMobile = window.innerWidth <= 768;\r\n    },\r\n    openContactModal() {\r\n      this.showContactModal = true;\r\n    },\r\n    closeContactModal() {\r\n      this.showContactModal = false;\r\n    },\r\n    handleBannerAction(item) {\r\n      if (item.content.link) {\r\n        this.$router.push(item.content.link);\r\n      }\r\n      this.$ga.event('Banner', 'click', item.content.title);\r\n    },\r\n    navigateTo(path) {\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300);\r\n            }\r\n          });\r\n\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant'\r\n          });\r\n          this.$router.go(0);\r\n        });\r\n      } else {\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    },\r\n    last() {\r\n      this.translateX = (this.translateX - 1 + this.bannerImages.length) % this.bannerImages.length;\r\n      this.tsion = true;\r\n    },\r\n    next() {\r\n      this.translateX = (this.translateX + 1) % this.bannerImages.length;\r\n      this.tsion = true;\r\n    },\r\n\r\n    goToSlide(index) {\r\n      this.mobileCurrentSlide = index;\r\n      // 重置自动播放计时器\r\n      clearInterval(this.mobileInterval);\r\n      this.mobileInterval = setInterval(() => {\r\n        this.mobileNext();\r\n      }, 5000);\r\n    },\r\n    changeTab(index) {\r\n      this.tabIndex = index;\r\n    },\r\n    useGpu(gpu) {\r\n      console.log(`Selected GPU: ${gpu.name}`);\r\n    },\r\n    getCustomSolution() {\r\n      console.log('Get Custom Solution');\r\n    },\r\n    contactUs() {\r\n      console.log('Contact Us');\r\n    },\r\n    prevSlide() {\r\n      this.activeIndex = (this.activeIndex > 0) ? this.activeIndex - 1 : this.slideshow.length - 1;\r\n    },\r\n    nextSlide() {\r\n      this.activeIndex = (this.activeIndex < this.slideshow.length - 1) ? this.activeIndex + 1 : 0;\r\n    },\r\n    selectGpu(gpu) {\r\n      console.log(`Selected GPU: ${gpu.name}`);\r\n    },\r\n  },\r\n  watch: {\r\n    translateX(newVal) {\r\n      const dots = this.$refs.swiperPagination?.querySelectorAll(\"span\");\r\n      if (dots) {\r\n        dots.forEach((dot, index) => {\r\n          dot.classList.toggle(\"active\", index === newVal);\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 通用样式 */\r\n.section-header {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 28px;\r\n  color: #333;\r\n  position: relative;\r\n  display: inline-block;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -5px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 50px;\r\n  height: 3px;\r\n  background-color: #2196f3;\r\n}\r\n\r\n.section-description {\r\n  color: #666;\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 电脑端样式 */\r\n.desktop-layout {\r\n  display: block;\r\n}\r\n\r\n/* Banner Section */\r\n.banner-section {\r\n  padding: 0;\r\n}\r\n\r\n.banner-container {\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  max-height: 1920px;\r\n  margin: 0;\r\n  position: relative;\r\n}\r\n\r\n.big-box {\r\n  width: 100%;\r\n  height: 93vh;\r\n  overflow: hidden;\r\n  position: relative;\r\n  box-shadow: 0 4px 20px rgba(0,0,0,0.15);\r\n}\r\n\r\n.img-box {\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.show-box {\r\n  display: flex;\r\n  height: 100%;\r\n  width: 100%;\r\n  transition: all 0.5s;\r\n}\r\n\r\n.slide-item {\r\n  position: relative;\r\n  flex-shrink: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.slide-item img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.banner-content {\r\n  position: absolute;\r\n  top: 40%;\r\n  background-color: rgba(0, 0, 0, 0);\r\n  padding: 40px;\r\n  font-weight: 500;\r\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0);\r\n  max-width: 800px;\r\n  color: #fdfcfc;\r\n  z-index: 4;\r\n}\r\n\r\n.banner-title {\r\n  font-size: 7vh;\r\n  line-height: 1;\r\n  margin-bottom: 0px;\r\n  font-weight: 700;\r\n}\r\n\r\n.banner-text {\r\n  font-size: 3vh;\r\n  line-height: 3.5;\r\n  margin-bottom: 0px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.banner-actions {\r\n  display: flex;\r\n  gap: 20px;\r\n  z-index: 20;\r\n}\r\n\r\n.banner-button {\r\n  padding: 10px 5px;\r\n  border-radius: 0px;\r\n  font-size: 1.5rem;\r\n  font-weight:200;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 10px rgba(248, 245, 245, 0.2);\r\n  text-decoration: none;\r\n  text-align: center;\r\n  display: inline-block;\r\n  background-color: transparent;\r\n  border: 2px solid white;\r\n  color: white;\r\n  position: relative;\r\n  z-index: 3;\r\n  pointer-events: auto;\r\n  width: 150px;\r\n}\r\n\r\n.banner-button:hover {\r\n  background-color: black;\r\n}\r\n\r\n.primary-btn {\r\n  color: white;\r\n  border: 1px solid white;\r\n}\r\n\r\n.primary-btn:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.secondary-btn {\r\n  color: white;\r\n  background-color: #f67b3d;\r\n  border-color: #f67b3d;\r\n}\r\n\r\n.secondary-btn:hover {\r\n  background-color: #f67b3d;\r\n}\r\n\r\n.pos-left {\r\n  left: 8%;\r\n  transform: translateY(-50%);\r\n  text-align: left;\r\n  animation: slideInFromLeft 0.8s ease;\r\n}\r\n\r\n.pos-right {\r\n  right: 10%;\r\n  transform: translateY(-50%);\r\n  text-align: left;\r\n  animation: slideInFromRight 0.8s ease;\r\n}\r\n\r\n.pos-center {\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  text-align: center;\r\n  animation: fadeIn 0.8s ease;\r\n}\r\n\r\n.arrowhead-box {\r\n  position: absolute;\r\n  top: 50%;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  transform: translateY(-50%);\r\n  padding: 0 20px;\r\n  z-index: 10;\r\n  pointer-events: none;\r\n}\r\n\r\n.nav-arrow {\r\n  display: flex;\r\n  pointer-events: auto;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background: rgba(0,0,0,0.3);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-arrow:hover {\r\n  background: rgba(0,0,0,0.5);\r\n}\r\n\r\n.arrow-icon {\r\n  height: 24px;\r\n  width: 24px;\r\n  transition: transform 0.3s;\r\n  filter: invert(1);\r\n}\r\n\r\n.rotated {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.swiper-pagination {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  display: flex;\r\n  gap: 10px;\r\n  z-index: 3;\r\n}\r\n\r\n.swiper-pagination span {\r\n  width: 12px;\r\n  height: 12px;\r\n  background: rgba(255,255,255,0.5);\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.swiper-pagination span.active {\r\n  width: 30px;\r\n  border-radius: 6px;\r\n  background: #fff;\r\n}\r\n\r\n/* GPU Section */\r\n.gpu-specs-pricing {\r\n  display: flex;\r\n  height: 8vh;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0px;\r\n}\r\n\r\n.specs-section {\r\n  flex: 1;\r\n}\r\n\r\n.price-section {\r\n  text-align: right;\r\n  min-width: 120px;\r\n}\r\n\r\n.gpu-pricing {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n}\r\n\r\n.original-price {\r\n  text-decoration: line-through;\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n\r\n.current-price {\r\n  font-size: 16px;\r\n  color: #333;\r\n}\r\n\r\n.price-value {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #2196f3;\r\n}\r\n\r\n/* GPU Card Grid */\r\n.gpu-section {\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  max-height: 800px;\r\n  margin: 0 auto;\r\n  /*margin-bottom: -10vh;*/\r\n\r\n}\r\n\r\n.gpu-card-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 20px;\r\n  padding: 0 50px;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.gpu-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);\r\n  padding: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.gpu-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #2196f3;\r\n}\r\n\r\n.gpu-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.gpu-name {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-left: 0px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.recommendation-tag {\r\n  background-color: #2196f3;\r\n  color: white;\r\n  font-size: 12px;\r\n  padding: 3px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.new-tag {\r\n  background-color: #ff4d4f;\r\n  color: white;\r\n  font-size: 12px;\r\n  padding: 3px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.spec-item {\r\n  display: flex;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.spec-label {\r\n  color: #666;\r\n  margin-right: 5px;\r\n  flex-shrink: 0;\r\n  width: 60px;\r\n}\r\n\r\n.spec-value {\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n/* GPU 性能对比 Section */\r\n.gpu-comparison-section {\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n  margin-bottom: -12vh;\r\n}\r\n\r\n.gpu-comparison-table {\r\n  width: 100%;\r\n  padding: 0px 50px;\r\n  border-collapse: collapse;\r\n  margin: 0;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\r\n  border-radius: 10px;\r\n  overflow:hidden;\r\n}\r\n\r\n.gpu-comparison-table thead {\r\n  background-color: #cddfec;\r\n}\r\n\r\n.gpu-comparison-table thead tr th {\r\n  padding: 15px 20px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.gpu-comparison-table tbody tr td {\r\n  padding: 15px 20px;\r\n  text-align: center;\r\n  border-bottom: 1px solid #e0e5eb;\r\n  transition: background-color 0.3s ease;\r\n  font-size: 15px;\r\n  color: #333;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:nth-child(even) {\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:hover {\r\n  background-color: #f1f6fd;\r\n}\r\n\r\n/* Services Section */\r\n.services-section {\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n}\r\n\r\n.services-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  padding: 0px 50px;\r\n}\r\n\r\n.service-item {\r\n  width: 33.33%;\r\n  padding: 0 11px;\r\n}\r\n\r\n.service-item:nth-child(-n+3) {\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.service-card {\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0 5px 15px rgba(0,0,0,0.05);\r\n  padding: 25px;\r\n  height: 100%;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: left;\r\n}\r\n\r\n.service-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 10px 25px rgba(0,0,0,0.1);\r\n}\r\n\r\n.service-icon {\r\n  font-size: 48px;\r\n  margin-bottom: -30px;\r\n  color: #2196f3;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.service-card:hover .service-icon {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.service-title {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.service-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 2;\r\n}\r\n\r\n/* Application Areas Section */\r\n.appsec-section {\r\n  background-color: #f8f9fa;\r\n  max-width: 2560px;\r\n  max-height: 1000px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.appsec-container {\r\n  margin: 0 auto;\r\n  max-height: 1000px;\r\n  width: 100%;\r\n  padding: 0 50px;\r\n}\r\n\r\n.appsec-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  grid-auto-rows: minmax(180px, auto);\r\n  gap: 20px;\r\n}\r\n\r\n.appsec-item {\r\n  position: relative;\r\n  overflow: hidden;\r\n  border-radius: 4px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.appsec-wide {\r\n  grid-column: span 2;\r\n  grid-row: span 1;\r\n  height: 180px;\r\n}\r\n\r\n.appsec-tall {\r\n  grid-column: span 1;\r\n  grid-row: span 2;\r\n  height: 380px;\r\n}\r\n\r\n.appsec-small {\r\n  grid-column: span 1;\r\n  grid-row: span 1;\r\n  height: 180px;\r\n}\r\n\r\n.appsec-card {\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n  cursor: pointer;\r\n  overflow: hidden;\r\n}\r\n\r\n.appsec-image {\r\n  height: 100%;\r\n  width: 100%;\r\n  transition: transform 0.5s ease;\r\n}\r\n\r\n.appsec-image img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.5s ease;\r\n}\r\n\r\n.appsec-cardtitle {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 20px;\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);\r\n  z-index: 2;\r\n  transition: transform 0.3s ease, font-size 0.3s ease;\r\n}\r\n\r\n.appsec-card::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 50%;\r\n  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);\r\n  z-index: 1;\r\n}\r\n\r\n.appsec-hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n/* Recommendation Section */\r\n.recommendation-tag1 {\r\n  margin-top: 3vh;\r\n  position: relative;\r\n  width: 100%;\r\n  height: 120px;\r\n  background-image: url(\"../../assets/images/index/back3.png\");\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.card1 {\r\n  position: relative;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  max-width: 1200px;\r\n  padding: 0 20px;\r\n  z-index: 2;\r\n}\r\n\r\n.banner-text1 {\r\n  color: white;\r\n  font-size: 2rem;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.consult-button1 {\r\n  background-color: white;\r\n  color: #0d47a1;\r\n  border: none;\r\n  border-radius: 25px;\r\n  padding: 10px 25px;\r\n  font-size: 1.7rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.consult-button1:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\r\n  background-color: #0a69ff;\r\n  color: white;\r\n}\r\n\r\n/* Contact Modal */\r\n.contact-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.contact-modal {\r\n  position: relative;\r\n  background-color: white;\r\n  padding: 30px;\r\n  border-radius: 8px;\r\n  width: 90%;\r\n  max-width: 250px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  text-align: center;\r\n}\r\n\r\n.close-modal {\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 15px;\r\n  font-size: 30px;\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n  color: #999;\r\n}\r\n\r\n.contact-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.contact-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 15px;\r\n  font-size: 18px;\r\n}\r\n\r\n.contact-item i {\r\n  margin-right: 10px;\r\n  color: #2196f3;\r\n}\r\n\r\n.contact-note {\r\n  color: #666;\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* Animations */\r\n@keyframes slideInFromLeft {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(-50px) translateY(-50%);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0) translateY(-50%);\r\n  }\r\n}\r\n\r\n@keyframes slideInFromRight {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(50px) translateY(-50%);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0) translateY(-50%);\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* 移动端样式 */\r\n.mobile-layout {\r\n  display: none;\r\n}\r\n\r\n/* 移动端响应式设计 */\r\n@media (max-width: 768px) {\r\n  .desktop-layout {\r\n    display: none;\r\n\r\n  }\r\n  .container, .banner-section, .services-grid, .appsec-container {\r\n    max-width: 10%;\r\n    padding-left: 15px;\r\n    padding-right: 15px;\r\n    overflow-x: hidden;\r\n  }\r\n\r\n  .mobile-layout {\r\n    display: block;\r\n    overflow-x: hidden;\r\n\r\n    /*padding: 0 15px;*/\r\n  }\r\n\r\n\r\n    /* 修改移动端轮播图样式 */\r\n  .mobile-banner {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 100%;\r\n    min-height: 30vh;\r\n    overflow: hidden;\r\n    touch-action: pan-y;\r\n    user-select: none;\r\n    /*margin-bottom: -5vh;*/\r\n  }\r\n\r\n  .mobile-banner-slider {\r\n    display: flex;\r\n    height: 100%;\r\n    transition: transform 0.5s ease;\r\n  }\r\n\r\n  .mobile-slide {\r\n    flex: 0 0 100%;\r\n    width: 100%;\r\n    height: 70%;\r\n    position: relative;\r\n  }\r\n\r\n  .mobile-slide-inner {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: relative;\r\n  }\r\n\r\n  .mobile-slide-img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n  .mobile-banner-content {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    padding: 15px;\r\n    /*background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);*/\r\n    color: white;\r\n    z-index: 2;\r\n  }\r\n\r\n  .mobile-banner-title {\r\n    font-size: 1.5rem;\r\n    margin-bottom: 8px;\r\n    font-weight: bold;\r\n    line-height: 1.3;\r\n  }\r\n\r\n  .mobile-banner-text {\r\n    font-size: 0.9rem;\r\n    margin-bottom: 12px;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  .mobile-banner-actions {\r\n    display: flex;\r\n    gap: 10px;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .mobile-banner-button {\r\n    padding: 8px 12px;\r\n    font-size: 14px;\r\n    border-radius: 4px;\r\n    text-decoration: none;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .mobile-banner-button.primary-btn {\r\n    background-color: transparent;\r\n    border: 1px solid white;\r\n    color: white;\r\n  }\r\n\r\n  .mobile-banner-button.secondary-btn {\r\n    background-color: #f67b3d;\r\n    color: white;\r\n    border: none;\r\n  }\r\n\r\n  .mobile-banner-pagination {\r\n    position: absolute;\r\n    bottom: 10px;\r\n    left: 0;\r\n    right: 0;\r\n    display: flex;\r\n    justify-content: center;\r\n    gap: 8px;\r\n    z-index: 3;\r\n  }\r\n\r\n  .mobile-banner-pagination span {\r\n    display: block;\r\n    width: 8px;\r\n    height: 8px;\r\n    border-radius: 50%;\r\n    background-color: rgba(255,255,255,0.5);\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .mobile-banner-pagination span.active {\r\n    background-color: white;\r\n    width: 20px;\r\n    border-radius: 4px;\r\n  }\r\n\r\n\r\n\r\n  /* 移动端 GPU 推荐 */\r\n  .mobile-section {\r\n    padding: 30px 0;\r\n  }\r\n\r\n  .mobile-section-header {\r\n    text-align: center;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .mobile-section-title {\r\n    font-size: 22px;\r\n    color: #333;\r\n    position: relative;\r\n    display: inline-block;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-section-title::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -5px;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    width: 40px;\r\n    height: 2px;\r\n    background-color: #2196f3;\r\n  }\r\n\r\n  .mobile-section-description {\r\n    font-size: 14px;\r\n    color: #666;\r\n    max-width: 80%;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .mobile-gpu-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 15px;\r\n  }\r\n\r\n  .mobile-gpu-card {\r\n    background-color: white;\r\n    border-radius: 8px;\r\n    padding: 15px;\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  }\r\n\r\n  .mobile-gpu-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-gpu-name {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin: 0;\r\n  }\r\n\r\n  .mobile-gpu-tags {\r\n    display: flex;\r\n    gap: 5px;\r\n  }\r\n\r\n  .mobile-recommend-tag {\r\n    background-color: #2196f3;\r\n    color: white;\r\n    font-size: 10px;\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  .mobile-new-tag {\r\n    background-color: #ff4d4f;\r\n    color: white;\r\n    font-size: 10px;\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n  }\r\n\r\n  .mobile-gpu-specs {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-spec-item {\r\n    display: flex;\r\n    margin-bottom: 5px;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .mobile-spec-label {\r\n    color: #666;\r\n    margin-right: 5px;\r\n    width: 60px;\r\n  }\r\n\r\n  .mobile-spec-value {\r\n    color: #333;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .mobile-gpu-price {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .mobile-original-price {\r\n    font-size: 12px;\r\n    color: #999;\r\n    text-decoration: line-through;\r\n  }\r\n\r\n  .mobile-current-price {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #2196f3;\r\n  }\r\n\r\n  /* 移动端 GPU 对比 */\r\n  .mobile-comparison-container {\r\n    overflow-x: auto;\r\n    -webkit-overflow-scrolling: touch;\r\n    margin: 0 -15px;\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .mobile-comparison-scroll {\r\n    min-width: 600px;\r\n  }\r\n\r\n  .mobile-comparison-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .mobile-comparison-table th,\r\n  .mobile-comparison-table td {\r\n    padding: 10px;\r\n    text-align: center;\r\n    border: 1px solid #eee;\r\n  }\r\n\r\n  .mobile-comparison-table thead {\r\n    background-color: #f5f5f5;\r\n  }\r\n\r\n  .mobile-comparison-table th {\r\n    font-weight: 600;\r\n    color: #333;\r\n  }\r\n\r\n  /* 移动端 核心优势 */\r\n  .mobile-services-list {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 15px;\r\n  }\r\n\r\n  .mobile-service-card {\r\n    background-color: white;\r\n    border-radius: 8px;\r\n    padding: 15px;\r\n    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n    text-align: center;\r\n  }\r\n\r\n  .mobile-service-icon {\r\n    font-size: 30px;\r\n    color: #2196f3;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .mobile-service-title {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .mobile-service-desc {\r\n    font-size: 12px;\r\n    color: #666;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  /* 移动端 行业应用 */\r\n  .mobile-applications-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 15px;\r\n  }\r\n\r\n  .mobile-app-item {\r\n    position: relative;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    height: 120px;\r\n  }\r\n\r\n  .mobile-app-image {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .mobile-app-image img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    transition: transform 0.5s ease;\r\n  }\r\n\r\n  .mobile-app-item:hover .mobile-app-image img {\r\n    transform: scale(1.05);\r\n  }\r\n\r\n  .mobile-app-title {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    padding: 10px;\r\n    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);\r\n    color: white;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    margin: 0;\r\n  }\r\n\r\n  /* 移动端 咨询按钮 */\r\n  .mobile-consult-section {\r\n    background-color: #2196f3;\r\n    padding: 20px;\r\n    text-align: center;\r\n    margin: 30px -15px 0;\r\n  }\r\n\r\n  .mobile-consult-title {\r\n    color: white;\r\n    font-size: 18px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .mobile-consult-button {\r\n    background-color: white;\r\n    color: #2196f3;\r\n    border: none;\r\n    border-radius: 25px;\r\n    padding: 10px 25px;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .mobile-consult-button:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 8px rgba(0,0,0,0.1);\r\n  }\r\n\r\n  /* 移动端 联系弹窗 */\r\n  .mobile-contact-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: rgba(0,0,0,0.5);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    z-index: 1000;\r\n  }\r\n\r\n  .mobile-contact-modal {\r\n    position: relative;\r\n    background-color: white;\r\n    border-radius: 10px;\r\n    width: 80%;\r\n    max-width: 300px;\r\n    padding: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .mobile-close-modal {\r\n    position: absolute;\r\n    top: 10px;\r\n    right: 15px;\r\n    font-size: 24px;\r\n    background: none;\r\n    border: none;\r\n    color: #999;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .mobile-contact-content {\r\n    margin: 20px 0;\r\n  }\r\n\r\n  .mobile-contact-item {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 15px;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .mobile-contact-item i {\r\n    margin-right: 10px;\r\n    color: #2196f3;\r\n  }\r\n\r\n  .mobile-contact-note {\r\n    color: #666;\r\n    font-size: 14px;\r\n  }\r\n\r\n  /* 移动端动画 */\r\n  @keyframes mobile-fade {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateY(20px);\r\n    }\r\n    to {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .mobile-fade-enter-active, .mobile-fade-leave-active {\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .mobile-fade-enter, .mobile-fade-leave-to {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n}\r\n\r\n/* 响应式设计 - 平板 */\r\n@media (min-width: 769px) and (max-width: 1024px) {\r\n  .gpu-card-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n\r\n\r\n  .services-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n  .appsec-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n  .appsec-wide {\r\n    grid-column: span 2;\r\n  }\r\n\r\n  .appsec-tall {\r\n    grid-column: span 1;\r\n    grid-row: span 1;\r\n    height: 180px;\r\n  }\r\n}\r\n\r\n\r\n\r\n</style>\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./IndexView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./IndexView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./IndexView.vue?vue&type=template&id=5d515a4a&scoped=true&\"\nimport script from \"./IndexView.vue?vue&type=script&lang=js&\"\nexport * from \"./IndexView.vue?vue&type=script&lang=js&\"\nimport style0 from \"./IndexView.vue?vue&type=style&index=0&id=5d515a4a&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5d515a4a\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "on", "pauseCarousel", "resumeCarousel", "attrs", "_l", "questions", "question", "index", "directives", "name", "rawName", "value", "currentQuestionIndex", "expression", "key", "$event", "sendCarouselQuestion", "witde", "_v", "_s", "class", "showChat", "toggleChat", "_m", "ref", "messages", "message", "type", "_e", "domProps", "formatMessage", "text", "formatTime", "time", "loading", "userInput", "indexOf", "_k", "keyCode", "sendMessage", "apply", "arguments", "target", "composing", "trim", "staticRenderFns", "data", "Date", "historyMessages", "carouselTimer", "carouselI<PERSON>val", "isPaused", "<PERSON><PERSON><PERSON><PERSON>", "clearCarousel", "mounted", "startCarousel", "document", "getElementById", "link", "createElement", "id", "rel", "href", "head", "append<PERSON><PERSON><PERSON>", "methods", "that", "setInterval", "length", "console", "log", "clearInterval", "$nextTick", "scrollToBottom", "push", "userQuestion", "role", "content", "requestBody", "model", "stream", "options", "presence_penalty", "frequency_penalty", "seed", "response", "fetch", "method", "headers", "Authorization", "body", "JSON", "stringify", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "aiResponseIndex", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "jsonString", "slice", "parse", "choices", "delta", "reasoning_content", "e", "error", "Promise", "resolve", "setTimeout", "container", "$refs", "messagesContainer", "scrollTop", "scrollHeight", "date", "toLocaleTimeString", "hour", "minute", "replace", "component", "isMobile", "handleTouchStart", "handleTouchMove", "handleTouchEnd", "style", "transform", "mobileCurrentSlide", "bannerImages", "item", "img", "position", "title", "is<PERSON>ogin", "secondaryLink", "preventDefault", "navigateTo", "secondaryBtnText", "primaryLink", "primaryBtnText", "thirdLink", "thirdBtnText", "active", "goToSlide", "gpus", "gpu", "recommended", "isNew", "singlePrecision", "halfPrecision", "originalPrice", "price", "comparisonGpus", "architecture", "fp16Performance", "fp32Performance", "memory", "memoryType", "bandwidth", "serviceList", "service", "icon", "desc", "mobileApplications", "app", "image", "openContactModal", "showContactModal", "currentTarget", "closeContactModal", "contactInfo", "phone", "translate", "transition", "tsion", "last", "require", "next", "translateX", "firstRowWide", "hover", "firstRowTallApps", "secondRowApps", "thirdRowSmallApps", "thirdRowWide", "created", "fetchComparison", "getNotAuth", "then", "req", "rows", "map", "fp16performance", "fp32performance", "memorytype", "components", "Layout", "chatAi", "Footer", "<PERSON><PERSON>", "GpuComparison", "computed", "cookie", "includes", "mobileTranslateX", "touchStartX", "touchEndX", "touchThreshold", "tabIndex", "tabList", "swiperOptions", "loop", "autoplay", "delay", "disableOnInteraction", "pagination", "el", "clickable", "navigation", "nextEl", "prevEl", "activeIndex", "fetchRecommendations", "url", "URL", "window", "location", "token", "searchParams", "get", "hasRefreshed", "localStorage", "getItem", "origin", "pathname", "setItem", "setToken", "checkIsMobile", "addEventListener", "desktopInterval", "mobileInterval", "mobileNext", "removeEventListener", "isnew", "touches", "clientX", "diff", "mobilePrev", "resetMobileInterval", "innerWidth", "handleBannerAction", "$router", "$ga", "event", "path", "currentPath", "previousActivePath", "navLinks", "querySelectorAll", "for<PERSON>ach", "classList", "contains", "add", "remove", "$route", "scrollTo", "top", "behavior", "go", "changeTab", "useGpu", "getCustomSolution", "contactUs", "prevSlide", "slideshow", "nextSlide", "selectGpu", "watch", "newVal", "dots", "swiperPagination", "dot", "toggle"], "sourceRoot": ""}