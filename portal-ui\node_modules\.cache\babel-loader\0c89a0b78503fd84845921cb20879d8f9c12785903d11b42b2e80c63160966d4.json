{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"iframe-page\"\n  }, [_c('div', {\n    staticClass: \"iframe-container\"\n  }, [_c('iframe', {\n    key: _vm.iframeKey,\n    ref: \"iframe\",\n    attrs: {\n      \"src\": _vm.iframeSrc\n    }\n  })])]);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "attrs", "iframeSrc", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/views/Console.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"iframe-page\"},[_c('div',{staticClass:\"iframe-container\"},[_c('iframe',{key:_vm.iframe<PERSON>ey,ref:\"iframe\",attrs:{\"src\":_vm.iframeSrc}})])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAkB,CAAC,EAAC,CAACF,EAAE,CAAC,QAAQ,EAAC;IAACG,GAAG,EAACJ,GAAG,CAACK,SAAS;IAACC,GAAG,EAAC,QAAQ;IAACC,KAAK,EAAC;MAAC,KAAK,EAACP,GAAG,CAACQ;IAAS;EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjO,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AAExB,SAASV,MAAM,EAAEU,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}