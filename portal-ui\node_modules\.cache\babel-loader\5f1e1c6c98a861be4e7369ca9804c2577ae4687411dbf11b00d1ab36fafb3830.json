{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { postAnyData, getAnyData, postLogin, postNotAuth } from \"@/api/login\";\nimport { getToken, setToken, removeToken } from '@/utils/auth';\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\nimport backgroundlogin from '@/views/Login/backgroundlogin.vue';\nexport default {\n  name: \"register\",\n  components: {\n    SlideNotification,\n    backgroundlogin\n  },\n  data() {\n    return {\n      registrationForm: {\n        phone: '',\n        password: '',\n        confirmPassword: '',\n        code: '',\n        username: 'user1',\n        usage: '商业办公',\n        otherUsage: '',\n        agreement: false\n      },\n      isSendingCode: false,\n      passwordVisible: false,\n      confirmPasswordVisible: false,\n      errors: {\n        phone: '',\n        password: '',\n        confirmPassword: '',\n        code: ''\n      },\n      codeSent: false,\n      countdown: 60,\n      timer: null,\n      showCodeSent: false,\n      minHeight: '50px'\n    };\n  },\n  watch: {\n    'registrationForm.phone'(newVal) {\n      this.registrationForm.username = newVal;\n    }\n  },\n  created() {\n    // Check for existing timer on page load\n    this.checkExistingTimer();\n    this.$emit('hiden-layout');\n  },\n  computed: {\n    isFormValid() {\n      return this.registrationForm.phone && this.registrationForm.password && this.registrationForm.confirmPassword && this.registrationForm.code && this.registrationForm.agreement && !this.errors.phone && !this.errors.password && !this.errors.confirmPassword && !this.errors.code;\n    }\n  },\n  methods: {\n    checkExistingTimer() {\n      // Get stored verification code data\n      const storedCodeData = localStorage.getItem('verificationCodeData');\n      if (storedCodeData) {\n        const codeData = JSON.parse(storedCodeData);\n        // Check if phone number matches\n        if (codeData.phone && this.registrationForm.phone && codeData.phone === this.registrationForm.phone) {\n          // Check if timer is still valid\n          const currentTime = Date.now();\n          const expiryTime = codeData.timestamp + 60 * 1000; // 60 seconds from sent time\n\n          if (currentTime < expiryTime) {\n            // Calculate remaining time\n            const remainingTime = Math.ceil((expiryTime - currentTime) / 1000);\n            this.countdown = remainingTime;\n            this.codeSent = true;\n            this.startCountdown();\n          } else {\n            // Timer expired, clear storage\n            localStorage.removeItem('verificationCodeData');\n          }\n        }\n      }\n    },\n    validatePhone() {\n      const phoneRegex = /^1[3-9]\\d{9}$/;\n      if (!this.registrationForm.phone) {\n        this.errors.phone = '请输入手机号';\n      } else if (!phoneRegex.test(this.registrationForm.phone)) {\n        this.errors.phone = '请输入有效的手机号';\n      } else {\n        this.errors.phone = '';\n      }\n    },\n    validatePassword() {\n      const password = this.registrationForm.password;\n      this.errors.password = ''; // 重置错误信息\n\n      if (!password) {\n        this.errors.password = '请输入密码';\n        return;\n      }\n\n      // 强制最小长度要求\n      if (password.length < 8) {\n        this.errors.password = '密码长度至少为8位';\n        return;\n      }\n\n      // 三选二的强度要求\n      const hasLetter = /[a-zA-Z]/.test(password);\n      const hasNumber = /[0-9]/.test(password);\n      const hasSymbol = /[!@#$%^&*()_+\\-=$${};':\"\\\\|,.<>\\/?]/.test(password);\n      const hasUpperLower = /[A-Z]/.test(password) && /[a-z]/.test(password);\n      let strengthCount = 0;\n      if (hasLetter && hasNumber) strengthCount++;\n      if (hasSymbol) strengthCount++;\n      if (hasUpperLower) strengthCount++;\n      if (strengthCount >= 2) {\n        this.errors.password = ''; // 密码符合要求\n      } else {\n        const missingRequirements = [];\n        if (!(hasLetter && hasNumber)) missingRequirements.push('包含数字和字母');\n        if (!hasSymbol) missingRequirements.push('包含特殊符号');\n        if (!hasUpperLower) missingRequirements.push('包含大小写字母');\n        this.errors.password = `密码强度不足，请满足以下至少两项要求：${missingRequirements.join('、')}`;\n      }\n\n      // 如果确认密码已填写，联动验证\n      if (this.registrationForm.confirmPassword) {\n        this.validateConfirmPassword();\n      }\n    },\n    navigateTo(path) {\n      // 如果当前路径与目标路径相同，则重新加载页面\n      if (this.$route.path === path) {\n        // 先跳转到一个临时路由（如果有的话）或者重新加载页面\n        this.$nextTick(() => {\n          window.scrollTo({\n            top: 0,\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\n          });\n\n          this.$router.go(0); // 刷新当前页面\n        });\n      } else {\n        // 不同路径，正常导航并滚动到顶部\n        this.$router.push(path);\n        window.scrollTo({\n          top: 0,\n          behavior: 'instant'\n        });\n      }\n      this.currentPath = path;\n    },\n    validateConfirmPassword() {\n      if (!this.registrationForm.confirmPassword) {\n        this.errors.confirmPassword = '请再次输入密码';\n      } else if (this.registrationForm.confirmPassword !== this.registrationForm.password) {\n        this.errors.confirmPassword = '两次输入的密码不一致';\n      } else {\n        this.errors.confirmPassword = '';\n      }\n    },\n    async validateCodegeshi() {\n      // 清空错误\n      this.errors.code = '';\n\n      // 前端基础验证（保持原有长度判断）\n      if (!this.registrationForm.code) {\n        this.errors.code = '请输入验证码';\n        return false;\n      }\n      if (this.registrationForm.code.length !== 4 || !/^\\d+$/.test(this.registrationForm.code)) {\n        this.errors.code = '验证码必须为4位数字';\n        return false;\n      }\n    },\n    async getVerificationCode() {\n      // First check if there's an existing timer for this phone number\n      const storedCodeData = localStorage.getItem('verificationCodeData');\n      if (storedCodeData) {\n        const codeData = JSON.parse(storedCodeData);\n        if (codeData.phone === this.registrationForm.phone) {\n          const currentTime = Date.now();\n          const expiryTime = codeData.timestamp + 60 * 1000;\n          if (currentTime < expiryTime) {\n            // Timer still active, update countdown and prevent new request\n            const remainingTime = Math.ceil((expiryTime - currentTime) / 1000);\n            this.countdown = remainingTime;\n            this.codeSent = true;\n            this.startCountdown();\n            return;\n          }\n        }\n      }\n\n      // 先验证手机号格式\n      this.validatePhone();\n      if (this.errors.phone) return;\n      try {\n        // 显示发送中状态\n        this.isSendingCode = true;\n\n        // 调用发送验证码接口\n        const response = await postLogin(\"/auth/sendCode\", {\n          phone: this.registrationForm.phone\n        });\n\n        // 处理响应 - 只有在成功时才显示通知和启动倒计时\n        if (response.data.code === 200) {\n          // Store sent time and phone in localStorage\n          const verificationData = {\n            phone: this.registrationForm.phone,\n            timestamp: Date.now()\n          };\n          localStorage.setItem('verificationCodeData', JSON.stringify(verificationData));\n          this.codeSent = true;\n          this.showCodeSent = true;\n          this.startCountdown();\n        } else {\n          // 处理失败响应\n          this.errors.code = response.data.msg || '验证码发送失败';\n          // 不启动倒计时，允许用户重试\n        }\n      } catch (error) {\n        this.errors.code = '网络异常，请稍后重试';\n      } finally {\n        this.isSendingCode = false;\n      }\n    },\n    startCountdown() {\n      // 清除可能存在的旧定时器\n      if (this.timer) {\n        clearInterval(this.timer);\n      }\n\n      // 使用固定的时间间隔\n      this.timer = setInterval(() => {\n        if (this.countdown <= 1) {\n          clearInterval(this.timer);\n          this.codeSent = false;\n          this.countdown = 60;\n          // Clear localStorage when timer expires\n          localStorage.removeItem('verificationCodeData');\n        } else {\n          this.countdown--;\n          // Update remaining time in localStorage\n          const storedCodeData = localStorage.getItem('verificationCodeData');\n          if (storedCodeData) {\n            const codeData = JSON.parse(storedCodeData);\n            // Only update if it's for the current phone\n            if (codeData.phone === this.registrationForm.phone) {\n              // Just keep the original timestamp, no need to update it\n            }\n          }\n        }\n      }, 1000);\n    },\n    register() {\n      this.validatePhone();\n      this.validatePassword();\n      this.validateConfirmPassword();\n      // 清空错误\n      this.errors.code = '';\n      // 新增接口验证（参数与发送接口一致）\n      postLogin(\"/auth/verifyCode\", {\n        phone: this.registrationForm.phone,\n        // 必须携带手机号\n        code: this.registrationForm.code\n      }).then(response => {\n        // 统一响应码判断标准\n        if (response.data.code !== 200) {\n          this.errors.code = response.data.msg || '验证码错误';\n          return;\n        }\n        // 注册API调用\n        postNotAuth(\"/auth/register\", this.registrationForm).then(res => {\n          if (res.data.code === 200) {\n            // Clear verification code data after successful registration\n            localStorage.removeItem('verificationCodeData');\n            // 跳转到登录页面\n            this.$router.push('/login');\n          } else {\n            this.errors.code = res.data.msg;\n          }\n        });\n      });\n    },\n    togglePasswordVisibility() {\n      this.passwordVisible = !this.passwordVisible;\n    },\n    toggleConfirmPasswordVisibility() {\n      this.confirmPasswordVisible = !this.confirmPasswordVisible;\n    },\n    goToLogin() {\n      // 在实际应用中重定向到登录页面\n      // this.$router.push('/login');\n    }\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n    this.$emit('hiden-layout');\n  }\n};", "map": {"version": 3, "names": ["postAnyData", "getAnyData", "postLogin", "postNotAuth", "getToken", "setToken", "removeToken", "SlideNotification", "backgroundlogin", "name", "components", "data", "registrationForm", "phone", "password", "confirmPassword", "code", "username", "usage", "otherUsage", "agreement", "isSendingCode", "passwordVisible", "confirmPasswordVisible", "errors", "codeSent", "countdown", "timer", "showCodeSent", "minHeight", "watch", "registrationForm.phone", "newVal", "created", "checkExistingTimer", "$emit", "computed", "isFormValid", "methods", "storedCodeData", "localStorage", "getItem", "codeData", "JSON", "parse", "currentTime", "Date", "now", "expiryTime", "timestamp", "remainingTime", "Math", "ceil", "startCountdown", "removeItem", "validatePhone", "phoneRegex", "test", "validatePassword", "length", "hasLetter", "hasNumber", "hasSymbol", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "strengthCount", "missingRequirements", "push", "join", "validateConfirmPassword", "navigateTo", "path", "$route", "$nextTick", "window", "scrollTo", "top", "behavior", "$router", "go", "currentPath", "validate<PERSON><PERSON><PERSON><PERSON>", "getVerificationCode", "response", "verificationData", "setItem", "stringify", "msg", "error", "clearInterval", "setInterval", "register", "then", "res", "togglePasswordVisibility", "toggleConfirmPasswordVisibility", "goToLogin", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/Login/register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login-page\">\r\n    <div class=\"left-side\">\r\n      <backgroundlogin />\r\n    </div>\r\n\r\n    <div class=\"right-side\">\r\n      <div class=\"login-form-container\">\r\n        <h3>注册 天工开物</h3>\r\n\r\n        <div class=\"form-container\">\r\n          <div class=\"login-form\">\r\n            <p class=\"form-note\">只需一个 天工开物 账号，即可访问 天工开物 的所有服务。</p>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.phone }\">\r\n              <div class=\"phone-input-container\">\r\n                <input\r\n                    type=\"text\"\r\n                    v-model=\"registrationForm.phone\"\r\n                    placeholder=\"请输入手机号\"\r\n                    @blur=\"validatePhone\"\r\n                />\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.phone\" class=\"error-message\">{{ errors.phone }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.password }\">\r\n              <div class=\"password-input-container\">\r\n                <input\r\n                    :type=\"passwordVisible ? 'text' : 'password'\"\r\n                    v-model=\"registrationForm.password\"\r\n                    placeholder=\"请输入密码\"\r\n                    @blur=\"validatePassword\"\r\n                />\r\n                <span class=\"password-toggle\" @click=\"togglePasswordVisibility\">\r\n                  <i :class=\"['eye-icon', passwordVisible ? 'visible' : '']\"></i>\r\n                </span>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.password\" class=\"error-message\">{{ errors.password }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.confirmPassword }\">\r\n              <div class=\"password-input-container\">\r\n                <input\r\n                    :type=\"confirmPasswordVisible ? 'text' : 'password'\"\r\n                    v-model=\"registrationForm.confirmPassword\"\r\n                    placeholder=\"请再次输入密码\"\r\n                    @blur=\"validateConfirmPassword\"\r\n                />\r\n                <span class=\"password-toggle\" @click=\"toggleConfirmPasswordVisibility\">\r\n                  <i :class=\"['eye-icon', confirmPasswordVisible ? 'visible' : '']\"></i>\r\n                </span>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.confirmPassword\" class=\"error-message\">{{ errors.confirmPassword }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group verification-code\" :class=\"{ 'error': errors.code }\">\r\n              <div class=\"code-input-container\">\r\n                <input\r\n                    type=\"text\"\r\n                    v-model=\"registrationForm.code\"\r\n                    placeholder=\"请输入验证码\"\r\n                    @blur=\"validateCodegeshi\"\r\n                />\r\n                <button\r\n                    class=\"get-code-btn-inline\"\r\n                    @click=\"getVerificationCode\"\r\n                    :disabled=\"!registrationForm.phone || errors.phone || codeSent\"\r\n                >\r\n                  {{ codeSent ? `${countdown}秒后重试` : '获取验证码' }}\r\n                </button>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.code\" class=\"error-message\">{{ errors.code }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"agreement-text\">\r\n              注册视为您已阅读并同意天工开物 \r\n              <router-link to=\"/help/user-agreement\" >服务条款</router-link> 和<router-link to=\"/help/privacy-policy\" >隐私政策</router-link>\r\n            </div>\r\n\r\n            <button\r\n                class=\"register-btn\"\r\n                @click=\"register\"\r\n            >\r\n              注册\r\n            </button>\r\n\r\n            <div class=\"login-link\">\r\n              <a href=\"#\" @click=\"navigateTo('/forgetpass')\">忘记密码</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"#\" @click=\"navigateTo('/login')\">返回登录</a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 使用 SlideNotification 组件替换原来的提示 -->\r\n    <SlideNotification\r\n        v-if=\"showCodeSent\"\r\n        message=\"验证码已发送，可能会有延迟，请耐心等待！\"\r\n        type=\"success\"\r\n        :duration=\"3000\"\r\n        @close=\"showCodeSent = false\"\r\n        :minHeight=\"minHeight\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { postAnyData, getAnyData, postLogin, postNotAuth } from \"@/api/login\";\r\nimport { getToken, setToken, removeToken } from '@/utils/auth'\r\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\r\nimport backgroundlogin from '@/views/Login/backgroundlogin.vue';\r\n\r\n\r\nexport default {\r\n  name: \"register\",\r\n  components: {\r\n    SlideNotification,\r\n    backgroundlogin\r\n  },\r\n  data() {\r\n    return {\r\n      registrationForm: {\r\n        phone: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        code: '',\r\n        username: 'user1',\r\n        usage: '商业办公',\r\n        otherUsage: '',\r\n        agreement: false\r\n      },\r\n      isSendingCode: false,\r\n      passwordVisible: false,\r\n      confirmPasswordVisible: false,\r\n      errors: {\r\n        phone: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        code: ''\r\n      },\r\n      codeSent: false,\r\n      countdown: 60,\r\n      timer: null,\r\n      showCodeSent: false,\r\n      minHeight: '50px'\r\n    }\r\n  },\r\n  watch: {\r\n    'registrationForm.phone'(newVal) {\r\n      this.registrationForm.username = newVal;\r\n    }\r\n  },\r\n  created() {\r\n    // Check for existing timer on page load\r\n    this.checkExistingTimer();\r\n    this.$emit('hiden-layout')\r\n  },\r\n  computed: {\r\n    isFormValid() {\r\n      return (\r\n          this.registrationForm.phone &&\r\n          this.registrationForm.password &&\r\n          this.registrationForm.confirmPassword &&\r\n          this.registrationForm.code &&\r\n          this.registrationForm.agreement &&\r\n          !this.errors.phone &&\r\n          !this.errors.password &&\r\n          !this.errors.confirmPassword &&\r\n          !this.errors.code\r\n      );\r\n    }\r\n  },\r\n  methods: {\r\n    checkExistingTimer() {\r\n      // Get stored verification code data\r\n      const storedCodeData = localStorage.getItem('verificationCodeData');\r\n      if (storedCodeData) {\r\n        const codeData = JSON.parse(storedCodeData);\r\n        // Check if phone number matches\r\n        if (codeData.phone && this.registrationForm.phone && codeData.phone === this.registrationForm.phone) {\r\n          // Check if timer is still valid\r\n          const currentTime = Date.now();\r\n          const expiryTime = codeData.timestamp + (60 * 1000); // 60 seconds from sent time\r\n\r\n          if (currentTime < expiryTime) {\r\n            // Calculate remaining time\r\n            const remainingTime = Math.ceil((expiryTime - currentTime) / 1000);\r\n            this.countdown = remainingTime;\r\n            this.codeSent = true;\r\n            this.startCountdown();\r\n          } else {\r\n            // Timer expired, clear storage\r\n            localStorage.removeItem('verificationCodeData');\r\n          }\r\n        }\r\n      }\r\n    },\r\n    validatePhone() {\r\n      const phoneRegex = /^1[3-9]\\d{9}$/;\r\n      if (!this.registrationForm.phone) {\r\n        this.errors.phone = '请输入手机号';\r\n      } else if (!phoneRegex.test(this.registrationForm.phone)) {\r\n        this.errors.phone = '请输入有效的手机号';\r\n      } else {\r\n        this.errors.phone = '';\r\n      }\r\n    },\r\n\r\n    validatePassword() {\r\n      const password = this.registrationForm.password;\r\n      this.errors.password = ''; // 重置错误信息\r\n\r\n      if (!password) {\r\n        this.errors.password = '请输入密码';\r\n        return;\r\n      }\r\n\r\n      // 强制最小长度要求\r\n      if (password.length < 8) {\r\n        this.errors.password = '密码长度至少为8位';\r\n        return;\r\n      }\r\n\r\n      // 三选二的强度要求\r\n      const hasLetter = /[a-zA-Z]/.test(password);\r\n      const hasNumber = /[0-9]/.test(password);\r\n      const hasSymbol = /[!@#$%^&*()_+\\-=$${};':\"\\\\|,.<>\\/?]/.test(password);\r\n      const hasUpperLower = /[A-Z]/.test(password) && /[a-z]/.test(password);\r\n\r\n      let strengthCount = 0;\r\n      if (hasLetter && hasNumber) strengthCount++;\r\n      if (hasSymbol) strengthCount++;\r\n      if (hasUpperLower) strengthCount++;\r\n\r\n      if (strengthCount >= 2) {\r\n        this.errors.password = ''; // 密码符合要求\r\n      } else {\r\n        const missingRequirements = [];\r\n        if (!(hasLetter && hasNumber)) missingRequirements.push('包含数字和字母');\r\n        if (!hasSymbol) missingRequirements.push('包含特殊符号');\r\n        if (!hasUpperLower) missingRequirements.push('包含大小写字母');\r\n\r\n        this.errors.password = `密码强度不足，请满足以下至少两项要求：${missingRequirements.join('、')}`;\r\n      }\r\n\r\n      // 如果确认密码已填写，联动验证\r\n      if (this.registrationForm.confirmPassword) {\r\n        this.validateConfirmPassword();\r\n      }\r\n    },\r\n\r\n    navigateTo(path) {\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        // 先跳转到一个临时路由（如果有的话）或者重新加载页面\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n      this.currentPath = path;\r\n    },\r\n    validateConfirmPassword() {\r\n      if (!this.registrationForm.confirmPassword) {\r\n        this.errors.confirmPassword = '请再次输入密码';\r\n      } else if (this.registrationForm.confirmPassword !== this.registrationForm.password) {\r\n        this.errors.confirmPassword = '两次输入的密码不一致';\r\n      } else {\r\n        this.errors.confirmPassword = '';\r\n      }\r\n    },\r\n    async validateCodegeshi() {\r\n      // 清空错误\r\n      this.errors.code = '';\r\n\r\n      // 前端基础验证（保持原有长度判断）\r\n      if (!this.registrationForm.code) {\r\n        this.errors.code = '请输入验证码';\r\n        return false;\r\n      }\r\n      if (this.registrationForm.code.length !== 4 || !/^\\d+$/.test(this.registrationForm.code)) {\r\n        this.errors.code = '验证码必须为4位数字';\r\n        return false;\r\n      }\r\n    },\r\n    async getVerificationCode() {\r\n      // First check if there's an existing timer for this phone number\r\n      const storedCodeData = localStorage.getItem('verificationCodeData');\r\n      if (storedCodeData) {\r\n        const codeData = JSON.parse(storedCodeData);\r\n        if (codeData.phone === this.registrationForm.phone) {\r\n          const currentTime = Date.now();\r\n          const expiryTime = codeData.timestamp + (60 * 1000);\r\n\r\n          if (currentTime < expiryTime) {\r\n            // Timer still active, update countdown and prevent new request\r\n            const remainingTime = Math.ceil((expiryTime - currentTime) / 1000);\r\n            this.countdown = remainingTime;\r\n            this.codeSent = true;\r\n            this.startCountdown();\r\n            return;\r\n          }\r\n        }\r\n      }\r\n\r\n      // 先验证手机号格式\r\n      this.validatePhone();\r\n      if (this.errors.phone) return;\r\n\r\n      try {\r\n        // 显示发送中状态\r\n        this.isSendingCode = true;\r\n\r\n        // 调用发送验证码接口\r\n        const response = await postLogin(\"/auth/sendCode\", {\r\n          phone: this.registrationForm.phone\r\n        });\r\n\r\n        // 处理响应 - 只有在成功时才显示通知和启动倒计时\r\n        if (response.data.code === 200) {\r\n          // Store sent time and phone in localStorage\r\n          const verificationData = {\r\n            phone: this.registrationForm.phone,\r\n            timestamp: Date.now()\r\n          };\r\n          localStorage.setItem('verificationCodeData', JSON.stringify(verificationData));\r\n\r\n          this.codeSent = true;\r\n          this.showCodeSent = true;\r\n          this.startCountdown();\r\n        } else {\r\n          // 处理失败响应\r\n          this.errors.code = response.data.msg || '验证码发送失败';\r\n          // 不启动倒计时，允许用户重试\r\n        }\r\n      } catch (error) {\r\n        this.errors.code = '网络异常，请稍后重试';\r\n      } finally {\r\n        this.isSendingCode = false;\r\n      }\r\n    },\r\n    startCountdown() {\r\n      // 清除可能存在的旧定时器\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n      }\r\n\r\n      // 使用固定的时间间隔\r\n      this.timer = setInterval(() => {\r\n        if (this.countdown <= 1) {\r\n          clearInterval(this.timer);\r\n          this.codeSent = false;\r\n          this.countdown = 60;\r\n          // Clear localStorage when timer expires\r\n          localStorage.removeItem('verificationCodeData');\r\n        } else {\r\n          this.countdown--;\r\n          // Update remaining time in localStorage\r\n          const storedCodeData = localStorage.getItem('verificationCodeData');\r\n          if (storedCodeData) {\r\n            const codeData = JSON.parse(storedCodeData);\r\n            // Only update if it's for the current phone\r\n            if (codeData.phone === this.registrationForm.phone) {\r\n              // Just keep the original timestamp, no need to update it\r\n            }\r\n          }\r\n        }\r\n      }, 1000);\r\n    },\r\n    register() {\r\n      this.validatePhone();\r\n      this.validatePassword();\r\n      this.validateConfirmPassword();\r\n      // 清空错误\r\n      this.errors.code = '';\r\n      // 新增接口验证（参数与发送接口一致）\r\n      postLogin(\"/auth/verifyCode\", {\r\n        phone: this.registrationForm.phone, // 必须携带手机号\r\n        code: this.registrationForm.code\r\n      }).then(response => {\r\n        // 统一响应码判断标准\r\n        if (response.data.code !== 200) {\r\n          this.errors.code = response.data.msg || '验证码错误';\r\n          return;\r\n        }\r\n        // 注册API调用\r\n        postNotAuth(\"/auth/register\",this.registrationForm).then(res =>{\r\n          if (res.data.code === 200) {\r\n            // Clear verification code data after successful registration\r\n            localStorage.removeItem('verificationCodeData');\r\n            // 跳转到登录页面\r\n            this.$router.push('/login')\r\n          }\r\n          else {\r\n            this.errors.code = res.data.msg;\r\n          }\r\n        })\r\n      })\r\n    },\r\n    togglePasswordVisibility(){\r\n      this.passwordVisible = !this.passwordVisible;\r\n    },\r\n    toggleConfirmPasswordVisibility() {\r\n      this.confirmPasswordVisible = !this.confirmPasswordVisible;\r\n    },\r\n    goToLogin() {\r\n      // 在实际应用中重定向到登录页面\r\n      // this.$router.push('/login');\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    if (this.timer) {\r\n      clearInterval(this.timer);\r\n    }\r\n    this.$emit('hiden-layout')\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* Full page layout */\r\n.login-page {\r\n  display: flex;\r\n  min-height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Left side styling - keeping original styles */\r\n.left-side {\r\n  flex: 1;\r\n  position: relative;\r\n  background: linear-gradient(135deg, #cdb3e5, #5127d5);\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 0rem;\r\n  color: #030303;\r\n}\r\n\r\n/* Animated background - keeping original styles */\r\n.animated-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  overflow: hidden;\r\n  opacity: 0.4;\r\n}\r\n\r\n.hex-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.hex {\r\n  position: absolute;\r\n  width: 100px;\r\n  height: 110px;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);\r\n}\r\n\r\n.hex1 {\r\n  top: 20%;\r\n  left: 10%;\r\n  transform: scale(1.5);\r\n  animation: float 8s infinite ease-in-out;\r\n}\r\n\r\n.hex2 {\r\n  top: 60%;\r\n  left: 20%;\r\n  transform: scale(1.2);\r\n  animation: float 7s infinite ease-in-out reverse;\r\n}\r\n\r\n.hex3 {\r\n  top: 30%;\r\n  left: 50%;\r\n  transform: scale(1.3);\r\n  animation: float 10s infinite ease-in-out 1s;\r\n}\r\n\r\n.hex4 {\r\n  top: 70%;\r\n  left: 70%;\r\n  transform: scale(1.1);\r\n  animation: float 6s infinite ease-in-out 2s;\r\n}\r\n\r\n.hex5 {\r\n  top: 40%;\r\n  left: 80%;\r\n  transform: scale(1.4);\r\n  animation: float 9s infinite ease-in-out 3s;\r\n}\r\n\r\n.floating-cube {\r\n  position: absolute;\r\n  width: 50px;\r\n  height: 50px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  top: 25%;\r\n  left: 30%;\r\n  animation: float 8s infinite ease-in-out, rotate 15s infinite linear;\r\n}\r\n\r\n.floating-sphere {\r\n  position: absolute;\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  top: 50%;\r\n  left: 40%;\r\n  animation: float 10s infinite ease-in-out reverse;\r\n}\r\n\r\n.floating-diamond {\r\n  position: absolute;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: rotate(45deg);\r\n  top: 65%;\r\n  left: 60%;\r\n  animation: float 7s infinite ease-in-out 2s;\r\n}\r\n\r\n.ripple-effect {\r\n  position: absolute;\r\n  width: 200px;\r\n  height: 200px;\r\n  border-radius: 50%;\r\n  border: 3px solid rgba(255, 255, 255, 0.1);\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  animation: ripple 6s infinite linear;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(20px);\r\n  }\r\n}\r\n\r\n@keyframes rotate {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n@keyframes ripple {\r\n  0% {\r\n    width: 0;\r\n    height: 0;\r\n    opacity: 0.8;\r\n  }\r\n  100% {\r\n    width: 300px;\r\n    height: 300px;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* Right side styling */\r\n.right-side {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #f8f9fa;\r\n  padding: 2rem;\r\n}\r\n\r\n.login-form-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  padding: 2rem;\r\n  width: 100%;\r\n  max-width: 400px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.login-form-container h3 {\r\n  font-size: 24px;\r\n  font-weight: 500;\r\n  margin-bottom: 20px;\r\n  text-align: center;\r\n  color: #333;\r\n}\r\n\r\n/* Form container */\r\n.form-container {\r\n  min-height: 300px;\r\n  position: relative;\r\n}\r\n\r\n.login-form {\r\n  margin-top: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.form-note {\r\n  color: #999;\r\n  font-size: 12px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.input-group {\r\n  margin-bottom: 15px;\r\n  position: relative;\r\n}\r\n\r\n.input-group input {\r\n  width: 100%;\r\n  padding: 12px 15px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.input-group input:focus {\r\n  outline: none;\r\n  border-color: #6a26cd;\r\n}\r\n\r\n/* Phone input with prefix */\r\n.phone-input-container {\r\n  display: flex;\r\n  align-items: center;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n}\r\n\r\n.phone-prefix {\r\n  padding: 0 10px;\r\n  color: #333;\r\n  border-right: 1px solid #ddd;\r\n  background-color: #f5f5f5;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 42px;\r\n}\r\n\r\n.phone-input-container input {\r\n  border: none;\r\n  flex: 1;\r\n}\r\n\r\n.error-container {\r\n  min-height: 19px;\r\n  display: block;\r\n}\r\n\r\n.error-message {\r\n  color: #f44336;\r\n  font-size: 12px;\r\n}\r\n\r\n/* Verification code input and button */\r\n.verification-code .code-input-container {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.verification-code input {\r\n  flex: 1;\r\n}\r\n\r\n.get-code-btn-inline {\r\n  flex-shrink: 0;\r\n  width: 110px;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.get-code-btn-inline:hover:not(:disabled) {\r\n  background-color: #4169E1;\r\n}\r\n\r\n.get-code-btn-inline:disabled {\r\n  border-color: #2196f3;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Password input with toggle */\r\n.password-input-container {\r\n  position: relative;\r\n}\r\n\r\n.password-toggle {\r\n  position: absolute;\r\n  right: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\ninput[type=\"password\"]::-ms-reveal,\r\ninput[type=\"password\"]::-webkit-credentials-auto-fill-button,\r\ninput[type=\"password\"]::-webkit-clear-button {\r\n  display: none !important;\r\n  pointer-events: none;\r\n}\r\n\r\n.eye-icon {\r\n  display: inline-block;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle></svg>');\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  opacity: 0.5;\r\n}\r\n\r\n.eye-icon.visible {\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle><line x1=\"1\" y1=\"1\" x2=\"23\" y2=\"23\"></line></svg>');\r\n}\r\n\r\n/* Usage section */\r\n.usage-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.usage-title {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.usage-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.usage-option {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.usage-option input {\r\n  margin-right: 5px;\r\n  width: auto;\r\n}\r\n\r\n.other-usage {\r\n  margin-top: 10px;\r\n}\r\n\r\n.other-usage input {\r\n  width: 100%;\r\n  padding: 8px 12px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* Agreement checkbox */\r\n.agreement-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.agreement-checkbox {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  cursor: pointer;\r\n}\r\n\r\n.agreement-checkbox input {\r\n  margin-right: 8px;\r\n  margin-top: 2px;\r\n  width: auto;\r\n}\r\n\r\n.agreement-checkbox span {\r\n  font-size: 13px;\r\n  color: #666;\r\n}\r\n\r\n.link {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n\r\n/* Register button */\r\n.register-btn {\r\n  width: 100%;\r\n  padding: 12px 0;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.register-btn:hover:not(:disabled) {\r\n  background-color: #4169E1;\r\n}\r\n\r\n.register-btn:disabled {\r\n  background-color: #4169E1;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Login link */\r\n.login-link {\r\n  text-align: center;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-link a {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n\r\n.divider {\r\n  margin: 0 10px;\r\n  color: #ddd;\r\n}\r\n\r\n.logo-area {\r\n  flex: 0 0 auto;\r\n  margin-right: 10px;\r\n}\r\n\r\n.logo-link {\r\n  display: flex;\r\n  align-items: center;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n}\r\n\r\n.logo-area img {\r\n  height: 30px;\r\n  max-width: 100%;\r\n}\r\n\r\n.logo-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-left: 10px;\r\n}\r\n.agreement-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.agreement-text a {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n@media screen and (max-width: 768px) {\r\n  .left-side {\r\n    display: none; /* 在手机端隐藏左侧背景 */\r\n  }\r\n\r\n  .right-side {\r\n    flex: 1 0 100%; /* 让右侧占据全部宽度 */\r\n    padding: 1rem; /* 减少内边距以适应小屏幕 */\r\n  }\r\n\r\n  .login-form-container {\r\n    max-width: 100%; /* 让登录表单占据全部可用宽度 */\r\n    box-shadow: none; /* 移除阴影以节省空间 */\r\n    padding: 1.5rem; /* 调整内边距 */\r\n  }\r\n\r\n}\r\n</style>\r\n"], "mappings": ";AAsHA,SAAAA,WAAA,EAAAC,UAAA,EAAAC,SAAA,EAAAC,WAAA;AACA,SAAAC,QAAA,EAAAC,QAAA,EAAAC,WAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,eAAA;AAGA;EACAC,IAAA;EACAC,UAAA;IACAH,iBAAA;IACAC;EACA;EACAG,KAAA;IACA;MACAC,gBAAA;QACAC,KAAA;QACAC,QAAA;QACAC,eAAA;QACAC,IAAA;QACAC,QAAA;QACAC,KAAA;QACAC,UAAA;QACAC,SAAA;MACA;MACAC,aAAA;MACAC,eAAA;MACAC,sBAAA;MACAC,MAAA;QACAX,KAAA;QACAC,QAAA;QACAC,eAAA;QACAC,IAAA;MACA;MACAS,QAAA;MACAC,SAAA;MACAC,KAAA;MACAC,YAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACA,wBAAAC,CAAAC,MAAA;MACA,KAAApB,gBAAA,CAAAK,QAAA,GAAAe,MAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,KAAAC,kBAAA;IACA,KAAAC,KAAA;EACA;EACAC,QAAA;IACAC,YAAA;MACA,OACA,KAAAzB,gBAAA,CAAAC,KAAA,IACA,KAAAD,gBAAA,CAAAE,QAAA,IACA,KAAAF,gBAAA,CAAAG,eAAA,IACA,KAAAH,gBAAA,CAAAI,IAAA,IACA,KAAAJ,gBAAA,CAAAQ,SAAA,IACA,MAAAI,MAAA,CAAAX,KAAA,IACA,MAAAW,MAAA,CAAAV,QAAA,IACA,MAAAU,MAAA,CAAAT,eAAA,IACA,MAAAS,MAAA,CAAAR,IAAA;IAEA;EACA;EACAsB,OAAA;IACAJ,mBAAA;MACA;MACA,MAAAK,cAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,IAAAF,cAAA;QACA,MAAAG,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,cAAA;QACA;QACA,IAAAG,QAAA,CAAA7B,KAAA,SAAAD,gBAAA,CAAAC,KAAA,IAAA6B,QAAA,CAAA7B,KAAA,UAAAD,gBAAA,CAAAC,KAAA;UACA;UACA,MAAAgC,WAAA,GAAAC,IAAA,CAAAC,GAAA;UACA,MAAAC,UAAA,GAAAN,QAAA,CAAAO,SAAA;;UAEA,IAAAJ,WAAA,GAAAG,UAAA;YACA;YACA,MAAAE,aAAA,GAAAC,IAAA,CAAAC,IAAA,EAAAJ,UAAA,GAAAH,WAAA;YACA,KAAAnB,SAAA,GAAAwB,aAAA;YACA,KAAAzB,QAAA;YACA,KAAA4B,cAAA;UACA;YACA;YACAb,YAAA,CAAAc,UAAA;UACA;QACA;MACA;IACA;IACAC,cAAA;MACA,MAAAC,UAAA;MACA,UAAA5C,gBAAA,CAAAC,KAAA;QACA,KAAAW,MAAA,CAAAX,KAAA;MACA,YAAA2C,UAAA,CAAAC,IAAA,MAAA7C,gBAAA,CAAAC,KAAA;QACA,KAAAW,MAAA,CAAAX,KAAA;MACA;QACA,KAAAW,MAAA,CAAAX,KAAA;MACA;IACA;IAEA6C,iBAAA;MACA,MAAA5C,QAAA,QAAAF,gBAAA,CAAAE,QAAA;MACA,KAAAU,MAAA,CAAAV,QAAA;;MAEA,KAAAA,QAAA;QACA,KAAAU,MAAA,CAAAV,QAAA;QACA;MACA;;MAEA;MACA,IAAAA,QAAA,CAAA6C,MAAA;QACA,KAAAnC,MAAA,CAAAV,QAAA;QACA;MACA;;MAEA;MACA,MAAA8C,SAAA,cAAAH,IAAA,CAAA3C,QAAA;MACA,MAAA+C,SAAA,WAAAJ,IAAA,CAAA3C,QAAA;MACA,MAAAgD,SAAA,yCAAAL,IAAA,CAAA3C,QAAA;MACA,MAAAiD,aAAA,WAAAN,IAAA,CAAA3C,QAAA,aAAA2C,IAAA,CAAA3C,QAAA;MAEA,IAAAkD,aAAA;MACA,IAAAJ,SAAA,IAAAC,SAAA,EAAAG,aAAA;MACA,IAAAF,SAAA,EAAAE,aAAA;MACA,IAAAD,aAAA,EAAAC,aAAA;MAEA,IAAAA,aAAA;QACA,KAAAxC,MAAA,CAAAV,QAAA;MACA;QACA,MAAAmD,mBAAA;QACA,MAAAL,SAAA,IAAAC,SAAA,GAAAI,mBAAA,CAAAC,IAAA;QACA,KAAAJ,SAAA,EAAAG,mBAAA,CAAAC,IAAA;QACA,KAAAH,aAAA,EAAAE,mBAAA,CAAAC,IAAA;QAEA,KAAA1C,MAAA,CAAAV,QAAA,yBAAAmD,mBAAA,CAAAE,IAAA;MACA;;MAEA;MACA,SAAAvD,gBAAA,CAAAG,eAAA;QACA,KAAAqD,uBAAA;MACA;IACA;IAEAC,WAAAC,IAAA;MACA;MACA,SAAAC,MAAA,CAAAD,IAAA,KAAAA,IAAA;QACA;QACA,KAAAE,SAAA;UACAC,MAAA,CAAAC,QAAA;YACAC,GAAA;YACAC,QAAA;UACA;;UACA,KAAAC,OAAA,CAAAC,EAAA;QACA;MACA;QACA;QACA,KAAAD,OAAA,CAAAX,IAAA,CAAAI,IAAA;QACAG,MAAA,CAAAC,QAAA;UACAC,GAAA;UACAC,QAAA;QACA;MACA;MACA,KAAAG,WAAA,GAAAT,IAAA;IACA;IACAF,wBAAA;MACA,UAAAxD,gBAAA,CAAAG,eAAA;QACA,KAAAS,MAAA,CAAAT,eAAA;MACA,gBAAAH,gBAAA,CAAAG,eAAA,UAAAH,gBAAA,CAAAE,QAAA;QACA,KAAAU,MAAA,CAAAT,eAAA;MACA;QACA,KAAAS,MAAA,CAAAT,eAAA;MACA;IACA;IACA,MAAAiE,kBAAA;MACA;MACA,KAAAxD,MAAA,CAAAR,IAAA;;MAEA;MACA,UAAAJ,gBAAA,CAAAI,IAAA;QACA,KAAAQ,MAAA,CAAAR,IAAA;QACA;MACA;MACA,SAAAJ,gBAAA,CAAAI,IAAA,CAAA2C,MAAA,mBAAAF,IAAA,MAAA7C,gBAAA,CAAAI,IAAA;QACA,KAAAQ,MAAA,CAAAR,IAAA;QACA;MACA;IACA;IACA,MAAAiE,oBAAA;MACA;MACA,MAAA1C,cAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,IAAAF,cAAA;QACA,MAAAG,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,cAAA;QACA,IAAAG,QAAA,CAAA7B,KAAA,UAAAD,gBAAA,CAAAC,KAAA;UACA,MAAAgC,WAAA,GAAAC,IAAA,CAAAC,GAAA;UACA,MAAAC,UAAA,GAAAN,QAAA,CAAAO,SAAA;UAEA,IAAAJ,WAAA,GAAAG,UAAA;YACA;YACA,MAAAE,aAAA,GAAAC,IAAA,CAAAC,IAAA,EAAAJ,UAAA,GAAAH,WAAA;YACA,KAAAnB,SAAA,GAAAwB,aAAA;YACA,KAAAzB,QAAA;YACA,KAAA4B,cAAA;YACA;UACA;QACA;MACA;;MAEA;MACA,KAAAE,aAAA;MACA,SAAA/B,MAAA,CAAAX,KAAA;MAEA;QACA;QACA,KAAAQ,aAAA;;QAEA;QACA,MAAA6D,QAAA,SAAAhF,SAAA;UACAW,KAAA,OAAAD,gBAAA,CAAAC;QACA;;QAEA;QACA,IAAAqE,QAAA,CAAAvE,IAAA,CAAAK,IAAA;UACA;UACA,MAAAmE,gBAAA;YACAtE,KAAA,OAAAD,gBAAA,CAAAC,KAAA;YACAoC,SAAA,EAAAH,IAAA,CAAAC,GAAA;UACA;UACAP,YAAA,CAAA4C,OAAA,yBAAAzC,IAAA,CAAA0C,SAAA,CAAAF,gBAAA;UAEA,KAAA1D,QAAA;UACA,KAAAG,YAAA;UACA,KAAAyB,cAAA;QACA;UACA;UACA,KAAA7B,MAAA,CAAAR,IAAA,GAAAkE,QAAA,CAAAvE,IAAA,CAAA2E,GAAA;UACA;QACA;MACA,SAAAC,KAAA;QACA,KAAA/D,MAAA,CAAAR,IAAA;MACA;QACA,KAAAK,aAAA;MACA;IACA;IACAgC,eAAA;MACA;MACA,SAAA1B,KAAA;QACA6D,aAAA,MAAA7D,KAAA;MACA;;MAEA;MACA,KAAAA,KAAA,GAAA8D,WAAA;QACA,SAAA/D,SAAA;UACA8D,aAAA,MAAA7D,KAAA;UACA,KAAAF,QAAA;UACA,KAAAC,SAAA;UACA;UACAc,YAAA,CAAAc,UAAA;QACA;UACA,KAAA5B,SAAA;UACA;UACA,MAAAa,cAAA,GAAAC,YAAA,CAAAC,OAAA;UACA,IAAAF,cAAA;YACA,MAAAG,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,cAAA;YACA;YACA,IAAAG,QAAA,CAAA7B,KAAA,UAAAD,gBAAA,CAAAC,KAAA;cACA;YAAA;UAEA;QACA;MACA;IACA;IACA6E,SAAA;MACA,KAAAnC,aAAA;MACA,KAAAG,gBAAA;MACA,KAAAU,uBAAA;MACA;MACA,KAAA5C,MAAA,CAAAR,IAAA;MACA;MACAd,SAAA;QACAW,KAAA,OAAAD,gBAAA,CAAAC,KAAA;QAAA;QACAG,IAAA,OAAAJ,gBAAA,CAAAI;MACA,GAAA2E,IAAA,CAAAT,QAAA;QACA;QACA,IAAAA,QAAA,CAAAvE,IAAA,CAAAK,IAAA;UACA,KAAAQ,MAAA,CAAAR,IAAA,GAAAkE,QAAA,CAAAvE,IAAA,CAAA2E,GAAA;UACA;QACA;QACA;QACAnF,WAAA,wBAAAS,gBAAA,EAAA+E,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAjF,IAAA,CAAAK,IAAA;YACA;YACAwB,YAAA,CAAAc,UAAA;YACA;YACA,KAAAuB,OAAA,CAAAX,IAAA;UACA,OACA;YACA,KAAA1C,MAAA,CAAAR,IAAA,GAAA4E,GAAA,CAAAjF,IAAA,CAAA2E,GAAA;UACA;QACA;MACA;IACA;IACAO,yBAAA;MACA,KAAAvE,eAAA,SAAAA,eAAA;IACA;IACAwE,gCAAA;MACA,KAAAvE,sBAAA,SAAAA,sBAAA;IACA;IACAwE,UAAA;MACA;MACA;IAAA;EAEA;EACAC,cAAA;IACA,SAAArE,KAAA;MACA6D,aAAA,MAAA7D,KAAA;IACA;IACA,KAAAQ,KAAA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}