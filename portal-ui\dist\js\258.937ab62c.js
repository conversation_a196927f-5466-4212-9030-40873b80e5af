"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[258],{7013:function(t,a,s){s.r(a),s.d(a,{default:function(){return o}});var e=function(){var t=this,a=t._self._c;return a("Layout",[a("div",{staticClass:"layout-container",staticStyle:{width:"100%"}},[a("div",{staticClass:"page-header",staticStyle:{"max-height":"14px"}}),a("div",{staticClass:"breadcrumb-box"},[a("div",{staticClass:"am-container"},[a("ol",{staticClass:"am-breadcrumb"},[a("li",[a("router-link",{attrs:{to:"/news"}},[t._v("公司动态")])],1),a("li",{staticClass:"am-active"},[t._v("文章详情")])])])])]),a("div",{staticClass:"section"},[a("div",{staticClass:"container",staticStyle:{"max-width":"1160px"}},[a("div",{staticClass:"section--header"},[a("h2",{staticClass:"section--title"},[t._v(t._s(t.article.title))]),a("p",{staticClass:"section--description"},[t._v(t._s(t.article.introduction))])]),a("div",{staticClass:"join-container"},[a("div",{staticClass:"am-g"},[a("div",{staticClass:"am-u-md-3"},[a("div",{staticClass:"careers--articles"},[a("div",{staticClass:"careers_articles"},t._l(t.recentArticles,(function(s,e){return a("div",{key:e,staticClass:"careers_article"},[a("div",{staticClass:"image"},[a("img",{staticStyle:{height:"160px"},attrs:{src:s.cover,alt:""}})]),a("h3",{staticClass:"careers_article--title"},[t._v(t._s(s.title))]),a("div",{staticClass:"careers_article--text"},[t._v(t._s(s.introduction))]),a("div",{staticClass:"careers_article--footer"},[a("router-link",{staticClass:"link",attrs:{to:{name:"newsDetails",params:{newsId:s.articleId}}}},[t._v("查看更多")])],1)])})),0)])]),a("div",{staticClass:"am-u-md-9"},[a("div",{staticClass:"careers--vacancies"},[a("div",{staticClass:"am-panel-group",attrs:{id:"accordion"}},[a("div",{staticClass:"am-panel am-panel-default"},[a("div",{staticClass:"am-panel-hd"},[a("h4",{staticClass:"am-panel-title",attrs:{"data-am-collapse":"{parent: '#accordion', target: '#do-not-say-1'}"}},[t._v(" 作者："+t._s(t.article.author)+"      /      发布时间："+t._s(t.article.createTime)+" ")])]),a("div",{staticClass:"am-panel-collapse am-collapse am-in"},[a("div",{staticClass:"am-panel-bd"},[a("div",{staticClass:"vacancies--item_content js-accordion--pane_content",domProps:{innerHTML:t._s(t.article.contentHtml)}})])])])])])])])])])])])},i=[],c=s(9891),l={name:"NewsDetailsView",components:{Layout:c.Z},data(){return{article:{},recentArticles:[]}},mounted(){this.getArticleByArticleId(this.$route.params.newsId)},methods:{getArticleByArticleId(t){this.getRequest(`/findArticleByArticleId/${t}`).then((a=>{a&&(this.article=a.data.data,this.getRecentArticle(t))}))},getRecentArticle(t){this.getRequest(`/findRecentArticle/${t}`).then((t=>{t&&(this.recentArticles=t.data.data)}))}}},r=l,n=s(1001),d=(0,n.Z)(r,e,i,!1,null,"e807f88e",null),o=d.exports}}]);
//# sourceMappingURL=258.937ab62c.js.map