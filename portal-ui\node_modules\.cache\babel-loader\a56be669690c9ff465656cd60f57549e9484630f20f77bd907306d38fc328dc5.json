{"ast": null, "code": "export default {\n  name: 'CommonPagination',\n  props: {\n    currentPage: {\n      type: Number,\n      required: true\n    },\n    total: {\n      type: Number,\n      required: true\n    },\n    pageSize: {\n      type: Number,\n      required: true\n    }\n  },\n  data() {\n    return {\n      inputPage: this.currentPage\n    };\n  },\n  watch: {\n    currentPage(newVal) {\n      this.inputPage = newVal;\n    }\n  },\n  computed: {\n    totalPages() {\n      return Math.ceil(this.total / this.pageSize);\n    }\n  },\n  methods: {\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.$emit('change-page', page);\n      }\n    },\n    handleJump() {\n      // 验证输入是否合法\n      if (this.inputPage && this.inputPage >= 1 && this.inputPage <= this.totalPages) {\n        if (this.inputPage !== this.currentPage) {\n          this.changePage(this.inputPage);\n        }\n      } else {\n        // 不合法则重置为当前页\n        this.inputPage = this.currentPage;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "currentPage", "type", "Number", "required", "total", "pageSize", "data", "inputPage", "watch", "newVal", "computed", "totalPages", "Math", "ceil", "methods", "changePage", "page", "$emit", "handleJump"], "sources": ["src/views/Ordermange/CommonPagination.vue"], "sourcesContent": ["<template>\r\n  <div class=\"custom-pagination\">\r\n    <span class=\"pagination-total\">共 {{ total }} 条</span>\r\n\r\n    <button\r\n        class=\"pagination-prev\"\r\n        @click=\"changePage(currentPage - 1)\"\r\n        :disabled=\"currentPage === 1\"\r\n    >\r\n      &lt;\r\n    </button>\r\n\r\n    <span class=\"pagination-current\">{{ currentPage }}</span>\r\n\r\n    <button\r\n        class=\"pagination-next\"\r\n        @click=\"changePage(currentPage + 1)\"\r\n        :disabled=\"currentPage === totalPages\"\r\n    >\r\n      &gt;\r\n    </button>\r\n\r\n    <span class=\"pagination-size\">{{ pageSize }}条 / 页</span>\r\n\r\n    <div class=\"pagination-jump\">\r\n      <span>前往</span>\r\n      <input\r\n          type=\"text\"\r\n          v-model.number=\"inputPage\"\r\n          min=\"1\"\r\n          :max=\"totalPages\"\r\n          @blur=\"handleJump\"\r\n          @keyup.enter=\"handleJump\"\r\n      >\r\n      <span>页</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CommonPagination',\r\n  props: {\r\n    currentPage: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    total: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    pageSize: {\r\n      type: Number,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      inputPage: this.currentPage\r\n    }\r\n  },\r\n  watch: {\r\n    currentPage(newVal) {\r\n      this.inputPage = newVal;\r\n    }\r\n  },\r\n  computed: {\r\n    totalPages() {\r\n      return Math.ceil(this.total / this.pageSize);\r\n    }\r\n  },\r\n  methods: {\r\n    changePage(page) {\r\n      if (page >= 1 && page <= this.totalPages) {\r\n        this.$emit('change-page', page);\r\n      }\r\n    },\r\n    handleJump() {\r\n      // 验证输入是否合法\r\n      if (this.inputPage && this.inputPage >= 1 && this.inputPage <= this.totalPages) {\r\n        if (this.inputPage !== this.currentPage) {\r\n          this.changePage(this.inputPage);\r\n        }\r\n      } else {\r\n        // 不合法则重置为当前页\r\n        this.inputPage = this.currentPage;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.custom-pagination {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 15px;\r\n  color: #888;\r\n  margin-top: 15px;\r\n  gap: 8px;\r\n}\r\n\r\n.pagination-total,\r\n.pagination-size,\r\n.pagination-current {\r\n  font-size: 15px;\r\n  color: #999;\r\n}\r\n\r\n.pagination-prev,\r\n.pagination-next {\r\n  background: #f5f5f5;\r\n  border: 1px solid #ddd;\r\n  border-radius: 2px;\r\n  padding: 2px 8px;\r\n  font-size: 15px;\r\n  color: #666;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.pagination-prev:hover,\r\n.pagination-next:hover {\r\n  background: #e8e8e8;\r\n  border-color: #ccc;\r\n}\r\n\r\n.pagination-prev:disabled,\r\n.pagination-next:disabled {\r\n  color: #ccc;\r\n  cursor: not-allowed;\r\n  background: #f9f9f9;\r\n}\r\n\r\n.pagination-current {\r\n  font-weight: normal;\r\n  padding: 0 5px;\r\n}\r\n\r\n.pagination-jump {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.pagination-jump input {\r\n  width: 40px;\r\n  height: 22px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 2px;\r\n  text-align: center;\r\n  font-size: 15px;\r\n  color: #666;\r\n  padding: 0 5px;\r\n}\r\n\r\n.pagination-jump input:focus {\r\n  outline: none;\r\n  border-color: #a0cfff;\r\n}\r\n</style>"], "mappings": "AAwCA;EACAA,IAAA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAE,QAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAG,KAAA;IACA;MACAC,SAAA,OAAAP;IACA;EACA;EACAQ,KAAA;IACAR,YAAAS,MAAA;MACA,KAAAF,SAAA,GAAAE,MAAA;IACA;EACA;EACAC,QAAA;IACAC,WAAA;MACA,OAAAC,IAAA,CAAAC,IAAA,MAAAT,KAAA,QAAAC,QAAA;IACA;EACA;EACAS,OAAA;IACAC,WAAAC,IAAA;MACA,IAAAA,IAAA,SAAAA,IAAA,SAAAL,UAAA;QACA,KAAAM,KAAA,gBAAAD,IAAA;MACA;IACA;IACAE,WAAA;MACA;MACA,SAAAX,SAAA,SAAAA,SAAA,cAAAA,SAAA,SAAAI,UAAA;QACA,SAAAJ,SAAA,UAAAP,WAAA;UACA,KAAAe,UAAA,MAAAR,SAAA;QACA;MACA;QACA;QACA,KAAAA,SAAA,QAAAP,WAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}