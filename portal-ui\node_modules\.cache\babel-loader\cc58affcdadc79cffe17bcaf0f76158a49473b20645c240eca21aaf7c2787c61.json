{"ast": null, "code": "import Layout from \"@/components/common/Layout\";\nexport default {\n  name: \"NewsView\",\n  components: {\n    Layout\n  },\n  data() {\n    return {\n      articles: {},\n      pageIndex: 1\n    };\n  },\n  mounted() {\n    this.getArticle(1);\n  },\n  methods: {\n    getArticle(pageIndex) {\n      this.getRequest(`/findArticles/${pageIndex}`).then(resp => {\n        if (resp) {\n          this.articles = resp.data.data;\n          console.log(this.articles);\n        }\n      });\n    },\n    changeIndex(p) {\n      if (p === 0) {\n        this.pageIndex = 1;\n      } else if (p === this.articles.pages + 1) {\n        this.pageIndex = this.articles.pages;\n      } else {\n        this.pageIndex = p;\n        this.getArticle(p);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "name", "components", "data", "articles", "pageIndex", "mounted", "getArticle", "methods", "getRequest", "then", "resp", "console", "log", "changeIndex", "p", "pages"], "sources": ["src/views/NewsView.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t\t<div class=\"layout-container\" style=\"width: 100%\">\r\n\t\t\t<div class=\"page-header\">\r\n\t\t\t\t<div class=\"am-container\">\r\n\t\t\t\t\t<h1 class=\"page-header-title\">公司动态</h1>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t<div class=\"breadcrumb-box\">\r\n\t\t\t\t<div class=\"am-container\">\r\n\t\t\t\t\t<ol class=\"am-breadcrumb\">\r\n\t\t\t\t\t\t<li><router-link to=\"/\">首页</router-link></li>\r\n\t\t\t\t\t\t<li class=\"am-active\">公司动态</li>\r\n\t\t\t\t\t</ol>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<div class=\"section\">\r\n\t\t\t<div class=\"container\" style=\"max-width: 1160px\">\r\n\t\t\t\t<div class=\"section--header\">\r\n\t\t\t\t\t<h2 class=\"section--title\">最近新闻</h2>\r\n\t\t\t\t\t<p class=\"section--description\">\r\n\t\t\t\t\t\t云适配与中建材信息技术股份有限公司（以下简称“中建信息”）联合举办的“战略\r\n\t\t\t\t\t\t<br>合作签约仪式暨全国跨屏行动启动大会”在北京成功举办。\r\n\t\t\t\t\t</p>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div class=\"news-contaier\">\r\n\t\t\t\t\t<div class=\"blog\">\r\n\t\t\t\t\t\t<div class=\"am-g\">\r\n\t\t\t\t\t\t\t<div class=\"am-u-lg-4 am-u-md-6 am-u-end\" v-for=\"(article,index) in articles.records\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<div class=\"article\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"article-img\">\r\n\t\t\t\t\t\t\t\t\t\t<img :src=\"article.cover\" alt=\"\" />\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"article-header\">\r\n\t\t\t\t\t\t\t\t\t\t<h2><router-link :to=\"{name:'newsDetails',params:{newsId:article.articleId}}\" rel=\"\">{{article.title}}</router-link></h2>\r\n\t\t\t\t\t\t\t\t\t\t<ul class=\"article--meta\">\r\n\t\t\t\t\t\t\t\t\t\t\t<li class=\"article--meta_item -date\">{{article.createTime}}</li>\r\n\t\t\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"article--content\">\r\n\t\t\t\t\t\t\t\t\t\t<p>{{article.introduction}}</p>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"article--footer\">\r\n\t\t\t\t\t\t\t\t\t\t<router-link :to=\"{name:'newsDetails',params:{newsId:article.articleId}}\" class=\"link\">查看更多</router-link>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<ul class=\"am-pagination\" style=\"text-align: center;\">\r\n\t\t\t\t\t\t\t<li :class=\"pageIndex === 1 ? 'am-disabled':''\"\r\n\t\t\t\t\t\t\t\t@click=\"changeIndex(pageIndex - 1)\">\r\n\t\t\t\t\t\t\t\t<a href=\"#\">&laquo;</a>\r\n\t\t\t\t\t\t\t</li>\r\n\r\n\t\t\t\t\t\t\t<li v-for=\"(p,index) in articles.pages\"\r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t\t@click=\"changeIndex(p)\"\r\n\t\t\t\t\t\t\t\t:class=\"pageIndex === p ? 'am-active':''\">\r\n\t\t\t\t\t\t\t\t<a href=\"#\">{{p}}</a>\r\n\t\t\t\t\t\t\t</li>\r\n\r\n\t\t\t\t\t\t\t<li :class=\"pageIndex === articles.pages ? 'am-disabled':''\"\r\n\t\t\t\t\t\t\t\t@click=\"changeIndex(pageIndex + 1)\">\r\n\t\t\t\t\t\t\t\t<a href=\"#\">&raquo;</a>\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nexport default {\r\n\tname: \"NewsView\",\r\n\tcomponents: {Layout},\r\n\tdata(){\r\n\t\treturn{\r\n\t\t\tarticles: {},\r\n\t\t\tpageIndex: 1,\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.getArticle(1)\r\n\t},\r\n\tmethods:{\r\n\t\tgetArticle(pageIndex){\r\n\t\t\tthis.getRequest(`/findArticles/${pageIndex}`).then(resp =>{\r\n\t\t\t\tif (resp){\r\n\t\t\t\t\tthis.articles = resp.data.data\r\n\t\t\t\t\tconsole.log(this.articles)\r\n\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tchangeIndex(p){\r\n\t\t\tif (p === 0){\r\n\t\t\t\tthis.pageIndex = 1\r\n\t\t\t}else if (p === this.articles.pages + 1){\r\n\t\t\t\tthis.pageIndex = this.articles.pages\r\n\t\t\t}else{\r\n\t\t\t\tthis.pageIndex = p;\r\n\t\t\t\tthis.getArticle(p)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": "AAiFA,OAAAA,MAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACAD,WAAAF,SAAA;MACA,KAAAI,UAAA,kBAAAJ,SAAA,IAAAK,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACA,KAAAP,QAAA,GAAAO,IAAA,CAAAR,IAAA,CAAAA,IAAA;UACAS,OAAA,CAAAC,GAAA,MAAAT,QAAA;QAEA;MACA;IACA;IACAU,YAAAC,CAAA;MACA,IAAAA,CAAA;QACA,KAAAV,SAAA;MACA,WAAAU,CAAA,UAAAX,QAAA,CAAAY,KAAA;QACA,KAAAX,SAAA,QAAAD,QAAA,CAAAY,KAAA;MACA;QACA,KAAAX,SAAA,GAAAU,CAAA;QACA,KAAAR,UAAA,CAAAQ,CAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}