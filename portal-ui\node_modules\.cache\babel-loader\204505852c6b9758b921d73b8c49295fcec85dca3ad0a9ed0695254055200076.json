{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { postAnyData, getAnyData, postLogin, postJsonData } from \"@/api/login\";\nimport { removeToken, getToken } from \"@/utils/auth\";\nimport SlideNotification from './SlideNotification.vue';\nimport Cookies from 'js-cookie';\nexport default {\n  name: \"Header\",\n  components: {\n    SlideNotification\n  },\n  data() {\n    return {\n      userBalance: null,\n      showComingSoon: false,\n      notificationMessage: \"\",\n      isLoggedIn: false,\n      userName: \"null\",\n      userPhone: \"\",\n      notificationCount: 2,\n      currentPath: '',\n      navHeight: 60,\n      scrollThrottleTimer: null,\n      previousActivePath: null,\n      isMobile: false,\n      mobileMenuOpen: false,\n      showUserMenu: false,\n      windowWidth: 0,\n      isReal: 0,\n      cookieWatcher: null,\n      isConsoleLoading: false,\n      isConsoleReady: false,\n      hasTriggeredRefresh: false\n    };\n  },\n  computed: {\n    userInitial() {\n      return this.userName && this.userName.length > 0 ? this.userName.charAt(0).toUpperCase() : 'N';\n    }\n  },\n  watch: {\n    '$route'(to) {\n      this.currentPath = to.path;\n      if (to.path === '/product') {\n        this.currentPath = '/product';\n      } else if (to.path === '/about') {\n        this.currentPath = '/about';\n      }\n    }\n  },\n  methods: {\n    isActive(route) {\n      return this.currentPath === route || route === '/' && this.currentPath === '/index' || route === '/product' && this.currentPath.startsWith('/product') || route === '/about' && this.currentPath === '/about' || route === '/help' && this.currentPath.startsWith('/help');\n    },\n    checkScreenSize() {\n      this.windowWidth = window.innerWidth;\n      this.isMobile = this.windowWidth <= 992;\n      if (!this.isMobile) {\n        this.mobileMenuOpen = false;\n        this.showUserMenu = false;\n      }\n    },\n    gotoPersonal() {\n      this.closeAllMenus();\n      this.$router.push({\n        path: '/personal',\n        query: {\n          activeTab: 'verification'\n        }\n      });\n    },\n    navigateToRecharge() {\n      this.closeAllMenus();\n      this.$router.push({\n        path: '/userorder',\n        query: {\n          activeTab: 'recharge'\n        }\n      });\n    },\n    handleConsoleNavigation() {\n      this.closeAllMenus();\n      if (this.isConsoleLoading) return;\n      if (!this.isLoggedIn) {\n        this.navigateTo('/login');\n        return;\n      }\n      if (this.isReal !== 1) {\n        // 未实名认证，显示弹窗\n        this.notificationMessage = \"请先完成实名认证，正在为您跳转到对应页面\";\n        this.showComingSoon = true;\n\n        // 2秒后跳转到实名认证页面\n        setTimeout(() => {\n          this.navigateTo('/personal', {\n            activeTab: 'verification'\n          });\n        }, 2000);\n        return;\n      }\n      if (!this.isConsoleReady) {\n        this.notificationMessage = '控制台初始化中，请稍候...';\n        this.showComingSoon = true;\n        this.isConsoleLoading = true;\n        if (!this.hasTriggeredRefresh) {\n          this.hasTriggeredRefresh = true;\n          setTimeout(() => {\n            this.getUserInfo();\n            this.startCookieWatcher();\n          }, 2000); // 2 秒后自动刷新\n        }\n\n        return;\n      }\n      this.isConsoleLoading = true;\n      this.navigateTo('/console');\n      this.isConsoleReady = true;\n      this.isConsoleLoading = false;\n    },\n    handleScroll() {\n      if (this.scrollThrottleTimer) return;\n      this.scrollThrottleTimer = setTimeout(() => {\n        this.scrollThrottleTimer = null;\n      }, 50);\n    },\n    logout() {\n      this.closeAllMenus();\n      postAnyData(\"/logout/cilent/logout\").then(res => {\n        if (res.data.code === 200) {\n          Object.keys(Cookies.get()).forEach(cookieName => {\n            Cookies.remove(cookieName);\n          });\n          removeToken();\n          this.isLoggedIn = false;\n          this.$router.push('/login');\n        }\n      }).catch(err => {});\n    },\n    triggerComingSoon() {\n      if (this.showComingSoon) return;\n      this.notificationMessage = \"我们正在努力建设中，敬请期待更多精彩内容！\";\n      this.showComingSoon = true;\n      this.closeAllMenus();\n    },\n    navigateTo(path, query = {}) {\n      if (this.currentPath && this.currentPath !== path) {\n        this.previousActivePath = this.currentPath;\n        this.$nextTick(() => {\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login, .mobile-nav-link');\n          navLinks.forEach(link => {\n            if ((link.classList.contains('active') || path === '/login' && link.classList.contains('btn-login')) && !link.classList.contains('active-exit')) {\n              link.classList.add('active-exit');\n              setTimeout(() => {\n                link.classList.remove('active-exit');\n              }, 300);\n            }\n          });\n          this.currentPath = path;\n        });\n      } else {\n        this.currentPath = path;\n      }\n      this.closeAllMenus();\n      if (this.$route.path === path && JSON.stringify(this.$route.query) === JSON.stringify(query)) {\n        return;\n      }\n      this.$router.push({\n        path: path,\n        query: query\n      });\n      window.scrollTo({\n        top: 0,\n        behavior: 'instant'\n      });\n    },\n    measureNavHeight() {\n      if (this.$refs.mainNav) {\n        const rect = this.$refs.mainNav.getBoundingClientRect();\n        if (rect.height > 0) {\n          this.navHeight = rect.height;\n        }\n      }\n    },\n    // 处理 token 过期\n    handleTokenExpired() {\n      // 清除登录状态\n      this.isLoggedIn = false;\n      removeToken();\n      Object.keys(Cookies.get()).forEach(cookieName => {\n        Cookies.remove(cookieName);\n      });\n\n      // 显示通知\n      this.notificationMessage = \"由于长时间未操作，登录状态已失效，请重新登录\";\n      this.showComingSoon = true;\n\n      // 2秒后自动跳转到登录页面\n      setTimeout(() => {\n        this.$router.push('/login');\n      }, 2000);\n    },\n    // 处理页面可见性变化\n    handleVisibilityChange() {\n      if (document.visibilityState === 'visible' && this.isLoggedIn) {\n        // 页面从隐藏变为可见时，检查 token 是否仍然有效\n        this.getUserInfo();\n      }\n    },\n    getUserInfo() {\n      // removeToken()\n      postAnyData(\"/logout/cilent/getInfo\").then(res => {\n        if (res.data.code === 200) {\n          this.userName = res.data.data.nickName || \"NCloud-user\";\n          Cookies.set('userName', res.data.data.nickName);\n          this.userPhone = res.data.data.username;\n          Cookies.set('userPhone', res.data.data.username);\n          this.userEmail = res.data.data.email || \"<EMAIL>\";\n          Cookies.set('userEmail', res.data.data.email);\n          this.tenantId = res.data.data.tenantId || \"te-default\";\n          Cookies.set('tenantId', res.data.data.tenantId);\n          this.userId = res.data.data.id || \"ac-default\";\n          Cookies.set('userId', res.data.data.id);\n          this.userBalance = res.data.data.balance;\n          Cookies.set('userBalance', res.data.data.balance);\n          this.isReal = res.data.data.isReal;\n          Cookies.set('isReal', this.isReal);\n          this.isLoggedIn = true;\n          if (Cookies.get('publicKey-C')) {\n            // console.log('rsa_pubk',publicKey-C);\n            return;\n          }\n          postJsonData(\"/suanleme/login\", {\n            correlationId: Cookies.get('userId'),\n            rsaPubk: Cookies.get('publicKey-B')\n          }).then(res => {\n            // console.log('rsa_pubk',res.data.data.rsa_pubk);\n            Cookies.set('publicKey-C', res.data.data.rsa_pubk);\n            Cookies.set('suanlemeToken', res.data.data.token);\n          });\n        } else {\n          this.handleTokenExpired();\n        }\n      });\n    },\n    toggleMobileMenu() {\n      this.mobileMenuOpen = !this.mobileMenuOpen;\n      if (this.mobileMenuOpen) {\n        this.showUserMenu = false;\n      }\n    },\n    toggleUserMenu() {\n      this.showUserMenu = !this.showUserMenu;\n      if (this.showUserMenu) {\n        this.mobileMenuOpen = false;\n      }\n    },\n    closeAllMenus() {\n      this.mobileMenuOpen = false;\n      this.showUserMenu = false;\n    },\n    startCookieWatcher() {\n      this.cookieWatcher = setInterval(() => {\n        const token = this.getCookie('suanlemeToken');\n        const pubKey = this.getCookie('publicKey-C');\n        if (token && pubKey) {\n          clearInterval(this.cookieWatcher);\n          this.cookieWatcher = null;\n          this.isConsoleReady = true;\n          this.isConsoleLoading = false;\n        }\n      }, 1000); // 每秒检查一次\n    },\n\n    getCookie(name) {\n      const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));\n      return match ? match[2] : null;\n    }\n  },\n  created() {\n    window.addEventListener('scroll', this.handleScroll, {\n      passive: true\n    });\n    window.addEventListener('resize', this.checkScreenSize);\n\n    // 优先从本地存储读取登录状态\n    const token = getToken();\n    this.isLoggedIn = !!token;\n\n    // 如果存在token，立即从cookie中读取用户信息\n    if (token) {\n      this.userName = Cookies.get('userName') || \"null\";\n      this.userPhone = Cookies.get('userPhone') || \"\";\n      this.userBalance = parseFloat(Cookies.get('userBalance')) || 0;\n      this.isReal = parseInt(Cookies.get('isReal')) || 0;\n\n      // 然后异步验证token有效性\n      this.getUserInfo();\n    }\n\n    // 添加页面可见性变化监听\n    document.addEventListener('visibilitychange', this.handleVisibilityChange);\n    this.currentPath = this.$route.path;\n    this.checkScreenSize();\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.measureNavHeight();\n      let resizeTimer;\n      window.addEventListener('resize', () => {\n        clearTimeout(resizeTimer);\n        resizeTimer = setTimeout(() => {\n          this.measureNavHeight();\n        }, 250);\n      }, {\n        passive: true\n      });\n      this.startCookieWatcher();\n    });\n  },\n  beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll);\n    window.removeEventListener('resize', this.measureNavHeight);\n    window.removeEventListener('resize', this.checkScreenSize);\n    document.removeEventListener('visibilitychange', this.handleVisibilityChange);\n    clearTimeout(this.scrollThrottleTimer);\n  }\n};", "map": {"version": 3, "names": ["postAnyData", "getAnyData", "postLogin", "postJsonData", "removeToken", "getToken", "SlideNotification", "Cookies", "name", "components", "data", "userBalance", "showComingSoon", "notificationMessage", "isLoggedIn", "userName", "userPhone", "notificationCount", "currentPath", "navHeight", "scrollThrottleTimer", "previousActivePath", "isMobile", "mobileMenuOpen", "showUserMenu", "windowWidth", "isReal", "cookieWatcher", "isConsoleLoading", "isConsoleReady", "hasTriggeredRefresh", "computed", "userInitial", "length", "char<PERSON>t", "toUpperCase", "watch", "$route", "to", "path", "methods", "isActive", "route", "startsWith", "checkScreenSize", "window", "innerWidth", "gotoPersonal", "closeAllMenus", "$router", "push", "query", "activeTab", "navigateToRecharge", "handleConsoleNavigation", "navigateTo", "setTimeout", "getUserInfo", "startCookieWatcher", "handleScroll", "logout", "then", "res", "code", "Object", "keys", "get", "for<PERSON>ach", "cookieName", "remove", "catch", "err", "triggerComingSoon", "$nextTick", "navLinks", "document", "querySelectorAll", "link", "classList", "contains", "add", "JSON", "stringify", "scrollTo", "top", "behavior", "measureNavHeight", "$refs", "mainNav", "rect", "getBoundingClientRect", "height", "handleTokenExpired", "handleVisibilityChange", "visibilityState", "nick<PERSON><PERSON>", "set", "username", "userEmail", "email", "tenantId", "userId", "id", "balance", "correlationId", "rsaPubk", "rsa_pubk", "token", "toggleMobileMenu", "toggleUserMenu", "setInterval", "<PERSON><PERSON><PERSON><PERSON>", "pubKey", "clearInterval", "match", "cookie", "RegExp", "created", "addEventListener", "passive", "parseFloat", "parseInt", "mounted", "resizeTimer", "clearTimeout", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener"], "sources": ["src/components/common/header/Header.vue"], "sourcesContent": ["<template>\r\n  <div class=\"header-wrapper\">\r\n    <SlideNotification v-if=\"showComingSoon\"\r\n                       :message=\"notificationMessage\"\r\n                       type=\"warning\"\r\n                       :duration=\"2000\"\r\n                       @close=\"showComingSoon = false\" />\r\n\r\n    <!-- 导航占位符 -->\r\n    <div class=\"nav-placeholder\" :style=\"{ height: navHeight + 'px' }\"></div>\r\n\r\n    <!-- 主导航栏 -->\r\n    <div class=\"main-nav\" ref=\"mainNav\">\r\n      <div class=\"container\">\r\n        <!-- 电脑端导航 -->\r\n        <div class=\"nav-container desktop-nav\" v-if=\"!isMobile\">\r\n          <!-- Logo区域 -->\r\n          <div class=\"logo-area\">\r\n            <a @click=\"navigateTo('/')\" class=\"logo-link\">\r\n              <img src=\"images/logo-tiangong.png\" alt=\"算力租赁\" loading=\"eager\">\r\n            </a>\r\n          </div>\r\n\r\n          <!-- 导航菜单 -->\r\n          <div class=\"nav-menu\">\r\n            <ul class=\"nav-list\">\r\n              <li class=\"nav-item\">\r\n                <a @click=\"navigateTo('/')\" class=\"nav-link\" :class=\"{'active': isActive('/')}\">首页</a>\r\n              </li>\r\n              <li class=\"nav-item dropdown\">\r\n                <a @click=\"navigateTo('/product')\" class=\"nav-link\" :class=\"{'active': isActive('/product')}\">\r\n                  算力市场<i class=\"iconfont icon-arrow-down\"></i>\r\n                </a>\r\n              </li>\r\n              <li class=\"nav-item dropdown\">\r\n                <a @click=\"triggerComingSoon\" class=\"nav-link\" :class=\"{'active': false}\">算法社区<i class=\"iconfont icon-arrow-down\"></i></a>\r\n              </li>\r\n\r\n              <!-- 私有云 -->\r\n              <li class=\"nav-item\">\r\n                <a @click=\"triggerComingSoon\" class=\"nav-link\" :class=\"{'active': false}\">私有云</a>\r\n              </li>\r\n\r\n              <!-- 关于我们 -->\r\n              <li class=\"nav-item\">\r\n                <a @click=\"triggerComingSoon\" class=\"nav-link\" :class=\"{'active': false}\">关于我们</a>\r\n              </li>\r\n              <!-- 帮助文档 -->\r\n              <!-- <li class=\"nav-item\">\r\n                <a @click=\"navigateTo('/help')\" class=\"nav-link\" :class=\"{'active': isActive('/help')}\">帮助文档</a>\r\n              </li> -->\r\n            </ul>\r\n          </div>\r\n\r\n          <!-- 用户操作区 -->\r\n          <div class=\"user-actions\">\r\n            <div class=\"auth-buttons\" v-if=\"!isLoggedIn\">\r\n              <a @click=\"navigateTo('/help')\" class=\"btn btn-login\" :class=\"{'active': isActive('/help')}\">帮助文档</a>\r\n              <a @click=\"navigateTo('/login')\" class=\"btn btn-login\" :class=\"{'active': isActive('/login')}\">控制台</a>\r\n              <a @click=\"navigateTo('/login')\" class=\"btn btn-login\" :class=\"{'active': isActive('/login')}\">登录</a>\r\n              <a @click=\"navigateTo('/register')\" class=\"btn btn-register\" :class=\"{'active': isActive('/register')}\">立即注册</a>\r\n            </div>\r\n            <div class=\"user-profile\" v-if=\"isLoggedIn\">\r\n              <a @click=\"navigateTo('/help')\" class=\"btn btn-login\" :class=\"{'active': isActive('/help')}\">帮助文档</a>\r\n              <a @click=\"handleConsoleNavigation\" class=\"btn btn-login\" :class=\"{'active': isActive('/console'),'disabled': isConsoleLoading }\" :title=\"isConsoleLoading ? '控制台加载中，请稍后...' : ''\">控制台</a>\r\n              <div class=\"user-dropdown\">\r\n                <div class=\"user-avatar\">\r\n                  <div class=\"avatar-letter\">{{ userInitial }}</div>\r\n                </div>\r\n                <div class=\"dropdown-menu\">\r\n                  <!-- 用户信息区域 -->\r\n                  <div class=\"user-info-section\">\r\n                    <div class=\"detail-item\">\r\n                      <span class=\"label\">用户名:</span>\r\n                      <span class=\"value\">{{ userName }}</span>\r\n                    </div>\r\n                    <div class=\"detail-item\">\r\n                      <span class=\"label\">手机号:</span>\r\n                      <span class=\"value\"><i class=\"copy-icon\"></i> {{ userPhone }}</span>\r\n                    </div>\r\n                    <div class=\"verification-tag\" @click=\"gotoPersonal\">\r\n                      <span class=\"check-icon\"></span> 个人认证\r\n                      <span class=\"status-text\">({{isReal === 1 ? '已认证' : '未认证'}})</span>\r\n                    </div>\r\n                    <div class=\"detail-item\">\r\n                      <span class=\"label\">可用余额:</span>\r\n                      <span class=\"value\">￥{{ userBalance.toFixed(2) }}</span>\r\n                      <div class=\"verification-tag recharge-btn\" @click=\"navigateToRecharge\">\r\n                        充值\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"menu-options\">\r\n                    <a @click=\"navigateTo('/personal')\" :class=\"{'active': isActive('/personal')}\">个人中心</a>\r\n                    <a @click=\"navigateTo('/userorder')\" :class=\"{'active': isActive('/userorder')}\">费用中心</a>\r\n                  </div>\r\n\r\n                  <!-- 退出登录按钮 -->\r\n                  <div class=\"logout-button-container\">\r\n                    <button class=\"logout-button\" @click=\"logout\">退出登录</button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 移动端导航 -->\r\n        <div class=\"mobile-nav\" v-else>\r\n          <div class=\"mobile-nav-container\">\r\n            <!-- 左侧折叠栏按钮 -->\r\n            <button class=\"hamburger-btn\" @click=\"toggleMobileMenu\">\r\n              <span class=\"hamburger-line\" :class=\"{'line-1': mobileMenuOpen}\"></span>\r\n              <span class=\"hamburger-line\" :class=\"{'line-2': mobileMenuOpen}\"></span>\r\n              <span class=\"hamburger-line\" :class=\"{'line-3': mobileMenuOpen}\"></span>\r\n            </button>\r\n\r\n            <!-- 中间Logo -->\r\n            <div class=\"mobile-logo-area\">\r\n              <a @click=\"navigateTo('/')\" class=\"logo-link\">\r\n                <img src=\"images/logo_tiangong.png\" alt=\"算力租赁\">\r\n              </a>\r\n            </div>\r\n\r\n            <!-- 右侧用户操作区 -->\r\n            <div class=\"mobile-user-actions\">\r\n              <template v-if=\"!isLoggedIn\">\r\n                <!-- <a @click=\"navigateTo('/help')\" class=\"mobile-console-btn\">\r\n                  帮助文档\r\n                </a> -->\r\n                <a @click=\"navigateTo('/login')\" class=\"mobile-login-btn\">\r\n                  登录\r\n                </a>\r\n                <a @click=\"navigateTo('/register')\" class=\"mobile-register-btn\">\r\n                  注册\r\n                </a>\r\n              </template>\r\n              <template v-else>\r\n                <a @click=\"navigateTo('/help')\" class=\"mobile-console-btn\">\r\n                  帮助文档\r\n                </a>\r\n                <a @click=\"handleConsoleNavigation\" class=\"mobile-console-btn\">\r\n                  控制台\r\n                </a>\r\n                <div class=\"mobile-user-profile\" @click=\"toggleUserMenu\">\r\n                  <div class=\"mobile-user-avatar\">\r\n                    <div class=\"avatar-letter\">{{ userInitial }}</div>\r\n                  </div>\r\n                </div>\r\n              </template>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 移动端菜单 -->\r\n          <div class=\"mobile-menu\" :class=\"{'open': mobileMenuOpen}\">\r\n            <div class=\"mobile-menu-content\">\r\n              <!-- 导航链接 -->\r\n              <ul class=\"mobile-nav-list\">\r\n                <li class=\"mobile-nav-item\">\r\n                  <a @click=\"navigateTo('/')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/')}\">\r\n                    <i class=\"iconfont icon-home\"></i>首页\r\n                  </a>\r\n                </li>\r\n                <li class=\"mobile-nav-item\">\r\n                  <a @click=\"navigateTo('/product')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/product')}\">\r\n                    <i class=\"iconfont icon-server\"></i>算力市场\r\n                  </a>\r\n                </li>\r\n                <li class=\"mobile-nav-item\">\r\n                  <a @click=\"triggerComingSoon\" class=\"mobile-nav-link\">\r\n                    <i class=\"iconfont icon-community\"></i>算法社区\r\n                  </a>\r\n                </li>\r\n                <li class=\"mobile-nav-item\">\r\n                  <a @click=\"triggerComingSoon\" class=\"mobile-nav-link\">\r\n                    <i class=\"iconfont icon-cloud\"></i>私有云\r\n                  </a>\r\n                </li>\r\n                <li class=\"mobile-nav-item\">\r\n                  <a @click=\"navigateTo('/about')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/about')}\">\r\n                    <i class=\"iconfont icon-info\"></i>关于我们\r\n                  </a>\r\n                </li>\r\n                <!-- <li class=\"mobile-nav-item\">\r\n                  <a @click=\"navigateTo('/help')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/help')}\">\r\n                    <i class=\"iconfont icon-docs\"></i>帮助文档\r\n                  </a>\r\n                </li> -->\r\n\r\n                <!-- 登录/注册/控制台入口 -->\r\n                <template v-if=\"!isLoggedIn\">\r\n                  <li class=\"mobile-nav-item\">\r\n                  <a @click=\"navigateTo('/help')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/help')}\">\r\n                    <i class=\"iconfont icon-docs\"></i>帮助文档\r\n                  </a>\r\n                </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"navigateTo('/login')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/login')}\">\r\n                      <i class=\"iconfont icon-user\"></i>登录\r\n                    </a>\r\n                  </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"navigateTo('/register')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/register')}\">\r\n                      <i class=\"iconfont icon-edit\"></i>注册\r\n                    </a>\r\n                  </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"navigateTo('/login')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/console')}\">\r\n                      <i class=\"iconfont icon-console\"></i>控制台\r\n                    </a>\r\n                  </li>\r\n\r\n                </template>\r\n                <template v-else>\r\n                <li class=\"mobile-nav-item\"> \r\n                  <a @click=\"navigateTo('/help')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/help')}\">\r\n                    <i class=\"iconfont icon-docs\"></i>帮助文档\r\n                  </a>\r\n                </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"handleConsoleNavigation\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/console')}\">\r\n                      <i class=\"iconfont icon-console\"></i>控制台\r\n                    </a>\r\n                  </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"navigateTo('/personal')\" class=\"mobile-nav-link\" :class=\"{'active': isActive('/personal')}\">\r\n                      <i class=\"iconfont icon-profile\"></i>个人中心\r\n                    </a>\r\n                  </li>\r\n                  <li class=\"mobile-nav-item\">\r\n                    <a @click=\"logout\" class=\"mobile-nav-link\">\r\n                      <i class=\"iconfont icon-logout\"></i>退出登录\r\n                    </a>\r\n                  </li>\r\n                </template>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 用户菜单 -->\r\n          <div class=\"mobile-user-menu\" v-if=\"showUserMenu\">\r\n            <div class=\"mobile-user-info\">\r\n              <div class=\"mobile-username\">{{ userName }}</div>\r\n              <div class=\"mobile-user-phone\">{{ userPhone }}</div>\r\n            </div>\r\n            <div class=\"mobile-menu-options\">\r\n              <a @click=\"navigateTo('/personal')\" class=\"mobile-menu-item\" :class=\"{'active': isActive('/personal')}\">\r\n                <i class=\"iconfont icon-profile\"></i>个人中心\r\n              </a>\r\n              <a @click=\"navigateTo('/userorder')\" class=\"mobile-menu-item\" :class=\"{'active': isActive('/userorder')}\">\r\n                <i class=\"iconfont icon-order\"></i>费用中心\r\n              </a>\r\n              <a @click=\"navigateToRecharge\" class=\"mobile-menu-item\" :class=\"{'active': false}\">\r\n                <i class=\"iconfont icon-recharge\"></i>充值\r\n              </a>\r\n              <a @click=\"logout\" class=\"mobile-menu-item logout\">\r\n                <i class=\"iconfont icon-logout\"></i>退出登录\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {postAnyData, getAnyData, postLogin, postJsonData} from \"@/api/login\";\r\nimport { removeToken, getToken } from \"@/utils/auth\"\r\nimport SlideNotification from './SlideNotification.vue';\r\nimport Cookies from 'js-cookie'\r\n\r\nexport default {\r\n  name: \"Header\",\r\n  components: {\r\n    SlideNotification\r\n  },\r\n  data() {\r\n    return {\r\n      userBalance: null,\r\n      showComingSoon: false,\r\n      notificationMessage: \"\",\r\n      isLoggedIn: false,\r\n      userName: \"null\",\r\n      userPhone: \"\",\r\n      notificationCount: 2,\r\n      currentPath: '',\r\n      navHeight: 60,\r\n      scrollThrottleTimer: null,\r\n      previousActivePath: null,\r\n      isMobile: false,\r\n      mobileMenuOpen: false,\r\n      showUserMenu: false,\r\n      windowWidth: 0,\r\n      isReal:0,\r\n      cookieWatcher: null,\r\n      isConsoleLoading: false,\r\n      isConsoleReady: false,\r\n      hasTriggeredRefresh: false,\r\n    };\r\n  },\r\n  computed: {\r\n    userInitial() {\r\n      return this.userName && this.userName.length > 0\r\n          ? this.userName.charAt(0).toUpperCase()\r\n          : 'N';\r\n    }\r\n  },\r\n  watch: {\r\n    '$route'(to) {\r\n      this.currentPath = to.path;\r\n      if (to.path === '/product') {\r\n        this.currentPath = '/product';\r\n      } else if (to.path === '/about') {\r\n        this.currentPath = '/about';\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    isActive(route) {\r\n      return this.currentPath === route ||\r\n          (route === '/' && this.currentPath === '/index') ||\r\n          (route === '/product' && this.currentPath.startsWith('/product')) ||\r\n          (route === '/about' && this.currentPath === '/about') ||\r\n          (route === '/help' && this.currentPath.startsWith('/help'));\r\n    },\r\n\r\n\r\n    checkScreenSize() {\r\n      this.windowWidth = window.innerWidth;\r\n      this.isMobile = this.windowWidth <= 992;\r\n      if (!this.isMobile) {\r\n        this.mobileMenuOpen = false;\r\n        this.showUserMenu = false;\r\n      }\r\n    },\r\n\r\n    gotoPersonal(){\r\n      this.closeAllMenus();\r\n      this.$router.push({\r\n        path: '/personal',\r\n        query: { activeTab: 'verification' }\r\n      });\r\n    },\r\n\r\n    navigateToRecharge() {\r\n      this.closeAllMenus();\r\n      this.$router.push({\r\n        path: '/userorder',\r\n        query: { activeTab: 'recharge' }\r\n      });\r\n    },\r\n\r\n    handleConsoleNavigation() {\r\n      this.closeAllMenus();\r\n\r\n      if (this.isConsoleLoading) return;\r\n\r\n      if (!this.isLoggedIn) {\r\n        this.navigateTo('/login');\r\n        return;\r\n      }\r\n\r\n      if (this.isReal !== 1) {\r\n        // 未实名认证，显示弹窗\r\n        this.notificationMessage = \"请先完成实名认证，正在为您跳转到对应页面\";\r\n        this.showComingSoon = true;\r\n\r\n        // 2秒后跳转到实名认证页面\r\n        setTimeout(() => {\r\n          this.navigateTo('/personal', { activeTab: 'verification' });\r\n        }, 2000);\r\n        return;\r\n      }\r\n\r\n      if (!this.isConsoleReady) {\r\n        this.notificationMessage = '控制台初始化中，请稍候...';\r\n        this.showComingSoon = true;\r\n        this.isConsoleLoading = true;\r\n\r\n\r\n        if (!this.hasTriggeredRefresh) {\r\n          this.hasTriggeredRefresh = true;\r\n          setTimeout(() => {\r\n            this.getUserInfo();\r\n            this.startCookieWatcher();\r\n          }, 2000); // 2 秒后自动刷新\r\n        }\r\n        return;\r\n      }\r\n\r\n      this.isConsoleLoading = true;\r\n      this.navigateTo('/console');\r\n\r\n      this.isConsoleReady = true;\r\n      this.isConsoleLoading = false;\r\n    },\r\n\r\n    handleScroll() {\r\n      if (this.scrollThrottleTimer) return;\r\n\r\n      this.scrollThrottleTimer = setTimeout(() => {\r\n        this.scrollThrottleTimer = null;\r\n      }, 50);\r\n    },\r\n\r\n    logout() {\r\n      this.closeAllMenus();\r\n      postAnyData(\"/logout/cilent/logout\").then(res => {\r\n        if (res.data.code === 200) {\r\n          Object.keys(Cookies.get()).forEach(cookieName => {\r\n            Cookies.remove(cookieName);\r\n          });\r\n          removeToken();\r\n          this.isLoggedIn = false;\r\n          this.$router.push('/login');\r\n        }\r\n      }).catch(err => {\r\n      });\r\n    },\r\n\r\n    triggerComingSoon() {\r\n      if (this.showComingSoon) return;\r\n      this.notificationMessage = \"我们正在努力建设中，敬请期待更多精彩内容！\";\r\n      this.showComingSoon = true;\r\n      this.closeAllMenus();\r\n    },\r\n\r\n    navigateTo(path, query = {}) {\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login, .mobile-nav-link');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300);\r\n            }\r\n          });\r\n\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      this.closeAllMenus();\r\n\r\n      if (this.$route.path === path && JSON.stringify(this.$route.query) === JSON.stringify(query)) {\r\n        return;\r\n      }\r\n\r\n      this.$router.push({\r\n        path: path,\r\n        query: query\r\n      });\r\n\r\n      window.scrollTo({\r\n        top: 0,\r\n        behavior: 'instant'\r\n      });\r\n    },\r\n\r\n    measureNavHeight() {\r\n      if (this.$refs.mainNav) {\r\n        const rect = this.$refs.mainNav.getBoundingClientRect();\r\n        if (rect.height > 0) {\r\n          this.navHeight = rect.height;\r\n        }\r\n      }\r\n    },\r\n    // 处理 token 过期\r\n    handleTokenExpired() {\r\n      // 清除登录状态\r\n      this.isLoggedIn = false;\r\n      removeToken();\r\n      Object.keys(Cookies.get()).forEach(cookieName => {\r\n        Cookies.remove(cookieName);\r\n      });\r\n\r\n      // 显示通知\r\n      this.notificationMessage = \"由于长时间未操作，登录状态已失效，请重新登录\";\r\n      this.showComingSoon = true;\r\n\r\n\r\n      // 2秒后自动跳转到登录页面\r\n      setTimeout(() => {\r\n        this.$router.push('/login');\r\n      }, 2000);\r\n    },\r\n    // 处理页面可见性变化\r\n    handleVisibilityChange() {\r\n      if (document.visibilityState === 'visible' && this.isLoggedIn) {\r\n        // 页面从隐藏变为可见时，检查 token 是否仍然有效\r\n        this.getUserInfo();\r\n      }\r\n    },\r\n    getUserInfo() {\r\n      // removeToken()\r\n      postAnyData(\"/logout/cilent/getInfo\").then(res => {\r\n        if (res.data.code === 200) {\r\n          this.userName = res.data.data.nickName || \"NCloud-user\";\r\n          Cookies.set('userName', res.data.data.nickName);\r\n          this.userPhone = res.data.data.username;\r\n          Cookies.set('userPhone', res.data.data.username);\r\n          this.userEmail = res.data.data.email || \"<EMAIL>\";\r\n          Cookies.set('userEmail', res.data.data.email);\r\n          this.tenantId = res.data.data.tenantId || \"te-default\";\r\n          Cookies.set('tenantId', res.data.data.tenantId);\r\n          this.userId = res.data.data.id || \"ac-default\";\r\n          Cookies.set('userId', res.data.data.id);\r\n          this.userBalance = res.data.data.balance;\r\n          Cookies.set('userBalance', res.data.data.balance);\r\n          this.isReal = res.data.data.isReal;\r\n          Cookies.set('isReal', this.isReal);\r\n          this.isLoggedIn = true;\r\n\r\n\r\n          if (Cookies.get('publicKey-C')) {\r\n            // console.log('rsa_pubk',publicKey-C);\r\n            return;\r\n          }\r\n          postJsonData(\"/suanleme/login\", {\r\n            correlationId: Cookies.get('userId'),\r\n            rsaPubk: Cookies.get('publicKey-B')\r\n          }).then(res => {\r\n            // console.log('rsa_pubk',res.data.data.rsa_pubk);\r\n            Cookies.set('publicKey-C', res.data.data.rsa_pubk);\r\n            Cookies.set('suanlemeToken', res.data.data.token);\r\n\r\n          });\r\n        } else {\r\n          this.handleTokenExpired();\r\n        }\r\n      })\r\n    },\r\n\r\n    toggleMobileMenu() {\r\n      this.mobileMenuOpen = !this.mobileMenuOpen;\r\n      if (this.mobileMenuOpen) {\r\n        this.showUserMenu = false;\r\n      }\r\n    },\r\n\r\n    toggleUserMenu() {\r\n      this.showUserMenu = !this.showUserMenu;\r\n      if (this.showUserMenu) {\r\n        this.mobileMenuOpen = false;\r\n      }\r\n    },\r\n\r\n    closeAllMenus() {\r\n      this.mobileMenuOpen = false;\r\n      this.showUserMenu = false;\r\n    },\r\n\r\n    startCookieWatcher() {\r\n      this.cookieWatcher = setInterval(() => {\r\n\r\n        const token = this.getCookie('suanlemeToken');\r\n        const pubKey = this.getCookie('publicKey-C');\r\n        if (token && pubKey) {\r\n          clearInterval(this.cookieWatcher);\r\n          this.cookieWatcher = null;\r\n          this.isConsoleReady = true;\r\n          this.isConsoleLoading = false;\r\n        }\r\n      }, 1000); // 每秒检查一次\r\n    },\r\n\r\n    getCookie(name) {\r\n      const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));\r\n      return match ? match[2] : null;\r\n    }\r\n  },\r\n\r\n  created() {\r\n    window.addEventListener('scroll', this.handleScroll, { passive: true });\r\n    window.addEventListener('resize', this.checkScreenSize);\r\n\r\n    // 优先从本地存储读取登录状态\r\n    const token = getToken();\r\n    this.isLoggedIn = !!token;\r\n\r\n    // 如果存在token，立即从cookie中读取用户信息\r\n    if (token) {\r\n      this.userName = Cookies.get('userName') || \"null\";\r\n      this.userPhone = Cookies.get('userPhone') || \"\";\r\n      this.userBalance = parseFloat(Cookies.get('userBalance')) || 0;\r\n      this.isReal = parseInt(Cookies.get('isReal')) || 0;\r\n\r\n      // 然后异步验证token有效性\r\n      this.getUserInfo();\r\n    }\r\n\r\n    // 添加页面可见性变化监听\r\n    document.addEventListener('visibilitychange', this.handleVisibilityChange);\r\n\r\n    this.currentPath = this.$route.path;\r\n    this.checkScreenSize();\r\n  },\r\n\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.measureNavHeight();\r\n\r\n      let resizeTimer;\r\n      window.addEventListener('resize', () => {\r\n        clearTimeout(resizeTimer);\r\n        resizeTimer = setTimeout(() => {\r\n          this.measureNavHeight();\r\n        }, 250);\r\n      }, { passive: true });\r\n\r\n      this.startCookieWatcher();\r\n    });\r\n  },\r\n\r\n  beforeDestroy() {\r\n    window.removeEventListener('scroll', this.handleScroll);\r\n    window.removeEventListener('resize', this.measureNavHeight);\r\n    window.removeEventListener('resize', this.checkScreenSize);\r\n    document.removeEventListener('visibilitychange', this.handleVisibilityChange);\r\n    clearTimeout(this.scrollThrottleTimer);\r\n  },\r\n\r\n\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 原有样式保持不变 */\r\n.header-wrapper {\r\n  position: relative;\r\n  width: 100%;\r\n  max-width: 100vw;\r\n  overflow: hidden;\r\n}\r\n\r\n.main-nav {\r\n  background-image: url(\"../../../assets/images/index/back3.png\");\r\n  background-size: cover;\r\n  background-position: top;\r\n  background-repeat: no-repeat;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  width: 100%;\r\n  z-index: 1005;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  transform: translate3d(0, 0, 0);\r\n  will-change: transform;\r\n  transition: background-color 0.3s ease;\r\n  width: 100vw;\r\n}\r\n\r\n.nav-placeholder {\r\n  width: 100%;\r\n  will-change: height;\r\n}\r\n\r\n.container {\r\n  width: 100%;\r\n  max-width: 100%;\r\n  height: 60px;\r\n  margin: 0 auto;\r\n  padding: 0 15px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 电脑端导航样式 */\r\n.nav-container {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 60px;\r\n  position: relative;\r\n}\r\n\r\n.logo-area {\r\n  flex: 0 0 auto;\r\n  margin-right: -50px;\r\n}\r\n\r\n.logo-link {\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n}\r\n\r\n\r\n.logo-area img {\r\n  height: 120px;\r\n  max-width: 100%;\r\n  display: block;\r\n}\r\n\r\n.nav-menu {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.nav-list {\r\n  display: flex;\r\n  list-style: none;\r\n  margin: 0 70px;\r\n  padding: 0;\r\n}\r\n\r\n.nav-item {\r\n  position: relative;\r\n  margin: 0 10px;\r\n}\r\n\r\n.nav-link {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15px;\r\n  height: 70px;\r\n  color: #ffffff;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  text-decoration: none;\r\n  transition: all 0.3s ease-in-out;\r\n  white-space: nowrap;\r\n  cursor: pointer;\r\n  position: relative;\r\n}\r\n\r\n.nav-link.active {\r\n  color: #ffffff;\r\n  font-weight: 500;\r\n}\r\n\r\n.nav-link::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: 2vh;\r\n  left: 50%;\r\n  transform: translateX(-50%) scaleX(0);\r\n  width: calc(100% - 4vh);\r\n  height: 2px;\r\n  background-color: #ffffff;\r\n  transition: transform 0.3s ease-in-out;\r\n  transform-origin: center;\r\n}\r\n\r\n.nav-link:hover::after,\r\n.nav-link.active::after {\r\n  transform: translateX(-50%) scaleX(1);\r\n}\r\n\r\n.nav-link.active-exit::after {\r\n  transform: translateX(-50%) scaleX(0);\r\n}\r\n\r\n.nav-link i {\r\n  font-size: 12px;\r\n  margin-left: 5px;\r\n}\r\n\r\n.user-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: auto;\r\n}\r\n\r\n.auth-buttons {\r\n  display: inline-flex !important;\r\n  align-items: center !important;\r\n  height: 100%;\r\n  white-space: nowrap;\r\n}\r\n\r\n.btn {\r\n  display: inline-block !important;\r\n  padding: 8px 16px !important;\r\n  font-size: 16px !important;\r\n  border-radius: 4px !important;\r\n  text-decoration: none !important;\r\n  transition: all 0.3s !important;\r\n  margin: 0 !important;\r\n  height: auto !important;\r\n  line-height: normal !important;\r\n  border: none !important;\r\n  cursor: pointer;\r\n}\r\n\r\n.btn-login {\r\n  color: #ffffff !important;\r\n  margin-right: 10px !important;\r\n  position: relative;\r\n}\r\n\r\n.btn-login::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: 2px;\r\n  left: 50%;\r\n  transform: translateX(-50%) scaleX(0);\r\n  width: calc(100% - 20px);\r\n  height: 2px;\r\n  background-color: #ffffff;\r\n  transition: transform 0.3s ease-in-out;\r\n  transform-origin: center;\r\n}\r\n\r\n.btn-login:hover::after,\r\n.btn-login.active::after {\r\n  transform: translateX(-50%) scaleX(1);\r\n}\r\n\r\n.btn-login.active-exit::after {\r\n  transform: translateX(-50%) scaleX(0);\r\n}\r\n\r\n.btn-register {\r\n  color: #fff !important;\r\n  border: 1px solid #ffffff!important;\r\n  border-radius: 0px !important;\r\n  font-size: 15px !important;\r\n}\r\n\r\n.btn-register:hover {\r\n  background-color: rgba(194, 187, 187, 0.3) !important;\r\n}\r\n\r\n.user-profile {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-dropdown {\r\n  position: relative;\r\n  cursor: pointer;\r\n  margin-right: 2vh;\r\n}\r\n\r\n.user-avatar {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  background-color: #6366f1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.avatar-letter {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.user-dropdown .dropdown-menu {\r\n  position: absolute;\r\n  top: 100%;\r\n  right: -10px;\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);\r\n  min-width: 240px;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transform: translateY(10px);\r\n  transition: all 0.3s ease;\r\n  z-index: 1010;\r\n  padding: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n.user-dropdown:hover .dropdown-menu {\r\n  opacity: 1;\r\n  visibility: visible;\r\n  transform: translateY(0);\r\n}\r\n\r\n.user-info-section {\r\n  padding: 16px;\r\n  position: relative;\r\n  background-color: #f9fafb;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.detail-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.label {\r\n  color: #666;\r\n  font-size: 14px;\r\n  width: 60px;\r\n}\r\n\r\n.value {\r\n  color: #333;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.verification-tag {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  background-color: #e6f7ff;\r\n  color: #1890ff;\r\n  font-size: 12px;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.recharge-btn {\r\n  cursor: pointer;\r\n  margin-left: 10px;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.recharge-btn:hover {\r\n  background-color: #bae7ff;\r\n}\r\n\r\n.check-icon {\r\n  display: inline-block;\r\n  width: 12px;\r\n  height: 12px;\r\n  background-color: #1890ff;\r\n  margin-right: 4px;\r\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E\") no-repeat center / contain;\r\n}\r\n\r\n.menu-options {\r\n  padding: 8px 0;\r\n}\r\n\r\n.menu-options a {\r\n  display: block;\r\n  padding: 10px 16px;\r\n  color: #333;\r\n  text-decoration: none;\r\n  transition: background-color 0.2s;\r\n  font-size: 14px;\r\n}\r\n\r\n.menu-options a:hover {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.logout-button-container {\r\n  padding: 12px 16px;\r\n  background-color: #f9fafb;\r\n}\r\n\r\n.logout-button {\r\n  width: 100%;\r\n  padding: 10px 0;\r\n  background-color: #f0f0f0;\r\n  border: none;\r\n  border-radius: 4px;\r\n  color: #333;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.logout-button:hover {\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n/* 移动端导航样式 */\r\n.mobile-nav {\r\n  position: relative;\r\n  height: 60px;\r\n}\r\n\r\n.mobile-nav-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 100%;\r\n  padding: 0 15px;\r\n  position: relative;\r\n}\r\n\r\n/* 汉堡菜单按钮 */\r\n.hamburger-btn {\r\n  background: none;\r\n  border: none;\r\n  width: 25px;\r\n  height: 20px;\r\n  position: relative;\r\n  cursor: pointer;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  padding: 0;\r\n  z-index: 1001;\r\n  flex: 0 0 auto;\r\n}\r\n\r\n.hamburger-line {\r\n  display: block;\r\n  width: 100%;\r\n  height: 3px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.hamburger-btn .line-1 {\r\n  transform: translateY(10px) rotate(45deg);\r\n}\r\n\r\n.hamburger-btn .line-2 {\r\n  opacity: 0;\r\n}\r\n\r\n.hamburger-btn .line-3 {\r\n  transform: translateY(-10px) rotate(-45deg);\r\n}\r\n\r\n/* 中间Logo - 调大尺寸 */\r\n.mobile-logo-area {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 0 10px;\r\n}\r\n\r\n.mobile-logo-area img {\r\n  height: 100px;\r\n  max-width: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 右侧用户操作区 - 增加登录/注册按钮 */\r\n.mobile-user-actions {\r\n  flex: 0 0 auto;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.mobile-login-btn,\r\n.mobile-register-btn,\r\n.mobile-console-btn {\r\n  color: #fff;\r\n  font-size: 14px;\r\n  text-decoration: none;\r\n  padding: 6px 10px;\r\n  border-radius: 4px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.mobile-login-btn {\r\n  background-color: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.mobile-register-btn {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.mobile-console-btn {\r\n  background-color: transparent;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  margin-right: 5px;\r\n}\r\n\r\n.mobile-user-profile {\r\n  position: relative;\r\n  cursor: pointer;\r\n}\r\n\r\n.mobile-user-avatar {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 50%;\r\n  background-color: #6366f1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 移动端菜单 */\r\n.mobile-menu {\r\n  position: fixed;\r\n  top: 0;\r\n  right: -100%;\r\n  width: 80%;\r\n  max-width: 320px;\r\n  height: 100vh;\r\n  background-color: #1a1a2e;\r\n  z-index: 1000;\r\n  transition: right 0.3s ease;\r\n  overflow-y: auto;\r\n}\r\n\r\n.mobile-menu.open {\r\n  right: 0;\r\n}\r\n\r\n.mobile-menu-content {\r\n  padding: 70px 20px 20px;\r\n}\r\n\r\n.mobile-nav-list {\r\n  list-style: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.mobile-nav-item {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.mobile-nav-link {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  color: #fff;\r\n  text-decoration: none;\r\n  font-size: 16px;\r\n  border-radius: 5px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.mobile-nav-link i {\r\n  margin-right: 15px;\r\n  font-size: 20px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n}\r\n\r\n.mobile-nav-link.active {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  font-weight: 500;\r\n}\r\n\r\n.mobile-nav-link:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* 用户菜单 */\r\n.mobile-user-menu {\r\n  position: fixed;\r\n  top: 60px;\r\n  right: 15px;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n  width: 220px;\r\n  z-index: 1011;\r\n  padding: 15px;\r\n  animation: fadeIn 0.2s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(-10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.mobile-user-info {\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #eee;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.mobile-username {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.mobile-user-phone {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.mobile-menu-options {\r\n  margin-top: 10px;\r\n}\r\n\r\n.mobile-menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 0;\r\n  color: #333;\r\n  text-decoration: none;\r\n  font-size: 15px;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.mobile-menu-item i {\r\n  margin-right: 10px;\r\n  font-size: 18px;\r\n  color: #666;\r\n}\r\n\r\n.mobile-menu-item:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.mobile-menu-item:hover i {\r\n  color: #1890ff;\r\n}\r\n\r\n.mobile-menu-item.logout {\r\n  color: #f5222d;\r\n}\r\n\r\n.mobile-menu-item.logout i {\r\n  color: #f5222d;\r\n}\r\n\r\n.disabled {\r\n  pointer-events: none;\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 992px) {\r\n  .desktop-nav {\r\n    display: none !important;\r\n  }\r\n\r\n  .mobile-nav {\r\n    display: block !important;\r\n  }\r\n}\r\n\r\n@media (min-width: 993px) {\r\n  .desktop-nav {\r\n    display: flex !important;\r\n  }\r\n\r\n  .mobile-nav {\r\n    display: none !important;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AA2QA,SAAAA,WAAA,EAAAC,UAAA,EAAAC,SAAA,EAAAC,YAAA;AACA,SAAAC,WAAA,EAAAC,QAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH;EACA;EACAI,KAAA;IACA;MACAC,WAAA;MACAC,cAAA;MACAC,mBAAA;MACAC,UAAA;MACAC,QAAA;MACAC,SAAA;MACAC,iBAAA;MACAC,WAAA;MACAC,SAAA;MACAC,mBAAA;MACAC,kBAAA;MACAC,QAAA;MACAC,cAAA;MACAC,YAAA;MACAC,WAAA;MACAC,MAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,cAAA;MACAC,mBAAA;IACA;EACA;EACAC,QAAA;IACAC,YAAA;MACA,YAAAjB,QAAA,SAAAA,QAAA,CAAAkB,MAAA,OACA,KAAAlB,QAAA,CAAAmB,MAAA,IAAAC,WAAA,KACA;IACA;EACA;EACAC,KAAA;IACA,QAAAC,CAAAC,EAAA;MACA,KAAApB,WAAA,GAAAoB,EAAA,CAAAC,IAAA;MACA,IAAAD,EAAA,CAAAC,IAAA;QACA,KAAArB,WAAA;MACA,WAAAoB,EAAA,CAAAC,IAAA;QACA,KAAArB,WAAA;MACA;IACA;EACA;EACAsB,OAAA;IACAC,SAAAC,KAAA;MACA,YAAAxB,WAAA,KAAAwB,KAAA,IACAA,KAAA,iBAAAxB,WAAA,iBACAwB,KAAA,wBAAAxB,WAAA,CAAAyB,UAAA,gBACAD,KAAA,sBAAAxB,WAAA,iBACAwB,KAAA,qBAAAxB,WAAA,CAAAyB,UAAA;IACA;IAGAC,gBAAA;MACA,KAAAnB,WAAA,GAAAoB,MAAA,CAAAC,UAAA;MACA,KAAAxB,QAAA,QAAAG,WAAA;MACA,UAAAH,QAAA;QACA,KAAAC,cAAA;QACA,KAAAC,YAAA;MACA;IACA;IAEAuB,aAAA;MACA,KAAAC,aAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAX,IAAA;QACAY,KAAA;UAAAC,SAAA;QAAA;MACA;IACA;IAEAC,mBAAA;MACA,KAAAL,aAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAX,IAAA;QACAY,KAAA;UAAAC,SAAA;QAAA;MACA;IACA;IAEAE,wBAAA;MACA,KAAAN,aAAA;MAEA,SAAApB,gBAAA;MAEA,UAAAd,UAAA;QACA,KAAAyC,UAAA;QACA;MACA;MAEA,SAAA7B,MAAA;QACA;QACA,KAAAb,mBAAA;QACA,KAAAD,cAAA;;QAEA;QACA4C,UAAA;UACA,KAAAD,UAAA;YAAAH,SAAA;UAAA;QACA;QACA;MACA;MAEA,UAAAvB,cAAA;QACA,KAAAhB,mBAAA;QACA,KAAAD,cAAA;QACA,KAAAgB,gBAAA;QAGA,UAAAE,mBAAA;UACA,KAAAA,mBAAA;UACA0B,UAAA;YACA,KAAAC,WAAA;YACA,KAAAC,kBAAA;UACA;QACA;;QACA;MACA;MAEA,KAAA9B,gBAAA;MACA,KAAA2B,UAAA;MAEA,KAAA1B,cAAA;MACA,KAAAD,gBAAA;IACA;IAEA+B,aAAA;MACA,SAAAvC,mBAAA;MAEA,KAAAA,mBAAA,GAAAoC,UAAA;QACA,KAAApC,mBAAA;MACA;IACA;IAEAwC,OAAA;MACA,KAAAZ,aAAA;MACAhD,WAAA,0BAAA6D,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAApD,IAAA,CAAAqD,IAAA;UACAC,MAAA,CAAAC,IAAA,CAAA1D,OAAA,CAAA2D,GAAA,IAAAC,OAAA,CAAAC,UAAA;YACA7D,OAAA,CAAA8D,MAAA,CAAAD,UAAA;UACA;UACAhE,WAAA;UACA,KAAAU,UAAA;UACA,KAAAmC,OAAA,CAAAC,IAAA;QACA;MACA,GAAAoB,KAAA,CAAAC,GAAA,KACA;IACA;IAEAC,kBAAA;MACA,SAAA5D,cAAA;MACA,KAAAC,mBAAA;MACA,KAAAD,cAAA;MACA,KAAAoC,aAAA;IACA;IAEAO,WAAAhB,IAAA,EAAAY,KAAA;MACA,SAAAjC,WAAA,SAAAA,WAAA,KAAAqB,IAAA;QACA,KAAAlB,kBAAA,QAAAH,WAAA;QAEA,KAAAuD,SAAA;UACA,MAAAC,QAAA,GAAAC,QAAA,CAAAC,gBAAA;UACAF,QAAA,CAAAP,OAAA,CAAAU,IAAA;YACA,KAAAA,IAAA,CAAAC,SAAA,CAAAC,QAAA,cACAxC,IAAA,iBAAAsC,IAAA,CAAAC,SAAA,CAAAC,QAAA,kBACA,CAAAF,IAAA,CAAAC,SAAA,CAAAC,QAAA;cACAF,IAAA,CAAAC,SAAA,CAAAE,GAAA;cAEAxB,UAAA;gBACAqB,IAAA,CAAAC,SAAA,CAAAT,MAAA;cACA;YACA;UACA;UAEA,KAAAnD,WAAA,GAAAqB,IAAA;QACA;MACA;QACA,KAAArB,WAAA,GAAAqB,IAAA;MACA;MAEA,KAAAS,aAAA;MAEA,SAAAX,MAAA,CAAAE,IAAA,KAAAA,IAAA,IAAA0C,IAAA,CAAAC,SAAA,MAAA7C,MAAA,CAAAc,KAAA,MAAA8B,IAAA,CAAAC,SAAA,CAAA/B,KAAA;QACA;MACA;MAEA,KAAAF,OAAA,CAAAC,IAAA;QACAX,IAAA,EAAAA,IAAA;QACAY,KAAA,EAAAA;MACA;MAEAN,MAAA,CAAAsC,QAAA;QACAC,GAAA;QACAC,QAAA;MACA;IACA;IAEAC,iBAAA;MACA,SAAAC,KAAA,CAAAC,OAAA;QACA,MAAAC,IAAA,QAAAF,KAAA,CAAAC,OAAA,CAAAE,qBAAA;QACA,IAAAD,IAAA,CAAAE,MAAA;UACA,KAAAxE,SAAA,GAAAsE,IAAA,CAAAE,MAAA;QACA;MACA;IACA;IACA;IACAC,mBAAA;MACA;MACA,KAAA9E,UAAA;MACAV,WAAA;MACA4D,MAAA,CAAAC,IAAA,CAAA1D,OAAA,CAAA2D,GAAA,IAAAC,OAAA,CAAAC,UAAA;QACA7D,OAAA,CAAA8D,MAAA,CAAAD,UAAA;MACA;;MAEA;MACA,KAAAvD,mBAAA;MACA,KAAAD,cAAA;;MAGA;MACA4C,UAAA;QACA,KAAAP,OAAA,CAAAC,IAAA;MACA;IACA;IACA;IACA2C,uBAAA;MACA,IAAAlB,QAAA,CAAAmB,eAAA,uBAAAhF,UAAA;QACA;QACA,KAAA2C,WAAA;MACA;IACA;IACAA,YAAA;MACA;MACAzD,WAAA,2BAAA6D,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAApD,IAAA,CAAAqD,IAAA;UACA,KAAAhD,QAAA,GAAA+C,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAqF,QAAA;UACAxF,OAAA,CAAAyF,GAAA,aAAAlC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAqF,QAAA;UACA,KAAA/E,SAAA,GAAA8C,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAuF,QAAA;UACA1F,OAAA,CAAAyF,GAAA,cAAAlC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAuF,QAAA;UACA,KAAAC,SAAA,GAAApC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAyF,KAAA;UACA5F,OAAA,CAAAyF,GAAA,cAAAlC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAyF,KAAA;UACA,KAAAC,QAAA,GAAAtC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAA0F,QAAA;UACA7F,OAAA,CAAAyF,GAAA,aAAAlC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAA0F,QAAA;UACA,KAAAC,MAAA,GAAAvC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAA4F,EAAA;UACA/F,OAAA,CAAAyF,GAAA,WAAAlC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAA4F,EAAA;UACA,KAAA3F,WAAA,GAAAmD,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAA6F,OAAA;UACAhG,OAAA,CAAAyF,GAAA,gBAAAlC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAA6F,OAAA;UACA,KAAA7E,MAAA,GAAAoC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAgB,MAAA;UACAnB,OAAA,CAAAyF,GAAA,gBAAAtE,MAAA;UACA,KAAAZ,UAAA;UAGA,IAAAP,OAAA,CAAA2D,GAAA;YACA;YACA;UACA;UACA/D,YAAA;YACAqG,aAAA,EAAAjG,OAAA,CAAA2D,GAAA;YACAuC,OAAA,EAAAlG,OAAA,CAAA2D,GAAA;UACA,GAAAL,IAAA,CAAAC,GAAA;YACA;YACAvD,OAAA,CAAAyF,GAAA,gBAAAlC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAgG,QAAA;YACAnG,OAAA,CAAAyF,GAAA,kBAAAlC,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAiG,KAAA;UAEA;QACA;UACA,KAAAf,kBAAA;QACA;MACA;IACA;IAEAgB,iBAAA;MACA,KAAArF,cAAA,SAAAA,cAAA;MACA,SAAAA,cAAA;QACA,KAAAC,YAAA;MACA;IACA;IAEAqF,eAAA;MACA,KAAArF,YAAA,SAAAA,YAAA;MACA,SAAAA,YAAA;QACA,KAAAD,cAAA;MACA;IACA;IAEAyB,cAAA;MACA,KAAAzB,cAAA;MACA,KAAAC,YAAA;IACA;IAEAkC,mBAAA;MACA,KAAA/B,aAAA,GAAAmF,WAAA;QAEA,MAAAH,KAAA,QAAAI,SAAA;QACA,MAAAC,MAAA,QAAAD,SAAA;QACA,IAAAJ,KAAA,IAAAK,MAAA;UACAC,aAAA,MAAAtF,aAAA;UACA,KAAAA,aAAA;UACA,KAAAE,cAAA;UACA,KAAAD,gBAAA;QACA;MACA;IACA;;IAEAmF,UAAAvG,IAAA;MACA,MAAA0G,KAAA,GAAAvC,QAAA,CAAAwC,MAAA,CAAAD,KAAA,KAAAE,MAAA,WAAA5G,IAAA;MACA,OAAA0G,KAAA,GAAAA,KAAA;IACA;EACA;EAEAG,QAAA;IACAxE,MAAA,CAAAyE,gBAAA,gBAAA3D,YAAA;MAAA4D,OAAA;IAAA;IACA1E,MAAA,CAAAyE,gBAAA,gBAAA1E,eAAA;;IAEA;IACA,MAAA+D,KAAA,GAAAtG,QAAA;IACA,KAAAS,UAAA,KAAA6F,KAAA;;IAEA;IACA,IAAAA,KAAA;MACA,KAAA5F,QAAA,GAAAR,OAAA,CAAA2D,GAAA;MACA,KAAAlD,SAAA,GAAAT,OAAA,CAAA2D,GAAA;MACA,KAAAvD,WAAA,GAAA6G,UAAA,CAAAjH,OAAA,CAAA2D,GAAA;MACA,KAAAxC,MAAA,GAAA+F,QAAA,CAAAlH,OAAA,CAAA2D,GAAA;;MAEA;MACA,KAAAT,WAAA;IACA;;IAEA;IACAkB,QAAA,CAAA2C,gBAAA,0BAAAzB,sBAAA;IAEA,KAAA3E,WAAA,QAAAmB,MAAA,CAAAE,IAAA;IACA,KAAAK,eAAA;EACA;EAEA8E,QAAA;IACA,KAAAjD,SAAA;MACA,KAAAa,gBAAA;MAEA,IAAAqC,WAAA;MACA9E,MAAA,CAAAyE,gBAAA;QACAM,YAAA,CAAAD,WAAA;QACAA,WAAA,GAAAnE,UAAA;UACA,KAAA8B,gBAAA;QACA;MACA;QAAAiC,OAAA;MAAA;MAEA,KAAA7D,kBAAA;IACA;EACA;EAEAmE,cAAA;IACAhF,MAAA,CAAAiF,mBAAA,gBAAAnE,YAAA;IACAd,MAAA,CAAAiF,mBAAA,gBAAAxC,gBAAA;IACAzC,MAAA,CAAAiF,mBAAA,gBAAAlF,eAAA;IACA+B,QAAA,CAAAmD,mBAAA,0BAAAjC,sBAAA;IACA+B,YAAA,MAAAxG,mBAAA;EACA;AAGA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}