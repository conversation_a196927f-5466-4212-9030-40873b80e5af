{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'chatAi',\n  data() {\n    return {\n      showChat: false,\n      userInput: '',\n      messages: [{\n        type: 'bot',\n        text: '您好！我是智能客服助手，有什么可以帮您？',\n        time: new Date()\n      }],\n      loading: false,\n      historyMessages: [],\n      questions: [\"如何租赁GPU算力？\", \"支持哪些支付方式？\", \"如何查看订单状态？\"],\n      currentQuestionIndex: 0,\n      carouselTimer: null,\n      carouselInterval: 3000,\n      isPaused: false\n    };\n  },\n  beforeDestroy() {\n    this.clearCarousel();\n  },\n  mounted() {\n    this.startCarousel();\n    // 导入 Font Awesome 图标库\n    if (!document.getElementById('font-awesome')) {\n      const link = document.createElement('link');\n      link.id = 'font-awesome';\n      link.rel = 'stylesheet';\n      link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';\n      document.head.appendChild(link);\n    }\n  },\n  methods: {\n    witde(index) {\n      this.currentQuestionIndex = index;\n      this.pauseCarousel();\n    },\n    startCarousel() {\n      let that = this;\n      this.clearCarousel();\n      this.carouselTimer = setInterval(() => {\n        if (!that.isPaused) {\n          this.currentQuestionIndex = (this.currentQuestionIndex + 1) % this.questions.length;\n          console.log(\"数据\", this.currentQuestionIndex);\n          console.log(\"ispasued\", that.isPaused);\n        }\n      }, this.carouselInterval);\n    },\n    pauseCarousel() {\n      this.isPaused = true;\n    },\n    resumeCarousel() {\n      this.isPaused = false;\n    },\n    clearCarousel() {\n      if (this.carouselTimer) {\n        clearInterval(this.carouselTimer);\n        this.carouselTimer = null;\n      }\n    },\n    // 点击轮播问题自动提问\n    sendCarouselQuestion(question) {\n      this.userInput = question;\n      this.sendMessage();\n    },\n    toggleChat() {\n      this.showChat = !this.showChat;\n      if (this.showChat) {\n        this.$nextTick(() => {\n          this.scrollToBottom();\n        });\n      }\n    },\n    async sendMessage() {\n      if (!this.userInput.trim() || this.loading) return;\n\n      // 添加用户消息\n      this.messages.push({\n        type: 'user',\n        text: this.userInput,\n        time: new Date()\n      });\n      const userQuestion = this.userInput;\n      this.userInput = '';\n      this.loading = true;\n      //添加历史记录\n      this.historyMessages.push({\n        role: 'user',\n        content: userQuestion\n      });\n\n      // 构造请求体\n      const requestBody = {\n        model: 'Qwen/QwQ-32B',\n        messages: [{\n          role: \"system\",\n          content: \"你是天工开物算力租赁网站的客服，当我问到算力租赁相关问题的时候再进行回答，如果我的问题与算力无关则回答“小天也不知道怎么办了呢，不如我们来聊聊算力相关的话题吧！”,如果问题涉及到你是谁或者你好的这种礼貌性踊跃，可以进行礼貌性的回复\"\n        }, ...this.historyMessages],\n        // 携带上下文历史\n        stream: true,\n        options: {\n          presence_penalty: 1.2,\n          // 重复内容惩罚（0-2）\n          frequency_penalty: 1.5,\n          // 高频词惩罚（0-2）\n          // repeat_last_n: 64,      // 检查重复的上下文长度\n          seed: 12345 // 固定随机种子\n        }\n      };\n      // 滚动到底部\n      this.$nextTick(() => {\n        this.scrollToBottom();\n      });\n      try {\n        // 调用后端API获取回复\n        // 替换为你的实际API\n        const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {\n          method: 'POST',\n          headers: {\n            Authorization: 'Bearer sk-yxjtnfarjndtcwbjwmscdejrgtlpaaebnfdxcfnhhuanbqty',\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(requestBody)\n        });\n\n        // 处理流式数据\n        const reader = response.body.getReader();\n        const decoder = new TextDecoder();\n        const aiResponseIndex = this.messages.push({\n          type: 'bot',\n          text: '',\n          time: new Date()\n        }) - 1;\n        while (true) {\n          const {\n            done,\n            value\n          } = await reader.read();\n          if (done) break;\n\n          // 解析流式数据块（可能包含多个JSON对象）\n          const chunk = decoder.decode(value);\n          const lines = chunk.split('\\n').filter(line => line.trim());\n          for (const line of lines) {\n            try {\n              const jsonString = line.slice(6).trim();\n              if (jsonString === \"\" || jsonString === \"[DONE]\") continue;\n              let data = JSON.parse(jsonString);\n              if (data.choices) {\n                if (data.choices[0].delta.reasoning_content != null) {\n                  continue;\n                }\n                if (data.choices[0].delta.content == '\\n\\n') {\n                  continue;\n                }\n                this.messages[aiResponseIndex].text += data.choices[0].delta.content;\n              }\n            } catch (e) {}\n          }\n        }\n        this.historyMessages.push({\n          role: \"assistant\",\n          content: this.messages[aiResponseIndex].text\n        });\n\n        // 添加机器人回复\n        // this.messages.push({\n        //     type: 'bot',\n        //     text: response,\n        //     time: new Date()\n        // });\n      } catch (error) {\n        // 添加错误消息\n        this.messages.push({\n          type: 'bot',\n          text: '抱歉，系统暂时无法响应，请稍后再试。',\n          time: new Date()\n        });\n      } finally {\n        this.loading = false;\n\n        // 滚动到底部\n        this.$nextTick(() => {\n          this.scrollToBottom();\n        });\n      }\n    },\n    // 模拟API调用，实际使用时替换为真实API\n    async callChatAPI(message) {\n      // 模拟网络延迟\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // TODO: 替换为实际的API调用\n      // const response = await fetch('YOUR_API_ENDPOINT', {\n      //   method: 'POST',\n      //   headers: {\n      //     'Content-Type': 'application/json',\n      //   },\n      //   body: JSON.stringify({ message }),\n      // });\n      // return await response.json();\n\n      // 模拟返回数据\n      return `感谢您的提问: \"${message}\"。这是一个模拟回复，请替换为真实API调用。`;\n    },\n    scrollToBottom() {\n      const container = this.$refs.messagesContainer;\n      container.scrollTop = container.scrollHeight;\n    },\n    formatTime(date) {\n      return new Date(date).toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n    formatMessage(text) {\n      // 处理文本中的链接、表情等\n      return text.replace(/\\n/g, '<br>').replace(/(https?:\\/\\/[^\\s]+)/g, '<a href=\"$1\" target=\"_blank\">$1</a>');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "showChat", "userInput", "messages", "type", "text", "time", "Date", "loading", "historyMessages", "questions", "currentQuestionIndex", "carouselTimer", "carouselI<PERSON>val", "isPaused", "<PERSON><PERSON><PERSON><PERSON>", "clearCarousel", "mounted", "startCarousel", "document", "getElementById", "link", "createElement", "id", "rel", "href", "head", "append<PERSON><PERSON><PERSON>", "methods", "witde", "index", "pauseCarousel", "that", "setInterval", "length", "console", "log", "resumeCarousel", "clearInterval", "sendCarouselQuestion", "question", "sendMessage", "toggleChat", "$nextTick", "scrollToBottom", "trim", "push", "userQuestion", "role", "content", "requestBody", "model", "stream", "options", "presence_penalty", "frequency_penalty", "seed", "response", "fetch", "method", "headers", "Authorization", "body", "JSON", "stringify", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "aiResponseIndex", "done", "value", "read", "chunk", "decode", "lines", "split", "filter", "line", "jsonString", "slice", "parse", "choices", "delta", "reasoning_content", "e", "error", "callChatAPI", "message", "Promise", "resolve", "setTimeout", "container", "$refs", "messagesContainer", "scrollTop", "scrollHeight", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "formatMessage", "replace"], "sources": ["src/components/common/mider/chatAi.vue"], "sourcesContent": ["<template>\r\n    <div >\r\n<!--        问题轮播-->\r\n        <!-- 悬浮客服容器 -->\r\n        <div class=\"chat-container\">\r\n            <!-- 问题轮播区 -->\r\n            <div class=\"question-carousel\"\r\n                 @mouseenter=\"pauseCarousel\"\r\n                 @mouseleave=\"resumeCarousel\">\r\n                <transition-group name=\"slide\" tag=\"div\" class=\"carousel-wrapper\">\r\n                    <div v-for=\"(question, index) in questions\"\r\n                         :key=\"question\"\r\n                         class=\"question-item\"\r\n                         v-show=\"currentQuestionIndex === index\"\r\n                         @click=\"sendCarouselQuestion(question)\"\r\n                         @mouseenter=\"witde(index)\"\r\n                    >\r\n                        {{ question }}\r\n                    </div>\r\n                </transition-group>\r\n            </div>\r\n\r\n            <!-- 原有悬浮按钮 -->\r\n            <div class=\"chat-icon\"\r\n                 :class=\"{ 'chat-icon-active': showChat }\"\r\n                 @click=\"toggleChat\">\r\n                <i class=\"fas fa-comment\"></i>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 聊天窗口 -->\r\n        <div class=\"chat-window\" v-show=\"showChat\">\r\n            <div class=\"chat-header\">\r\n                <div class=\"chat-title\">\r\n                    <i class=\"fas fa-robot\"></i>\r\n                    <span>智能客服</span>\r\n                </div>\r\n                <div class=\"chat-controls\">\r\n                    <i class=\"fas fa-times\" @click=\"toggleChat\"></i>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-messages\" ref=\"messagesContainer\">\r\n                <div\r\n                        v-for=\"(message, index) in messages\"\r\n                        :key=\"index\"\r\n                        :class=\"['message', message.type]\"\r\n                >\r\n                    <div class=\"avatar\" v-if=\"message.type === 'bot'\">\r\n                        <i class=\"fas fa-robot\"></i>\r\n                    </div>\r\n                    <div class=\"message-content\">\r\n                        <div class=\"message-text\" v-html=\"formatMessage(message.text)\"></div>\r\n                        <div class=\"message-time\">{{ formatTime(message.time) }}</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"typing-indicator\" v-if=\"loading\">\r\n                    <div class=\"dot\"></div>\r\n                    <div class=\"dot\"></div>\r\n                    <div class=\"dot\"></div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"chat-input\">\r\n                <input\r\n                        type=\"text\"\r\n                        v-model=\"userInput\"\r\n                        placeholder=\"请输入您的问题...\"\r\n                        @keyup.enter=\"sendMessage\"\r\n                        :disabled=\"loading\"\r\n                />\r\n                <button @click=\"sendMessage\" :disabled=\"loading || !userInput.trim()\">\r\n                    <i class=\"fas fa-paper-plane\"></i>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        name: 'chatAi',\r\n\r\n        data() {\r\n            return {\r\n                showChat: false,\r\n                userInput: '',\r\n                messages: [\r\n                    {\r\n                        type: 'bot',\r\n                        text: '您好！我是智能客服助手，有什么可以帮您？',\r\n                        time: new Date()\r\n                    }\r\n                ],\r\n                loading: false,\r\n                historyMessages:[],\r\n                questions: [\r\n                    \"如何租赁GPU算力？\",\r\n                    \"支持哪些支付方式？\",\r\n                    \"如何查看订单状态？\"\r\n                ],\r\n                currentQuestionIndex: 0,\r\n                carouselTimer: null,\r\n                carouselInterval: 3000,\r\n                isPaused: false\r\n            }\r\n        },\r\n        beforeDestroy() {\r\n            this.clearCarousel()\r\n        },\r\n        mounted() {\r\n            this.startCarousel();\r\n            // 导入 Font Awesome 图标库\r\n            if (!document.getElementById('font-awesome')) {\r\n                const link = document.createElement('link');\r\n                link.id = 'font-awesome';\r\n                link.rel = 'stylesheet';\r\n                link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';\r\n                document.head.appendChild(link);\r\n            }\r\n        },\r\n\r\n        methods: {\r\n            witde(index){\r\n                this.currentQuestionIndex = index;\r\n                this.pauseCarousel();\r\n            },\r\n            startCarousel() {\r\n                let that = this\r\n                this.clearCarousel();\r\n                this.carouselTimer = setInterval(() => {\r\n                    if (!that.isPaused) {\r\n                        this.currentQuestionIndex =\r\n                            (this.currentQuestionIndex + 1) % this.questions.length\r\n                        console.log(\"数据\", this.currentQuestionIndex)\r\n                        console.log(\"ispasued\",that.isPaused)\r\n                    }\r\n                }, this.carouselInterval)\r\n            },\r\n            pauseCarousel() {\r\n                this.isPaused = true;\r\n            },\r\n            resumeCarousel() {\r\n                this.isPaused = false;\r\n            },\r\n            clearCarousel() {\r\n                if (this.carouselTimer) {\r\n                    clearInterval(this.carouselTimer);\r\n                    this.carouselTimer = null;\r\n                }\r\n            },\r\n            // 点击轮播问题自动提问\r\n            sendCarouselQuestion(question) {\r\n                this.userInput = question;\r\n                this.sendMessage();\r\n            },\r\n            toggleChat() {\r\n                this.showChat = !this.showChat;\r\n\r\n                if (this.showChat) {\r\n                    this.$nextTick(() => {\r\n                        this.scrollToBottom();\r\n                    });\r\n                }\r\n            },\r\n\r\n            async sendMessage() {\r\n                if (!this.userInput.trim() || this.loading) return;\r\n\r\n                // 添加用户消息\r\n                this.messages.push({\r\n                    type: 'user',\r\n                    text: this.userInput,\r\n                    time: new Date()\r\n                });\r\n\r\n                const userQuestion = this.userInput;\r\n                this.userInput = '';\r\n                this.loading = true;\r\n                //添加历史记录\r\n                this.historyMessages.push({\r\n                    role:'user',\r\n                    content:userQuestion,\r\n                })\r\n\r\n                // 构造请求体\r\n                const requestBody = {\r\n                    model: 'Qwen/QwQ-32B',\r\n                    messages: [{role:\"system\",content: \"你是天工开物算力租赁网站的客服，当我问到算力租赁相关问题的时候再进行回答，如果我的问题与算力无关则回答“小天也不知道怎么办了呢，不如我们来聊聊算力相关的话题吧！”,如果问题涉及到你是谁或者你好的这种礼貌性踊跃，可以进行礼貌性的回复\"},...this.historyMessages], // 携带上下文历史\r\n                    stream: true,\r\n                    options: {\r\n                        presence_penalty: 1.2,  // 重复内容惩罚（0-2）\r\n                        frequency_penalty: 1.5, // 高频词惩罚（0-2）\r\n                        // repeat_last_n: 64,      // 检查重复的上下文长度\r\n                        seed: 12345             // 固定随机种子\r\n                    }\r\n                };\r\n                // 滚动到底部\r\n                this.$nextTick(() => {\r\n                    this.scrollToBottom();\r\n                });\r\n\r\n                try {\r\n                    // 调用后端API获取回复\r\n                    // 替换为你的实际API\r\n                    const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {\r\n                        method: 'POST',\r\n                        headers: {\r\n                            Authorization: 'Bearer sk-yxjtnfarjndtcwbjwmscdejrgtlpaaebnfdxcfnhhuanbqty',\r\n                            'Content-Type': 'application/json',\r\n                        },\r\n                        body: JSON.stringify(requestBody)\r\n                    });\r\n\r\n                    // 处理流式数据\r\n                    const reader = response.body.getReader();\r\n                    const decoder = new TextDecoder();\r\n                    const aiResponseIndex = this.messages.push({\r\n                        type: 'bot',\r\n                        text: '',\r\n                        time:new Date()\r\n                    }) - 1;\r\n                    while (true) {\r\n                        const { done, value } = await reader.read();\r\n                        if (done) break;\r\n\r\n                        // 解析流式数据块（可能包含多个JSON对象）\r\n                        const chunk = decoder.decode(value);\r\n                        const lines = chunk.split('\\n').filter(line => line.trim());\r\n\r\n                        for (const line of lines) {\r\n                            try {\r\n                                const jsonString = line.slice(6).trim();\r\n                                if (jsonString === \"\" || jsonString === \"[DONE]\") continue;\r\n\r\n                                let data = JSON.parse(jsonString)\r\n                                if (data.choices) {\r\n                                    if (data.choices[0].delta.reasoning_content!=null){\r\n                                        continue\r\n                                    }\r\n                                    if (data.choices[0].delta.content == '\\n\\n'){\r\n                                        continue\r\n                                    }\r\n                                    this.messages[aiResponseIndex].text += data.choices[0].delta.content;\r\n                                }\r\n                            } catch (e) {\r\n                            }\r\n                        }\r\n                    }\r\n                    this.historyMessages.push({\r\n                        role:\"assistant\",\r\n                        content:this.messages[aiResponseIndex].text\r\n                    })\r\n\r\n                    // 添加机器人回复\r\n                    // this.messages.push({\r\n                    //     type: 'bot',\r\n                    //     text: response,\r\n                    //     time: new Date()\r\n                    // });\r\n                } catch (error) {\r\n\r\n                    // 添加错误消息\r\n                    this.messages.push({\r\n                        type: 'bot',\r\n                        text: '抱歉，系统暂时无法响应，请稍后再试。',\r\n                        time: new Date()\r\n                    });\r\n                } finally {\r\n                    this.loading = false;\r\n\r\n                    // 滚动到底部\r\n                    this.$nextTick(() => {\r\n                        this.scrollToBottom();\r\n                    });\r\n                }\r\n            },\r\n\r\n            // 模拟API调用，实际使用时替换为真实API\r\n            async callChatAPI(message) {\r\n                // 模拟网络延迟\r\n                await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n                // TODO: 替换为实际的API调用\r\n                // const response = await fetch('YOUR_API_ENDPOINT', {\r\n                //   method: 'POST',\r\n                //   headers: {\r\n                //     'Content-Type': 'application/json',\r\n                //   },\r\n                //   body: JSON.stringify({ message }),\r\n                // });\r\n                // return await response.json();\r\n\r\n                // 模拟返回数据\r\n                return `感谢您的提问: \"${message}\"。这是一个模拟回复，请替换为真实API调用。`;\r\n            },\r\n\r\n            scrollToBottom() {\r\n                const container = this.$refs.messagesContainer;\r\n                container.scrollTop = container.scrollHeight;\r\n            },\r\n\r\n            formatTime(date) {\r\n                return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n            },\r\n\r\n            formatMessage(text) {\r\n                // 处理文本中的链接、表情等\r\n                return text\r\n                    .replace(/\\n/g, '<br>')\r\n                    .replace(/(https?:\\/\\/[^\\s]+)/g, '<a href=\"$1\" target=\"_blank\">$1</a>');\r\n            }\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped>\r\n    /* 修正后的轮播样式 */\r\n    .chat-container {\r\n        position: fixed;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n        gap: 10px;\r\n        z-index: 1001;\r\n    }\r\n\r\n    .question-carousel {\r\n        left: 20px;\r\n        padding-bottom: 85px;\r\n        padding-top: 20px;\r\n        color: white;\r\n        min-width: 150px;\r\n        text-align: left;\r\n        cursor: pointer;\r\n        overflow: hidden;\r\n        position: relative;\r\n        height: 60px; /* 固定高度避免跳动 */\r\n    }\r\n\r\n    .carousel-wrapper {\r\n        position: relative;\r\n        height: 100%;\r\n    }\r\n\r\n    .question-item {\r\n        position: absolute;\r\n        border-radius: 20px 20px 20px 20px;\r\n        background-color: black;\r\n        width: 100%;\r\n        left: 0;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        padding: 10px 10px;\r\n        font-size: 14px;\r\n        opacity: 1;\r\n        transition: all 0.5s ease;\r\n    }\r\n\r\n    .question-item:hover {\r\n        color: #4286f4;\r\n    }\r\n\r\n    /* 过渡动画修正 */\r\n    .slide-enter-active,\r\n    .slide-leave-active {\r\n        transition: all 0.5s ease;\r\n    }\r\n    .slide-enter-from {\r\n        opacity: 0;\r\n        transform: translateY(20px) translateY(-50%);\r\n    }\r\n    .slide-leave-to {\r\n        opacity: 0;\r\n        transform: translateY(-20px) translateY(-50%);\r\n    }\r\n    .slide-enter-to,\r\n    .slide-leave-from {\r\n        opacity: 1;\r\n        transform: translateY(0) translateY(-50%);\r\n    }\r\n    .chat-icon {\r\n        position: fixed;\r\n        bottom: 20px;\r\n        right: 20px;\r\n        width: 60px;\r\n        height: 60px;\r\n        background-color: blue;\r\n        border-radius: 50%;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n        transition: all 0.3s ease;\r\n        z-index: 1001;\r\n    }\r\n\r\n    .chat-icon i {\r\n        color: white;\r\n        font-size: 24px;\r\n    }\r\n\r\n    .chat-icon:hover, .chat-icon-active {\r\n        background-color: #3367d6;\r\n        transform: scale(1.05);\r\n    }\r\n\r\n    .chat-window {\r\n        position: fixed;\r\n        bottom: 90px;\r\n        right: 20px;\r\n        width: 350px;\r\n        height: 500px;\r\n        background-color: #fff;\r\n        border-radius: 10px;\r\n        box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16);\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: hidden;\r\n        z-index: 1002;\r\n    }\r\n\r\n    .chat-header {\r\n        padding: 15px;\r\n        background-color: #4286f4;\r\n        color: white;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n    }\r\n\r\n    .chat-title {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 10px;\r\n        font-weight: bold;\r\n    }\r\n\r\n    .chat-controls i {\r\n        cursor: pointer;\r\n        font-size: 18px;\r\n    }\r\n\r\n    .chat-messages {\r\n        flex: 1;\r\n        padding: 15px;\r\n        overflow-y: auto;\r\n        background-color: #f5f5f5;\r\n    }\r\n\r\n    .message {\r\n        display: flex;\r\n        margin-bottom: 15px;\r\n        align-items: flex-start;\r\n    }\r\n\r\n    .message.user {\r\n        flex-direction: row-reverse;\r\n    }\r\n\r\n    .avatar {\r\n        width: 36px;\r\n        height: 36px;\r\n        border-radius: 50%;\r\n        background-color: #4286f4;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        color: white;\r\n        margin-right: 10px;\r\n    }\r\n\r\n    .message-content {\r\n        max-width: 70%;\r\n    }\r\n\r\n    .message-text {\r\n        padding: 10px 15px;\r\n        border-radius: 18px;\r\n        margin-bottom: 5px;\r\n        word-break: break-word;\r\n    }\r\n\r\n    .message.bot .message-text {\r\n        background-color: white;\r\n        border: 1px solid #e0e0e0;\r\n    }\r\n\r\n    .message.user .message-text {\r\n        background-color: #4286f4;\r\n        color: white;\r\n        text-align: right;\r\n    }\r\n\r\n    .message-time {\r\n        font-size: 12px;\r\n        color: #999;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .message.user .message-time {\r\n        text-align: right;\r\n    }\r\n\r\n    .typing-indicator {\r\n        display: flex;\r\n        padding: 10px 15px;\r\n        background-color: white;\r\n        border-radius: 18px;\r\n        border: 1px solid #e0e0e0;\r\n        width: fit-content;\r\n        margin-bottom: 15px;\r\n    }\r\n\r\n    .dot {\r\n        width: 8px;\r\n        height: 8px;\r\n        background-color: #999;\r\n        border-radius: 50%;\r\n        margin: 0 2px;\r\n        animation: bounce 1.5s infinite;\r\n    }\r\n\r\n    .dot:nth-child(2) {\r\n        animation-delay: 0.2s;\r\n    }\r\n\r\n    .dot:nth-child(3) {\r\n        animation-delay: 0.4s;\r\n    }\r\n\r\n    @keyframes bounce {\r\n        0%, 60%, 100% {\r\n            transform: translateY(0);\r\n        }\r\n        30% {\r\n            transform: translateY(-4px);\r\n        }\r\n    }\r\n\r\n    .chat-input {\r\n        padding: 15px;\r\n        display: flex;\r\n        border-top: 1px solid #e0e0e0;\r\n        background-color: white;\r\n    }\r\n\r\n    .chat-input input {\r\n        flex: 1;\r\n        padding: 10px 15px;\r\n        border: 1px solid #e0e0e0;\r\n        border-radius: 20px;\r\n        font-size: 14px;\r\n        outline: none;\r\n    }\r\n\r\n    .chat-input button {\r\n        margin-left: 10px;\r\n        width: 40px;\r\n        height: 40px;\r\n        border-radius: 50%;\r\n        background-color: #4286f4;\r\n        color: white;\r\n        border: none;\r\n        cursor: pointer;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n    }\r\n\r\n    .chat-input button:disabled {\r\n        background-color: #b3c9f4;\r\n        cursor: not-allowed;\r\n    }\r\n\r\n    .chat-input button i {\r\n        font-size: 16px;\r\n    }\r\n\r\n    /* 移动端适配 */\r\n    @media (max-width: 480px) {\r\n        .chat-window {\r\n            width: 100%;\r\n            height: 100%;\r\n            bottom: 0;\r\n            right: 0;\r\n            border-radius: 0;\r\n        }\r\n\r\n        .chat-icon {\r\n            bottom: 15px;\r\n            right: 15px;\r\n        }\r\n    }\r\n</style>"], "mappings": ";AAgFA;EACAA,IAAA;EAEAC,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACAC,QAAA,GACA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA,MAAAC,IAAA;MACA,EACA;MACAC,OAAA;MACAC,eAAA;MACAC,SAAA,GACA,cACA,aACA,YACA;MACAC,oBAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,QAAA;IACA;EACA;EACAC,cAAA;IACA,KAAAC,aAAA;EACA;EACAC,QAAA;IACA,KAAAC,aAAA;IACA;IACA,KAAAC,QAAA,CAAAC,cAAA;MACA,MAAAC,IAAA,GAAAF,QAAA,CAAAG,aAAA;MACAD,IAAA,CAAAE,EAAA;MACAF,IAAA,CAAAG,GAAA;MACAH,IAAA,CAAAI,IAAA;MACAN,QAAA,CAAAO,IAAA,CAAAC,WAAA,CAAAN,IAAA;IACA;EACA;EAEAO,OAAA;IACAC,MAAAC,KAAA;MACA,KAAAnB,oBAAA,GAAAmB,KAAA;MACA,KAAAC,aAAA;IACA;IACAb,cAAA;MACA,IAAAc,IAAA;MACA,KAAAhB,aAAA;MACA,KAAAJ,aAAA,GAAAqB,WAAA;QACA,KAAAD,IAAA,CAAAlB,QAAA;UACA,KAAAH,oBAAA,GACA,MAAAA,oBAAA,aAAAD,SAAA,CAAAwB,MAAA;UACAC,OAAA,CAAAC,GAAA,YAAAzB,oBAAA;UACAwB,OAAA,CAAAC,GAAA,aAAAJ,IAAA,CAAAlB,QAAA;QACA;MACA,QAAAD,gBAAA;IACA;IACAkB,cAAA;MACA,KAAAjB,QAAA;IACA;IACAuB,eAAA;MACA,KAAAvB,QAAA;IACA;IACAE,cAAA;MACA,SAAAJ,aAAA;QACA0B,aAAA,MAAA1B,aAAA;QACA,KAAAA,aAAA;MACA;IACA;IACA;IACA2B,qBAAAC,QAAA;MACA,KAAAtC,SAAA,GAAAsC,QAAA;MACA,KAAAC,WAAA;IACA;IACAC,WAAA;MACA,KAAAzC,QAAA,SAAAA,QAAA;MAEA,SAAAA,QAAA;QACA,KAAA0C,SAAA;UACA,KAAAC,cAAA;QACA;MACA;IACA;IAEA,MAAAH,YAAA;MACA,UAAAvC,SAAA,CAAA2C,IAAA,WAAArC,OAAA;;MAEA;MACA,KAAAL,QAAA,CAAA2C,IAAA;QACA1C,IAAA;QACAC,IAAA,OAAAH,SAAA;QACAI,IAAA,MAAAC,IAAA;MACA;MAEA,MAAAwC,YAAA,QAAA7C,SAAA;MACA,KAAAA,SAAA;MACA,KAAAM,OAAA;MACA;MACA,KAAAC,eAAA,CAAAqC,IAAA;QACAE,IAAA;QACAC,OAAA,EAAAF;MACA;;MAEA;MACA,MAAAG,WAAA;QACAC,KAAA;QACAhD,QAAA;UAAA6C,IAAA;UAAAC,OAAA;QAAA,WAAAxC,eAAA;QAAA;QACA2C,MAAA;QACAC,OAAA;UACAC,gBAAA;UAAA;UACAC,iBAAA;UAAA;UACA;UACAC,IAAA;QACA;MACA;MACA;MACA,KAAAb,SAAA;QACA,KAAAC,cAAA;MACA;MAEA;QACA;QACA;QACA,MAAAa,QAAA,SAAAC,KAAA;UACAC,MAAA;UACAC,OAAA;YACAC,aAAA;YACA;UACA;UACAC,IAAA,EAAAC,IAAA,CAAAC,SAAA,CAAAd,WAAA;QACA;;QAEA;QACA,MAAAe,MAAA,GAAAR,QAAA,CAAAK,IAAA,CAAAI,SAAA;QACA,MAAAC,OAAA,OAAAC,WAAA;QACA,MAAAC,eAAA,QAAAlE,QAAA,CAAA2C,IAAA;UACA1C,IAAA;UACAC,IAAA;UACAC,IAAA,MAAAC,IAAA;QACA;QACA;UACA;YAAA+D,IAAA;YAAAC;UAAA,UAAAN,MAAA,CAAAO,IAAA;UACA,IAAAF,IAAA;;UAEA;UACA,MAAAG,KAAA,GAAAN,OAAA,CAAAO,MAAA,CAAAH,KAAA;UACA,MAAAI,KAAA,GAAAF,KAAA,CAAAG,KAAA,OAAAC,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAjC,IAAA;UAEA,WAAAiC,IAAA,IAAAH,KAAA;YACA;cACA,MAAAI,UAAA,GAAAD,IAAA,CAAAE,KAAA,IAAAnC,IAAA;cACA,IAAAkC,UAAA,WAAAA,UAAA;cAEA,IAAA/E,IAAA,GAAA+D,IAAA,CAAAkB,KAAA,CAAAF,UAAA;cACA,IAAA/E,IAAA,CAAAkF,OAAA;gBACA,IAAAlF,IAAA,CAAAkF,OAAA,IAAAC,KAAA,CAAAC,iBAAA;kBACA;gBACA;gBACA,IAAApF,IAAA,CAAAkF,OAAA,IAAAC,KAAA,CAAAlC,OAAA;kBACA;gBACA;gBACA,KAAA9C,QAAA,CAAAkE,eAAA,EAAAhE,IAAA,IAAAL,IAAA,CAAAkF,OAAA,IAAAC,KAAA,CAAAlC,OAAA;cACA;YACA,SAAAoC,CAAA,GACA;UACA;QACA;QACA,KAAA5E,eAAA,CAAAqC,IAAA;UACAE,IAAA;UACAC,OAAA,OAAA9C,QAAA,CAAAkE,eAAA,EAAAhE;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;MACA,SAAAiF,KAAA;QAEA;QACA,KAAAnF,QAAA,CAAA2C,IAAA;UACA1C,IAAA;UACAC,IAAA;UACAC,IAAA,MAAAC,IAAA;QACA;MACA;QACA,KAAAC,OAAA;;QAEA;QACA,KAAAmC,SAAA;UACA,KAAAC,cAAA;QACA;MACA;IACA;IAEA;IACA,MAAA2C,YAAAC,OAAA;MACA;MACA,UAAAC,OAAA,CAAAC,OAAA,IAAAC,UAAA,CAAAD,OAAA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,mBAAAF,OAAA;IACA;IAEA5C,eAAA;MACA,MAAAgD,SAAA,QAAAC,KAAA,CAAAC,iBAAA;MACAF,SAAA,CAAAG,SAAA,GAAAH,SAAA,CAAAI,YAAA;IACA;IAEAC,WAAAC,IAAA;MACA,WAAA3F,IAAA,CAAA2F,IAAA,EAAAC,kBAAA;QAAAC,IAAA;QAAAC,MAAA;MAAA;IACA;IAEAC,cAAAjG,IAAA;MACA;MACA,OAAAA,IAAA,CACAkG,OAAA,gBACAA,OAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}