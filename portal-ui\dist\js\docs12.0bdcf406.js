"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[261],{2733:function(o,t,n){n.r(t);var f=new URL(n(2908),n.b),l=new URL(n(6693),n.b),p=new URL(n(2878),n.b),r='<h1 id="个人用户实名">个人用户实名</h1> <h2 id="1-个人实名认证"><font style="color:#020817">1. 个人实名认证</font></h2> <p><font style="color:#020817">个人实名认证是对用户身份信息真实性验证的一种方式，对姓名、身份证号、手机号信息进行真实性和一致性的核验。</font></p> <p><font style="color:#020817">通过个人实名认证后您可以更方便地享受到天工开物平台的所有服务。</font></p> <h2 id="2-实名认证步骤"><font style="color:#020817">2. 实名认证步骤</font></h2> <h3 id="21-进入实名认证页面"><font style="color:#020817">2.1. 进入实名认证页面</font></h3> <p><font style="color:#020817">首先，需要您登录进天工开物平台，然后点击平台右上角账户信息菜单，选择【个人认证】子菜单。进入到实名认证页面。</font></p> <p><img src="'+f+'" alt=""></p> <p><img src="'+l+'" alt=""></p> <h3 id="22-开始认证"><font style="color:#020817">2.2. 开始认证</font></h3> <p><font style="color:#020817">天工开物平台实名认证包含三个必填认证要素，分别是：</font></p> <p><strong><font style="color:#020817">真实姓名</font></strong></p> <p><font style="color:#020817">【真实姓名】与您身份证上的名称一致。</font></p> <p><strong><font style="color:#020817">身份证号</font></strong></p> <p><font style="color:#020817">当前天工开物平台只支持身份证作为唯一认证证件类型，请按照您身份证上的号码填入【证件号码】输入框。</font></p> <p><strong><font style="color:#020817">阅读并同意天工开物的服务条款和隐私政策</font></strong></p> <p><font style="color:#020817">服务和隐私协议中详细介绍了本平台提供的服务以及收取的信息。</font></p> <p><font style="color:#020817">以上所有信息输入完成后点击【提交】即可。</font></p> <h2 id="23-认证成功"><font style="color:#020817">2.3. 认证成功</font></h2> <p><img src="'+p+'" alt=""></p> <font style="color:#020817"> </font> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/23 11:21</font></p> ';t["default"]=r},2908:function(o,t,n){o.exports=n.p+"img/certification1.dd0db7b8.png"},6693:function(o,t,n){o.exports=n.p+"img/certification2.f0001862.png"},2878:function(o,t,n){o.exports=n.p+"img/certification3.73409cad.png"}}]);
//# sourceMappingURL=docs12.0bdcf406.js.map