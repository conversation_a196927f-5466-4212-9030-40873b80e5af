{"ast": null, "code": "export default {\n  props: {\n    doc: String,\n    prevPage: Object,\n    nextPage: Object\n  },\n  data() {\n    return {\n      markdownContent: '',\n      loading: false,\n      error: null\n    };\n  },\n  watch: {\n    doc: {\n      immediate: true,\n      async handler(doc) {\n        this.loading = true;\n        this.error = null;\n        try {\n          const module = await import( /* webpackChunkName: \"docs\" */`../docs/${doc}.md`);\n          this.markdownContent = module.default;\n          this.$nextTick(() => {\n            this.processContent();\n            this.$emit('content-loaded');\n          });\n        } catch (e) {\n          this.error = e.message;\n          this.markdownContent = '<h1>文档加载失败</h1>';\n        } finally {\n          this.loading = false;\n        }\n      }\n    }\n  },\n  methods: {\n    slugify(text) {\n      let slug = text.toLowerCase().replace(/\\s+/g, '-') // 空格转为-\n      .replace(/[^a-z0-9\\-]+/g, '') // 只保留小写字母、数字、短横线\n      .replace(/\\-\\-+/g, '-') // 多个-合并为一个\n      .replace(/^-+/, '') // 去除开头-\n      .replace(/-+$/, ''); // 去除结尾-\n      if (!slug || /^[0-9]+$/.test(slug)) {\n        // 添加随机数确保唯一性\n        slug = 'content-section-' + slug + '-' + Math.random().toString(36).substring(2, 11);\n      }\n      return slug;\n    },\n    processContent() {\n      if (this.$refs.contentRef) {\n        const headings = this.$refs.contentRef.querySelectorAll('h2, h3');\n        headings.forEach(heading => {\n          heading.id = this.slugify(heading.textContent);\n        });\n        const codeBlocks = this.$refs.contentRef.querySelectorAll('pre code');\n        codeBlocks.forEach(block => {\n          block.classList.add('hljs');\n        });\n        const links = this.$refs.contentRef.querySelectorAll('a[href^=\"#\"]');\n        links.forEach(link => {\n          link.addEventListener('click', e => {\n            e.preventDefault();\n            const id = link.getAttribute('href').substring(1);\n            const targetElement = document.getElementById(id);\n            if (targetElement) {\n              targetElement.scrollIntoView({\n                behavior: 'smooth'\n              });\n            }\n          });\n        });\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["props", "doc", "String", "prevPage", "Object", "nextPage", "data", "markdownContent", "loading", "error", "watch", "immediate", "handler", "module", "default", "$nextTick", "processContent", "$emit", "e", "message", "methods", "slugify", "text", "slug", "toLowerCase", "replace", "test", "Math", "random", "toString", "substring", "$refs", "contentRef", "headings", "querySelectorAll", "for<PERSON>ach", "heading", "id", "textContent", "codeBlocks", "block", "classList", "add", "links", "link", "addEventListener", "preventDefault", "getAttribute", "targetElement", "document", "getElementById", "scrollIntoView", "behavior"], "sources": ["src/views/HelpContent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"doc-content\">\r\n    <div v-if=\"loading\" class=\"loading\">文档加载中...</div>\r\n    <div v-else-if=\"error\" class=\"error\">文档加载失败: {{ error }}</div>\r\n    <div v-else>\r\n      <div v-html=\"markdownContent\" ref=\"contentRef\"></div>\r\n      \r\n      <!-- 上一页/下一页导航 -->\r\n      <div class=\"page-navigation\">\r\n        <div class=\"prev-next-container\">\r\n          <router-link \r\n            v-if=\"prevPage\" \r\n            :to=\"prevPage.path\" \r\n            class=\"prev-page\">\r\n            <div class=\"nav-label\">上一篇</div>\r\n            <div class=\"nav-title\">{{ prevPage.name }}</div>\r\n          </router-link>\r\n          <div v-else class=\"prev-page empty\"></div>\r\n          \r\n          <router-link \r\n            v-if=\"nextPage\" \r\n            :to=\"nextPage.path\" \r\n            class=\"next-page\">\r\n            <div class=\"nav-label\">下一篇</div>\r\n            <div class=\"nav-title\">{{ nextPage.name }}</div>\r\n          </router-link>\r\n          <div v-else class=\"next-page empty\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    doc: String,\r\n    prevPage: Object,\r\n    nextPage: Object\r\n  },\r\n  data() {\r\n    return {\r\n      markdownContent: '',\r\n      loading: false,\r\n      error: null\r\n    };\r\n  },\r\n  watch: {\r\n    doc: {\r\n      immediate: true,\r\n      async handler(doc) {\r\n        this.loading = true;\r\n        this.error = null;\r\n        try {\r\n          const module = await import(/* webpackChunkName: \"docs\" */ `../docs/${doc}.md`);\r\n          this.markdownContent = module.default;\r\n          this.$nextTick(() => {\r\n            this.processContent();\r\n            this.$emit('content-loaded');\r\n          });\r\n        } catch (e) {\r\n          this.error = e.message;\r\n          this.markdownContent = '<h1>文档加载失败</h1>';\r\n        } finally {\r\n          this.loading = false;\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    slugify(text) {\r\n      let slug = text\r\n        .toLowerCase()\r\n        .replace(/\\s+/g, '-')           // 空格转为-\r\n        .replace(/[^a-z0-9\\-]+/g, '')   // 只保留小写字母、数字、短横线\r\n        .replace(/\\-\\-+/g, '-')         // 多个-合并为一个\r\n        .replace(/^-+/, '')              // 去除开头-\r\n        .replace(/-+$/, '');             // 去除结尾-\r\n      if (!slug || /^[0-9]+$/.test(slug)) {\r\n        // 添加随机数确保唯一性\r\n        slug = 'content-section-' + slug + '-' + Math.random().toString(36).substring(2, 11);\r\n      }\r\n      return slug;\r\n    },\r\n    processContent() {\r\n      if (this.$refs.contentRef) {\r\n        const headings = this.$refs.contentRef.querySelectorAll('h2, h3');\r\n        headings.forEach(heading => {\r\n          heading.id = this.slugify(heading.textContent);\r\n        });\r\n        \r\n        const codeBlocks = this.$refs.contentRef.querySelectorAll('pre code');\r\n        codeBlocks.forEach(block => {\r\n          block.classList.add('hljs');\r\n        });\r\n        \r\n        const links = this.$refs.contentRef.querySelectorAll('a[href^=\"#\"]');\r\n        links.forEach(link => {\r\n          link.addEventListener('click', (e) => {\r\n            e.preventDefault();\r\n            const id = link.getAttribute('href').substring(1);\r\n            const targetElement = document.getElementById(id);\r\n            if (targetElement) {\r\n              targetElement.scrollIntoView({ behavior: 'smooth' });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n/* 注意：这里不使用scoped，以便样式应用到动态生成的内容 */\r\n.doc-content h1 {\r\n  font-size: 28px;\r\n  margin-bottom: 20px;\r\n  color: #333;\r\n}\r\n\r\n.doc-content h2 {\r\n  font-size: 22px;\r\n  margin: 30px 0 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #eee;\r\n  color: #333;\r\n}\r\n\r\n.doc-content h3 {\r\n  font-size: 18px;\r\n  margin: 25px 0 15px;\r\n  color: #333;\r\n}\r\n\r\n.doc-content p {\r\n  margin: 15px 0;\r\n  line-height: 1.6;\r\n  color: #555;\r\n}\r\n\r\n.doc-content ul, .doc-content ol {\r\n  padding-left: 25px;\r\n  margin: 15px 0;\r\n}\r\n\r\n.doc-content li {\r\n  margin-bottom: 8px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.doc-content code {\r\n  background-color: #f5f5f5;\r\n  padding: 2px 5px;\r\n  border-radius: 3px;\r\n  font-family: Consolas, Monaco, 'Andale Mono', monospace;\r\n  color: #d63384;\r\n}\r\n\r\n.doc-content pre {\r\n  background-color: #f8f8f8;\r\n  padding: 15px;\r\n  border-radius: 5px;\r\n  overflow-x: auto;\r\n  margin: 20px 0;\r\n}\r\n\r\n.doc-content pre code {\r\n  background-color: transparent;\r\n  padding: 0;\r\n  color: #333;\r\n  display: block;\r\n}\r\n\r\n.doc-content a {\r\n  color: #1890ff;\r\n  text-decoration: none;\r\n}\r\n\r\n.doc-content a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.doc-content blockquote {\r\n  border-left: 4px solid #1890ff !important;\r\n  padding: 16px 20px 16px 50px !important;\r\n  color: #555 !important;\r\n  background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%) !important;\r\n  margin: 20px 0 !important;\r\n  border-radius: 8px !important;\r\n  font-family: inherit !important;\r\n  position: relative !important;\r\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1) !important;\r\n  border: 1px solid rgba(24, 144, 255, 0.2) !important;\r\n}\r\n\r\n.doc-content blockquote::before {\r\n  content: 'ℹ️';\r\n  position: absolute;\r\n  left: 16px;\r\n  top: 16px;\r\n  font-size: 16px;\r\n  background: #1890ff;\r\n  color: white;\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);\r\n}\r\n\r\n.doc-content blockquote p {\r\n  margin: 0 !important;\r\n  line-height: 1.7 !important;\r\n  font-size: 14px !important;\r\n}\r\n\r\n.doc-content blockquote p:not(:last-child) {\r\n  margin-bottom: 12px !important;\r\n}\r\n\r\n/* 确保引用块内的font标签样式正常显示 */\r\n.doc-content blockquote font {\r\n  color: inherit !important;\r\n}\r\n\r\n/* 引用块内的链接样式 */\r\n.doc-content blockquote a {\r\n  color: #1890ff !important;\r\n  text-decoration: none !important;\r\n  font-weight: 500 !important;\r\n}\r\n\r\n.doc-content blockquote a:hover {\r\n  text-decoration: underline !important;\r\n  color: #40a9ff !important;\r\n}\r\n\r\n/* 引用块内的代码样式 */\r\n.doc-content blockquote code {\r\n  background-color: rgba(255, 255, 255, 0.8) !important;\r\n  padding: 2px 6px !important;\r\n  border-radius: 3px !important;\r\n  border: 1px solid rgba(24, 144, 255, 0.2) !important;\r\n  color: #1890ff !important;\r\n  font-weight: 500 !important;\r\n}\r\n\r\n/* 引用块内的列表样式 */\r\n.doc-content blockquote ul,\r\n.doc-content blockquote ol {\r\n  margin: 8px 0 !important;\r\n  padding-left: 20px !important;\r\n}\r\n\r\n.doc-content blockquote li {\r\n  margin-bottom: 4px !important;\r\n  line-height: 1.6 !important;\r\n}\r\n\r\n.doc-content table {\r\n  border-collapse: collapse;\r\n  width: 100%;\r\n  margin: 20px 0;\r\n}\r\n\r\n.doc-content table th, .doc-content table td {\r\n  border: 1px solid #ddd;\r\n  padding: 10px;\r\n  text-align: left;\r\n}\r\n\r\n.doc-content table th {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.loading, .error {\r\n  padding: 20px;\r\n  text-align: center;\r\n  font-size: 18px;\r\n}\r\n\r\n.error {\r\n  color: red;\r\n}\r\n\r\n/* 上一页/下一页导航样式 */\r\n.page-navigation {\r\n  /* margin-top: 60px; */\r\n  padding-top: 20px;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.prev-next-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.prev-page, .next-page {\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 45%;\r\n  padding: 15px;\r\n  border: 1px solid #eee;\r\n  border-radius: 5px;\r\n  text-decoration: none;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.prev-page {\r\n  text-align: left;\r\n}\r\n\r\n.next-page {\r\n  text-align: right;\r\n}\r\n\r\n.prev-page:hover, .next-page:hover {\r\n  border-color: #1890ff;\r\n  background-color: #f0f8ff;\r\n}\r\n\r\n.nav-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.nav-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #1890ff;\r\n}\r\n\r\n.empty {\r\n  visibility: hidden;\r\n}\r\n\r\n.doc-content img {\r\n  display: block;\r\n  max-width: 100%;\r\n  height: auto;\r\n  margin: 24px auto;  /* 上下间距24px，左右自动居中 */\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.04);\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 引用块响应式样式 */\r\n@media (max-width: 768px) {\r\n  .doc-content blockquote {\r\n    padding: 12px 16px 12px 40px !important;\r\n    margin: 16px 0 !important;\r\n    border-radius: 6px !important;\r\n  }\r\n\r\n  .doc-content blockquote::before {\r\n    left: 12px !important;\r\n    top: 12px !important;\r\n    width: 20px !important;\r\n    height: 20px !important;\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  .doc-content blockquote p {\r\n    font-size: 13px !important;\r\n    line-height: 1.6 !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .doc-content blockquote {\r\n    padding: 10px 12px 10px 36px !important;\r\n    margin: 12px 0 !important;\r\n  }\r\n\r\n  .doc-content blockquote::before {\r\n    left: 10px !important;\r\n    top: 10px !important;\r\n    width: 18px !important;\r\n    height: 18px !important;\r\n    font-size: 10px !important;\r\n  }\r\n}\r\n</style>"], "mappings": "AAkCA;EACAA,KAAA;IACAC,GAAA,EAAAC,MAAA;IACAC,QAAA,EAAAC,MAAA;IACAC,QAAA,EAAAD;EACA;EACAE,KAAA;IACA;MACAC,eAAA;MACAC,OAAA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAT,GAAA;MACAU,SAAA;MACA,MAAAC,QAAAX,GAAA;QACA,KAAAO,OAAA;QACA,KAAAC,KAAA;QACA;UACA,MAAAI,MAAA,0DAAAZ,GAAA;UACA,KAAAM,eAAA,GAAAM,MAAA,CAAAC,OAAA;UACA,KAAAC,SAAA;YACA,KAAAC,cAAA;YACA,KAAAC,KAAA;UACA;QACA,SAAAC,CAAA;UACA,KAAAT,KAAA,GAAAS,CAAA,CAAAC,OAAA;UACA,KAAAZ,eAAA;QACA;UACA,KAAAC,OAAA;QACA;MACA;IACA;EACA;EACAY,OAAA;IACAC,QAAAC,IAAA;MACA,IAAAC,IAAA,GAAAD,IAAA,CACAE,WAAA,GACAC,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MACA,KAAAF,IAAA,eAAAG,IAAA,CAAAH,IAAA;QACA;QACAA,IAAA,wBAAAA,IAAA,SAAAI,IAAA,CAAAC,MAAA,GAAAC,QAAA,KAAAC,SAAA;MACA;MACA,OAAAP,IAAA;IACA;IACAP,eAAA;MACA,SAAAe,KAAA,CAAAC,UAAA;QACA,MAAAC,QAAA,QAAAF,KAAA,CAAAC,UAAA,CAAAE,gBAAA;QACAD,QAAA,CAAAE,OAAA,CAAAC,OAAA;UACAA,OAAA,CAAAC,EAAA,QAAAhB,OAAA,CAAAe,OAAA,CAAAE,WAAA;QACA;QAEA,MAAAC,UAAA,QAAAR,KAAA,CAAAC,UAAA,CAAAE,gBAAA;QACAK,UAAA,CAAAJ,OAAA,CAAAK,KAAA;UACAA,KAAA,CAAAC,SAAA,CAAAC,GAAA;QACA;QAEA,MAAAC,KAAA,QAAAZ,KAAA,CAAAC,UAAA,CAAAE,gBAAA;QACAS,KAAA,CAAAR,OAAA,CAAAS,IAAA;UACAA,IAAA,CAAAC,gBAAA,UAAA3B,CAAA;YACAA,CAAA,CAAA4B,cAAA;YACA,MAAAT,EAAA,GAAAO,IAAA,CAAAG,YAAA,SAAAjB,SAAA;YACA,MAAAkB,aAAA,GAAAC,QAAA,CAAAC,cAAA,CAAAb,EAAA;YACA,IAAAW,aAAA;cACAA,aAAA,CAAAG,cAAA;gBAAAC,QAAA;cAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}