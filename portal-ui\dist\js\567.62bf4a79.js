"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[567],{6963:function(t,r,s){s.d(r,{Z:function(){return p}});var o=function(){var t=this,r=t._self._c;t._self._setupProxy;return r("div",{staticClass:"login-left-side"},[r("div",{staticClass:"logo-container"},[r("a",{staticClass:"logo-link",on:{click:function(r){return t.navigateTo("/index")}}},[r("img",{staticClass:"logo",attrs:{src:s(2504),alt:"算力租赁"}})])]),t._m(0),r("div",{staticClass:"visual-element"},[r("div",{staticClass:"server-illustration"},t._l(t.servers,(function(t,s){return r("div",{key:s,staticClass:"server-unit",style:{animationDelay:.2*s+"s",transform:`translateY(${4*s}px)`}},[r("div",{staticClass:"server-light"})])})),0),r("div",{staticClass:"connections"})]),r("div",{staticClass:"features"},t._l(t.features,(function(s,o){return r("div",{key:o,staticClass:"feature-item"},[r("div",{staticClass:"feature-text"},[r("h3",[t._v(t._s(s.title))]),r("p",[t._v(t._s(s.description))])])])})),0),r("div",{staticClass:"background-elements"},t._l(20,(function(t){return r("div",{key:t,staticClass:"floating-particle",style:{left:100*Math.random()+"%",top:100*Math.random()+"%",animationDuration:3+10*Math.random()+"s",animationDelay:5*Math.random()+"s"}})})),0)])},e=[function(){var t=this,r=t._self._c;t._self._setupProxy;return r("div",{staticClass:"bottom-text"},[r("h2",{staticClass:"slogan"},[t._v("高效算力 · 智慧未来")]),r("p",{staticClass:"sub-slogan"},[t._v("专业算力租赁服务，为您的业务提供强大支持")])])}],i=(s(7658),s(144));const a={template:'\n    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n      <path d="M12 2v4"></path>\n      <path d="m16.24 7.76 2.83-2.83"></path>\n      <path d="M18 12h4"></path>\n      <path d="m16.24 16.24 2.83 2.83"></path>\n      <path d="M12 18v4"></path>\n      <path d="m7.76 16.24-2.83 2.83"></path>\n      <path d="M6 12H2"></path>\n      <path d="m7.76 7.76-2.83-2.83"></path>\n      <circle cx="12" cy="12" r="4"></circle>\n    </svg>\n  '},n={template:'\n    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n      <rect width="20" height="8" x="2" y="2" rx="2" ry="2"></rect>\n      <rect width="20" height="8" x="2" y="14" rx="2" ry="2"></rect>\n      <line x1="6" x2="6" y1="6" y2="6"></line>\n      <line x1="6" x2="6" y1="18" y2="18"></line>\n    </svg>\n  '},c={template:'\n    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>\n    </svg>\n  '};var d=(0,i.aZ)({name:"backgroundlogin",components:{PerformanceIcon:a,ServerIcon:n,ShieldIcon:c},methods:{navigateTo(t){this.currentPath&&this.currentPath!==t?(this.previousActivePath=this.currentPath,this.$nextTick((()=>{const r=document.querySelectorAll(".nav-link, .btn-login");r.forEach((r=>{(r.classList.contains("active")||"/login"===t&&r.classList.contains("btn-login"))&&!r.classList.contains("active-exit")&&(r.classList.add("active-exit"),setTimeout((()=>{r.classList.remove("active-exit")}),300))})),this.currentPath=t}))):this.currentPath=t,this.$route.path===t?this.$nextTick((()=>{window.scrollTo({top:0,behavior:"instant"}),this.$router.go(0)})):(this.$router.push(t),window.scrollTo({top:0,behavior:"instant"}))}},setup(){const t=(0,i.iH)("/api/placeholder/100/100"),r=(0,i.iH)(Array(5).fill(null)),s=(0,i.iH)([{icon:"PerformanceIcon",title:"高性能算力",description:"提供GPU/CPU灵活配置，满足AI训练、渲染等高算力需求"},{icon:"am-icon-shield",title:"安全可靠",description:"数据加密传输，多重备份，确保您的业务安全稳定运行"}]);return{logoSrc:t,servers:r,features:s}}}),l=d,h=s(1001),m=(0,h.Z)(l,o,e,!1,null,"771899f4",null),p=m.exports},2567:function(t,r,s){s.r(r),s.d(r,{default:function(){return m}});var o=function(){var t=this,r=t._self._c;return r("div",{staticClass:"login-page"},[r("div",{staticClass:"left-side"},[r("backgroundlogin")],1),r("div",{staticClass:"right-side"},[r("div",{staticClass:"login-form-container"},[r("h3",[t._v("注册 天工开物")]),r("div",{staticClass:"form-container"},[r("div",{staticClass:"login-form"},[r("p",{staticClass:"form-note"},[t._v("只需一个 天工开物 账号，即可访问 天工开物 的所有服务。")]),r("div",{staticClass:"input-group",class:{error:t.errors.phone}},[r("div",{staticClass:"phone-input-container"},[r("input",{directives:[{name:"model",rawName:"v-model",value:t.registrationForm.phone,expression:"registrationForm.phone"}],attrs:{type:"text",placeholder:"请输入手机号"},domProps:{value:t.registrationForm.phone},on:{blur:t.validatePhone,input:function(r){r.target.composing||t.$set(t.registrationForm,"phone",r.target.value)}}})]),r("div",{staticClass:"error-container"},[t.errors.phone?r("div",{staticClass:"error-message"},[t._v(t._s(t.errors.phone))]):t._e()])]),r("div",{staticClass:"input-group",class:{error:t.errors.password}},[r("div",{staticClass:"password-input-container"},["checkbox"===(t.passwordVisible?"text":"password")?r("input",{directives:[{name:"model",rawName:"v-model",value:t.registrationForm.password,expression:"registrationForm.password"}],attrs:{placeholder:"请输入密码",type:"checkbox"},domProps:{checked:Array.isArray(t.registrationForm.password)?t._i(t.registrationForm.password,null)>-1:t.registrationForm.password},on:{blur:t.validatePassword,change:function(r){var s=t.registrationForm.password,o=r.target,e=!!o.checked;if(Array.isArray(s)){var i=null,a=t._i(s,i);o.checked?a<0&&t.$set(t.registrationForm,"password",s.concat([i])):a>-1&&t.$set(t.registrationForm,"password",s.slice(0,a).concat(s.slice(a+1)))}else t.$set(t.registrationForm,"password",e)}}}):"radio"===(t.passwordVisible?"text":"password")?r("input",{directives:[{name:"model",rawName:"v-model",value:t.registrationForm.password,expression:"registrationForm.password"}],attrs:{placeholder:"请输入密码",type:"radio"},domProps:{checked:t._q(t.registrationForm.password,null)},on:{blur:t.validatePassword,change:function(r){return t.$set(t.registrationForm,"password",null)}}}):r("input",{directives:[{name:"model",rawName:"v-model",value:t.registrationForm.password,expression:"registrationForm.password"}],attrs:{placeholder:"请输入密码",type:t.passwordVisible?"text":"password"},domProps:{value:t.registrationForm.password},on:{blur:t.validatePassword,input:function(r){r.target.composing||t.$set(t.registrationForm,"password",r.target.value)}}}),r("span",{staticClass:"password-toggle",on:{click:t.togglePasswordVisibility}},[r("i",{class:["eye-icon",t.passwordVisible?"visible":""]})])]),r("div",{staticClass:"error-container"},[t.errors.password?r("div",{staticClass:"error-message"},[t._v(t._s(t.errors.password))]):t._e()])]),r("div",{staticClass:"input-group",class:{error:t.errors.confirmPassword}},[r("div",{staticClass:"password-input-container"},["checkbox"===(t.confirmPasswordVisible?"text":"password")?r("input",{directives:[{name:"model",rawName:"v-model",value:t.registrationForm.confirmPassword,expression:"registrationForm.confirmPassword"}],attrs:{placeholder:"请再次输入密码",type:"checkbox"},domProps:{checked:Array.isArray(t.registrationForm.confirmPassword)?t._i(t.registrationForm.confirmPassword,null)>-1:t.registrationForm.confirmPassword},on:{blur:t.validateConfirmPassword,change:function(r){var s=t.registrationForm.confirmPassword,o=r.target,e=!!o.checked;if(Array.isArray(s)){var i=null,a=t._i(s,i);o.checked?a<0&&t.$set(t.registrationForm,"confirmPassword",s.concat([i])):a>-1&&t.$set(t.registrationForm,"confirmPassword",s.slice(0,a).concat(s.slice(a+1)))}else t.$set(t.registrationForm,"confirmPassword",e)}}}):"radio"===(t.confirmPasswordVisible?"text":"password")?r("input",{directives:[{name:"model",rawName:"v-model",value:t.registrationForm.confirmPassword,expression:"registrationForm.confirmPassword"}],attrs:{placeholder:"请再次输入密码",type:"radio"},domProps:{checked:t._q(t.registrationForm.confirmPassword,null)},on:{blur:t.validateConfirmPassword,change:function(r){return t.$set(t.registrationForm,"confirmPassword",null)}}}):r("input",{directives:[{name:"model",rawName:"v-model",value:t.registrationForm.confirmPassword,expression:"registrationForm.confirmPassword"}],attrs:{placeholder:"请再次输入密码",type:t.confirmPasswordVisible?"text":"password"},domProps:{value:t.registrationForm.confirmPassword},on:{blur:t.validateConfirmPassword,input:function(r){r.target.composing||t.$set(t.registrationForm,"confirmPassword",r.target.value)}}}),r("span",{staticClass:"password-toggle",on:{click:t.toggleConfirmPasswordVisibility}},[r("i",{class:["eye-icon",t.confirmPasswordVisible?"visible":""]})])]),r("div",{staticClass:"error-container"},[t.errors.confirmPassword?r("div",{staticClass:"error-message"},[t._v(t._s(t.errors.confirmPassword))]):t._e()])]),r("div",{staticClass:"input-group verification-code",class:{error:t.errors.code}},[r("div",{staticClass:"code-input-container"},[r("input",{directives:[{name:"model",rawName:"v-model",value:t.registrationForm.code,expression:"registrationForm.code"}],attrs:{type:"text",placeholder:"请输入验证码"},domProps:{value:t.registrationForm.code},on:{blur:t.validateCodegeshi,input:function(r){r.target.composing||t.$set(t.registrationForm,"code",r.target.value)}}}),r("button",{staticClass:"get-code-btn-inline",attrs:{disabled:!t.registrationForm.phone||t.errors.phone||t.codeSent},on:{click:t.getVerificationCode}},[t._v(" "+t._s(t.codeSent?`${t.countdown}秒后重试`:"获取验证码")+" ")])]),r("div",{staticClass:"error-container"},[t.errors.code?r("div",{staticClass:"error-message"},[t._v(t._s(t.errors.code))]):t._e()])]),r("div",{staticClass:"agreement-text"},[t._v(" 注册视为您已阅读并同意天工开物 "),r("router-link",{attrs:{to:"/help/user-agreement"}},[t._v("服务条款")]),t._v(" 和"),r("router-link",{attrs:{to:"/help/privacy-policy"}},[t._v("隐私政策")])],1),r("button",{staticClass:"register-btn",on:{click:t.register}},[t._v(" 注册 ")]),r("div",{staticClass:"login-link"},[r("a",{attrs:{href:"#"},on:{click:function(r){return t.navigateTo("/forgetpass")}}},[t._v("忘记密码")]),r("span",{staticClass:"divider"},[t._v("|")]),r("a",{attrs:{href:"#"},on:{click:function(r){return t.navigateTo("/login")}}},[t._v("返回登录")])])])])])]),t.showCodeSent?r("SlideNotification",{attrs:{message:"验证码已发送，可能会有延迟，请耐心等待！",type:"success",duration:3e3,minHeight:t.minHeight},on:{close:function(r){t.showCodeSent=!1}}}):t._e()],1)},e=[],i=(s(7658),s(2223)),a=(s(1836),s(7234)),n=s(6963),c={name:"register",components:{SlideNotification:a.Z,backgroundlogin:n.Z},data(){return{registrationForm:{phone:"",password:"",confirmPassword:"",code:"",username:"user1",usage:"商业办公",otherUsage:"",agreement:!1},isSendingCode:!1,passwordVisible:!1,confirmPasswordVisible:!1,errors:{phone:"",password:"",confirmPassword:"",code:""},codeSent:!1,countdown:60,timer:null,showCodeSent:!1,minHeight:"50px"}},watch:{"registrationForm.phone"(t){this.registrationForm.username=t}},created(){this.checkExistingTimer(),this.$emit("hiden-layout")},computed:{isFormValid(){return this.registrationForm.phone&&this.registrationForm.password&&this.registrationForm.confirmPassword&&this.registrationForm.code&&this.registrationForm.agreement&&!this.errors.phone&&!this.errors.password&&!this.errors.confirmPassword&&!this.errors.code}},methods:{checkExistingTimer(){const t=localStorage.getItem("verificationCodeData");if(t){const r=JSON.parse(t);if(r.phone&&this.registrationForm.phone&&r.phone===this.registrationForm.phone){const t=Date.now(),s=r.timestamp+6e4;if(t<s){const r=Math.ceil((s-t)/1e3);this.countdown=r,this.codeSent=!0,this.startCountdown()}else localStorage.removeItem("verificationCodeData")}}},validatePhone(){const t=/^1[3-9]\d{9}$/;this.registrationForm.phone?t.test(this.registrationForm.phone)?this.errors.phone="":this.errors.phone="请输入有效的手机号":this.errors.phone="请输入手机号"},validatePassword(){const t=this.registrationForm.password;if(this.errors.password="",!t)return void(this.errors.password="请输入密码");if(t.length<8)return void(this.errors.password="密码长度至少为8位");const r=/[a-zA-Z]/.test(t),s=/[0-9]/.test(t),o=/[!@#$%^&*()_+\-=$${};':"\\|,.<>\/?]/.test(t),e=/[A-Z]/.test(t)&&/[a-z]/.test(t);let i=0;if(r&&s&&i++,o&&i++,e&&i++,i>=2)this.errors.password="";else{const t=[];r&&s||t.push("包含数字和字母"),o||t.push("包含特殊符号"),e||t.push("包含大小写字母"),this.errors.password=`密码强度不足，请满足以下至少两项要求：${t.join("、")}`}this.registrationForm.confirmPassword&&this.validateConfirmPassword()},navigateTo(t){this.$route.path===t?this.$nextTick((()=>{window.scrollTo({top:0,behavior:"instant"}),this.$router.go(0)})):(this.$router.push(t),window.scrollTo({top:0,behavior:"instant"})),this.currentPath=t},validateConfirmPassword(){this.registrationForm.confirmPassword?this.registrationForm.confirmPassword!==this.registrationForm.password?this.errors.confirmPassword="两次输入的密码不一致":this.errors.confirmPassword="":this.errors.confirmPassword="请再次输入密码"},async validateCodegeshi(){return this.errors.code="",this.registrationForm.code?4===this.registrationForm.code.length&&/^\d+$/.test(this.registrationForm.code)?void 0:(this.errors.code="验证码必须为4位数字",!1):(this.errors.code="请输入验证码",!1)},async getVerificationCode(){const t=localStorage.getItem("verificationCodeData");if(t){const r=JSON.parse(t);if(r.phone===this.registrationForm.phone){const t=Date.now(),s=r.timestamp+6e4;if(t<s){const r=Math.ceil((s-t)/1e3);return this.countdown=r,this.codeSent=!0,void this.startCountdown()}}}if(this.validatePhone(),!this.errors.phone)try{this.isSendingCode=!0;const t=await(0,i.SV)("/auth/sendCode",{phone:this.registrationForm.phone});if(200===t.data.code){const t={phone:this.registrationForm.phone,timestamp:Date.now()};localStorage.setItem("verificationCodeData",JSON.stringify(t)),this.codeSent=!0,this.showCodeSent=!0,this.startCountdown()}else this.errors.code=t.data.msg||"验证码发送失败"}catch(r){this.errors.code="网络异常，请稍后重试"}finally{this.isSendingCode=!1}},startCountdown(){this.timer&&clearInterval(this.timer),this.timer=setInterval((()=>{if(this.countdown<=1)clearInterval(this.timer),this.codeSent=!1,this.countdown=60,localStorage.removeItem("verificationCodeData");else{this.countdown--;const t=localStorage.getItem("verificationCodeData");if(t){const r=JSON.parse(t);r.phone,this.registrationForm.phone}}}),1e3)},register(){this.validatePhone(),this.validatePassword(),this.validateConfirmPassword(),this.errors.code="",(0,i.SV)("/auth/verifyCode",{phone:this.registrationForm.phone,code:this.registrationForm.code}).then((t=>{200===t.data.code?(0,i.km)("/auth/register",this.registrationForm).then((t=>{200===t.data.code?(localStorage.removeItem("verificationCodeData"),this.$router.push("/login")):this.errors.code=t.data.msg})):this.errors.code=t.data.msg||"验证码错误"}))},togglePasswordVisibility(){this.passwordVisible=!this.passwordVisible},toggleConfirmPasswordVisibility(){this.confirmPasswordVisible=!this.confirmPasswordVisible},goToLogin(){}},beforeDestroy(){this.timer&&clearInterval(this.timer),this.$emit("hiden-layout")}},d=c,l=s(1001),h=(0,l.Z)(d,o,e,!1,null,"40892a8f",null),m=h.exports},2504:function(t,r,s){t.exports=s.p+"img/logo_tiangong.48cfbe63.png"}}]);
//# sourceMappingURL=567.62bf4a79.js.map