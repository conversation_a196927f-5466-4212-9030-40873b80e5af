{"ast": null, "code": "export default {\n  name: \"SlideNotification\",\n  props: {\n    message: {\n      type: String,\n      required: true\n    },\n    type: {\n      type: String,\n      default: 'info',\n      validator: value => ['success', 'warning', 'error', 'info'].includes(value)\n    },\n    duration: {\n      type: Number,\n      default: 4000\n    },\n    closable: {\n      type: Boolean,\n      default: true\n    },\n    minHeight: {\n      type: String,\n      default: 'auto'\n    }\n  },\n  data() {\n    return {\n      visible: false,\n      timer: null\n    };\n  },\n  methods: {\n    show() {\n      this.$nextTick(() => {\n        this.visible = true;\n        this.startTimer();\n      });\n    },\n    close() {\n      this.visible = false;\n      this.$emit('close');\n    },\n    startTimer() {\n      if (this.duration > 0) {\n        clearTimeout(this.timer);\n        this.timer = setTimeout(() => {\n          this.close();\n        }, this.duration);\n      }\n    }\n  },\n  mounted() {\n    setTimeout(() => {\n      this.show();\n    }, 100);\n  },\n  beforeUnmount() {\n    clearTimeout(this.timer);\n  }\n};", "map": {"version": 3, "names": ["name", "props", "message", "type", "String", "required", "default", "validator", "value", "includes", "duration", "Number", "closable", "Boolean", "minHeight", "data", "visible", "timer", "methods", "show", "$nextTick", "startTimer", "close", "$emit", "clearTimeout", "setTimeout", "mounted", "beforeUnmount"], "sources": ["src/components/common/header/SlideNotification.vue"], "sourcesContent": ["<template>\r\n  <transition name=\"slide\">\r\n    <div v-if=\"visible\"\r\n         :class=\"['notification', `notification-${type}`]\"\r\n         role=\"alert\"\r\n         :style=\"{ minHeight: minHeight }\">\r\n      <div class=\"notification-content\">\r\n        <div class=\"icon-wrapper\" v-if=\"type === 'error'\">\r\n          <span class=\"error-icon\">×</span>\r\n        </div>\r\n        <span class=\"message\">{{ message }}</span>\r\n        <button v-if=\"closable\" class=\"close-btn\" @click=\"close\">×</button>\r\n      </div>\r\n    </div>\r\n  </transition>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"SlideNotification\",\r\n  props: {\r\n    message: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    type: {\r\n      type: String,\r\n      default: 'info',\r\n      validator: value => ['success', 'warning', 'error', 'info'].includes(value)\r\n    },\r\n    duration: {\r\n      type: Number,\r\n      default: 4000\r\n    },\r\n    closable: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    minHeight: {\r\n      type: String,\r\n      default: 'auto'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      timer: null\r\n    }\r\n  },\r\n  methods: {\r\n    show() {\r\n      this.$nextTick(() => {\r\n        this.visible = true;\r\n        this.startTimer();\r\n      });\r\n    },\r\n    close() {\r\n      this.visible = false;\r\n      this.$emit('close');\r\n    },\r\n    startTimer() {\r\n      if (this.duration > 0) {\r\n        clearTimeout(this.timer);\r\n        this.timer = setTimeout(() => {\r\n          this.close();\r\n        }, this.duration);\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    setTimeout(() => {\r\n      this.show();\r\n    }, 100);\r\n  },\r\n  beforeUnmount() {\r\n    clearTimeout(this.timer);\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.notification {\r\n  position: fixed;\r\n  top: 20px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 90%;\r\n  max-width: 450px;\r\n  border-radius: 6px;\r\n  z-index: 9999;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  padding: 0;\r\n}\r\n\r\n.notification-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n}\r\n\r\n.notification-error {\r\n  background-color: #f3f5f6;\r\n  /*border: 1px solid #ffecb3;*/\r\n}\r\n\r\n.notification-success {\r\n  background-color: #f3f5f6;\r\n  /*border: 1px solid #ffecb3;*/\r\n}\r\n\r\n.notification-warning {\r\n  background-color: #f3f5f6;\r\n  /*border: 1px solid #ffecb3;*/\r\n}\r\n\r\n.notification-info {\r\n  background-color: #f3f5f6;\r\n  /*border: 1px solid #ffecb3;*/\r\n}\r\n\r\n.icon-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  background-color: #f44336;\r\n  margin-right: 12px;\r\n}\r\n\r\n.error-icon {\r\n  color: white;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n.message {\r\n  flex: 1;\r\n  color: #444444;\r\n  font-size: 14px;\r\n  padding: 2px 0;\r\n}\r\n\r\n.notification-error .message {\r\n  color: #d32f2f;\r\n}\r\n\r\n.notification-success .message {\r\n  color: #388e3c;\r\n}\r\n\r\n.notification-warning .message {\r\n  color: #f57c00;\r\n}\r\n\r\n.notification-info .message {\r\n  color: #1976d2;\r\n}\r\n\r\n.close-btn {\r\n  background: transparent;\r\n  border: none;\r\n  color: #9e9e9e;\r\n  cursor: pointer;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  padding: 0;\r\n  margin-left: 10px;\r\n  opacity: 0.8;\r\n  width: 24px;\r\n  height: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.close-btn:hover {\r\n  opacity: 1;\r\n  color: #616161;\r\n}\r\n\r\n/* 改进后的滑动动画 */\r\n.slide-enter-active {\r\n  animation: slideDown 0.5s ease-out forwards;\r\n}\r\n\r\n.slide-leave-active {\r\n  animation: slideUp 0.5s ease-in forwards;\r\n}\r\n\r\n@keyframes slideDown {\r\n  0% {\r\n    transform: translateX(-50%) translateY(-100%);\r\n    opacity: 0;\r\n  }\r\n  70% {\r\n    transform: translateX(-50%) translateY(10%);\r\n    opacity: 0.9;\r\n  }\r\n  100% {\r\n    transform: translateX(-50%) translateY(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes slideUp {\r\n  0% {\r\n    transform: translateX(-50%) translateY(0);\r\n    opacity: 1;\r\n  }\r\n  30% {\r\n    transform: translateX(-50%) translateY(10%);\r\n    opacity: 0.9;\r\n  }\r\n  100% {\r\n    transform: translateX(-50%) translateY(-100%);\r\n    opacity: 0;\r\n  }\r\n}\r\n</style>"], "mappings": "AAkBA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAF,IAAA;MACAA,IAAA,EAAAC,MAAA;MACAE,OAAA;MACAC,SAAA,EAAAC,KAAA,4CAAAC,QAAA,CAAAD,KAAA;IACA;IACAE,QAAA;MACAP,IAAA,EAAAQ,MAAA;MACAL,OAAA;IACA;IACAM,QAAA;MACAT,IAAA,EAAAU,OAAA;MACAP,OAAA;IACA;IACAQ,SAAA;MACAX,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;EACA;EACAS,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA;IACAC,KAAA;MACA,KAAAC,SAAA;QACA,KAAAJ,OAAA;QACA,KAAAK,UAAA;MACA;IACA;IACAC,MAAA;MACA,KAAAN,OAAA;MACA,KAAAO,KAAA;IACA;IACAF,WAAA;MACA,SAAAX,QAAA;QACAc,YAAA,MAAAP,KAAA;QACA,KAAAA,KAAA,GAAAQ,UAAA;UACA,KAAAH,KAAA;QACA,QAAAZ,QAAA;MACA;IACA;EACA;EACAgB,QAAA;IACAD,UAAA;MACA,KAAAN,IAAA;IACA;EACA;EACAQ,cAAA;IACAH,YAAA,MAAAP,KAAA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}