{"version": 3, "file": "js/891.148dc965.js", "mappings": "iJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACF,EAAG,UAAUF,EAAIK,GAAG,WAAWH,EAAG,SAASA,EAAG,WAAW,EACpK,EACII,EAAkB,G,8BCYtB,GACAC,KAAA,SACAC,WAAA,CAAAC,OAAA,IAAAC,OAAA,IAAAC,MAAAA,EAAAA,IChB+P,I,UCQ3PC,GAAY,OACd,EACAb,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeM,EAAiB,O,uDCnBhC,IAAIb,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,iBAAiBX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIgB,WAAW,SAAS,IAAI,CAACd,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIgB,WAAW,WAAW,IAAI,CAACd,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,kBAAkBX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,WAAWX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,UAAUX,EAAG,IAAI,CAACY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOf,EAAIgB,WAAW,QAAQ,IAAI,CAACd,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,QAAQX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,cAAcb,EAAIiB,GAAG,GAAGjB,EAAIiB,GAAG,GAAGjB,EAAIiB,GAAG,KAAKjB,EAAIiB,GAAG,IACtjC,EACIX,EAAkB,CAAC,WAAY,IAAIN,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,aAAaX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,eAAe,CAACJ,EAAIa,GAAG,kBACza,EAAE,WAAY,IAAIb,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,YAAYX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAMC,EAAQ,IAAqC,IAAM,kBACtQ,EAAE,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,WAAWX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAMC,EAAQ,IAAqC,IAAM,iBACrQ,EAAE,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,IAAI,CAACkB,YAAY,CAAC,MAAQ,UAAU,kBAAkB,QAAQF,MAAM,CAAC,KAAO,4BAA4B,OAAS,WAAW,CAAClB,EAAIa,GAAG,0BAA0Bb,EAAIa,GAAG,8CAC7S,GC2EA,G,QAAA,CACAN,KAAA,SACAc,OACA,OACA,CAEA,EACAC,QAAA,CACAN,WAAAO,GAEA,KAAAC,aAAA,KAAAA,cAAAD,GACA,KAAAE,mBAAA,KAAAD,YAGA,KAAAE,WAAA,KACA,MAAAC,EAAAC,SAAAC,iBAAA,yBACAF,EAAAG,SAAAC,KACAA,EAAAC,UAAAC,SAAA,WACA,WAAAV,GAAAQ,EAAAC,UAAAC,SAAA,gBACAF,EAAAC,UAAAC,SAAA,iBACAF,EAAAC,UAAAE,IAAA,eAGAC,YAAA,KACAJ,EAAAC,UAAAI,OAAA,iBACA,KACA,IAIA,KAAAZ,YAAAD,CAAA,KAGA,KAAAC,YAAAD,EAIA,KAAAc,OAAAd,OAAAA,EACA,KAAAG,WAAA,KACAY,OAAAC,SAAA,CACAC,IAAA,EACAC,SAAA,YAEA,KAAAC,QAAAC,GAAA,OAIA,KAAAD,QAAAE,KAAArB,GACAe,OAAAC,SAAA,CACAC,IAAA,EACAC,SAAA,YAGA,KCtIwQ,I,UCQpQ7B,GAAY,OACd,EACAb,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeM,EAAiB,O,uDCnBhC,IAAIb,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYU,GAAG,CAAC,WAAa,SAASC,GAAQ,OAAOf,EAAI6C,UAAU,SAAS,EAAE,WAAa,SAAS9B,GAAQ,OAAOf,EAAI8C,UAAU,SAAS,IAAI,CAAC5C,EAAG,IAAI,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAAChB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,sfAAsf,KAAO,aAAahB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,6MAA6M,KAAO,aAAahB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,w3BAAw3B,KAAO,aAAahB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,wEAAwE,KAAO,aAAahB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,wEAAwE,KAAO,iBAAiBhB,EAAG,MAAM,CAAC6C,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAASC,MAA2B,WAApBjD,EAAIkD,YAA0BC,WAAW,6BAA6B/C,YAAY,gCAAgC,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACF,EAAIa,GAAG,cAAcX,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAMlB,EAAIoD,aAAa,IAAM,qBAAqBlD,EAAG,MAAM,CAACE,YAAY,YAAYU,GAAG,CAAC,WAAa,SAASC,GAAQ,OAAOf,EAAI6C,UAAU,UAAU,EAAE,WAAa,SAAS9B,GAAQ,OAAOf,EAAI8C,UAAU,UAAU,IAAI,CAAC5C,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAAChB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,mfAAmf,KAAO,iBAAiBhB,EAAG,MAAM,CAAC6C,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAASC,MAA2B,YAApBjD,EAAIkD,YAA2BC,WAAW,8BAA8B/C,YAAY,iCAAiC,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACF,EAAIa,GAAG,eAAeX,EAAG,IAAI,CAACE,YAAY,gBAAgB,CAACJ,EAAIa,GAAG,iBAAiBX,EAAG,IAAI,CAACF,EAAIa,GAAG,iBAAiBX,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,IAAMlB,EAAIqD,cAAc,IAAM,qBAAqBnD,EAAG,MAAM,CAACE,YAAY,YAAYU,GAAG,CAAC,MAAQd,EAAIsD,kBAAkB,WAAatD,EAAIuD,YAAY,WAAavD,EAAIwD,cAAc,CAACtD,EAAG,IAAI,CAACE,YAAY,0BAA0B,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAAChB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,qKAAqK,KAAO,aAAahB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,kxBAAkxB,KAAO,iBAAiBhB,EAAG,MAAM,CAAC6C,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAASC,MAAOjD,EAAIyD,oBAAqBN,WAAW,wBAAwB/C,YAAY,WAAW,CAACJ,EAAIa,GAAG,mBAAoBb,EAAI0D,UAAWxD,EAAG,MAAM,CAACE,YAAY,gBAAgBU,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAGA,EAAO4C,SAAW5C,EAAO6C,cAAqB,KAAY5D,EAAI6D,mBAAmBC,MAAM,KAAMC,UAAU,IAAI,CAAC7D,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACF,EAAIa,GAAG,WAAWX,EAAG,OAAO,CAACE,YAAY,YAAYU,GAAG,CAAC,MAAQd,EAAI6D,qBAAqB,CAAC7D,EAAIa,GAAG,SAASX,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,IAAI,CAACE,YAAY,yBAAyB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAAChB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,2PAA2P,KAAO,iBAAiBlB,EAAIa,GAAG,+BAA+BX,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACE,YAAY,YAAY,CAACJ,EAAIa,GAAG,WAAWX,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,SAAS,CAAC6C,WAAW,CAAC,CAACxC,KAAK,QAAQyC,QAAQ,UAAUC,MAAOjD,EAAIgE,SAASC,KAAMd,WAAW,kBAAkBjC,MAAM,CAAC,SAAW,IAAIJ,GAAG,CAAC,OAAS,SAASC,GAAQ,IAAImD,EAAgBC,MAAMC,UAAUC,OAAOC,KAAKvD,EAAO4C,OAAOY,SAAQ,SAASC,GAAG,OAAOA,EAAEC,QAAQ,IAAGC,KAAI,SAASF,GAAG,IAAIG,EAAM,WAAYH,EAAIA,EAAEI,OAASJ,EAAEvB,MAAM,OAAO0B,CAAG,IAAI3E,EAAI6E,KAAK7E,EAAIgE,SAAU,OAAQjD,EAAO4C,OAAOmB,SAAWZ,EAAgBA,EAAc,GAAG,IAAI,CAAChE,EAAG,SAAS,CAACgB,MAAM,CAAC,MAAQ,KAAK,CAAClB,EAAIa,GAAG,SAASX,EAAG,SAAS,CAACgB,MAAM,CAAC,MAAQ,SAAS,CAAClB,EAAIa,GAAG,UAAUX,EAAG,SAAS,CAACgB,MAAM,CAAC,MAAQ,SAAS,CAAClB,EAAIa,GAAG,UAAUX,EAAG,SAAS,CAACgB,MAAM,CAAC,MAAQ,SAAS,CAAClB,EAAIa,GAAG,UAAUX,EAAG,SAAS,CAACgB,MAAM,CAAC,MAAQ,SAAS,CAAClB,EAAIa,GAAG,UAAUX,EAAG,SAAS,CAACgB,MAAM,CAAC,MAAQ,OAAO,CAAClB,EAAIa,GAAG,aAAcb,EAAIgE,SAASC,MAAQjE,EAAI+E,WAAY7E,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIa,GAAG,aAAab,EAAIgF,OAAO9E,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACE,YAAY,YAAY,CAACJ,EAAIa,GAAG,WAAWX,EAAG,WAAW,CAAC6C,WAAW,CAAC,CAACxC,KAAK,QAAQyC,QAAQ,UAAUC,MAAOjD,EAAIgE,SAASiB,YAAa9B,WAAW,yBAAyBjC,MAAM,CAAC,YAAc,MAAM,SAAW,IAAIgE,SAAS,CAAC,MAASlF,EAAIgE,SAASiB,aAAcnE,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAO4C,OAAOwB,WAAiBnF,EAAI6E,KAAK7E,EAAIgE,SAAU,cAAejD,EAAO4C,OAAOV,MAAM,MAAOjD,EAAIgE,SAASiB,aAAejF,EAAI+E,WAAY7E,EAAG,IAAI,CAACE,YAAY,cAAc,CAACJ,EAAIa,GAAG,aAAab,EAAIgF,OAAO9E,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,QAAQ,CAACE,YAAY,aAAa,CAACJ,EAAIa,GAAG,WAAWX,EAAG,MAAM,CAACE,YAAY,iBAAiBU,GAAG,CAAC,MAAQd,EAAIoF,kBAAkB,SAAW,SAASrE,GAAQA,EAAOsE,gBAAiB,EAAE,KAAO,SAAStE,GAAgC,OAAxBA,EAAOsE,iBAAwBrF,EAAIsF,WAAWxB,MAAM,KAAMC,UAAU,IAAI,CAAC7D,EAAG,QAAQ,CAACqF,IAAI,YAAYnE,YAAY,CAAC,QAAU,QAAQF,MAAM,CAAC,KAAO,OAAO,OAAS,WAAWJ,GAAG,CAAC,OAASd,EAAIwF,gBAAkBxF,EAAIgE,SAASyB,MAAuoCvF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgBc,MAAM,CAAC,IAAMlB,EAAIgE,SAAS0B,aAAa,IAAM,YAAYxF,EAAG,MAAM,CAACE,YAAY,eAAeU,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAO4E,kBAAyB3F,EAAI4F,YAAY9B,MAAM,KAAMC,UAAU,IAAI,CAAC/D,EAAIa,GAAG,SAA/5CX,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,IAAI,CAACE,YAAY,wBAAwB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAAChB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,oKAAoK,KAAO,aAAahB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,8rBAA8rB,KAAO,iBAAiBhB,EAAG,IAAI,CAACF,EAAIa,GAAG,0BAAkUX,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,SAAS,CAACE,YAAY,iBAAiBU,GAAG,CAAC,MAAQd,EAAI6D,qBAAqB,CAAC7D,EAAIa,GAAG,QAAQX,EAAG,SAAS,CAACE,YAAY,iBAAiBU,GAAG,CAAC,MAAQd,EAAI6F,gBAAgB,CAAC7F,EAAIa,GAAG,cAAcb,EAAIgF,KAAMhF,EAAI8F,iBAAkB5F,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACgB,MAAM,CAAC,QAAU,gBAAgB,MAAQ,KAAK,OAAS,OAAO,CAAChB,EAAG,OAAO,CAACgB,MAAM,CAAC,EAAI,sRAAsR,KAAO,iBAAiBhB,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACJ,EAAIa,GAAG,UAAUX,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACJ,EAAIa,GAAG,oBAAoBX,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,SAAS,CAACE,YAAY,kBAAkBU,GAAG,CAAC,MAAQd,EAAI+F,oBAAoB,CAAC/F,EAAIa,GAAG,cAAcb,EAAIgF,MAC5tV,EACI1E,EAAkB,GCuKtB,GACAC,KAAA,QACAc,OACA,OACA6B,YAAA,KACAQ,WAAA,EACAqB,YAAA,EACAtB,qBAAA,EACAqC,kBAAA,EACA1C,aAAAjC,EAAA,IACAkC,cAAAlC,EAAA,IACA6C,SAAA,CACAC,KAAA,GACAgB,YAAA,GACAe,WAAA,GACAP,MAAA,KACAC,aAAA,MAGA,EACApE,QAAA,CACAuB,UAAAoB,GACA,KAAAf,YAAAe,CACA,EACAnB,UAAAmB,GACA,KAAAf,cAAAe,IACA,KAAAf,YAAA,KAEA,EACAK,cACA,KAAAE,qBAAA,CACA,EACAD,cACA,KAAAC,qBAAA,CACA,EACAH,oBACA,KAAAI,WAAA,EACA,KAAAqB,YAAA,CACA,EACAlB,qBACA,KAAAH,WAAA,EAEA,KAAAM,SAAA,CACAC,KAAA,GACAgB,YAAA,GACAe,WAAA,GACAP,MAAA,KACAC,aAAA,MAEA,KAAAX,YAAA,CACA,EACAK,oBACA,KAAAa,MAAAC,UAAAC,OACA,EACAX,aAAAY,GACA,MAAAC,EAAAD,EAAAzC,OAAA2C,MAAA,GACAD,GACA,KAAAE,aAAAF,EAEA,EACAf,WAAAc,GACA,MAAAC,EAAAD,EAAAI,aAAAF,MAAA,GACAD,GAAAA,EAAApC,KAAAwC,MAAA,YACA,KAAAF,aAAAF,EAEA,EACAE,aAAAF,GACA,GAAAA,GAAAA,EAAApC,KAAAwC,MAAA,YACA,KAAAzC,SAAAyB,MAAAY,EAGA,MAAAK,EAAA,IAAAC,WACAD,EAAAE,OAAAC,IACA,KAAA7C,SAAA0B,aAAAmB,EAAAlD,OAAAmD,MAAA,EAEAJ,EAAAK,cAAAV,EACA,CACA,EACAT,cACA,KAAA5B,SAAAyB,MAAA,KACA,KAAAzB,SAAA0B,aAAA,IACA,EACAG,gBAEA,KAAAd,YAAA,EACA,KAAAf,SAAAC,MAAA,KAAAD,SAAAiB,aAKA,KAAA+B,gBACA,EACAA,iBAIA,QAAAhD,SAAAyB,MAAA,CACA,MAAAwB,EAAA,IAAAC,SACAD,EAAAE,OAAA,YAAAnD,SAAAC,MACAgD,EAAAE,OAAA,mBAAAnD,SAAAiB,aACAgC,EAAAE,OAAA,kBAAAnD,SAAAgC,YACAiB,EAAAE,OAAA,aAAAnD,SAAAyB,MAIA,CAGA,KAAA/B,WAAA,EACA,KAAAoC,kBAAA,EAGA,KAAA9B,SAAA,CACAC,KAAA,GACAgB,YAAA,GACAe,WAAA,GACAP,MAAA,KACAC,aAAA,KAEA,EACAK,oBACA,KAAAD,kBAAA,CACA,ICnSuQ,I,UCQnQlF,GAAY,OACd,EACAb,EACAO,GACA,EACA,KACA,WACA,MAIF,EAAeM,EAAiB,O", "sources": ["webpack://portal-ui/./src/components/common/Layout.vue", "webpack://portal-ui/src/components/common/Layout.vue", "webpack://portal-ui/./src/components/common/Layout.vue?a648", "webpack://portal-ui/./src/components/common/Layout.vue?e255", "webpack://portal-ui/./src/components/common/footer/Footer.vue", "webpack://portal-ui/src/components/common/footer/Footer.vue", "webpack://portal-ui/./src/components/common/footer/Footer.vue?6062", "webpack://portal-ui/./src/components/common/footer/Footer.vue?8b5d", "webpack://portal-ui/./src/components/common/mider/Mider.vue", "webpack://portal-ui/src/components/common/mider/Mider.vue", "webpack://portal-ui/./src/components/common/mider/Mider.vue?6cd8", "webpack://portal-ui/./src/components/common/mider/Mider.vue?9943"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('main',{staticClass:\"page-wrapper\"},[_c('Header'),_vm._t(\"default\"),_c('Mider'),_c('Footer')],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<main class=\"page-wrapper\">\r\n\t\t<Header/>\r\n\t\t\t<slot></slot>\r\n    <Mider/>\r\n\t\t<Footer/>\r\n\t</main>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"@/components/common/header/Header\";\r\nimport Footer from \"@/components/common/footer/Footer\";\r\nimport Mider from \"@/components/common/mider/Mider\";\r\n\r\nexport default {\r\n\tname: \"Layout\",\r\n\tcomponents:{Header, Footer, Mider}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.main-content{\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Layout.vue?vue&type=template&id=0b27ec56&scoped=true&\"\nimport script from \"./Layout.vue?vue&type=script&lang=js&\"\nexport * from \"./Layout.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Layout.vue?vue&type=style&index=0&id=0b27ec56&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0b27ec56\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer\"},[_c('div',{staticClass:\"footer-content\"},[_c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"售前咨询热线\")]),_c('div',{staticClass:\"footer-phone\"},[_vm._v(\"13913283376\")]),_c('div',{staticClass:\"footer-links\"},[_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/index')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"首页\")])]),_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"AI算力市场\")])])])]),_c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"支持与服务\")]),_c('div',{staticClass:\"footer-links\"},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"联系我们\")]),_c('a',{on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"帮助文档\")])]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"公告\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"提交建议\")])])]),_vm._m(0),_vm._m(1),_vm._m(2)]),_vm._m(3)])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"关注天工开物\")]),_c('div',{staticClass:\"footer-links\"},[_c('div',{staticClass:\"footer-link\"},[_vm._v(\"关注天工开物\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物公众号\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物微博\")]),_c('div',{staticClass:\"footer-link\"},[_vm._v(\"天工开物支持与服务\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"联系专属客服\")]),_c('div',{staticClass:\"footer-qrcode\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/footer/wechat.jpg\"),\"alt\":\"联系专属客服二维码\"}})])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-column\"},[_c('div',{staticClass:\"footer-title\"},[_vm._v(\"官方公众号\")]),_c('div',{staticClass:\"footer-qrcode\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/footer/wechat.jpg\"),\"alt\":\"官方公众号二维码\"}})])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-bottom\"},[_c('div',{staticClass:\"footer-copyright\"},[_c('a',{staticStyle:{\"color\":\"inherit\",\"text-decoration\":\"none\"},attrs:{\"href\":\"https://beian.miit.gov.cn\",\"target\":\"_blank\"}},[_vm._v(\" 苏ICP备2025171841号-1 \")]),_vm._v(\" 天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. \")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"footer\">\r\n    <div class=\"footer-content\">\r\n      <!-- First column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">售前咨询热线</div>\r\n        <div class=\"footer-phone\">13913283376</div>\r\n        <div class=\"footer-links\">\r\n          <a @click=\"navigateTo('/index')\"><div class=\"footer-link\">首页</div></a>\r\n          <a @click=\"navigateTo('/product')\"><div class=\"footer-link\">AI算力市场</div></a>\r\n\r\n<!--          <div class=\"footer-link\">AI算力市场</div>-->\r\n\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Second column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">支持与服务</div>\r\n        <div class=\"footer-links\">\r\n          <div class=\"footer-link\">联系我们</div>\r\n          <a @click=\"navigateTo('/help')\"><div class=\"footer-link\">帮助文档</div></a>\r\n          <div class=\"footer-link\">公告</div>\r\n          <div class=\"footer-link\">提交建议</div>\r\n        </div>\r\n      </div>\r\n\r\n<!--      &lt;!&ndash; Third column &ndash;&gt;-->\r\n<!--      <div class=\"footer-column\">-->\r\n<!--        <div class=\"footer-title\">账户管理</div>-->\r\n<!--        <div class=\"footer-links\">-->\r\n<!--          <div class=\"footer-link\">控制台</div>-->\r\n<!--          <div class=\"footer-link\">账号管理</div>-->\r\n<!--          <div class=\"footer-link\">充值付款</div>-->\r\n<!--          <div class=\"footer-link\">线下款 / 电汇</div>-->\r\n<!--          <div class=\"footer-link\">索取发票</div>-->\r\n<!--          <div class=\"footer-link\">合规性</div>-->\r\n<!--        </div>-->\r\n<!--      </div>-->\r\n\r\n      <!-- Fourth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">关注天工开物</div>\r\n        <div class=\"footer-links\">\r\n          <div class=\"footer-link\">关注天工开物</div>\r\n          <div class=\"footer-link\">天工开物公众号</div>\r\n          <div class=\"footer-link\">天工开物微博</div>\r\n          <div class=\"footer-link\">天工开物支持与服务</div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Fifth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">联系专属客服</div>\r\n        <div class=\"footer-qrcode\">\r\n          <img src=\"@/assets/images/footer/wechat.jpg\" alt=\"联系专属客服二维码\" />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Sixth column -->\r\n      <div class=\"footer-column\">\r\n        <div class=\"footer-title\">官方公众号</div>\r\n        <div class=\"footer-qrcode\">\r\n          <img src=\"@/assets/images/footer/wechat.jpg\" alt=\"官方公众号二维码\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Bottom footer links -->\r\n    <div class=\"footer-bottom\">\r\n\r\n      <div class=\"footer-copyright\">\r\n        <a href=\"https://beian.miit.gov.cn\" target=\"_blank\" style=\"color: inherit; text-decoration: none;\">\r\n          苏ICP备2025171841号-1\r\n        </a>\r\n           天工开物智能科技（苏州）有限公司 ALL RIGHTS RESERVED. </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Footer\",\r\n  data() {\r\n    return {\r\n      // Data can be added here if needed\r\n    }\r\n  },\r\n  methods: {\r\n    navigateTo(path) {\r\n      // 记录当前活动路径作为上一个活动路径\r\n      if (this.currentPath && this.currentPath !== path) {\r\n        this.previousActivePath = this.currentPath;\r\n\r\n        // 为当前活动链接和登录按钮添加 active-exit 类\r\n        this.$nextTick(() => {\r\n          const navLinks = document.querySelectorAll('.nav-link, .btn-login');\r\n          navLinks.forEach(link => {\r\n            if ((link.classList.contains('active') ||\r\n                    (path === '/login' && link.classList.contains('btn-login'))) &&\r\n                !link.classList.contains('active-exit')) {\r\n              link.classList.add('active-exit');\r\n\r\n              // 等待动画完成后移除 active-exit 类\r\n              setTimeout(() => {\r\n                link.classList.remove('active-exit');\r\n              }, 300); // 匹配你的 CSS transition 持续时间 (0.3s)\r\n            }\r\n          });\r\n\r\n          // 更新当前路径\r\n          this.currentPath = path;\r\n        });\r\n      } else {\r\n        this.currentPath = path;\r\n      }\r\n\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.footer {\r\n  max-width: 2560px;\r\n  width: 100%;\r\n  background-color: #424242;\r\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n  color: white;\r\n  padding: 0;\r\n  margin: 0px;\r\n}\r\n\r\n.footer-content {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  max-width: 2560px;\r\n  margin: 0 40px;\r\n  padding: 20px 0;\r\n}\r\n\r\n.footer-column {\r\n  flex: 1;\r\n  min-width: 150px;\r\n  padding: 0 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.footer-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  color: white;\r\n}\r\n\r\n.footer-phone {\r\n  font-size: 16px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.footer-links {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.footer-link {\r\n  margin-bottom: 10px;\r\n  cursor: pointer;\r\n  color: white;\r\n  font-size: 14px;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.footer-link:hover {\r\n  color: #1890ff;\r\n}\r\n\r\n.footer-qrcode {\r\n  width: 120px;\r\n  height: 120px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.footer-qrcode img {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  margin-left: -18%;\r\n}\r\n\r\n.footer-bottom {\r\n  border-top: 1px solid #e8e8e8;\r\n  padding: 20px 0;\r\n  text-align: center;\r\n  max-width: 2560px;\r\n  margin: 0 40px;\r\n}\r\n\r\n.footer-bottom-links {\r\n  margin-bottom: 10px;\r\n  text-align: left;\r\n}\r\n\r\n.footer-bottom-link {\r\n  color: white;\r\n  margin: 0 10px;\r\n  font-size: 14px;\r\n  text-decoration: none;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.footer-bottom-link:hover {\r\n  color: #1890ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.footer-copyright, .footer-license {\r\n  font-size: 15px;\r\n  text-align: center;\r\n  color: white;\r\n  margin-left: 10px;\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Footer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Footer.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Footer.vue?vue&type=template&id=2d6e9349&scoped=true&\"\nimport script from \"./Footer.vue?vue&type=script&lang=js&\"\nexport * from \"./Footer.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Footer.vue?vue&type=style&index=0&id=2d6e9349&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2d6e9349\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"mider-container\"},[_c('div',{staticClass:\"mider-sidebar\"},[_c('div',{staticClass:\"icon-wrapper\"},[_c('div',{staticClass:\"icon-item\",on:{\"mouseenter\":function($event){return _vm.showPopup('wechat')},\"mouseleave\":function($event){return _vm.hidePopup('wechat')}}},[_c('i',{staticClass:\"iconfont icon-wechat\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"24\",\"height\":\"24\"}},[_c('path',{attrs:{\"d\":\"M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5-2.9 10.5-4.6 16.3-4.6 3 0 5.9 0.5 8.7 1.4 35.9 10.5 74.6 16.2 114.2 16.2 9.8 0 19.5-0.3 29-1C624.9 597 702.1 497.6 702.1 378.1c0-0.2-0.1-0.4-0.1-0.7 0 0-0.1-0.3-0.1-0.5-0.8 0-1.8-0.1-2.8-0.1-3.1 0-6.2 0.2-9 0.6z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M380.5 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7zM534.3 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M683.5 673.6c82.1 0 151.2-56 171.5-131.8 4.1-15.2-9.3-29.5-24.9-26.6-3.5 0.6-7.1 1-10.7 1-34.4 0-62.4-28-62.4-62.4 0-1.6 0.1-3.3 0.2-4.9 0.8-19.3-24.5-28.7-35.7-13.5-39.1 53-102.7 87.5-174.5 87.5-12.3 0-24.4-1-36.1-3-5.2-0.9-10.5 0.8-14.1 4.5-4.6 4.6-16.7 17.2-16.7 17.2l-0.6 0.6c-5.4 5.4-8.7 12.3-9.5 19.8-0.9 8.5 1.9 17.1 7.7 23.6l1.3 1.3c6.3 7.5 15.6 11.7 25.6 11.7 7.1 0 14.1-2.3 19.7-6.5l10.6-7.8c6.2-4.5 14.8-3.3 19.3 2.8 4.3 5.8 3.4 14-2.1 18.8-14.4 12.6-32.9 19.7-51.9 19.7-4.3 0-8.7-0.3-13-1-21.3-3.5-40.2-14.4-54.3-31.3l-0.3-0.3c-12.9-15.7-19.4-34.9-18.3-54.5 0.8-14.5 6.4-28.5 16.1-39.7 1.2-1.4 2.5-2.6 3.8-3.9l31.5-31.5c1.9-1.9 3.8-3.9 5.4-6.1 5.8-7.7 8.9-17 8.9-26.8 0-11.3-4.2-22.1-11.8-30.4-12.4-13.5-31.5-15.2-48.5-11.7-91.3 18.9-160.7 100.1-160.8 196.9 0 109.5 89.1 198.6 198.6 198.6 36.7 0 71.9-9.9 102.8-28.5 5.7-3.4 12.8-3.3 18.5 0.2 36.7 22.7 79.1 35.3 123.2 35.3z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M770.9 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\",\"fill\":\"#82c91e\"}}),_c('path',{attrs:{\"d\":\"M602.4 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\",\"fill\":\"#82c91e\"}})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.activePopup === 'wechat'),expression:\"activePopup === 'wechat'\"}],staticClass:\"popup-container wechat-popup\"},[_c('div',{staticClass:\"popup-content\"},[_c('h3',[_vm._v(\"微信扫码咨询客服\")]),_c('div',{staticClass:\"qr-code\"},[_c('img',{attrs:{\"src\":_vm.wechatQRCode,\"alt\":\"微信客服二维码\"}})])])])]),_c('div',{staticClass:\"icon-item\",on:{\"mouseenter\":function($event){return _vm.showPopup('contact')},\"mouseleave\":function($event){return _vm.hidePopup('contact')}}},[_c('i',{staticClass:\"iconfont icon-phone\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"24\",\"height\":\"24\"}},[_c('path',{attrs:{\"d\":\"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7c-20.6 47.8-49.6 90.6-86.4 127.3-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3-18.5 0-35.8 7.2-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-0.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4z\",\"fill\":\"#1677ff\"}})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.activePopup === 'contact'),expression:\"activePopup === 'contact'\"}],staticClass:\"popup-container contact-popup\"},[_c('div',{staticClass:\"popup-content\"},[_c('h3',[_vm._v(\"商务合作请联系电话\")]),_c('p',{staticClass:\"phone-number\"},[_vm._v(\"13913283376\")]),_c('p',[_vm._v(\"使用问题请咨询微信客服\")]),_c('div',{staticClass:\"qr-code\"},[_c('img',{attrs:{\"src\":_vm.contactQRCode,\"alt\":\"联系电话二维码\"}})])])])]),_c('div',{staticClass:\"icon-item\",on:{\"click\":_vm.showFeedbackModal,\"mouseenter\":_vm.showTooltip,\"mouseleave\":_vm.hideTooltip}},[_c('i',{staticClass:\"iconfont icon-feedback\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"24\",\"height\":\"24\"}},[_c('path',{attrs:{\"d\":\"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zM293 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z\",\"fill\":\"#fa8c16\"}}),_c('path',{attrs:{\"d\":\"M894 345c-48.1-66-115.3-110.1-189-130v0.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l0.8 132.6c0 3.2 0.5 6.4 1.5 9.4 5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.4 11.9 68.4 18 104.7 18 72.2 0 143.1-21.4 205.3-61.8 57.4-37.3 104-89.9 131.8-150.1 23.9-52 35.2-106.8 33.6-160.9 28.8-29.9 50.4-64.7 63.6-103 17.8-52.7 13.1-108.7-14-155.1z m-68 106.7c-25.8 53.9-71.3 89.7-126.2 99.8-5.7 1-9.7 3.9-13 10.3-8.1 16.1-19.1 30.6-32.7 43.1-11.8 10.9-24.9 20.5-42.9 14.6-9.7-3.2-15.2-9.4-19.1-20.8-6.6-19.2-12.9-18.2-27.2-9.9-81.5 47.5-165.2 37.3-232.5-25.3-30.5-28.6-54.2-65.1-62.7-109-8.4-44 0.4-89.6 26.7-130.5 22.9-35.6 52.4-62 89.9-79.3 24.8-11.5 50.9-18.7 78.5-20.5 101.5-6.2 197.2 41.2 244.1 132.2 12.8 24.8 25.6 60.7 30.4 87.3 5.5 30.3 2.9 53.5-13.3 87z\",\"fill\":\"#fa8c16\"}})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showFeedbackTooltip),expression:\"showFeedbackTooltip\"}],staticClass:\"tooltip\"},[_vm._v(\" 反馈与建议 \")])])])]),(_vm.showModal)?_c('div',{staticClass:\"modal-overlay\",on:{\"click\":function($event){if($event.target !== $event.currentTarget)return null;return _vm.closeFeedbackModal.apply(null, arguments)}}},[_c('div',{staticClass:\"feedback-modal\"},[_c('div',{staticClass:\"modal-header\"},[_c('h3',[_vm._v(\"反馈与建议\")]),_c('span',{staticClass:\"close-btn\",on:{\"click\":_vm.closeFeedbackModal}},[_vm._v(\"×\")])]),_c('div',{staticClass:\"modal-body\"},[_c('div',{staticClass:\"alert alert-warning\"},[_c('i',{staticClass:\"iconfont icon-warning\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"16\",\"height\":\"16\"}},[_c('path',{attrs:{\"d\":\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z\",\"fill\":\"#faad14\"}})])]),_vm._v(\" 您的反馈我们将认真对待，不断优化产品功能和体验 \")]),_c('div',{staticClass:\"form-group\"},[_c('label',{staticClass:\"required\"},[_vm._v(\"问题类型：\")]),_c('div',{staticClass:\"select-wrapper\"},[_c('select',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.feedback.type),expression:\"feedback.type\"}],attrs:{\"required\":\"\"},on:{\"change\":function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = \"_value\" in o ? o._value : o.value;return val}); _vm.$set(_vm.feedback, \"type\", $event.target.multiple ? $$selectedVal : $$selectedVal[0])}}},[_c('option',{attrs:{\"value\":\"\"}},[_vm._v(\"请选择\")]),_c('option',{attrs:{\"value\":\"功能建议\"}},[_vm._v(\"功能建议\")]),_c('option',{attrs:{\"value\":\"产品故障\"}},[_vm._v(\"产品故障\")]),_c('option',{attrs:{\"value\":\"体验不佳\"}},[_vm._v(\"体验不佳\")]),_c('option',{attrs:{\"value\":\"账户相关\"}},[_vm._v(\"账户相关\")]),_c('option',{attrs:{\"value\":\"其他\"}},[_vm._v(\"其他\")])])]),(!_vm.feedback.type && _vm.showErrors)?_c('p',{staticClass:\"error-text\"},[_vm._v(\"请选择问题类型\")]):_vm._e()]),_c('div',{staticClass:\"form-group\"},[_c('label',{staticClass:\"required\"},[_vm._v(\"问题描述：\")]),_c('textarea',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.feedback.description),expression:\"feedback.description\"}],attrs:{\"placeholder\":\"请输入\",\"required\":\"\"},domProps:{\"value\":(_vm.feedback.description)},on:{\"input\":function($event){if($event.target.composing)return;_vm.$set(_vm.feedback, \"description\", $event.target.value)}}}),(!_vm.feedback.description && _vm.showErrors)?_c('p',{staticClass:\"error-text\"},[_vm._v(\"请输入问题描述\")]):_vm._e()]),_c('div',{staticClass:\"form-group\"},[_c('label',{staticClass:\"required1\"},[_vm._v(\"问题截图：\")]),_c('div',{staticClass:\"image-uploader\",on:{\"click\":_vm.triggerFileUpload,\"dragover\":function($event){$event.preventDefault();},\"drop\":function($event){$event.preventDefault();return _vm.onFileDrop.apply(null, arguments)}}},[_c('input',{ref:\"fileInput\",staticStyle:{\"display\":\"none\"},attrs:{\"type\":\"file\",\"accept\":\"image/*\"},on:{\"change\":_vm.onFileChange}}),(!_vm.feedback.image)?_c('div',{staticClass:\"upload-placeholder\"},[_c('i',{staticClass:\"iconfont icon-upload\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"28\",\"height\":\"28\"}},[_c('path',{attrs:{\"d\":\"M518.3 459c-3.2-4.1-9.4-4.1-12.6 0l-112 141.7c-4.1 5.2-0.4 12.9 6.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z\",\"fill\":\"#bfbfbf\"}}),_c('path',{attrs:{\"d\":\"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c26.1 6.9 49.6 22.5 66.3 43.8 16.4 21 25.4 45.9 26.3 72.3 1.1 33.9-11.4 66.5-34.9 90.7-23.6 24.4-55.3 37.7-89 37.7h-40c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C758.5 760 848 670.5 848 560c0-92.7-63.1-170.7-148.6-193.3z\",\"fill\":\"#bfbfbf\"}})])]),_c('p',[_vm._v(\"点击/拖拽至此处添加图片\")])]):_c('div',{staticClass:\"preview-container\"},[_c('img',{staticClass:\"image-preview\",attrs:{\"src\":_vm.feedback.imagePreview,\"alt\":\"问题截图预览\"}}),_c('div',{staticClass:\"remove-image\",on:{\"click\":function($event){$event.stopPropagation();return _vm.removeImage.apply(null, arguments)}}},[_vm._v(\"×\")])])])])]),_c('div',{staticClass:\"modal-footer\"},[_c('button',{staticClass:\"btn btn-cancel\",on:{\"click\":_vm.closeFeedbackModal}},[_vm._v(\"取消\")]),_c('button',{staticClass:\"btn btn-submit\",on:{\"click\":_vm.confirmSubmit}},[_vm._v(\"提交\")])])])]):_vm._e(),(_vm.showConfirmation)?_c('div',{staticClass:\"modal-overlay\"},[_c('div',{staticClass:\"confirmation-dialog\"},[_c('div',{staticClass:\"confirmation-icon\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 1024 1024\",\"width\":\"32\",\"height\":\"32\"}},[_c('path',{attrs:{\"d\":\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z\",\"fill\":\"#52c41a\"}})])]),_c('div',{staticClass:\"confirmation-title\"},[_vm._v(\"提交成功\")]),_c('div',{staticClass:\"confirmation-message\"},[_vm._v(\"感谢您的反馈，我们会尽快处理\")]),_c('div',{staticClass:\"confirmation-actions\"},[_c('button',{staticClass:\"btn btn-primary\",on:{\"click\":_vm.closeConfirmation}},[_vm._v(\"确定\")])])])]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"mider-container\">\r\n    <!-- Floating sidebar with icons -->\r\n    <div class=\"mider-sidebar\">\r\n<!--      <div class=\"coupon-tag\">-->\r\n<!--        <span>领</span>-->\r\n<!--        <span>优</span>-->\r\n<!--        <span>惠</span>-->\r\n<!--        <span>券</span>-->\r\n<!--      </div>-->\r\n\r\n      <div class=\"icon-wrapper\">\r\n        <div class=\"icon-item\" @mouseenter=\"showPopup('wechat')\" @mouseleave=\"hidePopup('wechat')\">\r\n          <i class=\"iconfont icon-wechat\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5-2.9 10.5-4.6 16.3-4.6 3 0 5.9 0.5 8.7 1.4 35.9 10.5 74.6 16.2 114.2 16.2 9.8 0 19.5-0.3 29-1C624.9 597 702.1 497.6 702.1 378.1c0-0.2-0.1-0.4-0.1-0.7 0 0-0.1-0.3-0.1-0.5-0.8 0-1.8-0.1-2.8-0.1-3.1 0-6.2 0.2-9 0.6z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M380.5 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7zM534.3 503.3c-27.9 0-50.7-22.8-50.7-50.7s22.8-50.7 50.7-50.7 50.7 22.8 50.7 50.7-22.8 50.7-50.7 50.7z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M683.5 673.6c82.1 0 151.2-56 171.5-131.8 4.1-15.2-9.3-29.5-24.9-26.6-3.5 0.6-7.1 1-10.7 1-34.4 0-62.4-28-62.4-62.4 0-1.6 0.1-3.3 0.2-4.9 0.8-19.3-24.5-28.7-35.7-13.5-39.1 53-102.7 87.5-174.5 87.5-12.3 0-24.4-1-36.1-3-5.2-0.9-10.5 0.8-14.1 4.5-4.6 4.6-16.7 17.2-16.7 17.2l-0.6 0.6c-5.4 5.4-8.7 12.3-9.5 19.8-0.9 8.5 1.9 17.1 7.7 23.6l1.3 1.3c6.3 7.5 15.6 11.7 25.6 11.7 7.1 0 14.1-2.3 19.7-6.5l10.6-7.8c6.2-4.5 14.8-3.3 19.3 2.8 4.3 5.8 3.4 14-2.1 18.8-14.4 12.6-32.9 19.7-51.9 19.7-4.3 0-8.7-0.3-13-1-21.3-3.5-40.2-14.4-54.3-31.3l-0.3-0.3c-12.9-15.7-19.4-34.9-18.3-54.5 0.8-14.5 6.4-28.5 16.1-39.7 1.2-1.4 2.5-2.6 3.8-3.9l31.5-31.5c1.9-1.9 3.8-3.9 5.4-6.1 5.8-7.7 8.9-17 8.9-26.8 0-11.3-4.2-22.1-11.8-30.4-12.4-13.5-31.5-15.2-48.5-11.7-91.3 18.9-160.7 100.1-160.8 196.9 0 109.5 89.1 198.6 198.6 198.6 36.7 0 71.9-9.9 102.8-28.5 5.7-3.4 12.8-3.3 18.5 0.2 36.7 22.7 79.1 35.3 123.2 35.3z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M770.9 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\" fill=\"#82c91e\"></path>\r\n              <path d=\"M602.4 576.9m-50.7 0a50.7 50.7 0 1 0 101.4 0 50.7 50.7 0 1 0-101.4 0Z\" fill=\"#82c91e\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- WeChat QR code popup -->\r\n          <div class=\"popup-container wechat-popup\" v-show=\"activePopup === 'wechat'\">\r\n            <div class=\"popup-content\">\r\n              <h3>微信扫码咨询客服</h3>\r\n              <div class=\"qr-code\">\r\n                <img :src=\"wechatQRCode\" alt=\"微信客服二维码\">\r\n              </div>\r\n<!--              <div class=\"popup-footer\">-->\r\n<!--                <button class=\"btn-consult\">桌面版微信点击咨询客服</button>-->\r\n<!--              </div>-->\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"icon-item\" @mouseenter=\"showPopup('contact')\" @mouseleave=\"hidePopup('contact')\">\r\n          <i class=\"iconfont icon-phone\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7c-20.6 47.8-49.6 90.6-86.4 127.3-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3-18.5 0-35.8 7.2-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-0.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4z\" fill=\"#1677ff\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- Contact information popup -->\r\n          <div class=\"popup-container contact-popup\" v-show=\"activePopup === 'contact'\">\r\n            <div class=\"popup-content\">\r\n              <h3>商务合作请联系电话</h3>\r\n              <p class=\"phone-number\">13913283376</p>\r\n              <p>使用问题请咨询微信客服</p>\r\n              <div class=\"qr-code\">\r\n                <img :src=\"contactQRCode\" alt=\"联系电话二维码\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"icon-item\" @click=\"showFeedbackModal\" @mouseenter=\"showTooltip\" @mouseleave=\"hideTooltip\">\r\n          <i class=\"iconfont icon-feedback\">\r\n            <svg viewBox=\"0 0 1024 1024\" width=\"24\" height=\"24\">\r\n              <path d=\"M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zM293 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z\" fill=\"#fa8c16\"></path>\r\n              <path d=\"M894 345c-48.1-66-115.3-110.1-189-130v0.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l0.8 132.6c0 3.2 0.5 6.4 1.5 9.4 5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.4 11.9 68.4 18 104.7 18 72.2 0 143.1-21.4 205.3-61.8 57.4-37.3 104-89.9 131.8-150.1 23.9-52 35.2-106.8 33.6-160.9 28.8-29.9 50.4-64.7 63.6-103 17.8-52.7 13.1-108.7-14-155.1z m-68 106.7c-25.8 53.9-71.3 89.7-126.2 99.8-5.7 1-9.7 3.9-13 10.3-8.1 16.1-19.1 30.6-32.7 43.1-11.8 10.9-24.9 20.5-42.9 14.6-9.7-3.2-15.2-9.4-19.1-20.8-6.6-19.2-12.9-18.2-27.2-9.9-81.5 47.5-165.2 37.3-232.5-25.3-30.5-28.6-54.2-65.1-62.7-109-8.4-44 0.4-89.6 26.7-130.5 22.9-35.6 52.4-62 89.9-79.3 24.8-11.5 50.9-18.7 78.5-20.5 101.5-6.2 197.2 41.2 244.1 132.2 12.8 24.8 25.6 60.7 30.4 87.3 5.5 30.3 2.9 53.5-13.3 87z\" fill=\"#fa8c16\"></path>\r\n            </svg>\r\n          </i>\r\n          <!-- Tooltip for feedback icon -->\r\n          <div class=\"tooltip\" v-show=\"showFeedbackTooltip\">\r\n            反馈与建议\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- Feedback modal - Reduced size -->\r\n    <div class=\"modal-overlay\" v-if=\"showModal\" @click.self=\"closeFeedbackModal\">\r\n      <div class=\"feedback-modal\">\r\n        <div class=\"modal-header\">\r\n          <h3>反馈与建议</h3>\r\n          <span class=\"close-btn\" @click=\"closeFeedbackModal\">×</span>\r\n        </div>\r\n\r\n        <div class=\"modal-body\">\r\n          <div class=\"alert alert-warning\">\r\n            <i class=\"iconfont icon-warning\">\r\n              <svg viewBox=\"0 0 1024 1024\" width=\"16\" height=\"16\">\r\n                <path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z\" fill=\"#faad14\"></path>\r\n              </svg>\r\n            </i>\r\n            您的反馈我们将认真对待，不断优化产品功能和体验\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required\">问题类型：</label>\r\n            <div class=\"select-wrapper\">\r\n              <select v-model=\"feedback.type\" required>\r\n                <option value=\"\">请选择</option>\r\n                <option value=\"功能建议\">功能建议</option>\r\n                <option value=\"产品故障\">产品故障</option>\r\n                <option value=\"体验不佳\">体验不佳</option>\r\n                <option value=\"账户相关\">账户相关</option>\r\n                <option value=\"其他\">其他</option>\r\n              </select>\r\n            </div>\r\n            <p class=\"error-text\" v-if=\"!feedback.type && showErrors\">请选择问题类型</p>\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required\">问题描述：</label>\r\n            <textarea v-model=\"feedback.description\" placeholder=\"请输入\" required></textarea>\r\n            <p class=\"error-text\" v-if=\"!feedback.description && showErrors\">请输入问题描述</p>\r\n          </div>\r\n\r\n\r\n          <div class=\"form-group\">\r\n            <label class=\"required1\">问题截图：</label>\r\n            <div\r\n                class=\"image-uploader\"\r\n                @click=\"triggerFileUpload\"\r\n                @dragover.prevent\r\n                @drop.prevent=\"onFileDrop\"\r\n            >\r\n              <input\r\n                  type=\"file\"\r\n                  ref=\"fileInput\"\r\n                  accept=\"image/*\"\r\n                  @change=\"onFileChange\"\r\n                  style=\"display: none\"\r\n              >\r\n              <div v-if=\"!feedback.image\" class=\"upload-placeholder\">\r\n                <i class=\"iconfont icon-upload\">\r\n                  <svg viewBox=\"0 0 1024 1024\" width=\"28\" height=\"28\">\r\n                    <path d=\"M518.3 459c-3.2-4.1-9.4-4.1-12.6 0l-112 141.7c-4.1 5.2-0.4 12.9 6.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z\" fill=\"#bfbfbf\"></path>\r\n                    <path d=\"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c26.1 6.9 49.6 22.5 66.3 43.8 16.4 21 25.4 45.9 26.3 72.3 1.1 33.9-11.4 66.5-34.9 90.7-23.6 24.4-55.3 37.7-89 37.7h-40c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C758.5 760 848 670.5 848 560c0-92.7-63.1-170.7-148.6-193.3z\" fill=\"#bfbfbf\"></path>\r\n                  </svg>\r\n                </i>\r\n                <p>点击/拖拽至此处添加图片</p>\r\n              </div>\r\n              <div v-else class=\"preview-container\">\r\n                <img :src=\"feedback.imagePreview\" alt=\"问题截图预览\" class=\"image-preview\">\r\n                <div class=\"remove-image\" @click.stop=\"removeImage\">×</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"modal-footer\">\r\n          <button class=\"btn btn-cancel\" @click=\"closeFeedbackModal\">取消</button>\r\n          <button class=\"btn btn-submit\" @click=\"confirmSubmit\">提交</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Confirmation Dialog -->\r\n    <div class=\"modal-overlay\" v-if=\"showConfirmation\">\r\n      <div class=\"confirmation-dialog\">\r\n        <div class=\"confirmation-icon\">\r\n          <svg viewBox=\"0 0 1024 1024\" width=\"32\" height=\"32\">\r\n            <path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z\" fill=\"#52c41a\"></path>\r\n          </svg>\r\n        </div>\r\n        <div class=\"confirmation-title\">提交成功</div>\r\n        <div class=\"confirmation-message\">感谢您的反馈，我们会尽快处理</div>\r\n        <div class=\"confirmation-actions\">\r\n          <button class=\"btn btn-primary\" @click=\"closeConfirmation\">确定</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Mider',\r\n  data() {\r\n    return {\r\n      activePopup: null,\r\n      showModal: false,\r\n      showErrors: false,\r\n      showFeedbackTooltip: false,\r\n      showConfirmation: false,\r\n      wechatQRCode: require('@/assets/images/footer/wechat.jpg'), // 替换为实际路径\r\n      contactQRCode: require('@/assets/images/footer/wechat.jpg'), // 替换为实际路径\r\n      feedback: {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    showPopup(type) {\r\n      this.activePopup = type;\r\n    },\r\n    hidePopup(type) {\r\n      if (this.activePopup === type) {\r\n        this.activePopup = null;\r\n      }\r\n    },\r\n    showTooltip() {\r\n      this.showFeedbackTooltip = true;\r\n    },\r\n    hideTooltip() {\r\n      this.showFeedbackTooltip = false;\r\n    },\r\n    showFeedbackModal() {\r\n      this.showModal = true;\r\n      this.showErrors = false;\r\n    },\r\n    closeFeedbackModal() {\r\n      this.showModal = false;\r\n      // Reset form\r\n      this.feedback = {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      };\r\n      this.showErrors = false;\r\n    },\r\n    triggerFileUpload() {\r\n      this.$refs.fileInput.click();\r\n    },\r\n    onFileChange(event) {\r\n      const file = event.target.files[0];\r\n      if (file) {\r\n        this.processImage(file);\r\n      }\r\n    },\r\n    onFileDrop(event) {\r\n      const file = event.dataTransfer.files[0];\r\n      if (file && file.type.match('image.*')) {\r\n        this.processImage(file);\r\n      }\r\n    },\r\n    processImage(file) {\r\n      if (file && file.type.match('image.*')) {\r\n        this.feedback.image = file;\r\n\r\n        // Create preview\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => {\r\n          this.feedback.imagePreview = e.target.result;\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    },\r\n    removeImage() {\r\n      this.feedback.image = null;\r\n      this.feedback.imagePreview = null;\r\n    },\r\n    confirmSubmit() {\r\n      // Validate form\r\n      this.showErrors = true;\r\n      if (!this.feedback.type || !this.feedback.description) {\r\n        return;\r\n      }\r\n\r\n      // Submit feedback\r\n      this.submitFeedback();\r\n    },\r\n    submitFeedback() {\r\n      // Here you would typically send the data to your backend\r\n\r\n      // Create FormData object if there's an image\r\n      if (this.feedback.image) {\r\n        const formData = new FormData();\r\n        formData.append('type', this.feedback.type);\r\n        formData.append('description', this.feedback.description);\r\n        formData.append('instanceId', this.feedback.instanceId);\r\n        formData.append('image', this.feedback.image);\r\n\r\n        // Send formData to your API\r\n        // this.$axios.post('/api/feedback', formData)\r\n      }\r\n\r\n      // Close feedback modal and show confirmation dialog\r\n      this.showModal = false;\r\n      this.showConfirmation = true;\r\n\r\n      // Reset form data\r\n      this.feedback = {\r\n        type: '',\r\n        description: '',\r\n        instanceId: '',\r\n        image: null,\r\n        imagePreview: null\r\n      };\r\n    },\r\n    closeConfirmation() {\r\n      this.showConfirmation = false;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.mider-container {\r\n  position: relative;\r\n\r\n}\r\n\r\n/* Sidebar styles */\r\n.mider-sidebar {\r\n  /* 原有定位属性 */\r\n  position: fixed;\r\n  right: 0;\r\n  top: 85%;             /* 当前纵向定位 */\r\n  transform: translateY(-50%);\r\n  z-index: 1000;\r\n\r\n  /* 新增尺寸控制 */\r\n  width: 42px;         /* 固定宽度 */\r\n  height: 50vh;         /* 视口高度的50% */\r\n  max-width: 90%;       /* 防溢出保护 */\r\n  max-height: 80vh;     /* 高度上限 */\r\n  min-height: 200px;    /* 高度下限 */\r\n  /*box-sizing: 0; !* 包含内边距 *!*/\r\n\r\n  /* 布局优化 */\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n\r\n.coupon-tag {\r\n  background-color: #ff6b6b;\r\n  color: white;\r\n  padding: 8px 12px;\r\n  border-radius: 8px 0 0 8px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.icon-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: white;\r\n  border-radius: 8px 0 0 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.icon-item {\r\n  position: relative;\r\n  padding: 15px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.icon-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.icon-item i {\r\n  font-size: 24px;\r\n  color: #666;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.icon-item:hover i {\r\n  color: #1890ff;\r\n}\r\n\r\n.icon-item:hover i svg path {\r\n  fill: #1890ff;\r\n}\r\n\r\n/* Tooltip styles */\r\n.tooltip {\r\n  position: absolute;\r\n  left: -90px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background-color: #fff;\r\n  color: #333;\r\n  padding: 6px 10px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  z-index: 1000;\r\n}\r\n\r\n.tooltip:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: -6px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-width: 6px 0 6px 6px;\r\n  border-style: solid;\r\n  border-color: transparent transparent transparent white;\r\n}\r\n\r\n/* Popup styles */\r\n.popup-container {\r\n  position: absolute;\r\n  right: 60px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n  width: 240px;\r\n  z-index: 1000;\r\n}\r\n\r\n.popup-container:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: -10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-width: 10px 0 10px 10px;\r\n  border-style: solid;\r\n  border-color: transparent transparent transparent white;\r\n}\r\n\r\n.popup-content {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.popup-content h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n  font-size: 16px;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.qr-code {\r\n  margin: 10px 0;\r\n}\r\n\r\n.qr-code img {\r\n  width: 150px;\r\n  height: 150px;\r\n}\r\n\r\n.phone-number {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin: 5px 0;\r\n}\r\n\r\n.popup-footer {\r\n  margin-top: 10px;\r\n}\r\n\r\n.btn-consult {\r\n  background-color: #f5f5f5;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 8px 16px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  color: #333;\r\n  width: 100%;\r\n}\r\n\r\n/* Modal styles - Reduced size */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1001;\r\n}\r\n\r\n.feedback-modal {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  width: 500px;\r\n  max-width: 100vw;\r\n  max-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.modal-header {\r\n  padding: 14px 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.modal-header h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  color: #333;\r\n}\r\n\r\n.close-btn {\r\n  cursor: pointer;\r\n  font-size: 20px;\r\n  color: #999;\r\n}\r\n\r\n.modal-body {\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.alert {\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 13px;\r\n}\r\n\r\n.alert-warning {\r\n  background-color: #fffbe6;\r\n  border: 1px solid #ffe58f;\r\n  font-size: 10px;\r\n  color: #d48806;\r\n}\r\n\r\n.alert i {\r\n  margin-right: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 6px;\r\n  font-size: 13px;\r\n  color: #333;\r\n}\r\n\r\n.required:before {\r\n  content: '*';\r\n  color: #ff4d4f;\r\n  margin-left: 4px;\r\n}\r\n.required1:before {\r\n  content: '';\r\n  color: #ff4d4f;\r\n  margin-left: 9px;\r\n}\r\n\r\ninput, select, textarea {\r\n  width: 100%;\r\n  padding: 8px 12px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  font-size: 13px;\r\n  transition: all 0.3s;\r\n}\r\n\r\ninput:focus, select:focus, textarea:focus {\r\n  outline: none;\r\n  border-color: #40a9ff;\r\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\ntextarea {\r\n  min-height: 70px;\r\n  resize: vertical;\r\n}\r\n\r\n.select-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.select-wrapper:after {\r\n  content: '';\r\n  position: absolute;\r\n  right: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 12px;\r\n  color: #999;\r\n  pointer-events: none;\r\n}\r\n\r\n.error-text {\r\n  color: #ff4d4f;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.image-uploader {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 4px;\r\n  padding: 20px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  background-color: #fafafa;\r\n  min-height: 120px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.image-uploader:hover {\r\n  border-color: #40a9ff;\r\n}\r\n\r\n.upload-placeholder {\r\n  color: #999;\r\n}\r\n\r\n.upload-placeholder i {\r\n  font-size: 32px;\r\n  display: block;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.preview-container {\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.image-preview {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.remove-image {\r\n  position: absolute;\r\n  top: -10px;\r\n  right: -10px;\r\n  width: 24px;\r\n  height: 24px;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.modal-footer {\r\n  padding: 10px 24px 24px;\r\n  text-align: right;\r\n}\r\n\r\n.btn {\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  border: none;\r\n}\r\n\r\n.btn-cancel {\r\n  background-color: white;\r\n  border: 1px solid #d9d9d9;\r\n  color: #333;\r\n  margin-right: 12px;\r\n}\r\n\r\n.btn-cancel:hover {\r\n  color: #40a9ff;\r\n  border-color: #40a9ff;\r\n}\r\n\r\n.btn-submit {\r\n  background-color: #1890ff;\r\n  color: white;\r\n}\r\n\r\n.btn-submit:hover {\r\n  background-color: #40a9ff;\r\n}\r\n\r\n/* For responsiveness */\r\n@media (max-width: 768px) {\r\n  .popup-container {\r\n    width: 200px;\r\n  }\r\n\r\n  .qr-code img {\r\n    width: 120px;\r\n    height: 120px;\r\n  }\r\n}\r\n\r\n.confirmation-dialog {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  /*margin-left: 50%;*/\r\n  /*text-align: left;*/\r\n  width: 500px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.confirmation-icon {\r\n  margin-left: 1%;\r\n  margin-bottom: -37px;\r\n}\r\n\r\n.confirmation-title {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  margin-left: 10%;\r\n  color: #333;\r\n}\r\n\r\n.confirmation-message {\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin-left: 10%;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.confirmation-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.btn-primary {\r\n  background-color: #1677ff;\r\n  /*margin-right: 2px;*/\r\n  color: white;\r\n  cursor: pointer;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 8px 32px;\r\n  font-size: 14px;\r\n  margin-left: 80%;\r\n}\r\n\r\n.btn-primary:hover {\r\n  background-color: #4096ff;\r\n}\r\n\r\n/* Form styles */\r\n.form-group {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.required:after {\r\n  content: '*';\r\n  color: #f5222d;\r\n  margin-left: 4px;\r\n}\r\n\r\n.select-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.select-wrapper:after {\r\n  content: '▼';\r\n  font-size: 10px;\r\n  color: #999;\r\n  position: absolute;\r\n  right: 10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  pointer-events: none;\r\n}\r\n\r\nselect {\r\n  width: 100%;\r\n  padding: 8px 10px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  background-color: white;\r\n  font-size: 14px;\r\n  appearance: none;\r\n}\r\n\r\ntextarea {\r\n  width: 100%;\r\n  padding: 8px 10px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  min-height: 100px;\r\n  resize: vertical;\r\n  font-size: 14px;\r\n}\r\n\r\n.error-text {\r\n  color: #f5222d;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.image-uploader {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  background-color: #fafafa;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.image-uploader:hover {\r\n  border-color: #1890ff;\r\n}\r\n\r\n.upload-placeholder {\r\n  color: #bfbfbf;\r\n}\r\n\r\n.upload-placeholder p {\r\n  margin-top: 8px;\r\n  font-size: 13px;\r\n}\r\n\r\n.preview-container {\r\n  position: relative;\r\n}\r\n\r\n.image-preview {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n}\r\n\r\n.remove-image {\r\n  position: absolute;\r\n  top: -10px;\r\n  right: -10px;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.modal-footer {\r\n  padding: 16px 20px;\r\n  border-top: 1px solid #f0f0f0;\r\n  text-align: right;\r\n}\r\n\r\n.btn {\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.btn-cancel {\r\n  background-color: white;\r\n  border: 1px solid #d9d9d9;\r\n  color: #666;\r\n  margin-right: 8px;\r\n}\r\n\r\n.btn-cancel:hover {\r\n  color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n.btn-submit {\r\n  background-color: #1677ff;\r\n  border: none;\r\n  color: white;\r\n}\r\n\r\n.btn-submit:hover {\r\n  background-color: #4096ff;\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Mider.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Mider.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Mider.vue?vue&type=template&id=37397f14&scoped=true&\"\nimport script from \"./Mider.vue?vue&type=script&lang=js&\"\nexport * from \"./Mider.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Mider.vue?vue&type=style&index=0&id=37397f14&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"37397f14\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_t", "staticRenderFns", "name", "components", "Header", "Footer", "<PERSON><PERSON>", "component", "_v", "on", "$event", "navigateTo", "_m", "attrs", "require", "staticStyle", "data", "methods", "path", "currentPath", "previousActivePath", "$nextTick", "navLinks", "document", "querySelectorAll", "for<PERSON>ach", "link", "classList", "contains", "add", "setTimeout", "remove", "$route", "window", "scrollTo", "top", "behavior", "$router", "go", "push", "showPopup", "hidePopup", "directives", "rawName", "value", "activePopup", "expression", "wechatQRCode", "contactQRCode", "showFeedbackModal", "showTooltip", "hideTooltip", "showFeedbackTooltip", "showModal", "target", "currentTarget", "closeFeedbackModal", "apply", "arguments", "feedback", "type", "$$selectedVal", "Array", "prototype", "filter", "call", "options", "o", "selected", "map", "val", "_value", "$set", "multiple", "showErrors", "_e", "description", "domProps", "composing", "triggerFileUpload", "preventDefault", "onFileDrop", "ref", "onFileChange", "image", "imagePreview", "stopPropagation", "removeImage", "confirmSubmit", "showConfirmation", "closeConfirmation", "instanceId", "$refs", "fileInput", "click", "event", "file", "files", "processImage", "dataTransfer", "match", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "submitFeedback", "formData", "FormData", "append"], "sourceRoot": ""}