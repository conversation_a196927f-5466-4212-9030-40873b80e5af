{"version": 3, "file": "js/796.5ccc8b27.js", "mappings": "8JAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAEF,EAAII,iBAAkBF,EAAG,oBAAoB,CAACG,MAAM,CAAC,QAAUL,EAAIM,oBAAoB,KAAON,EAAIO,iBAAiB,SAAW,IAAK,UAAYP,EAAIQ,WAAWC,GAAG,CAAC,MAAQ,SAASC,GAAQV,EAAII,kBAAmB,CAAK,KAAKJ,EAAIW,KAAKT,EAAG,MAAM,CAACU,YAAY,CAAC,MAAQ,SAAS,CAACV,EAAG,MAAM,CAACW,YAAY,wBAAwB,CAACX,EAAG,MAAM,CAACW,YAAY,sBAAsB,CAACX,EAAG,KAAK,CAACW,YAAY,aAAa,CAACb,EAAIc,GAAG,UAAUZ,EAAG,KAAK,CAACW,YAAY,YAAY,CAACX,EAAG,KAAK,CAACW,YAAY,WAAWE,MAAM,CAAEC,OAA+B,iBAAvBhB,EAAIiB,iBAAqC,CAACf,EAAG,IAAI,CAACG,MAAM,CAAC,KAAO,KAAKI,GAAG,CAAC,MAAQ,SAASC,GAAgC,OAAxBA,EAAOQ,iBAAwBlB,EAAImB,cAAc,eAAe,IAAI,CAACjB,EAAG,IAAI,CAACW,YAAY,kBAAkBX,EAAG,OAAO,CAACF,EAAIc,GAAG,cAAcZ,EAAG,KAAK,CAACW,YAAY,WAAWE,MAAM,CAAEC,OAA+B,aAAvBhB,EAAIiB,iBAAiC,CAACf,EAAG,IAAI,CAACG,MAAM,CAAC,KAAO,KAAKI,GAAG,CAAC,MAAQ,SAASC,GAAgC,OAAxBA,EAAOQ,iBAAwBlB,EAAImB,cAAc,WAAW,IAAI,CAACjB,EAAG,IAAI,CAACW,YAAY,mBAAmBX,EAAG,OAAO,CAACF,EAAIc,GAAG,gBAAgBZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAAyB,WAAvBb,EAAIiB,eAA6Bf,EAAG,MAAM,CAACA,EAAG,MAAM,CAACkB,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,OAAQvB,EAAIwB,iBAAkBC,WAAW,sBAAsBZ,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,kBAAkB,CAACX,EAAG,MAAM,CAACW,YAAY,cAAc,CAACX,EAAG,QAAQ,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOvB,EAAI0B,iBAAkBD,WAAW,qBAAqBpB,MAAM,CAAC,KAAO,OAAO,YAAc,SAASsB,SAAS,CAAC,MAAS3B,EAAI0B,kBAAmBjB,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAOkB,OAAOC,YAAiB7B,EAAI0B,iBAAiBhB,EAAOkB,OAAOL,MAAK,KAAKrB,EAAG,SAAS,CAACW,YAAY,gBAAgBJ,GAAG,CAAC,MAAQT,EAAI8B,eAAe,CAAC5B,EAAG,IAAI,CAACW,YAAY,qBAAsBb,EAAI0B,iBAAkBxB,EAAG,SAAS,CAACW,YAAY,eAAeJ,GAAG,CAAC,MAAQT,EAAI+B,mBAAmB,CAAC7B,EAAG,IAAI,CAACW,YAAY,oBAAoBb,EAAIW,OAAOT,EAAG,MAAM,CAACW,YAAY,oBAAoB,CAACb,EAAIc,GAAG,aAAaZ,EAAG,OAAO,CAACW,YAAY,cAAc,CAACb,EAAIc,GAAG,SAASd,EAAIgC,GAAGhC,EAAIiC,0BAA0B/B,EAAG,MAAM,CAACW,YAAY,mBAAmB,CAAEb,EAAIkC,aAAchC,EAAG,MAAM,CAACW,YAAY,iBAAiB,CAACX,EAAG,IAAI,CAACW,YAAY,oBAAoBX,EAAG,OAAO,CAACF,EAAIc,GAAG,mBAAmBd,EAAIW,KAAMX,EAAImC,WAAYjC,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,IAAI,CAACW,YAAY,kBAAkBX,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAImC,eAAejC,EAAG,SAAS,CAACO,GAAG,CAAC,MAAQT,EAAIoC,cAAc,CAACpC,EAAIc,GAAG,UAAUd,EAAIW,KAAKT,EAAG,QAAQ,CAACW,YAAY,cAAc,CAACX,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIc,GAAG,QAAQZ,EAAG,IAAI,CAACW,YAAY,eAAeJ,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIqC,OAAO,eAAe,OAAOnC,EAAG,KAAK,CAACF,EAAIc,GAAG,WAAWZ,EAAG,IAAI,CAACW,YAAY,eAAeJ,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIqC,OAAO,aAAa,OAAOnC,EAAG,KAAK,CAACF,EAAIc,GAAG,SAASZ,EAAG,IAAI,CAACW,YAAY,eAAeJ,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIqC,OAAO,iBAAiB,OAAOnC,EAAG,KAAK,CAACF,EAAIc,GAAG,OAAOZ,EAAG,IAAI,CAACW,YAAY,eAAeJ,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIqC,OAAO,aAAa,OAAOnC,EAAG,KAAK,CAACF,EAAIc,GAAG,OAAOZ,EAAG,IAAI,CAACW,YAAY,eAAeJ,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIqC,OAAO,WAAW,OAAOnC,EAAG,KAAK,CAACF,EAAIc,GAAG,SAASZ,EAAG,IAAI,CAACW,YAAY,eAAeJ,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIqC,OAAO,iBAAiB,OAAOnC,EAAG,KAAK,CAACF,EAAIc,GAAG,QAAQZ,EAAG,KAAK,CAACF,EAAIc,GAAG,YAAYZ,EAAG,QAAQ,CAACF,EAAIsC,GAAItC,EAAIuC,iBAAiB,SAASC,EAAMC,GAAO,OAAOvC,EAAG,KAAK,CAACwC,IAAI,SAASD,GAAO,CAACvC,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAGQ,EAAMG,iBAAiBzC,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAI4C,eAAeJ,EAAMK,gBAAgB3C,EAAG,KAAK,CAACA,EAAG,OAAO,CAACW,YAAY,aAAaE,MAAMf,EAAI8C,sBAAsBN,EAAMO,iBAAiB,CAAC/C,EAAIc,GAAG,IAAId,EAAIgC,GAAGhC,EAAIgD,qBAAqBR,EAAMO,iBAAiB,SAAS7C,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIiD,YAAYT,EAAMU,gBAAgBhD,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAGQ,EAAMW,aAAajD,EAAG,KAAK,CAACA,EAAG,OAAO,CAACW,YAAY,qBAAqBE,MAAMf,EAAIoD,sBAAsBZ,EAAMa,iBAAiB,CAACrD,EAAIc,GAAG,IAAId,EAAIgC,GAAGhC,EAAIsD,qBAAqBd,EAAMa,iBAAiB,SAASnD,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIiD,YAAYT,EAAMe,iBAAiBrD,EAAG,KAAK,CAACA,EAAG,OAAO,CAACW,YAAY,iBAAiBJ,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIwD,iBAAiBhB,EAAM,IAAI,CAACxC,EAAIc,GAAG,aAAa,IAAqC,IAAjCd,EAAIyD,kBAAkBC,QAAiB1D,EAAIkC,aAAmClC,EAAIW,KAAzBT,EAAG,KAAK,CAACF,EAAI2D,GAAG,MAAe,OAAOzD,EAAG,oBAAoB,CAACG,MAAM,CAAC,eAAeL,EAAI4D,YAAY,MAAQ5D,EAAIyD,kBAAkBC,OAAO,YAAY1D,EAAI6D,UAAUpD,GAAG,CAAC,cAAcT,EAAI8D,SAAS,mBAAmB9D,EAAI+D,yBAAyB,GAAG7D,EAAG,MAAM,CAACkB,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOvB,EAAIwB,iBAAkBC,WAAW,qBAAqBZ,YAAY,iBAAiB,CAACX,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,iBAAiB,CAACX,EAAG,KAAK,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,UAAUZ,EAAG,SAAS,CAACW,YAAY,cAAcJ,GAAG,CAAC,MAAQT,EAAIgE,gBAAgB,CAAC9D,EAAG,IAAI,CAACW,YAAY,iBAAiBb,EAAIc,GAAG,cAAcZ,EAAG,MAAM,CAACW,YAAY,kBAAkB,CAACX,EAAG,KAAK,CAACW,YAAY,mBAAmB,CAACb,EAAIc,GAAG,UAAUZ,EAAG,MAAM,CAACW,YAAY,kBAAkB,CAACX,EAAG,MAAM,CAACW,YAAY,cAAc,CAACX,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,UAAUZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIiE,cAActB,mBAAmBzC,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,WAAWZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACX,EAAG,OAAO,CAACW,YAAY,aAAaE,MAAMf,EAAI8C,sBAAsB9C,EAAIiE,cAAclB,iBAAiB,CAAC/C,EAAIc,GAAG,IAAId,EAAIgC,GAAGhC,EAAIgD,qBAAqBhD,EAAIiE,cAAclB,iBAAiB,aAAa7C,EAAG,MAAM,CAACW,YAAY,cAAc,CAACX,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,WAAWZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACX,EAAG,OAAO,CAACW,YAAY,qBAAqBE,MAAMf,EAAIoD,sBAAsBpD,EAAIiE,cAAcZ,iBAAiB,CAACrD,EAAIc,GAAG,IAAId,EAAIgC,GAAGhC,EAAIsD,qBAAqBtD,EAAIiE,cAAcZ,iBAAiB,WAAWnD,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,SAASZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,KAAKd,EAAIgC,GAAGhC,EAAIiD,YAAYjD,EAAIiE,cAAcf,oBAAoBhD,EAAG,MAAM,CAACW,YAAY,cAAc,CAACX,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,aAAaZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAI4C,eAAe5C,EAAIiE,cAAcpB,kBAAkB3C,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,WAAWZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,KAAKd,EAAIgC,GAAGhC,EAAIiD,YAAYjD,EAAIiE,cAAcV,uBAAuBrD,EAAG,KAAK,CAACW,YAAY,mBAAmB,CAACb,EAAIc,GAAG,WAAWZ,EAAG,MAAM,CAACW,YAAY,kBAAkB,CAACX,EAAG,MAAM,CAACW,YAAY,cAAc,CAACX,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,SAASZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIiE,cAAcC,gBAAgBhE,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,SAASZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIiE,cAAcE,eAAejE,EAAG,MAAM,CAACW,YAAY,cAAc,CAACX,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,WAAWZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIiE,cAAcG,WAAW,UAAUlE,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,SAASZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIiE,cAAcI,cAAc,aAAanE,EAAG,MAAM,CAACW,YAAY,cAAc,CAACX,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,aAAaZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIiE,cAAcK,WAAW,UAAUpE,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,UAAUZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIiE,cAAcM,aAAa,cAAcrE,EAAG,MAAM,CAACW,YAAY,cAAc,CAACX,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,SAASZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIiE,cAAcO,YAAY,WAAWtE,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAG,SAASZ,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIiE,cAAcQ,QAAQ,uBAAuBzE,EAAIW,KAA6B,iBAAvBX,EAAIiB,eAAmCf,EAAG,MAAM,CAACA,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,kBAAkB,CAACX,EAAG,MAAM,CAACW,YAAY,qBAAqB,CAACX,EAAG,SAAS,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOvB,EAAI0E,qBAAsBjD,WAAW,yBAAyBhB,GAAG,CAAC,OAAS,SAASC,GAAQ,IAAIiE,EAAgBC,MAAMC,UAAUC,OAAOC,KAAKrE,EAAOkB,OAAOoD,SAAQ,SAASC,GAAG,OAAOA,EAAEC,QAAQ,IAAGC,KAAI,SAASF,GAAG,IAAIG,EAAM,WAAYH,EAAIA,EAAEI,OAASJ,EAAE1D,MAAM,OAAO6D,CAAG,IAAIpF,EAAI0E,qBAAqBhE,EAAOkB,OAAO0D,SAAWX,EAAgBA,EAAc,EAAE,IAAI,CAACzE,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,MAAM,CAACL,EAAIc,GAAG,UAAUZ,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,OAAO,CAACL,EAAIc,GAAG,WAAWZ,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,OAAO,CAACL,EAAIc,GAAG,WAAWZ,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,WAAW,CAACL,EAAIc,GAAG,cAA4C,WAA7Bd,EAAI0E,qBAAmCxE,EAAG,MAAM,CAACW,YAAY,qBAAqB,CAACX,EAAG,QAAQ,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOvB,EAAIuF,gBAAiB9D,WAAW,oBAAoBpB,MAAM,CAAC,KAAO,QAAQsB,SAAS,CAAC,MAAS3B,EAAIuF,iBAAkB9E,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAOkB,OAAOC,YAAiB7B,EAAIuF,gBAAgB7E,EAAOkB,OAAOL,MAAK,KAAKrB,EAAG,OAAO,CAACF,EAAIc,GAAG,OAAOZ,EAAG,QAAQ,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOvB,EAAIwF,cAAe/D,WAAW,kBAAkBpB,MAAM,CAAC,KAAO,QAAQsB,SAAS,CAAC,MAAS3B,EAAIwF,eAAgB/E,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAOkB,OAAOC,YAAiB7B,EAAIwF,cAAc9E,EAAOkB,OAAOL,MAAK,OAAOvB,EAAIW,OAAOT,EAAG,MAAM,CAACW,YAAY,kBAAkB,CAACX,EAAG,SAAS,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOvB,EAAIyF,gBAAiBhE,WAAW,oBAAoBhB,GAAG,CAAC,OAAS,SAASC,GAAQ,IAAIiE,EAAgBC,MAAMC,UAAUC,OAAOC,KAAKrE,EAAOkB,OAAOoD,SAAQ,SAASC,GAAG,OAAOA,EAAEC,QAAQ,IAAGC,KAAI,SAASF,GAAG,IAAIG,EAAM,WAAYH,EAAIA,EAAEI,OAASJ,EAAE1D,MAAM,OAAO6D,CAAG,IAAIpF,EAAIyF,gBAAgB/E,EAAOkB,OAAO0D,SAAWX,EAAgBA,EAAc,EAAE,IAAI,CAACzE,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,KAAK,CAACL,EAAIc,GAAG,UAAUZ,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,WAAW,CAACL,EAAIc,GAAG,QAAQZ,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,YAAY,CAACL,EAAIc,GAAG,UAAUZ,EAAG,QAAQ,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOvB,EAAI0F,uBAAwBjE,WAAW,2BAA2BpB,MAAM,CAAC,KAAO,OAAO,YAAc,SAASsB,SAAS,CAAC,MAAS3B,EAAI0F,wBAAyBjF,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOiF,KAAKC,QAAQ,QAAQ5F,EAAI6F,GAAGnF,EAAOoF,QAAQ,QAAQ,GAAGpF,EAAOgC,IAAI,SAAgB,KAAY1C,EAAI+F,mBAAmBC,MAAM,KAAMC,UAAU,EAAE,MAAQ,SAASvF,GAAWA,EAAOkB,OAAOC,YAAiB7B,EAAI0F,uBAAuBhF,EAAOkB,OAAOL,MAAK,KAAKrB,EAAG,SAAS,CAACW,YAAY,gBAAgBJ,GAAG,CAAC,MAAQT,EAAI+F,qBAAqB,CAAC7F,EAAG,IAAI,CAACW,YAAY,yBAAyBX,EAAG,MAAM,CAACW,YAAY,uBAAuB,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACX,EAAG,MAAM,CAACW,YAAY,iBAAiB,CAACb,EAAIc,GAAG,SAASZ,EAAG,MAAM,CAACW,YAAY,wBAAwB,CAACb,EAAIc,GAAG,KAAKd,EAAIgC,GAAGhC,EAAIiD,YAAYjD,EAAIkG,YAAYC,qBAAqBjG,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACX,EAAG,MAAM,CAACW,YAAY,iBAAiB,CAACb,EAAIc,GAAG,SAASZ,EAAG,MAAM,CAACW,YAAY,yBAAyB,CAACb,EAAIc,GAAG,KAAKd,EAAIgC,GAAGhC,EAAIiD,YAAYjD,EAAIkG,YAAYE,oBAAoBlG,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACX,EAAG,MAAM,CAACW,YAAY,iBAAiB,CAACb,EAAIc,GAAG,UAAUZ,EAAG,MAAM,CAACW,YAAY,iBAAiB,CAACb,EAAIc,GAAG,KAAKd,EAAIgC,GAAGhC,EAAIiD,YAAYjD,EAAIqG,qBAAqBnG,EAAG,MAAM,CAACW,YAAY,mBAAmB,CAAEb,EAAIsG,mBAAoBpG,EAAG,MAAM,CAACW,YAAY,iBAAiB,CAACX,EAAG,IAAI,CAACW,YAAY,oBAAoBX,EAAG,OAAO,CAACF,EAAIc,GAAG,mBAAmBd,EAAIW,KAAMX,EAAIuG,iBAAkBrG,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,IAAI,CAACW,YAAY,kBAAkBX,EAAG,OAAO,CAACF,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIuG,qBAAqBrG,EAAG,SAAS,CAACO,GAAG,CAAC,MAAQT,EAAIwG,oBAAoB,CAACxG,EAAIc,GAAG,UAAUd,EAAIW,KAAKT,EAAG,QAAQ,CAACW,YAAY,cAAc,CAACb,EAAI2D,GAAG,GAAGzD,EAAG,QAAQ,CAACF,EAAIsC,GAAItC,EAAIyG,uBAAuB,SAASC,EAAYjE,GAAO,OAAOvC,EAAG,KAAK,CAACwC,IAAI,eAAeD,GAAO,CAACvC,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAG0E,EAAYC,mBAAmBzG,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAI4C,eAAe8D,EAAY7D,gBAAgB3C,EAAG,KAAK,CAACA,EAAG,OAAO,CAACW,YAAY,mBAAmBE,MAAMf,EAAI4G,wBAAwBF,EAAYf,OAAO,CAAC3F,EAAIc,GAAG,IAAId,EAAIgC,GAAGhC,EAAI6G,uBAAuBH,EAAYf,OAAO,SAASzF,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAI8G,0BAA0BJ,EAAYK,cAAc7G,EAAG,KAAK,CAACa,MAA2B,YAArB2F,EAAYf,KAAqB,iBAAmB,iBAAiB,CAAC3F,EAAIc,GAAG,IAAId,EAAIgC,GAAwB,YAArB0E,EAAYf,KAAqB,OAAS,QAAQ3F,EAAIgC,GAAGhC,EAAIiD,YAAYyD,EAAYM,SAAS,OAAO9G,EAAG,KAAK,CAACa,MAA2B,YAArB2F,EAAYf,KAAqB,gBAAkB,iBAAiB,CAAC3F,EAAIc,GAAG,IAAId,EAAIgC,GAAGhC,EAAIsD,qBAAqBoD,EAAYO,kBAAkB,OAAO/G,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAG0E,EAAYQ,iBAAiB,IAA2C,IAAvClH,EAAImH,wBAAwBzD,QAAiB1D,EAAIsG,mBAAyCtG,EAAIW,KAAzBT,EAAG,KAAK,CAACF,EAAI2D,GAAG,MAAe,OAAOzD,EAAG,oBAAoB,CAACG,MAAM,CAAC,eAAeL,EAAIoH,gBAAgB,MAAQpH,EAAImH,wBAAwBzD,OAAO,YAAY1D,EAAI6D,UAAUpD,GAAG,CAAC,cAAe4G,GAASrH,EAAIoH,gBAAkBC,EAAK,mBAAoBC,IAAWtH,EAAI6D,SAAWyD,EAAMtH,EAAIoH,gBAAkB,CAAC,MAAQ,KAAKpH,EAAIW,KAA6B,UAAvBX,EAAIiB,eAA4Bf,EAAG,MAAM,CAACA,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,kBAAkB,CAACX,EAAG,MAAM,CAACW,YAAY,qBAAqB,CAACX,EAAG,SAAS,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOvB,EAAIuH,eAAgB9F,WAAW,mBAAmBhB,GAAG,CAAC,OAAS,SAASC,GAAQ,IAAIiE,EAAgBC,MAAMC,UAAUC,OAAOC,KAAKrE,EAAOkB,OAAOoD,SAAQ,SAASC,GAAG,OAAOA,EAAEC,QAAQ,IAAGC,KAAI,SAASF,GAAG,IAAIG,EAAM,WAAYH,EAAIA,EAAEI,OAASJ,EAAE1D,MAAM,OAAO6D,CAAG,IAAIpF,EAAIuH,eAAe7G,EAAOkB,OAAO0D,SAAWX,EAAgBA,EAAc,EAAE,IAAI,CAACzE,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,MAAM,CAACL,EAAIc,GAAG,UAAUZ,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,OAAO,CAACL,EAAIc,GAAG,WAAWZ,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,OAAO,CAACL,EAAIc,GAAG,WAAWZ,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,WAAW,CAACL,EAAIc,GAAG,cAAsC,WAAvBd,EAAIuH,eAA6BrH,EAAG,MAAM,CAACW,YAAY,qBAAqB,CAACX,EAAG,QAAQ,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOvB,EAAIwH,qBAAsB/F,WAAW,yBAAyBpB,MAAM,CAAC,KAAO,QAAQsB,SAAS,CAAC,MAAS3B,EAAIwH,sBAAuB/G,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAOkB,OAAOC,YAAiB7B,EAAIwH,qBAAqB9G,EAAOkB,OAAOL,MAAK,KAAKrB,EAAG,OAAO,CAACF,EAAIc,GAAG,OAAOZ,EAAG,QAAQ,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOvB,EAAIyH,mBAAoBhG,WAAW,uBAAuBpB,MAAM,CAAC,KAAO,QAAQsB,SAAS,CAAC,MAAS3B,EAAIyH,oBAAqBhH,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAOkB,OAAOC,YAAiB7B,EAAIyH,mBAAmB/G,EAAOkB,OAAOL,MAAK,OAAOvB,EAAIW,OAAOT,EAAG,MAAM,CAACW,YAAY,kBAAkB,CAACX,EAAG,SAAS,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOvB,EAAI0H,eAAgBjG,WAAW,mBAAmBhB,GAAG,CAAC,OAAS,SAASC,GAAQ,IAAIiE,EAAgBC,MAAMC,UAAUC,OAAOC,KAAKrE,EAAOkB,OAAOoD,SAAQ,SAASC,GAAG,OAAOA,EAAEC,QAAQ,IAAGC,KAAI,SAASF,GAAG,IAAIG,EAAM,WAAYH,EAAIA,EAAEI,OAASJ,EAAE1D,MAAM,OAAO6D,CAAG,IAAIpF,EAAI0H,eAAehH,EAAOkB,OAAO0D,SAAWX,EAAgBA,EAAc,EAAE,IAAI,CAACzE,EAAG,SAAS,CAACG,MAAM,CAAC,MAAQ,KAAK,CAACL,EAAIc,GAAG,aAAad,EAAIsC,GAAItC,EAAI2H,WAAW,SAASC,GAAK,OAAO1H,EAAG,SAAS,CAACwC,IAAIkF,EAAIC,GAAGlG,SAAS,CAAC,MAAQiG,EAAIC,KAAK,CAAC7H,EAAIc,GAAG,IAAId,EAAIgC,GAAG4F,EAAIvG,MAAM,MAAM,KAAI,OAAOnB,EAAG,MAAM,CAACW,YAAY,mBAAmB,CAACX,EAAG,QAAQ,CAACW,YAAY,cAAc,CAACb,EAAI2D,GAAG,GAAGzD,EAAG,QAAQ,CAACF,EAAIsC,GAAItC,EAAI8H,uBAAuB,SAASC,EAAOtF,GAAO,OAAOvC,EAAG,KAAK,CAACwC,IAAI,SAASD,GAAO,CAACvC,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAG+F,EAAO7D,cAAchE,EAAG,KAAK,CAACA,EAAG,OAAO,CAACW,YAAY,aAAaE,MAAMf,EAAIgI,oBAAoBD,EAAOE,SAAS,CAACjI,EAAIc,GAAG,IAAId,EAAIgC,GAAGhC,EAAIkI,mBAAmBH,EAAOE,SAAS,SAAS/H,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAI4C,eAAemF,EAAOI,gBAAgBjI,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAG+F,EAAOK,SAAWpI,EAAI4C,eAAemF,EAAOK,UAAY,SAASlI,EAAG,KAAK,CAACF,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAIqI,kBAAkBN,EAAOI,WAAYJ,EAAOK,cAAclI,EAAG,KAAK,CAACF,EAAIc,GAAG,KAAKd,EAAIgC,GAAGhC,EAAIiD,YAAY8E,EAAOO,KAAO,SAAWpI,EAAG,KAAK,CAACA,EAAG,OAAO,CAACW,YAAY,iBAAiBJ,GAAG,CAAC,MAAQT,EAAIuI,qBAAqB,CAACvI,EAAIc,GAAG,QAA2B,cAAlBiH,EAAOE,OAAwB/H,EAAG,OAAO,CAACW,YAAY,6BAA6BJ,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIwI,kBAAkBT,EAAO,IAAI,CAAC/H,EAAIc,GAAG,QAAQd,EAAIW,QAAQ,IAAqC,IAAjCX,EAAIyI,kBAAkB/E,OAAcxD,EAAG,KAAK,CAACF,EAAI2D,GAAG,KAAK3D,EAAIW,MAAM,OAAOT,EAAG,oBAAoB,CAACG,MAAM,CAAC,eAAeL,EAAI0I,UAAU,MAAQ1I,EAAIyI,kBAAkB/E,OAAO,YAAY1D,EAAI6D,UAAUpD,GAAG,CAAC,cAAe4G,GAASrH,EAAI0I,UAAYrB,EAAK,mBAAoBC,IAAWtH,EAAI6D,SAAWyD,EAAMtH,EAAI0I,UAAY,CAAC,MAAQ,KAAK1I,EAAIW,KAA6B,aAAvBX,EAAIiB,eAA+Bf,EAAG,MAAM,CAACA,EAAG,MAAM,CAACW,YAAY,eAAe,CAACX,EAAG,MAAM,CAACW,YAAY,mBAAmB,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACX,EAAG,MAAM,CAACW,YAAY,iBAAiB,CAACb,EAAIc,GAAG,YAAYZ,EAAG,MAAM,CAACW,YAAY,iBAAiB,CAACb,EAAIc,GAAG,KAAKd,EAAIgC,GAAGhC,EAAIiD,YAAYjD,EAAIqG,qBAAqBnG,EAAG,MAAM,CAACW,YAAY,oBAAoB,CAACX,EAAG,KAAK,CAACW,YAAY,kBAAkB,CAACb,EAAIc,GAAG,YAAYZ,EAAG,MAAM,CAACW,YAAY,kBAAkB,CAACb,EAAIsC,GAAItC,EAAI2I,iBAAiB,SAAS3B,GAAQ,OAAO9G,EAAG,MAAM,CAACwC,IAAI,UAAUsE,EAAOjG,MAAM,CAAC,gBAAiB,CAAEmE,SAAUlF,EAAI4I,iBAAmB5B,IAAUvG,GAAG,CAAC,MAAQ,SAASC,GAAQV,EAAI4I,eAAiB5B,CAAM,IAAI,CAAChH,EAAIc,GAAG,IAAId,EAAIgC,GAAGgF,GAAQ,OAAO,IAAG9G,EAAG,MAAM,CAACW,YAAY,+BAA+B,CAACX,EAAG,QAAQ,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAOvB,EAAI6I,qBAAsBpH,WAAW,yBAAyBpB,MAAM,CAAC,KAAO,SAAS,YAAc,QAAQsB,SAAS,CAAC,MAAS3B,EAAI6I,sBAAuBpI,GAAG,CAAC,MAAQ,SAASC,GAAQV,EAAI4I,eAAiB,IAAI,EAAE,MAAQ,CAAC,SAASlI,GAAWA,EAAOkB,OAAOC,YAAiB7B,EAAI6I,qBAAqBnI,EAAOkB,OAAOL,MAAK,EAAEvB,EAAI8I,+BAA+B,GAAG5I,EAAG,KAAK,CAACW,YAAY,kBAAkB,CAACb,EAAIc,GAAG,YAAYZ,EAAG,MAAM,CAACW,YAAY,mBAAmB,CAACX,EAAG,MAAM,CAACa,MAAM,CAAC,iBAAkB,CAAEmE,SAAgC,WAAtBlF,EAAI+I,gBAA8BtI,GAAG,CAAC,MAAQ,SAASC,GAAQV,EAAI+I,cAAgB,QAAQ,IAAI,CAAC7I,EAAG,MAAM,CAACW,YAAY,eAAeR,MAAM,CAAC,IAAM2I,EAAQ,MAA0C,IAAM,SAAS9I,EAAG,OAAO,CAACF,EAAIc,GAAG,aAAaZ,EAAG,MAAM,CAACW,YAAY,mBAAmB,CAACX,EAAG,SAAS,CAACW,YAAY,kBAAkBR,MAAM,CAAC,UAAYL,EAAIiJ,aAAaxI,GAAG,CAAC,MAAQT,EAAIkJ,iBAAiB,CAAClJ,EAAIc,GAAG,oBAAoBd,EAAIW,YAAY,EACz9kB,EACIwI,EAAkB,CAAC,WAAY,IAAInJ,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,KAAK,CAACW,YAAY,cAAcR,MAAM,CAAC,QAAU,MAAM,CAACH,EAAG,MAAM,CAACW,YAAY,mBAAmB,CAACX,EAAG,IAAI,CAACW,YAAY,gCAAgCX,EAAG,MAAM,CAACW,YAAY,cAAc,CAACb,EAAIc,GAAG,kBAC5Q,EAAE,WAAY,IAAId,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIc,GAAG,SAASZ,EAAG,KAAK,CAACF,EAAIc,GAAG,UAAUZ,EAAG,KAAK,CAACF,EAAIc,GAAG,UAAUZ,EAAG,KAAK,CAACF,EAAIc,GAAG,UAAUZ,EAAG,KAAK,CAACF,EAAIc,GAAG,QAAQZ,EAAG,KAAK,CAACF,EAAIc,GAAG,UAAUZ,EAAG,KAAK,CAACF,EAAIc,GAAG,WAChP,EAAE,WAAY,IAAId,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,KAAK,CAACW,YAAY,cAAcR,MAAM,CAAC,QAAU,MAAM,CAACH,EAAG,MAAM,CAACW,YAAY,mBAAmB,CAACX,EAAG,IAAI,CAACW,YAAY,6BAA6BX,EAAG,MAAM,CAACW,YAAY,cAAc,CAACb,EAAIc,GAAG,oBACpP,EAAE,WAAY,IAAId,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIc,GAAG,WAAWZ,EAAG,KAAK,CAACF,EAAIc,GAAG,QAAQZ,EAAG,KAAK,CAACF,EAAIc,GAAG,UAAUZ,EAAG,KAAK,CAACF,EAAIc,GAAG,UAAUZ,EAAG,KAAK,CAACF,EAAIc,GAAG,UAAUZ,EAAG,KAAK,CAACF,EAAIc,GAAG,UAAUZ,EAAG,KAAK,CAACF,EAAIc,GAAG,WAClP,EAAE,WAAY,IAAId,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,KAAK,CAACW,YAAY,cAAcR,MAAM,CAAC,QAAU,MAAM,CAACH,EAAG,MAAM,CAACW,YAAY,mBAAmB,CAACX,EAAG,IAAI,CAACW,YAAY,4BAA4BX,EAAG,MAAM,CAACW,YAAY,cAAc,CAACb,EAAIc,GAAG,oBACnP,GCPIf,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,OAAO,CAACW,YAAY,gBAAgB,CAACX,EAAG,UAAUA,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACb,EAAIoJ,GAAG,YAAY,GAAGlJ,EAAG,WAAW,EACnM,EACIiJ,EAAkB,G,UCctB,GACA9H,KAAA,SACAgI,WAAA,CAAAC,OAAAA,EAAAA,IClBmQ,I,UCQ/PC,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIF,EAAeA,EAAiB,QCnB5BxJ,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACW,YAAY,qBAAqB,CAACX,EAAG,OAAO,CAACW,YAAY,oBAAoB,CAACb,EAAIc,GAAG,KAAKd,EAAIgC,GAAGhC,EAAIwJ,OAAO,QAAQtJ,EAAG,SAAS,CAACW,YAAY,kBAAkBR,MAAM,CAAC,SAA+B,IAApBL,EAAI4D,aAAmBnD,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIyJ,WAAWzJ,EAAI4D,YAAc,EAAE,IAAI,CAAC5D,EAAIc,GAAG,SAASZ,EAAG,OAAO,CAACW,YAAY,sBAAsB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAI4D,gBAAgB1D,EAAG,SAAS,CAACW,YAAY,kBAAkBR,MAAM,CAAC,SAAWL,EAAI4D,cAAgB5D,EAAI0J,YAAYjJ,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOV,EAAIyJ,WAAWzJ,EAAI4D,YAAc,EAAE,IAAI,CAAC5D,EAAIc,GAAG,SAASZ,EAAG,OAAO,CAACW,YAAY,mBAAmB,CAACb,EAAIc,GAAGd,EAAIgC,GAAGhC,EAAI6D,UAAU,WAAW3D,EAAG,MAAM,CAACW,YAAY,mBAAmB,CAACX,EAAG,OAAO,CAACF,EAAIc,GAAG,QAAQZ,EAAG,QAAQ,CAACkB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,iBAAiBC,MAAOvB,EAAI2J,UAAWlI,WAAW,YAAYmI,UAAU,CAAC,QAAS,KAAQvJ,MAAM,CAAC,KAAO,OAAO,IAAM,IAAI,IAAML,EAAI0J,YAAY/H,SAAS,CAAC,MAAS3B,EAAI2J,WAAYlJ,GAAG,CAAC,KAAO,CAACT,EAAI6J,WAAW,SAASnJ,GAAQ,OAAOV,EAAI8J,cAAc,GAAG,MAAQ,SAASpJ,GAAQ,OAAIA,EAAOiF,KAAKC,QAAQ,QAAQ5F,EAAI6F,GAAGnF,EAAOoF,QAAQ,QAAQ,GAAGpF,EAAOgC,IAAI,SAAgB,KAAY1C,EAAI6J,WAAW7D,MAAM,KAAMC,UAAU,EAAE,MAAQ,SAASvF,GAAWA,EAAOkB,OAAOC,YAAiB7B,EAAI2J,UAAU3J,EAAI+J,GAAGrJ,EAAOkB,OAAOL,OAAM,KAAKrB,EAAG,OAAO,CAACF,EAAIc,GAAG,UAC/1C,EACIqI,EAAkB,GCsCtB,GACA9H,KAAA,mBACA2I,MAAA,CACApG,YAAA,CACA+B,KAAAsE,OACAC,UAAA,GAEAV,MAAA,CACA7D,KAAAsE,OACAC,UAAA,GAEArG,SAAA,CACA8B,KAAAsE,OACAC,UAAA,IAGAC,OACA,OACAR,UAAA,KAAA/F,YAEA,EACAwG,MAAA,CACAxG,YAAAyG,GACA,KAAAV,UAAAU,CACA,GAEAC,SAAA,CACAZ,aACA,OAAAa,KAAAC,KAAA,KAAAhB,MAAA,KAAA3F,SACA,GAEA4G,QAAA,CACAhB,WAAApC,GACAA,GAAA,GAAAA,GAAA,KAAAqC,YACA,KAAAgB,MAAA,cAAArD,EAEA,EACAwC,aAEA,KAAAF,WAAA,KAAAA,WAAA,QAAAA,WAAA,KAAAD,WACA,KAAAC,YAAA,KAAA/F,aACA,KAAA6F,WAAA,KAAAE,WAIA,KAAAA,UAAA,KAAA/F,WAEA,ICvFyQ,ICQrQ,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIF,EAAe,EAAiB,Q,oBCqehC,G,QAAA,CACAvC,KAAA,YACAgI,WAAA,CAAAsB,kBAAA,IAAAC,OAAA,EAAAC,iBAAAA,GACAV,OACA,OAEAlJ,eAAA,eACA4C,SAAA,EACAwC,YAAA,EACAjG,kBAAA,EACAE,oBAAA,GACAC,iBAAA,UAGAuK,UAAA,GACApJ,iBAAA,GACAkC,YAAA,EACApC,kBAAA,EACAyC,cAAA,GACA/B,cAAA,EACAC,WAAA,KAGA4I,gBAAA,GACArF,uBAAA,GACAhB,qBAAA,KACAe,gBAAA,GACA2B,gBAAA,EACA7B,gBAAA,GACAC,cAAA,GACAc,oBAAA,EACAC,iBAAA,KACAL,YAAA,CACAC,cAAA,EACAC,aAAA,EACA4E,QAAA,GAIAxD,qBAAA,GACAC,mBAAA,GACAwD,UAAA,GACA1D,eAAA,KACAG,eAAA,GACAwD,cAAA,EACAC,WAAA,KACAzC,UAAA,EACAf,UAAA,CACA,CAAAE,GAAA,OAAAxG,KAAA,eACA,CAAAwG,GAAA,OAAAxG,KAAA,eACA,CAAAwG,GAAA,UAAAxG,KAAA,oBAIAsH,gBAAA,sBACAC,eAAA,IACAC,qBAAA,KACAE,cAAA,SACAqC,iBAAA,EAEA,EAEAd,SAAA,CAEA7G,oBACA,SAAA/B,iBAAA,YAAAoJ,UAEA,MAAAO,EAAA,KAAA3J,iBAAA4J,cACA,YAAAR,UAAAhG,QAAAtC,GACAA,EAAAG,aAAA2I,cAAAC,SAAAF,IAEA,EAEA9I,kBACA,MAAAiJ,GAAA,KAAA5H,YAAA,QAAAC,SACA4H,EAAAD,EAAA,KAAA3H,SACA,YAAAJ,kBAAAiI,MAAAF,EAAAC,EACA,EAEAxJ,oBACA,YAAAwB,kBAAAC,MACA,EAGAyD,0BACA,IAAAwE,EAAA,SAAAZ,iBAGA,mBAAArG,qBAAA,CACA,MAAAkH,EAAAC,SAAA,KAAAnH,sBACAoH,EAAA,IAAAC,KACAD,EAAAE,QAAAF,EAAAG,UAAAL,GAEAD,EAAAA,EAAA7G,QAAA4B,IACA,MAAAwF,EAAA,IAAAH,KAAArF,EAAA7D,YACA,OAAAqJ,GAAAJ,CAAA,GAEA,cAAAvG,iBAAA,KAAAC,cAAA,CACA,MAAA2G,EAAA,IAAAJ,KAAA,KAAAxG,iBACA6G,EAAA,IAAAL,KAAA,KAAAvG,eACA4G,EAAAC,SAAA,cAEAV,EAAAA,EAAA7G,QAAA4B,IACA,MAAAwF,EAAA,IAAAH,KAAArF,EAAA7D,YACA,OAAAqJ,GAAAC,GAAAD,GAAAE,CAAA,GAEA,CAUA,GAPA,KAAA3G,kBACAkG,EAAAA,EAAA7G,QAAA4B,GACAA,EAAAf,OAAA,KAAAF,mBAKA,KAAAC,uBAAA,CACA,MAAA2F,EAAA,KAAA3F,uBAAA4F,cACAK,EAAAA,EAAA7G,QAAA4B,GACAA,EAAAC,eAAA2E,cAAAC,SAAAF,IAEA,CAEA,OAAAM,CACA,EAEAlF,wBACA,MAAA+E,GAAA,KAAApE,gBAAA,QAAAvD,SACA4H,EAAAD,EAAA,KAAA3H,SACA,YAAAsD,wBAAAuE,MAAAF,EAAAC,EACA,EAIAhD,oBACA,SAAAwC,WAAA,SAAAA,UAAAvH,OAAA,SAEA,IAAAiI,EAAA,SAAAV,WAGA,mBAAA1D,eAAA,CACA,MAAAqE,EAAAC,SAAA,KAAAtE,gBACAuE,EAAA,IAAAC,KACAD,EAAAE,QAAAF,EAAAG,UAAAL,GAEAD,EAAAA,EAAA7G,QAAAiD,IACA,MAAAuE,EAAA,IAAAP,KAAAhE,EAAAI,YAAAJ,EAAAlF,YACA,OAAAyJ,GAAAR,CAAA,GAEA,cAAAtE,sBAAA,KAAAC,mBAAA,CACA,MAAA0E,EAAA,IAAAJ,KAAA,KAAAvE,sBACA4E,EAAA,IAAAL,KAAA,KAAAtE,oBACA2E,EAAAC,SAAA,cAEAV,EAAAA,EAAA7G,QAAAiD,IACA,MAAAuE,EAAA,IAAAP,KAAAhE,EAAAI,YAAAJ,EAAAlF,YACA,OAAAyJ,GAAAH,GAAAG,GAAAF,CAAA,GAEA,CASA,OANA,KAAA1E,iBACAiE,EAAAA,EAAA7G,QAAAiD,GACAA,EAAA7D,UAAAoH,cAAAC,SAAA,KAAA7D,eAAA4D,kBAIAK,CACA,EAEA7D,wBACA,MAAA0D,GAAA,KAAA9C,UAAA,QAAA7E,SACA4H,EAAAD,EAAA,KAAA3H,SACA,YAAA4E,kBAAAiD,MAAAF,EAAAC,EACA,EAGAxC,cACA,MAAAjC,EAAA,KAAAuF,oBACA,OAAAvF,GAAAA,EAAA,QAAA+B,gBAAA,KAAAqC,eACA,GAGAhB,MAAA,CACAoC,OAAAC,GAEA,MAAAC,EAAAD,EAAApB,MAAAqB,UACAA,GAAA,6CAAAnB,SAAAmB,KACA,KAAAzL,eAAAyL,EAEA,EACAzL,eAAAoJ,GACA,iBAAAA,GACA,KAAA7D,oBACA,KAAAmG,oBACA,WAAAtC,EACA,KAAAjI,cACA,aAAAiI,EACA,KAAAuC,mBACA,iBAAAvC,IACA,KAAA7D,oBAEA,KAAAqG,gBAAA,gBAEA,GAGAC,UACA,KAAAF,mBACA,KAAApG,oBACA,MAAAkG,EAAA,KAAAF,OAAAnB,MAAAqB,WAAA,eAEA,KAAAzL,eAAAyL,EAGA,KAAAG,gBAAAH,GACA,KAAAK,QACA,SAAAP,OAAAnB,QACA2B,IACAA,EAAAN,WAAA,6CAAAnB,SAAAyB,EAAAN,aACA,KAAAzL,eAAA+L,EAAAN,UACA,GAEA,CAAAO,WAAA,IAEA,KAAA7K,cAAA8K,MAAA,KAEA,KAAA7K,OAAA,cAGA,MAAAqK,EAAA,KAAAF,OAAAnB,MAAAqB,UACAA,GAAA,6CAAAnB,SAAAmB,KACA,KAAAzL,eAAAyL,EACA,IAEA,KAAAC,kBACA,EACAQ,gBACA,KAAAzC,MAAA,iBACA,EAEAD,QAAA,CAEA2C,wBAAAC,EAAA1H,EAAA,QACA,KAAArF,oBAAA+M,EACA,KAAA9M,iBAAAoF,EACA,KAAAvF,kBAAA,CACA,EAEAe,cAAAmM,GAEA,KAAArM,eAAAqM,EAGA,KAAAC,QAAAC,QAAA,CACAnC,MAAA,IACA,KAAAmB,OAAAnB,MACAqB,UAAAY,KAKA,WAAAA,EACA,KAAAlL,cACA,iBAAAkL,GACA,KAAA9G,oBACA,KAAAmG,oBACA,UAAAW,EACA,KAAAG,iBACA,aAAAH,GACA,KAAAV,kBAEA,EACAC,gBAAAS,GACA,OAAAA,GACA,aACA,KAAAlL,cACA,MACA,mBACA,KAAAoE,oBACA,KAAAmG,mBACA,MACA,YACA,KAAAc,iBACA,MACA,eACA,KAAAb,mBACA,MACA,EAEA,yBACA,IACA,MAAAc,QAAAC,EAAAA,EAAAA,IAAA,0BACA,MAAAD,EAAAvD,KAAAyD,OACA,KAAAvH,YAAAqH,EAAAvD,KAAAA,KAAAa,SAAA,EACA,KAAAN,MAAA,kBAEA,OAAAmD,GACA,KAAAC,SAAAD,MAAA,WACA,CACA,EAEAE,4BACA,SAAAvG,uBAAA,KAAAC,mBAEA,YADA,KAAAqG,SAAAD,MAAA,cAIA,MAAA1B,EAAA,IAAAJ,KAAA,KAAAvE,sBACA4E,EAAA,IAAAL,KAAA,KAAAtE,oBAEA0E,EAAAC,EACA,KAAA0B,SAAAD,MAAA,iBAIA,KAAAnF,UAAA,EACA,KAAA+E,iBACA,EAGAO,oBAAAC,GACA,IAAAA,EAAA,WAEA,IACA,MAAAC,EAAA,IAAAnC,KAAAkC,GAEA,OAAAC,EAAAC,eAAA,SACAC,KAAA,UACAC,MAAA,UACAC,IAAA,UACAC,KAAA,UACAC,OAAA,UACAC,OAAA,UACAC,QAAA,IACAlB,QAAA,UACA,OAAAmB,GACA,UACA,CACA,EAGAtG,kBAAAuG,EAAAC,GACA,IAAAD,EAAA,YAEA,IACA,MAAAE,EAAA,IAAA/C,KAAA6C,GACAG,EAAAF,EAAA,IAAA9C,KAAA8C,GAAA,IAAA9C,KAGA,GAAAiD,MAAAF,EAAAG,WAAA,aACA,GAAAJ,GAAAG,MAAAD,EAAAE,WAAA,aAGA,GAAAH,EAAA,IAAA/C,KACA,YAIA,IAAA8C,GAAAE,EAAA,IAAAhD,KAAA,CACA,MAAAmD,EAAA,IAAAnD,KAAA+C,EACAK,EAAA5E,KAAA6E,MAAAF,EAAA,MACAG,EAAA9E,KAAA6E,MAAAF,EAAA,UAEA,SAAAC,MAAAE,MACA,CAGA,MAAAH,EAAAH,EAAAD,EACAK,EAAA5E,KAAA6E,MAAAF,EAAA,MACAG,EAAA9E,KAAA6E,MAAAF,EAAA,UAEA,OAAAC,EAAA,EACA,GAAAA,MAAAE,MAEA,GAAAA,KACA,OAAAV,GACA,UACA,CACA,EAIApG,qBAEA,KAAAgF,QAAAC,QAAA,CACA8B,KAAA,aACAjE,MAAA,CACAqB,UAAA,WACA6C,UAAAxD,KAAAyD,QAGA,EAGA,oBACA,KAAAtN,cAAA,EACA,KAAAC,WAAA,KACA,IACA,MAAAuL,QAAAC,EAAAA,EAAAA,IAAA,oCAGA,KAAA7C,UAAA4C,EAAAvD,KAAAA,KAAAhF,KAAA3C,IAAA,CACAqF,GAAArF,EAAAnB,KACAsB,aAAAH,EAAAiN,WACA1M,eAAA,KAAA2M,eAAAlN,EAAAmN,aACAtM,eAAAb,EAAAoN,sBAAA,OACArM,YAAAf,EAAAqN,YACA3M,WAAA,KAAA4M,mBAAAtN,GACAW,SAAAX,EAAAuN,eACAlN,WAAAL,EAAAwN,YACA9L,UAAA1B,EAAAnB,MAAA,OACA8C,OAAA3B,EAAAyN,QAAA,OACA7L,UAAA5B,EAAA0N,sBAAA,EACA7L,aAAA7B,EAAA6B,cAAA,KACAC,UAAA9B,EAAA2N,oBAAA,EACA5L,YAAA/B,EAAA+B,aAAA,KACAC,WAAAhC,EAAA4N,WAAA,KACA3L,OAAAjC,EAAA6N,iBAAA,KACAC,QAAA9N,MACA+N,MAAA,CAAAC,EAAAC,IAEA,IAAA1E,KAAA0E,EAAA5N,YAAA,IAAAkJ,KAAAyE,EAAA3N,aAEA,OAAAgL,GACA,KAAA1L,WAAA,gBAEA,SACA,KAAAD,cAAA,CACA,CACA,EAGA,0BACA,KAAAoE,oBAAA,EACA,KAAAC,iBAAA,KACA,IACA,MAAAmH,QAAAC,EAAAA,EAAAA,IAAA,qCACA2C,EAAA5C,EAAAvD,KAAAA,KAGAuG,EAAA,IAAAC,IACAL,EAAAM,SAAAlK,IAEA,MAAAmK,EAAAnK,EAAAoK,UAAApK,EAAAqK,aAEAL,EAAAM,IAAAH,IACAH,EAAAO,IAAAJ,EAAA,CACAlK,eAAAD,EAAAwK,gBAAAxK,EAAAyK,eACAxL,KAAA,OAAAe,EAAA0K,aAAA,mBACArK,SAAA,KAAAsK,WAAA3K,EAAA0K,cACApK,OAAAN,EAAA4K,aAAA5K,EAAA6K,gBACA1O,WAAA6D,EAAAsJ,YACA/I,gBAAAP,EAAArD,gBAAA,KACA4E,OAAA,KAAAuJ,qBAAA9K,EAAAiJ,aACAzI,YAAAR,EAAAQ,aAAA,KAEA,IAGA,KAAA6D,gBAAAnG,MAAA6M,KAAAf,EAAAgB,SACA,OAAA7D,GACA,KAAAvH,mBAAA,MACA,SACA,KAAAA,oBAAA,CACA,CACA,EAGA,uBACA,KAAA4E,cAAA,EACA,KAAAC,WAAA,KACA,IACA,IAAAwG,EAAA,GAGA,mBAAApK,gBAAA,KAAAC,sBAAA,KAAAC,mBACAkK,EAAAC,WAAA,KAAApK,qBACAmK,EAAAE,SAAA,KAAApK,wBACA,mBAAAF,eAAA,CAEA,MAAAqE,EAAAC,SAAA,KAAAtE,gBACAuE,EAAA,IAAAC,KACAD,EAAAE,QAAAF,EAAAG,UAAAL,GACA+F,EAAAC,WAAA9F,EAAAgG,cAAAC,MAAA,OACA,CAGA,KAAArK,iBACAiK,EAAAzN,UAAA,KAAAwD,sBAGAiG,EAAAA,EAAAA,IAAA,4CAMAqE,EAAA,GAKA,OADA,KAAA/G,UAAA+G,EACA,KAAA/G,SAEA,OAAA4C,GAGA,OAFA,KAAA1C,WAAA,iBAEA,EACA,SACA,KAAAD,cAAA,CACA,CACA,EAQA,yBACA,IACA,MAAAwC,QAAAC,EAAAA,EAAAA,IAAA,qCACAsE,EAAAvE,EAAAvD,KAAAA,MAAA,GAGAuG,EAAA,IAAAC,IACAsB,EAAArB,SAAAsB,IACA,MAAArB,EAAAqB,EAAApB,UAAAoB,EAAAnB,aACAL,EAAAM,IAAAH,IACAH,EAAAO,IAAAJ,EAAAqB,EACA,IAGA,MAAAC,EAAAvN,MAAA6M,KAAAf,EAAAgB,UAGAvL,EAAAgM,EACArN,QAAAoN,GAAA,OAAAA,EAAAd,eACAgB,QAAA,CAAAC,EAAAH,IAAAG,GAAAC,WAAAJ,EAAAZ,cAAA,OAGAlL,EAAA+L,EACArN,QAAAoN,GAAA,OAAAA,EAAAd,eACAgB,QAAA,CAAAC,EAAAH,IAAAG,GAAAC,WAAAJ,EAAAX,kBAAA,OAEA,KAAArL,YAAA,CACAC,gBACAC,eACA4E,QAAA,KAAA3E,YAEA,OAAAwH,GACA,KAAAC,SAAAD,MAAA,aACA,CACA,EAGA6B,eAAAzH,GACA,MAAAsK,EAAA,CACA,SACA,WACA,YAEA,OAAAA,EAAAtK,IAAAA,CACA,EAGAoJ,WAAA1L,GACA,aAAAA,EAAA,SACA,EAGA6L,qBAAAvJ,GACA,MAAAsK,EAAA,CACA,YACA,WACA,gBAEA,OAAAA,EAAAtK,IAAAA,CACA,EAGA6H,mBAAAtN,GACA,IAAAA,EAAAgQ,UAAA,OAAAhQ,EAAAqN,YAGA,OAAArN,EAAAiQ,YACA,WACA,OAAAjQ,EAAAgQ,UAAAE,WACA,UACA,OAAAlQ,EAAAgQ,UAAAG,UACA,YACA,OAAAnQ,EAAAgQ,UAAAI,YACA,WACA,OAAApQ,EAAAgQ,UAAAK,WACA,QACA,OAAArQ,EAAAqN,YAEA,EAGA5M,YAAA6P,GACA,OAAA9D,MAAA8D,GAAA,OACAR,WAAAQ,GAAAC,QAAA,EACA,EAEAC,WAAAC,GACA,WAAAlH,KAAAkH,GAAAC,oBACA,EAEAtQ,eAAAqQ,GACA,IAAAA,EAAA,SACA,MAAA/E,EAAA,IAAAnC,KAAAkH,GACA,OAAA/E,EAAAC,gBACA,EAGArL,sBAAAmF,GACA,OACA,sBACA,wBACA,uBACA,sBACA,4BACAA,IAAA,EACA,EAEAjF,qBAAAiF,GACA,OACA,WACA,aACA,eACA,cACA,iBACAA,IAAAA,CACA,EAEA7E,sBAAA+P,GACA,OACA,uBACA,wBACA,qBACA,yBACA,yBACAA,IAAA,EACA,EAEA7P,qBAAA6P,GACA,OACA,aACA,cACA,WACA,cACAA,IAAAA,CACA,EAEArR,eACA,KAAA8B,YAAA,CACA,EAEA7B,mBACA,KAAAL,iBAAA,GACA,KAAAkC,YAAA,CACA,EAEAvB,OAAA+Q,GACA,eAAAA,EACA,KAAAtI,UAAAyF,MAAA,CAAAC,EAAAC,IACA,IAAA1E,KAAA0E,EAAA2C,IAAA,IAAArH,KAAAyE,EAAA4C,MAGA,KAAAtI,UAAAyF,MAAA,CAAAC,EAAAC,IACAD,EAAA4C,GAAA3C,EAAA2C,GAAA,MAGA,EAEA5P,iBAAAhB,GACA,KAAAyB,cAAAzB,EACA,KAAAhB,kBAAA,EACA,KAAA6R,WAAA,KACAC,SAAAC,cAAA,kBAAAC,eAAA,CAAAC,SAAA,aAEA,EAEAzP,gBACA,KAAAxC,kBAAA,EACAkS,OAAAC,SAAA,CAAAC,IAAA,EAAAH,SAAA,UACA,EAEA3P,SAAAuD,GACA,KAAAzD,YAAAyD,CACA,EAEAtD,qBAAAuD,GACA,KAAAzD,SAAAyD,EACA,KAAA1D,YAAA,CACA,EAGAmC,qBACA,KAAAqB,gBAAA,CACA,EAEAyM,uBACA,KAAAtO,iBAAA,KAAAC,gBACA,KAAA4B,gBAAA,EAEA,EAEAR,wBAAAjB,GACA,OACA,qBACA,uBACA,sBACAA,IAAA,EACA,EAEAkB,uBAAAlB,GACA,OACA,YACA,aACA,aACAA,IAAAA,CACA,EAEAmB,0BAAAgN,GACA,OACA,YACA,cACAA,IAAAA,CACA,EAEAC,0BAAA9L,GACA,OACA,yBACA,yBACA,sBACA,0BACAA,IAAA,EACA,EAEA+L,yBAAA/L,GACA,OACA,aACA,cACA,YACA,kBACAA,IAAAA,CACA,EAGAD,oBAAAC,GACA,OACA,wBACA,yBACA,iCACA,uBACA,4BACA,sBACAA,IAAA,EACA,EAEAC,mBAAAD,GACA,OACA,YACA,cACA,uBACA,gBACA,cACAA,IAAAA,CACA,EAEAgM,aAAAlM,GACA,KAAA+F,SAAAoG,QAAA,OAAAnM,EAAA7D,eACA,EAGAqI,oBACA,YAAA3D,gBAAA,KAAAC,oBACA,EAEAC,0BACA,KAAAF,eAAA,IACA,EAEA,uBACA,MAAA5B,EAAA,KAAAuF,oBACA,IAAAvF,GAAAA,GAAA,EAEA,KAAAoG,wBAAA,0BAFA,CAOA,KAAAhC,iBAAA,EAEA,IAEA,MAAAsC,QAAAyG,EAAAA,EAAAA,IAAA,gBAAAnN,OAAAA,IACA,GAAA0G,EAAAvD,KAAAoB,SAAA,UAGA,YADA,KAAA6B,wBAAAM,EAAAvD,KAAA,SAIA,MAAAiK,EAAAd,SAAAe,cAAA,OAEAD,EAAAE,UAAA5G,EAAAvD,KACAmJ,SAAAiB,KAAAC,YAAAJ,GACA,MAAAK,EAAAL,EAAAb,cAAA,QAMAkB,GACA,KAAArH,wBAAA,0BACAqH,EAAA7S,OAAA,SACA6S,EAAAC,SAIAC,YAAA,KACA,KAAA/H,mBACA,KAAApG,mBAAA,GACA,MAEA,KAAA4G,wBAAA,mBAEA,OAAAS,GACA,KAAAT,wBAAA,uBACA,SACA,KAAAhC,iBAAA,CACA,CAzCA,CA0CA,KC3zCkQ,ICQ9P,GAAY,OACd,EACArL,EACAoJ,GACA,EACA,KACA,WACA,MAIF,EAAe,EAAiB,O", "sources": ["webpack://portal-ui/./src/views/Ordermange/OrderView.vue", "webpack://portal-ui/./src/components/common/layout-fee.vue", "webpack://portal-ui/src/components/common/layout-fee.vue", "webpack://portal-ui/./src/components/common/layout-fee.vue?63c0", "webpack://portal-ui/./src/components/common/layout-fee.vue?5cb0", "webpack://portal-ui/./src/views/Ordermange/CommonPagination.vue", "webpack://portal-ui/src/views/Ordermange/CommonPagination.vue", "webpack://portal-ui/./src/views/Ordermange/CommonPagination.vue?3834", "webpack://portal-ui/./src/views/Ordermange/CommonPagination.vue?947f", "webpack://portal-ui/src/views/Ordermange/OrderView.vue", "webpack://portal-ui/./src/views/Ordermange/OrderView.vue?858f", "webpack://portal-ui/./src/views/Ordermange/OrderView.vue?c69d"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(_vm.showNotification)?_c('SlideNotification',{attrs:{\"message\":_vm.notificationMessage,\"type\":_vm.notificationType,\"duration\":3000,\"minHeight\":_vm.minHeight},on:{\"close\":function($event){_vm.showNotification = false}}}):_vm._e(),_c('div',{staticStyle:{\"width\":\"100%\"}},[_c('div',{staticClass:\"fee-center-container\"},[_c('div',{staticClass:\"navigation-sidebar\"},[_c('h2',{staticClass:\"nav-title\"},[_vm._v(\"费用中心\")]),_c('ul',{staticClass:\"nav-list\"},[_c('li',{staticClass:\"nav-item\",class:{ active: _vm.currentSection === 'transactions' }},[_c('a',{attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.changeSection('transactions')}}},[_c('i',{staticClass:\"el-icon-money\"}),_c('span',[_vm._v(\"收支明细\")])])]),_c('li',{staticClass:\"nav-item\",class:{ active: _vm.currentSection === 'recharge' }},[_c('a',{attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.changeSection('recharge')}}},[_c('i',{staticClass:\"el-icon-wallet\"}),_c('span',[_vm._v(\"充值\")])])])])]),_c('div',{staticClass:\"main-content\"},[(_vm.currentSection === 'orders')?_c('div',[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.showOrderDetails),expression:\"!showOrderDetails\"}],staticClass:\"tab-content\"},[_c('div',{staticClass:\"search-section\"},[_c('div',{staticClass:\"search-bar\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.orderSearchQuery),expression:\"orderSearchQuery\"}],attrs:{\"type\":\"text\",\"placeholder\":\"搜索订单号\"},domProps:{\"value\":(_vm.orderSearchQuery)},on:{\"input\":function($event){if($event.target.composing)return;_vm.orderSearchQuery=$event.target.value}}}),_c('button',{staticClass:\"search-button\",on:{\"click\":_vm.searchOrders}},[_c('i',{staticClass:\"el-icon-search\"})]),(_vm.orderSearchQuery)?_c('button',{staticClass:\"clear-button\",on:{\"click\":_vm.clearOrderSearch}},[_c('i',{staticClass:\"el-icon-close\"})]):_vm._e()]),_c('div',{staticClass:\"currency-display\"},[_vm._v(\" 金额单位: ¥ \"),_c('span',{staticClass:\"flow-count\"},[_vm._v(\"订单总数: \"+_vm._s(_vm.currentOrderTotal))])])]),_c('div',{staticClass:\"table-container\"},[(_vm.orderLoading)?_c('div',{staticClass:\"loading-state\"},[_c('i',{staticClass:\"el-icon-loading\"}),_c('span',[_vm._v(\"正在加载订单数据...\")])]):_vm._e(),(_vm.orderError)?_c('div',{staticClass:\"error-state\"},[_c('i',{staticClass:\"el-icon-error\"}),_c('span',[_vm._v(_vm._s(_vm.orderError))]),_c('button',{on:{\"click\":_vm.fetchOrders}},[_vm._v(\"重试\")])]):_vm._e(),_c('table',{staticClass:\"data-table\"},[_c('thead',[_c('tr',[_c('th',[_vm._v(\"订单号 \"),_c('i',{staticClass:\"el-icon-sort\",on:{\"click\":function($event){return _vm.sortBy('order_number')}}})]),_c('th',[_vm._v(\"订单创建时间 \"),_c('i',{staticClass:\"el-icon-sort\",on:{\"click\":function($event){return _vm.sortBy('created_at')}}})]),_c('th',[_vm._v(\"支付状态 \"),_c('i',{staticClass:\"el-icon-sort\",on:{\"click\":function($event){return _vm.sortBy('payment_status')}}})]),_c('th',[_vm._v(\"单价 \"),_c('i',{staticClass:\"el-icon-sort\",on:{\"click\":function($event){return _vm.sortBy('unit_price')}}})]),_c('th',[_vm._v(\"时长 \"),_c('i',{staticClass:\"el-icon-sort\",on:{\"click\":function($event){return _vm.sortBy('duration')}}})]),_c('th',[_vm._v(\"付款方式 \"),_c('i',{staticClass:\"el-icon-sort\",on:{\"click\":function($event){return _vm.sortBy('payment_method')}}})]),_c('th',[_vm._v(\"合计\")]),_c('th',[_vm._v(\"操作\")])])]),_c('tbody',[_vm._l((_vm.paginatedOrders),function(order,index){return _c('tr',{key:'order-'+index},[_c('td',[_vm._v(_vm._s(order.order_number))]),_c('td',[_vm._v(_vm._s(_vm.formatDateTime(order.created_at)))]),_c('td',[_c('span',{staticClass:\"status-tag\",class:_vm.getPaymentStatusClass(order.payment_status)},[_vm._v(\" \"+_vm._s(_vm.getPaymentStatusText(order.payment_status))+\" \")])]),_c('td',[_vm._v(_vm._s(_vm.formatPrice(order.unit_price)))]),_c('td',[_vm._v(_vm._s(order.duration))]),_c('td',[_c('span',{staticClass:\"payment-method-tag\",class:_vm.getPaymentMethodClass(order.payment_method)},[_vm._v(\" \"+_vm._s(_vm.getPaymentMethodText(order.payment_method))+\" \")])]),_c('td',[_vm._v(_vm._s(_vm.formatPrice(order.total_price)))]),_c('td',[_c('span',{staticClass:\"operation-link\",on:{\"click\":function($event){return _vm.viewOrderDetails(order)}}},[_vm._v(\"查看详情\")])])])}),(_vm.filteredOrderData.length === 0 && !_vm.orderLoading)?_c('tr',[_vm._m(0)]):_vm._e()],2)])]),_c('common-pagination',{attrs:{\"current-page\":_vm.currentPage,\"total\":_vm.filteredOrderData.length,\"page-size\":_vm.pageSize},on:{\"change-page\":_vm.goToPage,\"change-page-size\":_vm.handlePageSizeChange}})],1),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showOrderDetails),expression:\"showOrderDetails\"}],staticClass:\"order-details\"},[_c('div',{staticClass:\"detail-card\"},[_c('div',{staticClass:\"detail-header\"},[_c('h2',{staticClass:\"detail-title\"},[_vm._v(\"订单详情\")]),_c('button',{staticClass:\"back-button\",on:{\"click\":_vm.showOrderList}},[_c('i',{staticClass:\"el-icon-back\"}),_vm._v(\" 返回列表 \")])]),_c('div',{staticClass:\"detail-content\"},[_c('h3',{staticClass:\"detail-subtitle\"},[_vm._v(\"订单概况\")]),_c('div',{staticClass:\"detail-section\"},[_c('div',{staticClass:\"detail-row\"},[_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"订单号:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedOrder.order_number))])]),_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"订单状态:\")]),_c('div',{staticClass:\"detail-value\"},[_c('span',{staticClass:\"status-tag\",class:_vm.getPaymentStatusClass(_vm.selectedOrder.payment_status)},[_vm._v(\" \"+_vm._s(_vm.getPaymentStatusText(_vm.selectedOrder.payment_status))+\" \")])])])]),_c('div',{staticClass:\"detail-row\"},[_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"支付方式:\")]),_c('div',{staticClass:\"detail-value\"},[_c('span',{staticClass:\"payment-method-tag\",class:_vm.getPaymentMethodClass(_vm.selectedOrder.payment_method)},[_vm._v(\" \"+_vm._s(_vm.getPaymentMethodText(_vm.selectedOrder.payment_method))+\" \")])])]),_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"单价:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(\"¥ \"+_vm._s(_vm.formatPrice(_vm.selectedOrder.unit_price)))])])]),_c('div',{staticClass:\"detail-row\"},[_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"订单创建时间:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.formatDateTime(_vm.selectedOrder.created_at)))])]),_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"订单金额:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(\"¥ \"+_vm._s(_vm.formatPrice(_vm.selectedOrder.total_price)))])])])]),_c('h3',{staticClass:\"detail-subtitle\"},[_vm._v(\"GPU信息\")]),_c('div',{staticClass:\"detail-section\"},[_c('div',{staticClass:\"detail-row\"},[_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"型号:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedOrder.gpu_model))])]),_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"地区:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedOrder.region))])])]),_c('div',{staticClass:\"detail-row\"},[_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"显卡数量:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedOrder.gpu_count)+\" 个\")])]),_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"显存:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedOrder.video_memory)+\" GB\")])])]),_c('div',{staticClass:\"detail-row\"},[_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"VCPU核数:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedOrder.cpu_cores)+\" 核\")])]),_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"系统盘:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedOrder.system_disk)+\" SSD\")])])]),_c('div',{staticClass:\"detail-row\"},[_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"云盘:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedOrder.cloud_disk)+\" GB\")])]),_c('div',{staticClass:\"detail-item\"},[_c('div',{staticClass:\"detail-label\"},[_vm._v(\"内存:\")]),_c('div',{staticClass:\"detail-value\"},[_vm._v(_vm._s(_vm.selectedOrder.memory)+\" GB\")])])])])])])])]):_vm._e(),(_vm.currentSection === 'transactions')?_c('div',[_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"search-section\"},[_c('div',{staticClass:\"date-range-picker\"},[_c('select',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.transactionDateRange),expression:\"transactionDateRange\"}],on:{\"change\":function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = \"_value\" in o ? o._value : o.value;return val}); _vm.transactionDateRange=$event.target.multiple ? $$selectedVal : $$selectedVal[0]}}},[_c('option',{attrs:{\"value\":\"7\"}},[_vm._v(\"最近7天\")]),_c('option',{attrs:{\"value\":\"30\"}},[_vm._v(\"最近一个月\")]),_c('option',{attrs:{\"value\":\"90\"}},[_vm._v(\"最近三个月\")]),_c('option',{attrs:{\"value\":\"custom\"}},[_vm._v(\"自定义时间段\")])]),(_vm.transactionDateRange === 'custom')?_c('div',{staticClass:\"custom-date-range\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.customDateStart),expression:\"customDateStart\"}],attrs:{\"type\":\"date\"},domProps:{\"value\":(_vm.customDateStart)},on:{\"input\":function($event){if($event.target.composing)return;_vm.customDateStart=$event.target.value}}}),_c('span',[_vm._v(\"至\")]),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.customDateEnd),expression:\"customDateEnd\"}],attrs:{\"type\":\"date\"},domProps:{\"value\":(_vm.customDateEnd)},on:{\"input\":function($event){if($event.target.composing)return;_vm.customDateEnd=$event.target.value}}})]):_vm._e()]),_c('div',{staticClass:\"search-filters\"},[_c('select',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.transactionType),expression:\"transactionType\"}],on:{\"change\":function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = \"_value\" in o ? o._value : o.value;return val}); _vm.transactionType=$event.target.multiple ? $$selectedVal : $$selectedVal[0]}}},[_c('option',{attrs:{\"value\":\"\"}},[_vm._v(\"全部类型\")]),_c('option',{attrs:{\"value\":\"income\"}},[_vm._v(\"收入\")]),_c('option',{attrs:{\"value\":\"expense\"}},[_vm._v(\"支出\")])]),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.transactionSearchQuery),expression:\"transactionSearchQuery\"}],attrs:{\"type\":\"text\",\"placeholder\":\"搜索流水号\"},domProps:{\"value\":(_vm.transactionSearchQuery)},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.searchTransactions.apply(null, arguments)},\"input\":function($event){if($event.target.composing)return;_vm.transactionSearchQuery=$event.target.value}}}),_c('button',{staticClass:\"search-button\",on:{\"click\":_vm.searchTransactions}},[_c('i',{staticClass:\"el-icon-search\"})])])]),_c('div',{staticClass:\"transaction-summary\"},[_c('div',{staticClass:\"summary-card\"},[_c('div',{staticClass:\"summary-title\"},[_vm._v(\"总充值\")]),_c('div',{staticClass:\"summary-value income\"},[_vm._v(\"¥ \"+_vm._s(_vm.formatPrice(_vm.summaryData.totalRecharge)))])]),_c('div',{staticClass:\"summary-card\"},[_c('div',{staticClass:\"summary-title\"},[_vm._v(\"总消费\")]),_c('div',{staticClass:\"summary-value expense\"},[_vm._v(\"¥ \"+_vm._s(_vm.formatPrice(_vm.summaryData.totalExpense)))])]),_c('div',{staticClass:\"summary-card\"},[_c('div',{staticClass:\"summary-title\"},[_vm._v(\"账户余额\")]),_c('div',{staticClass:\"summary-value\"},[_vm._v(\"¥ \"+_vm._s(_vm.formatPrice(_vm.userBalance)))])])]),_c('div',{staticClass:\"table-container\"},[(_vm.transactionLoading)?_c('div',{staticClass:\"loading-state\"},[_c('i',{staticClass:\"el-icon-loading\"}),_c('span',[_vm._v(\"正在加载交易数据...\")])]):_vm._e(),(_vm.transactionError)?_c('div',{staticClass:\"error-state\"},[_c('i',{staticClass:\"el-icon-error\"}),_c('span',[_vm._v(_vm._s(_vm.transactionError))]),_c('button',{on:{\"click\":_vm.fetchTransactions}},[_vm._v(\"重试\")])]):_vm._e(),_c('table',{staticClass:\"data-table\"},[_vm._m(1),_c('tbody',[_vm._l((_vm.paginatedTransactions),function(transaction,index){return _c('tr',{key:'transaction-'+index},[_c('td',[_vm._v(_vm._s(transaction.transaction_id))]),_c('td',[_vm._v(_vm._s(_vm.formatDateTime(transaction.created_at)))]),_c('td',[_c('span',{staticClass:\"transaction-type\",class:_vm.getTransactionTypeClass(transaction.type)},[_vm._v(\" \"+_vm._s(_vm.getTransactionTypeName(transaction.type))+\" \")])]),_c('td',[_vm._v(_vm._s(_vm.getTransactionTypeNamePay(transaction.pay_type)))]),_c('td',{class:transaction.type === 'expense' ? 'expense-amount' : 'income-amount'},[_vm._v(\" \"+_vm._s(transaction.type === 'expense' ? '￥ - ' : '￥ + ')+_vm._s(_vm.formatPrice(transaction.amount))+\" \")]),_c('td',{class:transaction.type === 'expense' ? 'expense-zhifu' : 'income-shouru'},[_vm._v(\" \"+_vm._s(_vm.getPaymentMethodText(transaction.payment_channel))+\" \")]),_c('td',[_vm._v(_vm._s(transaction.description))])])}),(_vm.filteredTransactionData.length === 0 && !_vm.transactionLoading)?_c('tr',[_vm._m(2)]):_vm._e()],2)])]),_c('common-pagination',{attrs:{\"current-page\":_vm.transactionPage,\"total\":_vm.filteredTransactionData.length,\"page-size\":_vm.pageSize},on:{\"change-page\":(page) => _vm.transactionPage = page,\"change-page-size\":(size) => { _vm.pageSize = size; _vm.transactionPage = 1; }}})],1)]):_vm._e(),(_vm.currentSection === 'usage')?_c('div',[_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"search-section\"},[_c('div',{staticClass:\"date-range-picker\"},[_c('select',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.usageDateRange),expression:\"usageDateRange\"}],on:{\"change\":function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = \"_value\" in o ? o._value : o.value;return val}); _vm.usageDateRange=$event.target.multiple ? $$selectedVal : $$selectedVal[0]}}},[_c('option',{attrs:{\"value\":\"7\"}},[_vm._v(\"最近7天\")]),_c('option',{attrs:{\"value\":\"30\"}},[_vm._v(\"最近一个月\")]),_c('option',{attrs:{\"value\":\"90\"}},[_vm._v(\"最近三个月\")]),_c('option',{attrs:{\"value\":\"custom\"}},[_vm._v(\"自定义时间段\")])]),(_vm.usageDateRange === 'custom')?_c('div',{staticClass:\"custom-date-range\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.customUsageDateStart),expression:\"customUsageDateStart\"}],attrs:{\"type\":\"date\"},domProps:{\"value\":(_vm.customUsageDateStart)},on:{\"input\":function($event){if($event.target.composing)return;_vm.customUsageDateStart=$event.target.value}}}),_c('span',[_vm._v(\"至\")]),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.customUsageDateEnd),expression:\"customUsageDateEnd\"}],attrs:{\"type\":\"date\"},domProps:{\"value\":(_vm.customUsageDateEnd)},on:{\"input\":function($event){if($event.target.composing)return;_vm.customUsageDateEnd=$event.target.value}}})]):_vm._e()]),_c('div',{staticClass:\"search-filters\"},[_c('select',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.usageFilterGpu),expression:\"usageFilterGpu\"}],on:{\"change\":function($event){var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = \"_value\" in o ? o._value : o.value;return val}); _vm.usageFilterGpu=$event.target.multiple ? $$selectedVal : $$selectedVal[0]}}},[_c('option',{attrs:{\"value\":\"\"}},[_vm._v(\"全部GPU型号\")]),_vm._l((_vm.gpuModels),function(gpu){return _c('option',{key:gpu.id,domProps:{\"value\":gpu.id}},[_vm._v(\" \"+_vm._s(gpu.name)+\" \")])})],2)])]),_c('div',{staticClass:\"table-container\"},[_c('table',{staticClass:\"data-table\"},[_vm._m(3),_c('tbody',[_vm._l((_vm.paginatedUsageRecords),function(record,index){return _c('tr',{key:'usage-'+index},[_c('td',[_vm._v(_vm._s(record.gpu_model))]),_c('td',[_c('span',{staticClass:\"status-tag\",class:_vm.getUsageStatusClass(record.status)},[_vm._v(\" \"+_vm._s(_vm.getUsageStatusText(record.status))+\" \")])]),_c('td',[_vm._v(_vm._s(_vm.formatDateTime(record.start_time)))]),_c('td',[_vm._v(_vm._s(record.end_time ? _vm.formatDateTime(record.end_time) : '--'))]),_c('td',[_vm._v(_vm._s(_vm.calculateDuration(record.start_time, record.end_time)))]),_c('td',[_vm._v(\"¥ \"+_vm._s(_vm.formatPrice(record.cost / 10000)))]),_c('td',[_c('span',{staticClass:\"operation-link\",on:{\"click\":_vm.navigateToRecharge}},[_vm._v(\"续费\")]),(record.status === 'scheduled')?_c('span',{staticClass:\"operation-link cancel-link\",on:{\"click\":function($event){return _vm.cancelReservation(record)}}},[_vm._v(\"取消\")]):_vm._e()])])}),(_vm.filteredUsageData.length === 0)?_c('tr',[_vm._m(4)]):_vm._e()],2)])]),_c('common-pagination',{attrs:{\"current-page\":_vm.usagePage,\"total\":_vm.filteredUsageData.length,\"page-size\":_vm.pageSize},on:{\"change-page\":(page) => _vm.usagePage = page,\"change-page-size\":(size) => { _vm.pageSize = size; _vm.usagePage = 1; }}})],1)]):_vm._e(),(_vm.currentSection === 'recharge')?_c('div',[_c('div',{staticClass:\"tab-content\"},[_c('div',{staticClass:\"account-balance\"},[_c('div',{staticClass:\"balance-info\"},[_c('div',{staticClass:\"balance-label\"},[_vm._v(\"当前账户余额\")]),_c('div',{staticClass:\"balance-value\"},[_vm._v(\"¥ \"+_vm._s(_vm.formatPrice(_vm.userBalance)))])])]),_c('div',{staticClass:\"recharge-options\"},[_c('h3',{staticClass:\"recharge-title\"},[_vm._v(\"选择充值金额\")]),_c('div',{staticClass:\"amount-options\"},[_vm._l((_vm.rechargeAmounts),function(amount){return _c('div',{key:'amount-'+amount,class:['amount-option', { selected: _vm.rechargeAmount === amount }],on:{\"click\":function($event){_vm.rechargeAmount = amount}}},[_vm._v(\" \"+_vm._s(amount)+\"元 \")])}),_c('div',{staticClass:\"amount-option custom-amount\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.customRechargeAmount),expression:\"customRechargeAmount\"}],attrs:{\"type\":\"number\",\"placeholder\":\"其他金额\"},domProps:{\"value\":(_vm.customRechargeAmount)},on:{\"focus\":function($event){_vm.rechargeAmount = null},\"input\":[function($event){if($event.target.composing)return;_vm.customRechargeAmount=$event.target.value},_vm.handleCustomAmountInput]}})])],2),_c('h3',{staticClass:\"recharge-title\"},[_vm._v(\"选择支付方式\")]),_c('div',{staticClass:\"payment-methods\"},[_c('div',{class:['payment-method', { selected: _vm.paymentMethod === 'alipay' }],on:{\"click\":function($event){_vm.paymentMethod = 'alipay'}}},[_c('img',{staticClass:\"payment-icon\",attrs:{\"src\":require(\"../../assets/images/payment/alipay.svg\"),\"alt\":\"支付宝\"}}),_c('span',[_vm._v(\"支付宝\")])])]),_c('div',{staticClass:\"recharge-action\"},[_c('button',{staticClass:\"recharge-button\",attrs:{\"disabled\":!_vm.canRecharge},on:{\"click\":_vm.submitRecharge}},[_vm._v(\" 立即充值 \")])])])])]):_vm._e()])])])],1)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('td',{staticClass:\"empty-state\",attrs:{\"colspan\":\"8\"}},[_c('div',{staticClass:\"empty-container\"},[_c('i',{staticClass:\"el-icon-document empty-icon\"}),_c('div',{staticClass:\"empty-text\"},[_vm._v(\"没有找到匹配的订单\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('thead',[_c('tr',[_c('th',[_vm._v(\"流水号\")]),_c('th',[_vm._v(\"交易时间\")]),_c('th',[_vm._v(\"收支类型\")]),_c('th',[_vm._v(\"交易类型\")]),_c('th',[_vm._v(\"金额\")]),_c('th',[_vm._v(\"交易渠道\")]),_c('th',[_vm._v(\"备注\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('td',{staticClass:\"empty-state\",attrs:{\"colspan\":\"8\"}},[_c('div',{staticClass:\"empty-container\"},[_c('i',{staticClass:\"el-icon-money empty-icon\"}),_c('div',{staticClass:\"empty-text\"},[_vm._v(\"没有找到匹配的交易记录\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('thead',[_c('tr',[_c('th',[_vm._v(\"GPU型号\")]),_c('th',[_vm._v(\"状态\")]),_c('th',[_vm._v(\"开始时间\")]),_c('th',[_vm._v(\"结束时间\")]),_c('th',[_vm._v(\"使用时长\")]),_c('th',[_vm._v(\"计费金额\")]),_c('th',[_vm._v(\"操作\")])])])\n},function (){var _vm=this,_c=_vm._self._c;return _c('td',{staticClass:\"empty-state\",attrs:{\"colspan\":\"7\"}},[_c('div',{staticClass:\"empty-container\"},[_c('i',{staticClass:\"el-icon-time empty-icon\"}),_c('div',{staticClass:\"empty-text\"},[_vm._v(\"没有找到匹配的使用记录\")])])])\n}]\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('main',{staticClass:\"page-wrapper\"},[_c('Header'),_c('div',{staticClass:\"main-content\"},[_vm._t(\"default\")],2),_c('Footer')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <main class=\"page-wrapper\">\r\n    <Header/>\r\n\r\n    <div class=\"main-content\">\r\n      <slot></slot>\r\n    </div>\r\n\r\n    <Footer/>\r\n  </main>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"@/components/common/header/Header\";\r\n// import Footer from \"@/components/common/footer/Footer\";\r\n\r\nexport default {\r\n  name: \"Layout\",\r\n  components:{Header}\r\n}\r\n</script>\r\n<style scoped>\r\n.main-content{\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  /*display: flex;*/\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./layout-fee.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./layout-fee.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./layout-fee.vue?vue&type=template&id=3be2ba12&scoped=true&\"\nimport script from \"./layout-fee.vue?vue&type=script&lang=js&\"\nexport * from \"./layout-fee.vue?vue&type=script&lang=js&\"\nimport style0 from \"./layout-fee.vue?vue&type=style&index=0&id=3be2ba12&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3be2ba12\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"custom-pagination\"},[_c('span',{staticClass:\"pagination-total\"},[_vm._v(\"共 \"+_vm._s(_vm.total)+\" 条\")]),_c('button',{staticClass:\"pagination-prev\",attrs:{\"disabled\":_vm.currentPage === 1},on:{\"click\":function($event){return _vm.changePage(_vm.currentPage - 1)}}},[_vm._v(\" < \")]),_c('span',{staticClass:\"pagination-current\"},[_vm._v(_vm._s(_vm.currentPage))]),_c('button',{staticClass:\"pagination-next\",attrs:{\"disabled\":_vm.currentPage === _vm.totalPages},on:{\"click\":function($event){return _vm.changePage(_vm.currentPage + 1)}}},[_vm._v(\" > \")]),_c('span',{staticClass:\"pagination-size\"},[_vm._v(_vm._s(_vm.pageSize)+\"条 / 页\")]),_c('div',{staticClass:\"pagination-jump\"},[_c('span',[_vm._v(\"前往\")]),_c('input',{directives:[{name:\"model\",rawName:\"v-model.number\",value:(_vm.inputPage),expression:\"inputPage\",modifiers:{\"number\":true}}],attrs:{\"type\":\"text\",\"min\":\"1\",\"max\":_vm.totalPages},domProps:{\"value\":(_vm.inputPage)},on:{\"blur\":[_vm.handleJump,function($event){return _vm.$forceUpdate()}],\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.handleJump.apply(null, arguments)},\"input\":function($event){if($event.target.composing)return;_vm.inputPage=_vm._n($event.target.value)}}}),_c('span',[_vm._v(\"页\")])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"custom-pagination\">\r\n    <span class=\"pagination-total\">共 {{ total }} 条</span>\r\n\r\n    <button\r\n        class=\"pagination-prev\"\r\n        @click=\"changePage(currentPage - 1)\"\r\n        :disabled=\"currentPage === 1\"\r\n    >\r\n      &lt;\r\n    </button>\r\n\r\n    <span class=\"pagination-current\">{{ currentPage }}</span>\r\n\r\n    <button\r\n        class=\"pagination-next\"\r\n        @click=\"changePage(currentPage + 1)\"\r\n        :disabled=\"currentPage === totalPages\"\r\n    >\r\n      &gt;\r\n    </button>\r\n\r\n    <span class=\"pagination-size\">{{ pageSize }}条 / 页</span>\r\n\r\n    <div class=\"pagination-jump\">\r\n      <span>前往</span>\r\n      <input\r\n          type=\"text\"\r\n          v-model.number=\"inputPage\"\r\n          min=\"1\"\r\n          :max=\"totalPages\"\r\n          @blur=\"handleJump\"\r\n          @keyup.enter=\"handleJump\"\r\n      >\r\n      <span>页</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CommonPagination',\r\n  props: {\r\n    currentPage: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    total: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    pageSize: {\r\n      type: Number,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      inputPage: this.currentPage\r\n    }\r\n  },\r\n  watch: {\r\n    currentPage(newVal) {\r\n      this.inputPage = newVal;\r\n    }\r\n  },\r\n  computed: {\r\n    totalPages() {\r\n      return Math.ceil(this.total / this.pageSize);\r\n    }\r\n  },\r\n  methods: {\r\n    changePage(page) {\r\n      if (page >= 1 && page <= this.totalPages) {\r\n        this.$emit('change-page', page);\r\n      }\r\n    },\r\n    handleJump() {\r\n      // 验证输入是否合法\r\n      if (this.inputPage && this.inputPage >= 1 && this.inputPage <= this.totalPages) {\r\n        if (this.inputPage !== this.currentPage) {\r\n          this.changePage(this.inputPage);\r\n        }\r\n      } else {\r\n        // 不合法则重置为当前页\r\n        this.inputPage = this.currentPage;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.custom-pagination {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 15px;\r\n  color: #888;\r\n  margin-top: 15px;\r\n  gap: 8px;\r\n}\r\n\r\n.pagination-total,\r\n.pagination-size,\r\n.pagination-current {\r\n  font-size: 15px;\r\n  color: #999;\r\n}\r\n\r\n.pagination-prev,\r\n.pagination-next {\r\n  background: #f5f5f5;\r\n  border: 1px solid #ddd;\r\n  border-radius: 2px;\r\n  padding: 2px 8px;\r\n  font-size: 15px;\r\n  color: #666;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.pagination-prev:hover,\r\n.pagination-next:hover {\r\n  background: #e8e8e8;\r\n  border-color: #ccc;\r\n}\r\n\r\n.pagination-prev:disabled,\r\n.pagination-next:disabled {\r\n  color: #ccc;\r\n  cursor: not-allowed;\r\n  background: #f9f9f9;\r\n}\r\n\r\n.pagination-current {\r\n  font-weight: normal;\r\n  padding: 0 5px;\r\n}\r\n\r\n.pagination-jump {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.pagination-jump input {\r\n  width: 40px;\r\n  height: 22px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 2px;\r\n  text-align: center;\r\n  font-size: 15px;\r\n  color: #666;\r\n  padding: 0 5px;\r\n}\r\n\r\n.pagination-jump input:focus {\r\n  outline: none;\r\n  border-color: #a0cfff;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./CommonPagination.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./CommonPagination.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./CommonPagination.vue?vue&type=template&id=6343bdf2&scoped=true&\"\nimport script from \"./CommonPagination.vue?vue&type=script&lang=js&\"\nexport * from \"./CommonPagination.vue?vue&type=script&lang=js&\"\nimport style0 from \"./CommonPagination.vue?vue&type=style&index=0&id=6343bdf2&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6343bdf2\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div>\r\n    <SlideNotification\r\n        v-if=\"showNotification\"\r\n        :message=\"notificationMessage\"\r\n        :type=\"notificationType\"\r\n        :duration=\"3000\"\r\n        :minHeight= minHeight\r\n        @close=\"showNotification = false\"\r\n    />\r\n    <div style=\"width: 100%\">\r\n      <div class=\"fee-center-container\">\r\n        <div class=\"navigation-sidebar\">\r\n          <h2 class=\"nav-title\">费用中心</h2>\r\n          <ul class=\"nav-list\">\r\n\r\n            <li class=\"nav-item\" :class=\"{ active: currentSection === 'transactions' }\">\r\n              <a href=\"#\" @click.prevent=\"changeSection('transactions')\">\r\n                <i class=\"el-icon-money\"></i>\r\n                <span>收支明细</span>\r\n              </a>\r\n            </li>\r\n\r\n            <li class=\"nav-item\" :class=\"{ active: currentSection === 'recharge' }\">\r\n              <a href=\"#\" @click.prevent=\"changeSection('recharge')\">\r\n                <i class=\"el-icon-wallet\"></i>\r\n                <span>充值</span>\r\n              </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n\r\n        <!-- Main Content Area -->\r\n        <div class=\"main-content\">\r\n          <!-- Orders Management Section -->\r\n          <div v-if=\"currentSection === 'orders'\">\r\n            <!-- Order List View -->\r\n            <div class=\"tab-content\" v-show=\"!showOrderDetails\">\r\n              <div class=\"search-section\">\r\n                <div class=\"search-bar\">\r\n                  <input type=\"text\" placeholder=\"搜索订单号\" v-model=\"orderSearchQuery\" />\r\n                  <button class=\"search-button\" @click=\"searchOrders\">\r\n                    <i class=\"el-icon-search\"></i>\r\n                  </button>\r\n                  <button v-if=\"orderSearchQuery\" class=\"clear-button\" @click=\"clearOrderSearch\">\r\n                    <i class=\"el-icon-close\"></i>\r\n                  </button>\r\n                </div>\r\n                <div class=\"currency-display\">\r\n                  金额单位: ¥\r\n                  <span class=\"flow-count\">订单总数: {{ currentOrderTotal }}</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"table-container\">\r\n                <div v-if=\"orderLoading\" class=\"loading-state\">\r\n                  <i class=\"el-icon-loading\"></i>\r\n                  <span>正在加载订单数据...</span>\r\n                </div>\r\n\r\n                <div v-if=\"orderError\" class=\"error-state\">\r\n                  <i class=\"el-icon-error\"></i>\r\n                  <span>{{ orderError }}</span>\r\n                  <button @click=\"fetchOrders\">重试</button>\r\n                </div>\r\n                <table class=\"data-table\">\r\n                  <thead>\r\n                  <tr>\r\n                    <th>订单号 <i class=\"el-icon-sort\" @click=\"sortBy('order_number')\"></i></th>\r\n                    <th>订单创建时间 <i class=\"el-icon-sort\" @click=\"sortBy('created_at')\"></i></th>\r\n                    <th>支付状态 <i class=\"el-icon-sort\" @click=\"sortBy('payment_status')\"></i></th>\r\n                    <th>单价 <i class=\"el-icon-sort\" @click=\"sortBy('unit_price')\"></i></th>\r\n                    <th>时长 <i class=\"el-icon-sort\" @click=\"sortBy('duration')\"></i></th>\r\n                    <th>付款方式 <i class=\"el-icon-sort\" @click=\"sortBy('payment_method')\"></i></th>\r\n                    <th>合计</th>\r\n                    <th>操作</th>\r\n                  </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                  <tr v-for=\"(order, index) in paginatedOrders\" :key=\"'order-'+index\">\r\n                    <td>{{ order.order_number }}</td>\r\n                    <td>{{ formatDateTime(order.created_at) }}</td>\r\n                    <td>\r\n                      <span class=\"status-tag\" :class=\"getPaymentStatusClass(order.payment_status)\">\r\n                        {{ getPaymentStatusText(order.payment_status) }}\r\n                      </span>\r\n                    </td>\r\n                    <td>{{ formatPrice(order.unit_price) }}</td>\r\n                    <td>{{ order.duration }}</td>\r\n                    <td>\r\n                      <span class=\"payment-method-tag\" :class=\"getPaymentMethodClass(order.payment_method)\">\r\n                        {{ getPaymentMethodText(order.payment_method) }}\r\n                      </span>\r\n                    </td>\r\n                    <td>{{ formatPrice(order.total_price) }}</td>\r\n                    <td>\r\n                      <span class=\"operation-link\" @click=\"viewOrderDetails(order)\">查看详情</span>\r\n                    </td>\r\n                  </tr>\r\n                  <tr v-if=\"filteredOrderData.length === 0 && !orderLoading\">\r\n                    <td colspan=\"8\" class=\"empty-state\">\r\n                      <div class=\"empty-container\">\r\n                        <i class=\"el-icon-document empty-icon\"></i>\r\n                        <div class=\"empty-text\">没有找到匹配的订单</div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n\r\n              <common-pagination\r\n                  :current-page=\"currentPage\"\r\n                  :total=\"filteredOrderData.length\"\r\n                  :page-size=\"pageSize\"\r\n                  @change-page=\"goToPage\"\r\n                  @change-page-size=\"handlePageSizeChange\"\r\n              />\r\n            </div>\r\n\r\n            <!-- Order Details View -->\r\n            <div class=\"order-details\" v-show=\"showOrderDetails\">\r\n              <div class=\"detail-card\">\r\n                <div class=\"detail-header\">\r\n                  <h2 class=\"detail-title\">订单详情</h2>\r\n                  <button class=\"back-button\" @click=\"showOrderList\">\r\n                    <i class=\"el-icon-back\"></i> 返回列表\r\n                  </button>\r\n                </div>\r\n\r\n                <div class=\"detail-content\">\r\n                  <h3 class=\"detail-subtitle\">订单概况</h3>\r\n                  <div class=\"detail-section\">\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">订单号:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.order_number }}</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">订单状态:</div>\r\n                        <div class=\"detail-value\">\r\n                          <span class=\"status-tag\" :class=\"getPaymentStatusClass(selectedOrder.payment_status)\">\r\n                            {{ getPaymentStatusText(selectedOrder.payment_status) }}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">支付方式:</div>\r\n                        <div class=\"detail-value\">\r\n                          <span class=\"payment-method-tag\" :class=\"getPaymentMethodClass(selectedOrder.payment_method)\">\r\n                            {{ getPaymentMethodText(selectedOrder.payment_method) }}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">单价:</div>\r\n                        <div class=\"detail-value\">¥ {{ formatPrice(selectedOrder.unit_price) }}</div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">订单创建时间:</div>\r\n                        <div class=\"detail-value\">{{ formatDateTime(selectedOrder.created_at) }}</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">订单金额:</div>\r\n                        <div class=\"detail-value\">¥ {{ formatPrice(selectedOrder.total_price) }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <h3 class=\"detail-subtitle\">GPU信息</h3>\r\n                  <div class=\"detail-section\">\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">型号:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.gpu_model }}</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">地区:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.region }}</div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">显卡数量:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.gpu_count }} 个</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">显存:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.video_memory }} GB</div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">VCPU核数:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.cpu_cores }} 核</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">系统盘:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.system_disk }} SSD</div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">云盘:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.cloud_disk }} GB</div>\r\n                      </div>\r\n                      <div class=\"detail-item\">\r\n                        <div class=\"detail-label\">内存:</div>\r\n                        <div class=\"detail-value\">{{ selectedOrder.memory }} GB</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Transactions Section -->\r\n          <div v-if=\"currentSection === 'transactions'\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"search-section\">\r\n                <div class=\"date-range-picker\">\r\n                  <select v-model=\"transactionDateRange\">\r\n                    <option value=\"7\">最近7天</option>\r\n                    <option value=\"30\">最近一个月</option>\r\n                    <option value=\"90\">最近三个月</option>\r\n                    <option value=\"custom\">自定义时间段</option>\r\n                  </select>\r\n\r\n                  <div v-if=\"transactionDateRange === 'custom'\" class=\"custom-date-range\">\r\n                    <input type=\"date\" v-model=\"customDateStart\" />\r\n                    <span>至</span>\r\n                    <input type=\"date\" v-model=\"customDateEnd\" />\r\n<!--                    <button class=\"apply-button\" @click=\"applyCustomDateRange\">确认</button>-->\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"search-filters\">\r\n                  <select v-model=\"transactionType\">\r\n                    <option value=\"\">全部类型</option>\r\n                    <option value=\"income\">收入</option>\r\n                    <option value=\"expense\">支出</option>\r\n                  </select>\r\n\r\n                  <input\r\n                      type=\"text\"\r\n                      placeholder=\"搜索流水号\"\r\n                      v-model=\"transactionSearchQuery\"\r\n                      @keyup.enter=\"searchTransactions\"\r\n                  />\r\n                  <button class=\"search-button\" @click=\"searchTransactions\">\r\n                    <i class=\"el-icon-search\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"transaction-summary\">\r\n                <div class=\"summary-card\">\r\n                  <div class=\"summary-title\">总充值</div>\r\n                  <div class=\"summary-value income\">¥ {{ formatPrice(summaryData.totalRecharge) }}</div>\r\n                </div>\r\n                <div class=\"summary-card\">\r\n                  <div class=\"summary-title\">总消费</div>\r\n                  <div class=\"summary-value expense\">¥ {{ formatPrice(summaryData.totalExpense) }}</div>\r\n                </div>\r\n                <div class=\"summary-card\">\r\n                  <div class=\"summary-title\">账户余额</div>\r\n                  <div class=\"summary-value\">¥ {{ formatPrice(userBalance) }}</div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"table-container\">\r\n                <div v-if=\"transactionLoading\" class=\"loading-state\">\r\n                  <i class=\"el-icon-loading\"></i>\r\n                  <span>正在加载交易数据...</span>\r\n                </div>\r\n\r\n                <div v-if=\"transactionError\" class=\"error-state\">\r\n                  <i class=\"el-icon-error\"></i>\r\n                  <span>{{ transactionError }}</span>\r\n                  <button @click=\"fetchTransactions\">重试</button>\r\n                </div>\r\n\r\n                <table class=\"data-table\">\r\n                  <thead>\r\n                  <tr>\r\n                    <th>流水号</th>\r\n                    <th>交易时间</th>\r\n                    <th>收支类型</th>\r\n                    <th>交易类型</th>\r\n                    <th>金额</th>\r\n                    <th>交易渠道</th>\r\n                    <th>备注</th>\r\n                  </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                  <tr v-for=\"(transaction, index) in paginatedTransactions\" :key=\"'transaction-'+index\">\r\n                    <td>{{ transaction.transaction_id }}</td>\r\n                    <td>{{ formatDateTime(transaction.created_at) }}</td>\r\n                    <td>\r\n                      <span class=\"transaction-type\" :class=\"getTransactionTypeClass(transaction.type)\">\r\n                        {{ getTransactionTypeName(transaction.type) }}\r\n                      </span>\r\n                    </td>\r\n                    <td>{{ getTransactionTypeNamePay(transaction.pay_type) }}</td>\r\n                    <td :class=\"transaction.type === 'expense' ? 'expense-amount' : 'income-amount'\">\r\n                      {{ transaction.type === 'expense' ? '￥ - ' : '￥ + ' }}{{ formatPrice(transaction.amount) }}\r\n                    </td>\r\n\r\n                    <td :class=\"transaction.type === 'expense' ? 'expense-zhifu' : 'income-shouru'\">\r\n                      {{ getPaymentMethodText(transaction.payment_channel) }}\r\n                    </td>\r\n                    <td>{{ transaction.description }}</td>\r\n                  </tr>\r\n                  <tr v-if=\"filteredTransactionData.length === 0 && !transactionLoading\">\r\n                    <td colspan=\"8\" class=\"empty-state\">\r\n                      <div class=\"empty-container\">\r\n                        <i class=\"el-icon-money empty-icon\"></i>\r\n                        <div class=\"empty-text\">没有找到匹配的交易记录</div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n\r\n              <common-pagination\r\n                  :current-page=\"transactionPage\"\r\n                  :total=\"filteredTransactionData.length\"\r\n                  :page-size=\"pageSize\"\r\n                  @change-page=\"(page) => transactionPage = page\"\r\n                  @change-page-size=\"(size) => { pageSize = size; transactionPage = 1; }\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Usage Records Section -->\r\n          <div v-if=\"currentSection === 'usage'\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"search-section\">\r\n                <div class=\"date-range-picker\">\r\n                  <select v-model=\"usageDateRange\">\r\n                    <option value=\"7\">最近7天</option>\r\n                    <option value=\"30\">最近一个月</option>\r\n                    <option value=\"90\">最近三个月</option>\r\n                    <option value=\"custom\">自定义时间段</option>\r\n                  </select>\r\n\r\n                  <div v-if=\"usageDateRange === 'custom'\" class=\"custom-date-range\">\r\n                    <input type=\"date\" v-model=\"customUsageDateStart\" />\r\n                    <span>至</span>\r\n                    <input type=\"date\" v-model=\"customUsageDateEnd\" />\r\n<!--                    <button class=\"apply-button\" @click=\"applyUsageCustomDateRange\">确认</button>-->\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"search-filters\">\r\n                  <select v-model=\"usageFilterGpu\">\r\n                    <option value=\"\">全部GPU型号</option>\r\n                    <option v-for=\"gpu in gpuModels\" :key=\"gpu.id\" :value=\"gpu.id\">\r\n                      {{ gpu.name }}\r\n                    </option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"table-container\">\r\n                <table class=\"data-table\">\r\n                  <thead>\r\n                  <tr>\r\n                    <th>GPU型号</th>\r\n                    <th>状态</th>\r\n                    <th>开始时间</th>\r\n                    <th>结束时间</th>\r\n                    <th>使用时长</th>\r\n                    <th>计费金额</th>\r\n                    <th>操作</th>\r\n                  </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                  <tr v-for=\"(record, index) in paginatedUsageRecords\" :key=\"'usage-'+index\">\r\n                    <td>{{ record.gpu_model }}</td>\r\n                    <td>\r\n              <span class=\"status-tag\" :class=\"getUsageStatusClass(record.status)\">\r\n                {{ getUsageStatusText(record.status) }}\r\n              </span>\r\n                    </td>\r\n                    <td>{{ formatDateTime(record.start_time) }}</td>\r\n                    <td>{{ record.end_time ? formatDateTime(record.end_time) : '--' }}</td>\r\n                    <td>{{ calculateDuration(record.start_time, record.end_time) }}</td>\r\n                    <td>¥ {{ formatPrice(record.cost / 10000) }}</td>\r\n                    <td>\r\n                      <span class=\"operation-link\" @click=\"navigateToRecharge\">续费</span>\r\n                      <span v-if=\"record.status === 'scheduled'\" class=\"operation-link cancel-link\" @click=\"cancelReservation(record)\">取消</span>\r\n                    </td>\r\n                  </tr>\r\n                  <tr v-if=\"filteredUsageData.length === 0\">\r\n                    <td colspan=\"7\" class=\"empty-state\">\r\n                      <div class=\"empty-container\">\r\n                        <i class=\"el-icon-time empty-icon\"></i>\r\n                        <div class=\"empty-text\">没有找到匹配的使用记录</div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n\r\n              <common-pagination\r\n                  :current-page=\"usagePage\"\r\n                  :total=\"filteredUsageData.length\"\r\n                  :page-size=\"pageSize\"\r\n                  @change-page=\"(page) => usagePage = page\"\r\n                  @change-page-size=\"(size) => { pageSize = size; usagePage = 1; }\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Recharge Section -->\r\n          <div v-if=\"currentSection === 'recharge'\">\r\n            <div class=\"tab-content\">\r\n              <div class=\"account-balance\">\r\n                <div class=\"balance-info\">\r\n                  <div class=\"balance-label\">当前账户余额</div>\r\n                  <div class=\"balance-value\">¥ {{ formatPrice(userBalance) }}</div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"recharge-options\">\r\n                <h3 class=\"recharge-title\">选择充值金额</h3>\r\n\r\n                <div class=\"amount-options\">\r\n                  <div\r\n                      v-for=\"amount in rechargeAmounts\"\r\n                      :key=\"'amount-'+amount\"\r\n                      :class=\"['amount-option', { selected: rechargeAmount === amount }]\"\r\n                      @click=\"rechargeAmount = amount\"\r\n                  >\r\n                    {{ amount }}元\r\n                  </div>\r\n                  <div class=\"amount-option custom-amount\">\r\n                    <input\r\n                        type=\"number\"\r\n                        placeholder=\"其他金额\"\r\n                        v-model=\"customRechargeAmount\"\r\n                        @focus=\"rechargeAmount = null\"\r\n                        @input=\"handleCustomAmountInput\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <h3 class=\"recharge-title\">选择支付方式</h3>\r\n\r\n                <div class=\"payment-methods\">\r\n                  <div\r\n                      :class=\"['payment-method', { selected: paymentMethod === 'alipay' }]\"\r\n                      @click=\"paymentMethod = 'alipay'\"\r\n                  >\r\n                    <img src=\"../../assets/images/payment/alipay.svg\" alt=\"支付宝\" class=\"payment-icon\">\r\n                    <span>支付宝</span>\r\n                  </div>\r\n<!--                  <div-->\r\n<!--                      :class=\"['payment-method', { selected: paymentMethod === 'wechat' }]\"-->\r\n<!--                      @click=\"paymentMethod = 'wechat'\"-->\r\n<!--                  >-->\r\n<!--                    <i class=\"el-icon-wechat\"></i>-->\r\n<!--                    <span>微信支付</span>-->\r\n<!--                  </div>-->\r\n                </div>\r\n\r\n                <div class=\"recharge-action\">\r\n                  <button\r\n                      class=\"recharge-button\"\r\n                      @click=\"submitRecharge\"\r\n                      :disabled=\"!canRecharge\"\r\n                  >\r\n                    立即充值\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/layout-fee\";\r\nimport CommonPagination from \"@/views/Ordermange/CommonPagination\";\r\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\r\nimport { postAnyData, getAnyData } from \"@/api/login\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"OrderView\",\r\n  components: { SlideNotification, Layout, CommonPagination },\r\n  data() {\r\n    return {\r\n      // General\r\n      currentSection: 'transactions',\r\n      pageSize: 6,\r\n      userBalance: 0,\r\n      showNotification: false,\r\n      notificationMessage: '',\r\n      notificationType: 'success',\r\n\r\n      // Orders\r\n      orderData: [],\r\n      orderSearchQuery: '',\r\n      currentPage: 1,\r\n      showOrderDetails: false,\r\n      selectedOrder: {},\r\n      orderLoading: false,\r\n      orderError: null,\r\n\r\n      // Transactions\r\n      transactionData: [],\r\n      transactionSearchQuery: '',\r\n      transactionDateRange: '30',\r\n      transactionType: '',\r\n      transactionPage: 1,\r\n      customDateStart: '',\r\n      customDateEnd: '',\r\n      transactionLoading: false,\r\n      transactionError: null,\r\n      summaryData: {\r\n        totalRecharge: 0,\r\n        totalExpense: 0,\r\n        balance: 0\r\n      },\r\n\r\n      // Usage\r\n      customUsageDateStart: '',\r\n      customUsageDateEnd: '',\r\n      usageData: [],\r\n      usageDateRange: '30',\r\n      usageFilterGpu: '',\r\n      usageLoading: false,\r\n      usageError: null,\r\n      usagePage: 1,\r\n      gpuModels: [\r\n        { id: 'a100', name: 'NVIDIA A100' },\r\n        { id: 'v100', name: 'NVIDIA V100' },\r\n        { id: 'rtx3090', name: 'NVIDIA RTX 3090' }\r\n      ],\r\n\r\n      // Recharge\r\n      rechargeAmounts: [100, 200, 500, 1000, 2000],\r\n      rechargeAmount: 100,\r\n      customRechargeAmount: null,\r\n      paymentMethod: 'alipay',\r\n      rechargeLoading: false\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // Orders\r\n    filteredOrderData() {\r\n      if (!this.orderSearchQuery) return this.orderData;\r\n\r\n      const query = this.orderSearchQuery.toLowerCase();\r\n      return this.orderData.filter(order =>\r\n          order.order_number.toLowerCase().includes(query)\r\n      );\r\n    },\r\n\r\n    paginatedOrders() {\r\n      const startIndex = (this.currentPage - 1) * this.pageSize;\r\n      const endIndex = startIndex + this.pageSize;\r\n      return this.filteredOrderData.slice(startIndex, endIndex);\r\n    },\r\n\r\n    currentOrderTotal() {\r\n      return this.filteredOrderData.length;\r\n    },\r\n\r\n    // Transactions\r\n    filteredTransactionData() {\r\n      let filtered = [...this.transactionData];\r\n\r\n      // Filter by date range\r\n      if (this.transactionDateRange !== 'custom') {\r\n        const days = parseInt(this.transactionDateRange);\r\n        const cutoffDate = new Date();\r\n        cutoffDate.setDate(cutoffDate.getDate() - days);\r\n\r\n        filtered = filtered.filter(transaction => {\r\n          const transactionDate = new Date(transaction.created_at);\r\n          return transactionDate >= cutoffDate;\r\n        });\r\n      } else if (this.customDateStart && this.customDateEnd) {\r\n        const startDate = new Date(this.customDateStart);\r\n        const endDate = new Date(this.customDateEnd);\r\n        endDate.setHours(23, 59, 59, 999); // End of the day\r\n\r\n        filtered = filtered.filter(transaction => {\r\n          const transactionDate = new Date(transaction.created_at);\r\n          return transactionDate >= startDate && transactionDate <= endDate;\r\n        });\r\n      }\r\n\r\n      // Filter by type\r\n      if (this.transactionType) {\r\n        filtered = filtered.filter(transaction =>\r\n            transaction.type === this.transactionType\r\n        );\r\n      }\r\n\r\n      // Filter by search query\r\n      if (this.transactionSearchQuery) {\r\n        const query = this.transactionSearchQuery.toLowerCase();\r\n        filtered = filtered.filter(transaction =>\r\n            transaction.transaction_id.toLowerCase().includes(query)\r\n        );\r\n      }\r\n\r\n      return filtered;\r\n    },\r\n\r\n    paginatedTransactions() {\r\n      const startIndex = (this.transactionPage - 1) * this.pageSize;\r\n      const endIndex = startIndex + this.pageSize;\r\n      return this.filteredTransactionData.slice(startIndex, endIndex);\r\n    },\r\n\r\n\r\n// usage\r\n    filteredUsageData() {\r\n      if (!this.usageData || this.usageData.length === 0) return [];\r\n\r\n      let filtered = [...this.usageData];\r\n\r\n      // 按日期范围过滤\r\n      if (this.usageDateRange !== 'custom') {\r\n        const days = parseInt(this.usageDateRange);\r\n        const cutoffDate = new Date();\r\n        cutoffDate.setDate(cutoffDate.getDate() - days);\r\n\r\n        filtered = filtered.filter(record => {\r\n          const recordDate = new Date(record.start_time || record.created_at);\r\n          return recordDate >= cutoffDate;\r\n        });\r\n      } else if (this.customUsageDateStart && this.customUsageDateEnd) {\r\n        const startDate = new Date(this.customUsageDateStart);\r\n        const endDate = new Date(this.customUsageDateEnd);\r\n        endDate.setHours(23, 59, 59, 999); // 设置为当天的最后一刻\r\n\r\n        filtered = filtered.filter(record => {\r\n          const recordDate = new Date(record.start_time || record.created_at);\r\n          return recordDate >= startDate && recordDate <= endDate;\r\n        });\r\n      }\r\n\r\n      // 按GPU型号过滤\r\n      if (this.usageFilterGpu) {\r\n        filtered = filtered.filter(record =>\r\n            record.gpu_model.toLowerCase().includes(this.usageFilterGpu.toLowerCase())\r\n        );\r\n      }\r\n\r\n      return filtered;\r\n    },\r\n\r\n    paginatedUsageRecords() {\r\n      const startIndex = (this.usagePage - 1) * this.pageSize;\r\n      const endIndex = startIndex + this.pageSize;\r\n      return this.filteredUsageData.slice(startIndex, endIndex);\r\n    },\r\n\r\n    // Recharge\r\n    canRecharge() {\r\n      const amount = this.getRechargeAmount();\r\n      return amount && amount > 0 && this.paymentMethod && !this.rechargeLoading;\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    $route(to) {\r\n      // 当路由变化时检查activeTab参数\r\n      const activeTab = to.query.activeTab;\r\n      if (activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(activeTab)) {\r\n        this.currentSection = activeTab;\r\n      }\r\n    },\r\n    currentSection(newVal) {\r\n      if (newVal === 'transactions') {\r\n        this.fetchTransactions();\r\n        this.fetchSummaryData();\r\n      } else if (newVal === 'orders') {\r\n        this.fetchOrders();\r\n      } else if (newVal === 'recharge') {\r\n        this.fetchUserBalance();\r\n      }else if (newVal === 'transactions') {\r\n        this.fetchTransactions();\r\n        // this.currentSection = 'transactions';\r\n        this.loadSectionData('transactions');\r\n      }\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.fetchUserBalance();\r\n    this.fetchTransactions();\r\n    const activeTab = this.$route.query.activeTab || 'transactions';\r\n    // console.log(\"activetab\",activeTab)\r\n    this.currentSection = activeTab;\r\n\r\n    // 根据当前激活的标签页加载对应数据\r\n    this.loadSectionData(activeTab);\r\n    this.$watch(\r\n        () => this.$route.query,\r\n        (newQuery) => {\r\n          if (newQuery.activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(newQuery.activeTab)) {\r\n            this.currentSection = newQuery.activeTab;\r\n          }\r\n        },\r\n        { immediate: true }\r\n    );\r\n    this.fetchOrders().then(() => {\r\n      // 默认按创建时间排序\r\n      this.sortBy('created_at');\r\n\r\n      // 检查URL中的activeTab参数\r\n      const activeTab = this.$route.query.activeTab;\r\n      if (activeTab && ['orders', 'transactions', 'usage', 'recharge'].includes(activeTab)) {\r\n        this.currentSection = activeTab;\r\n      }\r\n    });\r\n    this.fetchSummaryData();\r\n  },\r\n  beforeDestroy() {\r\n    this.$emit('refresh-header')\r\n  },\r\n\r\n  methods: {\r\n\r\n    showNotificationMessage(message, type = 'info') {\r\n      this.notificationMessage = message;\r\n      this.notificationType = type;\r\n      this.showNotification = true;\r\n    },\r\n\r\n    changeSection(section) {\r\n      // 更新当前标签页\r\n      this.currentSection = section;\r\n\r\n      // 更新 URL，确保刷新后仍然能恢复\r\n      this.$router.replace({\r\n        query: {\r\n          ...this.$route.query, // 保留其他查询参数\r\n          activeTab: section,   // 更新 activeTab\r\n        },\r\n      });\r\n\r\n      // 加载对应数据\r\n      if (section === 'orders') {\r\n        this.fetchOrders();\r\n      } else if (section === 'transactions') {\r\n        this.fetchTransactions();\r\n        this.fetchSummaryData();\r\n      } else if (section === 'usage') {\r\n        this.fetchUsageData();\r\n      } else if (section === 'recharge') {\r\n        this.fetchUserBalance();\r\n      }\r\n    },\r\n    loadSectionData(section) {\r\n      switch(section) {\r\n        case 'orders':\r\n          this.fetchOrders();\r\n          break;\r\n        case 'transactions':\r\n          this.fetchTransactions();\r\n          this.fetchSummaryData();\r\n          break;\r\n        case 'usage':\r\n          this.fetchUsageData();\r\n          break;\r\n        case 'recharge':\r\n          this.fetchUserBalance();\r\n          break;\r\n      }},\r\n    // 获取用户余额\r\n    async fetchUserBalance() {\r\n      try {\r\n        const response = await postAnyData(\"/logout/cilent/getInfo\");\r\n        if (response.data.code === 200) {\r\n          this.userBalance = response.data.data.balance || 0;\r\n          this.$emit('refresh-header')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取用户余额失败');\r\n      }\r\n    },\r\n\r\n    applyUsageCustomDateRange() {\r\n      if (!this.customUsageDateStart || !this.customUsageDateEnd) {\r\n        this.$message.error('请选择开始和结束日期');\r\n        return;\r\n      }\r\n\r\n      const startDate = new Date(this.customUsageDateStart);\r\n      const endDate = new Date(this.customUsageDateEnd);\r\n\r\n      if (startDate > endDate) {\r\n        this.$message.error('开始日期不能晚于结束日期');\r\n        return;\r\n      }\r\n\r\n      this.usagePage = 1; // 重置到第一页\r\n      this.fetchUsageData(); // 重新获取数据\r\n    },\r\n\r\n    // 格式化日期时间（带时区处理）\r\n    formatDateTimeusage(dateTimeString) {\r\n      if (!dateTimeString) return '--';\r\n\r\n      try {\r\n        const date = new Date(dateTimeString);\r\n        // 转换为本地时间字符串\r\n        return date.toLocaleString('zh-CN', {\r\n          year: 'numeric',\r\n          month: '2-digit',\r\n          day: '2-digit',\r\n          hour: '2-digit',\r\n          minute: '2-digit',\r\n          second: '2-digit',\r\n          hour12: false\r\n        }).replace(/\\//g, '-');\r\n      } catch (e) {\r\n        return '--';\r\n      }\r\n    },\r\n\r\n    // 计算运行时长（带时区处理）\r\n    calculateDuration(startTime, endTime) {\r\n      if (!startTime) return \"0分钟\";\r\n\r\n      try {\r\n        const start = new Date(startTime);\r\n        const end = endTime ? new Date(endTime) : new Date();\r\n\r\n        // 检查时间是否有效\r\n        if (isNaN(start.getTime())) return \"时间无效\";\r\n        if (endTime && isNaN(end.getTime())) return \"时间无效\";\r\n\r\n        // 如果开始时间在未来\r\n        if (start > new Date()) {\r\n          return \"未开始\";\r\n        }\r\n\r\n        // 如果结束时间在未来（仍在运行中）\r\n        if (!endTime || end > new Date()) {\r\n          const diffMs = new Date() - start;\r\n          const hours = Math.floor(diffMs / (1000 * 60 * 60));\r\n          const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n          return `${hours}小时${minutes}分钟 `;\r\n        }\r\n\r\n        // 正常计算时长\r\n        const diffMs = end - start;\r\n        const hours = Math.floor(diffMs / (1000 * 60 * 60));\r\n        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n        if (hours > 0) {\r\n          return `${hours}小时${minutes}分钟`;\r\n        }\r\n        return `${minutes}分钟`;\r\n      } catch (e) {\r\n        return \"--\";\r\n      }\r\n    },\r\n\r\n\r\n\r\n    navigateToRecharge() {\r\n      // 使用 replace 而不是 push 避免路由历史堆积\r\n      this.$router.replace({\r\n        path: '/userorder',\r\n        query: {\r\n          activeTab: 'recharge',\r\n          timestamp: Date.now() // 确保每次导航都是唯一的\r\n        }\r\n      });\r\n    },\r\n\r\n    // 从后端获取订单数据\r\n    async fetchOrders() {\r\n      this.orderLoading = true;\r\n      this.orderError = null;\r\n      try {\r\n        const response = await postAnyData(\"/system/order/getOrderAndProduct\");\r\n\r\n        // 添加排序逻辑\r\n        this.orderData = response.data.data.map(order => ({\r\n          id: order.name,\r\n          order_number: order.order_name,\r\n          payment_status: this.mapOrderStatus(order.order_staus),\r\n          payment_method: order.order_payment_method || '余额支付',\r\n          total_price: order.order_price,\r\n          unit_price: this.calculateUnitPrice(order),\r\n          duration: order.order_buy_time,\r\n          created_at: order.create_time,\r\n          gpu_model: order.name || '未知型号',\r\n          region: order.Region || '未知地区',\r\n          gpu_count: order.graphics_card_number || 0,\r\n          video_memory: order.video_memory || '未知',\r\n          cpu_cores: order.gpu_nuclear_number || 0,\r\n          system_disk: order.system_disk || '未知',\r\n          cloud_disk: order.data_disk || '未知',\r\n          memory: order.internal_memory || '未知',\r\n          rawData: order\r\n        })).sort((a, b) => {\r\n          // 按创建时间降序排序（最新的在前）\r\n          return new Date(b.created_at) - new Date(a.created_at);\r\n        });\r\n      } catch (error) {\r\n        this.orderError = '获取订单数据失败，请稍后重试';\r\n        // this.$message.error(this.orderError);\r\n      } finally {\r\n        this.orderLoading = false;\r\n      }\r\n    },\r\n\r\n    // 获取收支明细数据\r\n    async fetchTransactions() {\r\n      this.transactionLoading = true;\r\n      this.transactionError = null;\r\n      try {\r\n        const response = await postAnyData(\"/system/order/getConsumptionOrder\");\r\n        const rawData = response.data.data;\r\n\r\n        // 用map去重，确保不会因为相同order_id重复\r\n        const uniqueMap = new Map();\r\n        rawData.forEach(transaction => {\r\n          // 用topup_id或gpu_order_id作为唯一主键\r\n          const uniqueId = transaction.topup_id || transaction.gpu_order_id;\r\n\r\n          if (!uniqueMap.has(uniqueId)) {\r\n            uniqueMap.set(uniqueId, {\r\n              transaction_id: transaction.topup_order_id || transaction.gpu_order_name,\r\n              type: transaction.source_table === \"收入\" ? 'income' : 'expense',\r\n              pay_type: this.mapPayType(transaction.source_table),\r\n              amount: transaction.topup_topup || transaction.gpu_order_price,\r\n              created_at: transaction.create_time,\r\n              payment_channel: transaction.payment_method || '未知',\r\n              status: this.mapTransactionStatus(transaction.order_staus),\r\n              description: transaction.description || '无'\r\n            });\r\n          }\r\n        });\r\n\r\n        this.transactionData = Array.from(uniqueMap.values());\r\n      } catch (error) {\r\n        this.transactionLoading = '暂无记录';\r\n      } finally {\r\n        this.transactionLoading = false;\r\n      }\r\n    },\r\n\r\n\r\n    async fetchUsageData() {\r\n      this.usageLoading = true;\r\n      this.usageError = null;\r\n      try {\r\n        let params = {};\r\n\r\n        // 添加日期范围参数\r\n        if (this.usageDateRange === 'custom' && this.customUsageDateStart && this.customUsageDateEnd) {\r\n          params.start_date = this.customUsageDateStart;\r\n          params.end_date = this.customUsageDateEnd;\r\n        } else if (this.usageDateRange !== 'custom') {\r\n          // 处理预设日期范围\r\n          const days = parseInt(this.usageDateRange);\r\n          const cutoffDate = new Date();\r\n          cutoffDate.setDate(cutoffDate.getDate() - days);\r\n          params.start_date = cutoffDate.toISOString().split('T')[0];\r\n        }\r\n\r\n        // 添加GPU过滤参数\r\n        if (this.usageFilterGpu) {\r\n          params.gpu_model = this.usageFilterGpu;\r\n        }\r\n        // 这里应该改为实际的API调用，目前使用模拟数据\r\n        const response = await postAnyData(\"/system/order/getProductResumption\");\r\n\r\n        // 如果API已准备好，应该使用实际数据\r\n        // this.usageData = response.data.data;\r\n\r\n        // 临时使用模拟数据\r\n        const mockData = [\r\n\r\n        ];\r\n\r\n        this.usageData = mockData;\r\n        return this.usageData;\r\n\r\n      } catch (error) {\r\n        this.usageError = '加载使用记录失败，请稍后重试';\r\n        // this.$message.error(this.usageError);\r\n        return []; // 出错时返回空数组\r\n      } finally {\r\n        this.usageLoading = false;\r\n      }\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    // 获取汇总数据\r\n    // 获取汇总数据 (去重版)\r\n    async fetchSummaryData() {\r\n      try {\r\n        const response = await postAnyData(\"/system/order/getConsumptionOrder\");\r\n        const transactions = response.data.data || [];\r\n\r\n        // 先做去重，避免重复计算\r\n        const uniqueMap = new Map();\r\n        transactions.forEach(t => {\r\n          const uniqueId = t.topup_id || t.gpu_order_id;\r\n          if (!uniqueMap.has(uniqueId)) {\r\n            uniqueMap.set(uniqueId, t);\r\n          }\r\n        });\r\n\r\n        const uniqueTransactions = Array.from(uniqueMap.values());\r\n\r\n        // 计算总充值\r\n        const totalRecharge = uniqueTransactions\r\n            .filter(t => t.source_table === \"收入\")\r\n            .reduce((sum, t) => sum + (parseFloat(t.topup_topup) || 0), 0);\r\n\r\n        // 计算总消费\r\n        const totalExpense = uniqueTransactions\r\n            .filter(t => t.source_table === \"支出\")\r\n            .reduce((sum, t) => sum + (parseFloat(t.gpu_order_price) || 0), 0);\r\n\r\n        this.summaryData = {\r\n          totalRecharge,\r\n          totalExpense,\r\n          balance: this.userBalance\r\n        };\r\n      } catch (error) {\r\n        this.$message.error('获取账户汇总数据失败');\r\n      }\r\n    },\r\n\r\n    // 映射订单状态\r\n    mapOrderStatus(status) {\r\n      const statusMap = {\r\n        '1': 'paid',     // 支付成功\r\n        '2': 'failed',   // 支付失败\r\n        '3': 'unpaid'    // 未支付\r\n      };\r\n      return statusMap[status] || status;\r\n    },\r\n\r\n    // 映射交易类型\r\n    mapPayType(type) {\r\n      return type === \"收入\" ? '充值' : '消费';\r\n    },\r\n\r\n    // 映射交易状态\r\n    mapTransactionStatus(status) {\r\n      const statusMap = {\r\n        '1': 'success',    // 成功\r\n        '2': 'failed',     // 失败\r\n        '3': 'processing'  // 处理中\r\n      };\r\n      return statusMap[status] || status;\r\n    },\r\n\r\n    // 计算单价（根据商品信息和订单时长）\r\n    calculateUnitPrice(order) {\r\n      if (!order.computing) return order.order_price;\r\n\r\n      // 根据购买单位选择对应的价格\r\n      switch(order.order_unit) {\r\n        case 'hour':\r\n          return order.computing.price_bour;\r\n        case 'day':\r\n          return order.computing.price_day;\r\n        case 'month':\r\n          return order.computing.price_mouth;\r\n        case 'year':\r\n          return order.computing.price_year;\r\n        default:\r\n          return order.order_price;\r\n      }\r\n    },\r\n\r\n    // Formatting methods\r\n    formatPrice(price) {\r\n      if (isNaN(price)) return '0.00';\r\n      return parseFloat(price).toFixed(2);\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      return new Date(dateString).toLocaleDateString();\r\n    },\r\n\r\n    formatDateTime(dateString) {\r\n      if (!dateString) return '';\r\n      const date = new Date(dateString);\r\n      return date.toLocaleString();\r\n    },\r\n\r\n    // Order methods\r\n    getPaymentStatusClass(status) {\r\n      return {\r\n        'paid': 'status-success',\r\n        'unpaid': 'status-pending',\r\n        'refunded': 'status-info',\r\n        'failed': 'status-error',\r\n        'cancelled': 'status-warning'\r\n      }[status] || '';\r\n    },\r\n\r\n    getPaymentStatusText(status) {\r\n      return {\r\n        'paid': '已支付',\r\n        'unpaid': '未支付',\r\n        'refunded': '已退款',\r\n        'failed': '支付失败',\r\n        'cancelled': '已取消'\r\n      }[status] || status;\r\n    },\r\n\r\n    getPaymentMethodClass(method) {\r\n      return {\r\n        '支付宝': 'payment-alipay',\r\n        '微信支付': 'payment-wechat',\r\n        '银行卡': 'payment-bank',\r\n        '余额支付': 'payment-balance',\r\n        '未支付': 'payment-warning'\r\n      }[method] || '';\r\n    },\r\n\r\n    getPaymentMethodText(method) {\r\n      return {\r\n        'alipay': '支付宝',\r\n        'wechat': '微信支付',\r\n        'bank': '银行卡',\r\n        'balance': '余额'\r\n      }[method] || method;\r\n    },\r\n\r\n    searchOrders() {\r\n      this.currentPage = 1;\r\n    },\r\n\r\n    clearOrderSearch() {\r\n      this.orderSearchQuery = '';\r\n      this.currentPage = 1;\r\n    },\r\n\r\n    sortBy(field) {\r\n      if (field === 'created_at') {\r\n        this.orderData.sort((a, b) => {\r\n          return new Date(b[field]) - new Date(a[field]);\r\n        });\r\n      } else {\r\n        this.orderData.sort((a, b) => {\r\n          return a[field] > b[field] ? 1 : -1;\r\n        });\r\n      }\r\n    },\r\n\r\n    viewOrderDetails(order) {\r\n      this.selectedOrder = order;\r\n      this.showOrderDetails = true;\r\n      this.$nextTick(() => {\r\n        document.querySelector('.order-details').scrollIntoView({ behavior: 'smooth' });\r\n      });\r\n    },\r\n\r\n    showOrderList() {\r\n      this.showOrderDetails = false;\r\n      window.scrollTo({ top: 0, behavior: 'smooth' });\r\n    },\r\n\r\n    goToPage(page) {\r\n      this.currentPage = page;\r\n    },\r\n\r\n    handlePageSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n    },\r\n\r\n    // Transaction methods\r\n    searchTransactions() {\r\n      this.transactionPage = 1;\r\n    },\r\n\r\n    applyCustomDateRange() {\r\n      if (this.customDateStart && this.customDateEnd) {\r\n        this.transactionPage = 1;\r\n      }\r\n    },\r\n\r\n    getTransactionTypeClass(type) {\r\n      return {\r\n        'income': 'income-type',\r\n        'expense': 'expense-type',\r\n        'refund': 'refund-type'\r\n      }[type] || '';\r\n    },\r\n\r\n    getTransactionTypeName(type) {\r\n      return {\r\n        'income': '收入',\r\n        'expense': '支出',\r\n        'refund': '退款'\r\n      }[type] || type;\r\n    },\r\n\r\n    getTransactionTypeNamePay(payType) {\r\n      return {\r\n        'income': '收入',\r\n        'expense': '支出',\r\n      }[payType] || payType;\r\n    },\r\n\r\n    getTransactionStatusClass(status) {\r\n      return {\r\n        'success': 'status-success',\r\n        'pending': 'status-pending',\r\n        'failed': 'status-error',\r\n        'processing': 'status-info'\r\n      }[status] || '';\r\n    },\r\n\r\n    getTransactionStatusName(status) {\r\n      return {\r\n        'success': '成功',\r\n        'pending': '处理中',\r\n        'failed': '失败',\r\n        'processing': '处理中'\r\n      }[status] || status;\r\n    },\r\n\r\n    // Usage methods\r\n    getUsageStatusClass(status) {\r\n      return {\r\n        'active': 'status-success',\r\n        'running': 'status-running',\r\n        'about_to_expire': 'status-warning',\r\n        'expired': 'status-error',\r\n        'completed': 'status-complete',\r\n        'paused': 'status-info'\r\n      }[status] || '';\r\n    },\r\n\r\n    getUsageStatusText(status) {\r\n      return {\r\n        'active': '可用',\r\n        'running': '使用中',\r\n        'about_to_expire': '即将到期',\r\n        'completed': '已结束',\r\n        'paused': '已暂停'\r\n      }[status] || status;\r\n    },\r\n\r\n    renewService(record) {\r\n      this.$message.success(`正在为 ${record.gpu_model} 续费`);\r\n    },\r\n\r\n    // Recharge methods\r\n    getRechargeAmount() {\r\n      return this.rechargeAmount || this.customRechargeAmount;\r\n    },\r\n\r\n    handleCustomAmountInput() {\r\n      this.rechargeAmount = null;\r\n    },\r\n\r\n    async submitRecharge() {\r\n      const amount = this.getRechargeAmount();\r\n      if (!amount || amount <= 0) {\r\n        // this.$message.error('请输入有效的充值金额');\r\n        this.showNotificationMessage('请输入有效的充值金额', 'error');\r\n\r\n        return;\r\n      }\r\n\r\n      this.rechargeLoading = true;\r\n\r\n      try {\r\n        // 调用充值API\r\n        const response = await getAnyData('/yun/scanPay', { amount: amount });\r\n        if (response.data.includes('充值金额不足')) {\r\n          // 提取需要充值的金额（正则表达式匹配数字和小数点）\r\n          this.showNotificationMessage(response.data, 'error');\r\n          return;\r\n        }\r\n        // 处理支付表单\r\n        const tempDiv = document.createElement('div');\r\n\r\n        tempDiv.innerHTML = response.data;\r\n        document.body.appendChild(tempDiv);\r\n        const form = tempDiv.querySelector('form');\r\n\r\n        // console.log(response)\r\n\r\n\r\n\r\n        if (form) {\r\n          this.showNotificationMessage('正在跳转到支付页面...', 'success');\r\n          form.target = '_blank'; // 设置表单提交目标为新窗口\r\n          form.submit();\r\n          // this.$message.success('正在跳转到支付页面...');\r\n\r\n          // 支付成功后刷新余额和交易记录\r\n          setTimeout(() => {\r\n            this.fetchUserBalance();\r\n            this.fetchTransactions();\r\n          }, 3000);\r\n        } else {\r\n          this.showNotificationMessage('支付表单生成失败', 'error');\r\n        }\r\n      } catch (error) {\r\n        this.showNotificationMessage('充值请求失败，请稍后重试', 'error');\r\n      } finally {\r\n        this.rechargeLoading = false;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.fee-center-container {\r\n  display: flex;\r\n  max-width: 2560px;\r\n  background-color: #f5f7fa;\r\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n.navigation-sidebar {\r\n  width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #ebeef5;\r\n  position: fixed;\r\n  height: 100%;\r\n  z-index: 10;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.nav-title {\r\n  font-size: 18px;\r\n  color: #303133;\r\n  padding: 20px 16px;\r\n  margin: 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.nav-list {\r\n  list-style: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.nav-item {\r\n  padding: 14px 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.nav-item a {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #303133;\r\n  text-decoration: none;\r\n}\r\n\r\n.nav-item i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.nav-item.active {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.nav-item.active a {\r\n  color: #409eff;\r\n}\r\n\r\n.nav-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.main-content {\r\n  flex: 1;\r\n  margin-left: 200px;\r\n  padding: 20px;\r\n}\r\n\r\n.tab-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-bar {\r\n  position: relative;\r\n  width: 300px;\r\n}\r\n\r\n.search-bar input {\r\n  width: 100%;\r\n  height: 36px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  padding: 0 30px 0 12px;\r\n  font-size: 14px;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.search-bar input:focus {\r\n  border-color: #409eff;\r\n  outline: none;\r\n}\r\n\r\n.search-button, .clear-button {\r\n  position: absolute;\r\n  right: 8px;\r\n  top: 8px;\r\n  background: none;\r\n  border: none;\r\n  color: #606266;\r\n  cursor: pointer;\r\n}\r\n\r\n.clear-button {\r\n  right: 30px;\r\n}\r\n\r\n.currency-display {\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.flow-count {\r\n  margin-left: 16px;\r\n}\r\n\r\n.table-container {\r\n  overflow-x: auto;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.data-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n}\r\n\r\n.data-table th, .data-table td {\r\n  padding: 12px 8px;\r\n  text-align: left;\r\n  font-size: 14px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.data-table th {\r\n  color: #909399;\r\n  font-weight: 500;\r\n  white-space: nowrap;\r\n}\r\n\r\n.data-table td {\r\n  color: #606266;\r\n}\r\n\r\n.data-table th i {\r\n  margin-left: 4px;\r\n  cursor: pointer;\r\n  color: #c0c4cc;\r\n}\r\n\r\n.data-table th i:hover {\r\n  color: #409eff;\r\n}\r\n\r\n.status-tag {\r\n  width: 10vh;\r\n  text-align: center;\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-success {\r\n  background-color: #f0f9eb;\r\n  color: #67c23a;\r\n}\r\n\r\n.status-pending {\r\n  background-color: #fdf6ec;\r\n  color: #e6a23c;\r\n}\r\n\r\n.status-error {\r\n  background-color: #fef0f0;\r\n  color: #f56c6c;\r\n}\r\n\r\n.status-info {\r\n  background-color: #f4f4f5;\r\n  color: #909399;\r\n}\r\n\r\n.status-warning {\r\n  background-color: #fdf6ec;\r\n  color: #e6a23c;\r\n}\r\n\r\n.status-running {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n.status-complete {\r\n  background-color: #ecf5ff;\r\n  color: #e6a23c;\r\n}\r\n.payment-method-tag {\r\n  width: 10vh;\r\n  text-align: center;\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.payment-alipay {\r\n  background-color: #e6f7ff;\r\n  color: #1890ff;\r\n}\r\n\r\n.payment-wechat {\r\n  background-color: #f6ffed;\r\n  color: #52c41a;\r\n}\r\n\r\n.payment-bank {\r\n  background-color: #fff2e8;\r\n  color: #fa8c16;\r\n}\r\n\r\n.payment-balance {\r\n  background-color: #f9f0ff;\r\n  color: #722ed1;\r\n}\r\n\r\n.payment-warning {\r\n  background-color: #fdf6ec;\r\n  color: #e6a23c;\r\n}\r\n\r\n.operation-link {\r\n  color: #409eff;\r\n  cursor: pointer;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.operation-link:hover {\r\n  color: #66b1ff;\r\n  text-decoration: underline;\r\n}\r\n\r\n.expense-amount {\r\n  color: #e52a2a !important;\r\n  font-weight: bold !important;\r\n}\r\n.income-amount {\r\n  color: #4fc44f !important;\r\n  font-weight: bold !important;\r\n}\r\n\r\n.empty-state {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n}\r\n\r\n.empty-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 48px;\r\n  color: #c0c4cc;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.empty-text {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.order-details {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.detail-card {\r\n  padding: 20px;\r\n}\r\n\r\n.detail-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  padding-bottom: 16px;\r\n}\r\n\r\n.detail-title {\r\n  font-size: 18px;\r\n  color: #303133;\r\n  margin: 0;\r\n}\r\n\r\n.back-button {\r\n  display: flex;\r\n  align-items: center;\r\n  background: none;\r\n  border: none;\r\n  color: #409eff;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  padding: 6px 12px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.back-button:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.back-button i {\r\n  margin-right: 4px;\r\n}\r\n\r\n.detail-content {\r\n  padding: 0 16px;\r\n  max-height: 395px;\r\n}\r\n\r\n.detail-subtitle {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  margin: 24px 0 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.detail-section {\r\n  margin-bottom: 24px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n}\r\n\r\n.detail-row {\r\n  display: flex;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.detail-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.detail-item {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.detail-label {\r\n  width: 120px;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.detail-value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.transaction-summary, .usage-summary {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.summary-card {\r\n  flex: 1;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n  margin-right: 16px;\r\n  text-align: center;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.summary-card:hover {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.summary-card:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n.summary-title {\r\n  color: #909399;\r\n  font-size: 14px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.summary-value {\r\n  color: #303133;\r\n  font-size: 24px;\r\n  font-weight: 500;\r\n}\r\n\r\n.summary-value.income {\r\n  color: #67c23a;\r\n}\r\n\r\n.summary-value.expense {\r\n  color: #f56c6c;\r\n}\r\n\r\n.transaction-type {\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.income-type {\r\n  background-color: #f0f9eb;\r\n  color: #67c23a;\r\n}\r\n\r\n.expense-type {\r\n  background-color: #fef0f0;\r\n  color: #f56c6c;\r\n}\r\n\r\n.refund-type {\r\n  background-color: #ecf5ff;\r\n  color: #409eff;\r\n}\r\n\r\n.date-range-picker {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.date-range-picker select,\r\n.search-filters select,\r\n.search-filters input {\r\n  height: 36px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  padding: 0 12px;\r\n  margin-right: 12px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  transition: border-color 0.2s;\r\n}\r\n\r\n.date-range-picker select:focus,\r\n.search-filters select:focus,\r\n.search-filters input:focus {\r\n  border-color: #409eff;\r\n  outline: none;\r\n}\r\n\r\n.custom-date-range {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: 12px;\r\n}\r\n\r\n.custom-date-range input {\r\n  width: 140px;\r\n  margin-right: 8px;\r\n  height: 36px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  padding: 0 12px;\r\n}\r\n\r\n.custom-date-range span {\r\n  margin: 0 8px;\r\n  color: #606266;\r\n}\r\n\r\n.apply-button {\r\n  background-color: #409eff;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 8px 16px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.apply-button:hover {\r\n  background-color: #66b1ff;\r\n}\r\n\r\n.account-balance {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.balance-info {\r\n  display: flex;\r\n  align-items: baseline;\r\n  justify-content: flex-start;\r\n  background-color: #fff;\r\n  padding: 16px 24px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  width: 100%;\r\n}\r\n\r\n.balance-label {\r\n  font-size: 20px;\r\n  color: #606266;\r\n  margin-right: 12px;\r\n}\r\n\r\n.balance-value {\r\n  font-size: 22px;\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.recharge-options {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.recharge-title {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  margin: 0 0 16px 0;\r\n  font-weight: 500;\r\n}\r\n\r\n.amount-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.amount-option {\r\n  width: calc(20% - 16px);\r\n  min-width: 100px;\r\n  height: 50px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  color: #606266;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.amount-option:hover {\r\n  border-color: #c6e2ff;\r\n  color: #409eff;\r\n}\r\n\r\n.amount-option.selected {\r\n  border-color: #409eff;\r\n  color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.custom-amount {\r\n  width: calc(20% - 16px);\r\n  min-width: 100px;\r\n}\r\n\r\n.custom-amount input {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n  outline: none;\r\n  text-align: center;\r\n  font-size: 16px;\r\n  color: #606266;\r\n}\r\n\r\n.payment-methods {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.payment-method {\r\n  width: 140px;\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;  /* 垂直居中 */\r\n  justify-content: center;  /* 水平居中 */\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  padding: 0 16px;  /* 添加内边距 */\r\n}\r\n\r\n.payment-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  margin-right: 8px;  /* 图标和文字之间的间距 */\r\n}\r\n\r\n.payment-text {\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.payment-method:hover {\r\n  border-color: #c6e2ff;\r\n}\r\n\r\n.payment-method.selected {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.payment-method.selected .payment-icon,\r\n.payment-method.selected .payment-text {\r\n  color: #409eff;\r\n}\r\n\r\n.recharge-action {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 32px;\r\n}\r\n\r\n.recharge-button {\r\n  width: 200px;\r\n  height: 40px;\r\n  background-color: #409eff;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.recharge-button:hover {\r\n  background-color: #66b1ff;\r\n}\r\n\r\n.recharge-button:disabled {\r\n  background-color: #a0cfff;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.loading-state, .error-state {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.loading-state i {\r\n  font-size: 18px;\r\n  margin-right: 8px;\r\n  color: #409eff;\r\n  animation: rotating 2s linear infinite;\r\n}\r\n\r\n.error-state i {\r\n  font-size: 18px;\r\n  margin-right: 8px;\r\n  color: #f56c6c;\r\n}\r\n\r\n.error-state button {\r\n  margin-left: 12px;\r\n  padding: 4px 12px;\r\n  background-color: #f56c6c;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n}\r\n\r\n.payment-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n@keyframes rotating {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 992px) {\r\n  .navigation-sidebar {\r\n    width: 180px;\r\n  }\r\n\r\n  .main-content {\r\n    margin-left: 180px;\r\n  }\r\n\r\n  .amount-option {\r\n    width: calc(25% - 16px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .navigation-sidebar {\r\n    width: 160px;\r\n  }\r\n\r\n  .main-content {\r\n    margin-left: 160px;\r\n  }\r\n\r\n  .search-section {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .search-bar {\r\n    width: 100%;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .amount-option {\r\n    width: calc(33.33% - 16px);\r\n  }\r\n}\r\n\r\n@media (max-width: 576px) {\r\n  .navigation-sidebar {\r\n    width: 100%;\r\n    position: static;\r\n    height: auto;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .main-content {\r\n    margin-left: 0;\r\n  }\r\n\r\n  .transaction-summary, .usage-summary {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .summary-card {\r\n    margin-right: 0;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .summary-card:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .amount-option {\r\n    width: calc(50% - 16px);\r\n  }\r\n\r\n  .payment-methods {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .payment-method {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./OrderView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./OrderView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./OrderView.vue?vue&type=template&id=2f8bfd3e&scoped=true&\"\nimport script from \"./OrderView.vue?vue&type=script&lang=js&\"\nexport * from \"./OrderView.vue?vue&type=script&lang=js&\"\nimport style0 from \"./OrderView.vue?vue&type=style&index=0&id=2f8bfd3e&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2f8bfd3e\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "showNotification", "attrs", "notificationMessage", "notificationType", "minHeight", "on", "$event", "_e", "staticStyle", "staticClass", "_v", "class", "active", "currentSection", "preventDefault", "changeSection", "directives", "name", "rawName", "value", "showOrderDetails", "expression", "orderSearchQuery", "domProps", "target", "composing", "searchOrders", "clearOrderSearch", "_s", "currentOrderTotal", "orderLoading", "orderError", "fetchOrders", "sortBy", "_l", "paginatedOrders", "order", "index", "key", "order_number", "formatDateTime", "created_at", "getPaymentStatusClass", "payment_status", "getPaymentStatusText", "formatPrice", "unit_price", "duration", "getPaymentMethodClass", "payment_method", "getPaymentMethodText", "total_price", "viewOrderDetails", "filteredOrderData", "length", "_m", "currentPage", "pageSize", "goToPage", "handlePageSizeChange", "showOrderList", "<PERSON><PERSON><PERSON><PERSON>", "gpu_model", "region", "gpu_count", "video_memory", "cpu_cores", "system_disk", "cloud_disk", "memory", "transactionDateRange", "$$selectedVal", "Array", "prototype", "filter", "call", "options", "o", "selected", "map", "val", "_value", "multiple", "customDateStart", "customDateEnd", "transactionType", "transactionSearchQuery", "type", "indexOf", "_k", "keyCode", "searchTransactions", "apply", "arguments", "summaryData", "totalRecharge", "totalExpense", "userBalance", "transactionLoading", "transactionError", "fetchTransactions", "paginatedTransactions", "transaction", "transaction_id", "getTransactionTypeClass", "getTransactionTypeName", "getTransactionTypeNamePay", "pay_type", "amount", "payment_channel", "description", "filteredTransactionData", "transactionPage", "page", "size", "usageDateRange", "customUsageDateStart", "customUsageDateEnd", "usageFilterGpu", "gpuModels", "gpu", "id", "paginatedUsageRecords", "record", "getUsageStatusClass", "status", "getUsageStatusText", "start_time", "end_time", "calculateDuration", "cost", "navigateToRecharge", "cancelReservation", "filteredUsageData", "usagePage", "rechargeAmounts", "rechargeAmount", "customRechargeAmount", "handleCustomAmountInput", "paymentMethod", "require", "can<PERSON>ech<PERSON><PERSON>", "submit<PERSON>echarge", "staticRenderFns", "_t", "components", "Header", "component", "total", "changePage", "totalPages", "inputPage", "modifiers", "handleJump", "$forceUpdate", "_n", "props", "Number", "required", "data", "watch", "newVal", "computed", "Math", "ceil", "methods", "$emit", "SlideNotification", "Layout", "CommonPagination", "orderData", "transactionData", "balance", "usageData", "usageLoading", "usageError", "rechargeLoading", "query", "toLowerCase", "includes", "startIndex", "endIndex", "slice", "filtered", "days", "parseInt", "cutoffDate", "Date", "setDate", "getDate", "transactionDate", "startDate", "endDate", "setHours", "recordDate", "getRechargeAmount", "$route", "to", "activeTab", "fetchSummaryData", "fetchUserBalance", "loadSectionData", "created", "$watch", "<PERSON><PERSON><PERSON><PERSON>", "immediate", "then", "<PERSON><PERSON><PERSON><PERSON>", "showNotificationMessage", "message", "section", "$router", "replace", "fetchUsageData", "response", "postAnyData", "code", "error", "$message", "applyUsageCustomDateRange", "formatDateTimeusage", "dateTimeString", "date", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "hour12", "e", "startTime", "endTime", "start", "end", "isNaN", "getTime", "diffMs", "hours", "floor", "minutes", "path", "timestamp", "now", "order_name", "mapOrderStatus", "order_staus", "order_payment_method", "order_price", "calculateUnitPrice", "order_buy_time", "create_time", "Region", "graphics_card_number", "gpu_nuclear_number", "data_disk", "internal_memory", "rawData", "sort", "a", "b", "uniqueMap", "Map", "for<PERSON>ach", "uniqueId", "topup_id", "gpu_order_id", "has", "set", "topup_order_id", "gpu_order_name", "source_table", "mapPayType", "topup_topup", "gpu_order_price", "mapTransactionStatus", "from", "values", "params", "start_date", "end_date", "toISOString", "split", "mockData", "transactions", "t", "uniqueTransactions", "reduce", "sum", "parseFloat", "statusMap", "computing", "order_unit", "price_bour", "price_day", "price_mouth", "price_year", "price", "toFixed", "formatDate", "dateString", "toLocaleDateString", "method", "field", "$nextTick", "document", "querySelector", "scrollIntoView", "behavior", "window", "scrollTo", "top", "applyCustomDateRange", "payType", "getTransactionStatusClass", "getTransactionStatusName", "renewService", "success", "getAnyData", "tempDiv", "createElement", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "form", "submit", "setTimeout"], "sourceRoot": ""}