{"version": 3, "file": "js/docs11.067befee.js", "mappings": "wHACA,IAAIA,EAA6B,IAAIC,IAAI,YACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,YACrCI,EAA6B,IAAIJ,IAAI,aACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,aACrCO,EAA6B,IAAIP,IAAI,aAErCQ,EAAO,k4BAAm6BT,EAA6B,qLAAiME,EAA6B,uSAAyTC,EAA6B,kHAA4HC,EAA6B,+QAA6RC,EAA6B,ybAA2cC,EAA6B,wzBAAk1BC,EAA6B,6eAAugBC,EAA6B,q3JAE70H,c", "sources": ["webpack://portal-ui/./src/docs/ollama-qwen.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/Ollama+Qwen1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/Ollama+Qwen2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/Ollama+Qwen3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/Ollama+Qwen4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/Ollama+Qwen5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/Ollama+Qwen6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/Ollama+Qwen7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/ollama-qwen-webui11.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"容器化部署-ollamaqwen3\\\"><font style=\\\"color:#020817\\\">容器化部署 Ollama+Qwen3</font></h1> <p><font style=\\\"color:#020817\\\">本指南全面介绍了在天工开物平台上部署 Ollama 与 Qwen3 大语言模型 API 服务的完整解决方案。该方案不仅提供了详实的部署流程，还提供了如何制作镜像的方案。</font></p> <blockquote> <p><font style=\\\"color:#1f2937\\\">此镜像提供了标准化的</font><strong><font style=\\\"color:#1f2937\\\"> API 接口</font></strong><font style=\\\"color:#1f2937\\\">，让您能够便捷地通过 </font><strong><font style=\\\"color:#1f2937\\\">API 调用方式</font></strong><font style=\\\"color:#1f2937\\\">访问和使用所有功能。</font></p> <p><font style=\\\"color:#1f2937\\\">如果您希望通过 Web UI 的方式使用大模型，可以参考另外的最佳实践，参考：容器化部署 Ollama + Qwen3 + Open WebUI</font></p> </blockquote> <h2 id=\\\"1、部署服务\\\"><strong><font style=\\\"color:#020817\\\">1、部署服务</font></strong></h2> <h3 id=\\\"11-访问天工开物控制台，点击新增部署。\\\"><font style=\\\"color:#020817\\\">1.1 访问</font><a href=\\\"https://tiangongkaiwu.top/portal/#/console\\\">天工开物控制台</a><font style=\\\"color:#020817\\\">，点击新增部署。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"12-选择设备\\\"><font style=\\\"color:#020817\\\">1.2 选择设备</font></h3> <p><font style=\\\"color:#020817\\\">基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"13-选择相应预制镜像\\\"><font style=\\\"color:#020817\\\">1.3 选择相应预制镜像</font></h3> <p><font style=\\\"color:#020817\\\">这里选择</font><font style=\\\"color:#020817\\\"> </font><strong><font style=\\\"color:#020817\\\">Qwen3 30B A3B，</font></strong><font style=\\\"color:#020817\\\">点击部署服务。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"14-耐心等待节点拉取镜像并启动\\\"><font style=\\\"color:#020817\\\">1.4 耐心等待节点拉取镜像并启动</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"15-部署完成\\\"><font style=\\\"color:#020817\\\">1.5 部署完成</font></h3> <p><font style=\\\"color:#020817\\\">在部署完成页面，能看到一个公开访问链接。这个链接就是 Ollama 服务的 API 访问地址。</font></p> <p><font style=\\\"color:#020817\\\">将这个 API 地址复制下来，就可以在任何支持 Ollama 协议的应用程序中使用。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">在“常规”面板里可以看到公开访问的地址，此地址即为 Ollama 服务的 API 地址。 请耐心一点哦~ 模型镜像会比较大，</font><strong><font style=\\\"color:#020817\\\">qwen3:30b-a3b 镜像本身 20G+，打包之后大约 40G+，</font></strong><font style=\\\"color:#020817\\\"> 拉取镜像会需要一段时间。</font></p> <h3 id=\\\"16-验证一下\\\"><font style=\\\"color:#020817\\\">1.6 验证一下</font></h3> <p><font style=\\\"color:#020817\\\">访问复制的链接，{快捷访问的地址} /api/tags，将链接复制到浏览器，就可以看到以下内容，说明模型已经部署并运行了。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">如果需要在其他兼容 Ollama 的客户端使用时，需要提供的参数如下：</font></p> <ul> <li><font style=\\\"color:#020817\\\">访问地址</font></li> </ul> <p><font style=\\\"color:#020817\\\">常规 -&gt; 快捷访问中 11434 对应的链接。有的会需要在链接后面加上 /api</font></p> <ul> <li><font style=\\\"color:#020817\\\">ModelId</font></li> </ul> <p><strong><font style=\\\"color:#020817\\\">qwen3:30b-a3b</font></strong></p> <ul> <li><font style=\\\"color:#020817\\\">上下文长度</font></li> </ul> <p><font style=\\\"color:#020817\\\">32k</font></p> <ul> <li><font style=\\\"color:#020817\\\">模型建议的其他参数（非必须，可以根据需要自行修改）</font></li> </ul> <pre><code class=\\\"language-plain\\\">{\\n    &quot;repeat_penalty&quot;: 1,\\n    &quot;temperature&quot;: 0.6,\\n    &quot;top_k&quot;: 20,\\n    &quot;top_p&quot;: 0.95\\n}\\n</code></pre> <p><font style=\\\"color:#020817\\\">使用第三方客户端时，可以按照下图填写内容</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"2、模型速度测试\\\"><font style=\\\"color:#020817\\\">2、模型速度测试</font></h2> <p><font style=\\\"color:#020817\\\">qwen3 部署完成了，速度怎么样呢？点击</font><font style=\\\"color:#020817\\\"> </font><a href=\\\"https://lmspeed.net/zh-CN\\\"><font style=\\\"color:#2f8ef4\\\">LM Speed</font></a><font style=\\\"color:#020817\\\"> </font><font style=\\\"color:#020817\\\">测试一下速度吧~</font></p> <blockquote> <p><font style=\\\"color:#67676c\\\">如果 LM Speed 无法访问，多刷新几次就可以了 </font><font style=\\\"color:#67676c\\\">😊</font></p> </blockquote> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"></p> <h2 id=\\\"3、构建-ollama--qwen3-模型镜像\\\"><strong><font style=\\\"color:#020817\\\">3、构建 Ollama + Qwen3 模型镜像</font></strong></h2> <blockquote> <p><font style=\\\"color:#67676c\\\">温馨提示，如果你只希望使用我们默认的镜像，那么下面的内容您无需关注。</font></p> </blockquote> <h3 id=\\\"31-clone-项目\\\"><font style=\\\"color:#020817\\\">3.1 clone 项目</font></h3> <pre><code class=\\\"language-plain\\\">git clone https://github.com/slmnb-lab/llm-deployment.git\\n</code></pre> <h3 id=\\\"32-修改模型名称\\\"><font style=\\\"color:#020817\\\">3.2 修改模型名称</font></h3> <ul> <li><font style=\\\"color:#020817\\\">修改 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">ollama</font><font style=\\\"color:#020817\\\"> 目录下的 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">ollama_pull.sh</font><font style=\\\"color:#020817\\\"> 文件中的模型名称。当前使用的模型是</font><strong><font style=\\\"color:#020817\\\"> qwen3:30b-a3b</font></strong></li> </ul> <p><font style=\\\"color:#67676c\\\">模型列表参考 </font><a href=\\\"https://ollama.com/library\\\"><font style=\\\"color:#2f8ef4\\\">Ollama 官网</font></a></p> <pre><code class=\\\"language-plain\\\">#!/bin/bash\\nollama serve &amp;\\nsleep 15\\nollama pull qwen3:30b-a3b  # 替换成你需要的模型\\n</code></pre> <ul> <li><font style=\\\"color:#020817\\\">修改 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">ollama</font><font style=\\\"color:#020817\\\"> 目录下的 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">compose.yml</font><font style=\\\"color:#020817\\\"> 文件中的模型名称。</font></li> </ul> <p><font style=\\\"color:#67676c\\\">开始之前需要在天工开物中创建一个镜像仓库，使用你自己的镜像仓库</font><strong><font style=\\\"color:#67676c\\\">账号名称</font></strong><font style=\\\"color:#67676c\\\">替换{yourusername}，镜像仓库名称为 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">qwen</font><font style=\\\"color:#67676c\\\">，镜像标签为 </font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">30b-a3b</font><font style=\\\"color:#67676c\\\">。访问这里 </font><a href=\\\"https://tiangongkaiwu.top/#/console\\\"><font style=\\\"color:#2f8ef4\\\">初始化镜像仓库</font></a></p> <pre><code class=\\\"language-plain\\\">services:\\n  qwen:\\n    ## {yourusername}是天工开物的镜像仓库账号名称 \\n    ## qwen3 是镜像名称 30b-a3b 是镜像标签\\n    image: harbor.suanleme.cn/{yourusername}/qwen3:30b-a3b  \\n    build: .\\n    labels:\\n      - suanleme_0.http.port=11434          # 这里是 ollama 运行的端口，不要修改\\n      - suanleme_0.http.prefix=qwen332b     # 这里是发布的回传域名前缀\\n    restart: unless-stopped\\n    deploy:\\n      resources:\\n        reservations:\\n          devices:\\n            - driver: nvidia\\n              count: all\\n              capabilities: [gpu]\\n    ports:\\n      - &quot;11434:11434&quot;                        # 这里是 ollama 运行的端口，不要修改\\n</code></pre> <h3 id=\\\"33-运行打包脚本\\\"><font style=\\\"color:#020817\\\">3.3 运行打包脚本</font></h3> <p><font style=\\\"color:#020817\\\">执行成功之后，会在本地生成镜像</font></p> <pre><code class=\\\"language-plain\\\">docker compose build\\n</code></pre> <h4 id=\\\"问题1failed-to-solve-ollamaollama-failed-to-resolve-source-metadata-for-dockerioollamaollamalatest\\\">问题1:<font style=\\\"color:#333\\\">‘failed to solve: ollama/ollama: failed to resolve source metadata for docker.io/ollama/ollama:latest’</font></h4> <p><font style=\\\"color:#020817\\\">解决办法：先拉取下来哈</font></p> <pre><code class=\\\"language-plain\\\">docker pull ollama/ollama:latest\\n</code></pre> <h4 id=\\\"问题2failed-to-solve-process-binsh--c-chmod-x-ollama_pullsh--ollama_pullsh-did-not-complete-successfully-exit-code-127\\\">问题2:<font style=\\\"color:#333\\\">‘failed to solve: process &quot;/bin/sh -c chmod +x ollama_pull.sh &amp;&amp; ./ollama_pull.sh&quot; did not complete successfully: exit code: 127’</font></h4> <p><font style=\\\"color:#020817\\\">解决办法：将dockerfile中的‘RUN chmod +x ollama_pull.sh &amp;&amp; ./ollama_pull.sh’改为‘RUN chmod +x /ollama_pull.sh &amp;&amp; /bin/bash /ollama_pull.sh’，再运行打包脚本就好了。</font></p> <h2 id=\\\"4、镜像上传\\\"><strong><font style=\\\"color:#020817\\\">4、镜像上传</font></strong></h2> <p><font style=\\\"color:#020817\\\">将打包的镜像上传到天工开物的镜像仓库</font></p> <h3 id=\\\"41-登录镜像仓库\\\"><font style=\\\"color:#020817\\\">4.1 登录镜像仓库</font></h3> <p><font style=\\\"color:#020817\\\">username 需要替换为自己的天工开物</font><strong><font style=\\\"color:#020817\\\">镜像仓库</font></strong><font style=\\\"color:#020817\\\">的</font><strong><font style=\\\"color:#020817\\\">用户名</font></strong><font style=\\\"color:#020817\\\">！</font></p> <p><font style=\\\"color:#020817\\\">输入密码需要输入初始化镜像仓库时设置的密码</font></p> <pre><code class=\\\"language-plain\\\">### harbor.suanleme.cn 是固定值，{yourusername}需要替换为自己的镜像仓库的用户名！\\ndocker login harbor.suanleme.cn --username={yourusername}\\n\\n## 输入密码  镜像仓库的密码!\\n*******\\n</code></pre> <h3 id=\\\"42-上传镜像\\\"><font style=\\\"color:#020817\\\">4.2 上传镜像</font></h3> <p><font style=\\\"color:#020817\\\">执行以下代码，进行镜像上传</font></p> <pre><code class=\\\"language-plain\\\">## 为新生成的镜像打上标签\\ndocker tag harbor.suanleme.cn/{yourusername}/qwen3:30b-a3b harbor.suanleme.cn/{yourusername}/qwen3:30b-a3b\\n\\n## 上传镜像\\ndocker push harbor.suanleme.cn/{yourusername}/qwen3:30b-a3b\\n</code></pre> <p><font style=\\\"color:#67676c\\\">备注：镜像比较大，如果推送失败了，多试几次就好啦。</font><font style=\\\"color:#67676c\\\">😊</font></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/12 13:59</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "code"], "sourceRoot": ""}