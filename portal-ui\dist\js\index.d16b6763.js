(function(){"use strict";var t={4863:function(t,e,i){i.d(e,{Z:function(){return u}});var n=function(){var t=this,e=t._self._c;return e("main",{staticClass:"page-wrapper"},[e("Header"),e("div",{staticClass:"main-content"},[t._t("default")],2),e("Footer")],1)},s=[],a=i(2503),o={name:"Layout",components:{Header:a.Z}},c=o,r=i(1001),l=(0,r.Z)(c,n,s,!1,null,"18a49a4c",null),u=l.exports},2503:function(t,e,i){i.d(e,{Z:function(){return v}});var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"header-wrapper"},[t.showComingSoon?e("SlideNotification",{attrs:{message:t.notificationMessage,type:"warning",duration:2e3},on:{close:function(e){t.showComingSoon=!1}}}):t._e(),e("div",{staticClass:"nav-placeholder",style:{height:t.navHeight+"px"}}),e("div",{ref:"mainNav",staticClass:"main-nav"},[e("div",{staticClass:"container"},[t.isMobile?e("div",{staticClass:"mobile-nav"},[e("div",{staticClass:"mobile-nav-container"},[e("button",{staticClass:"hamburger-btn",on:{click:t.toggleMobileMenu}},[e("span",{staticClass:"hamburger-line",class:{"line-1":t.mobileMenuOpen}}),e("span",{staticClass:"hamburger-line",class:{"line-2":t.mobileMenuOpen}}),e("span",{staticClass:"hamburger-line",class:{"line-3":t.mobileMenuOpen}})]),e("div",{staticClass:"mobile-logo-area"},[e("a",{staticClass:"logo-link",on:{click:function(e){return t.navigateTo("/")}}},[e("img",{attrs:{src:"images/logo_tiangong.png",alt:"算力租赁"}})])]),e("div",{staticClass:"mobile-user-actions"},[t.isLoggedIn?[e("a",{staticClass:"mobile-console-btn",on:{click:function(e){return t.navigateTo("/help")}}},[t._v(" 帮助文档 ")]),e("a",{staticClass:"mobile-console-btn",on:{click:t.handleConsoleNavigation}},[t._v(" 控制台 ")]),e("div",{staticClass:"mobile-user-profile",on:{click:t.toggleUserMenu}},[e("div",{staticClass:"mobile-user-avatar"},[e("div",{staticClass:"avatar-letter"},[t._v(t._s(t.userInitial))])])])]:[e("a",{staticClass:"mobile-login-btn",on:{click:function(e){return t.navigateTo("/login")}}},[t._v(" 登录 ")]),e("a",{staticClass:"mobile-register-btn",on:{click:function(e){return t.navigateTo("/register")}}},[t._v(" 注册 ")])]],2)]),e("div",{staticClass:"mobile-menu",class:{open:t.mobileMenuOpen}},[e("div",{staticClass:"mobile-menu-content"},[e("ul",{staticClass:"mobile-nav-list"},[e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",class:{active:t.isActive("/")},on:{click:function(e){return t.navigateTo("/")}}},[e("i",{staticClass:"iconfont icon-home"}),t._v("首页 ")])]),e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",class:{active:t.isActive("/product")},on:{click:function(e){return t.navigateTo("/product")}}},[e("i",{staticClass:"iconfont icon-server"}),t._v("算力市场 ")])]),e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",on:{click:t.triggerComingSoon}},[e("i",{staticClass:"iconfont icon-community"}),t._v("算法社区 ")])]),e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",on:{click:t.triggerComingSoon}},[e("i",{staticClass:"iconfont icon-cloud"}),t._v("私有云 ")])]),e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",class:{active:t.isActive("/about")},on:{click:function(e){return t.navigateTo("/about")}}},[e("i",{staticClass:"iconfont icon-info"}),t._v("关于我们 ")])]),t.isLoggedIn?[e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",class:{active:t.isActive("/help")},on:{click:function(e){return t.navigateTo("/help")}}},[e("i",{staticClass:"iconfont icon-docs"}),t._v("帮助文档 ")])]),e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",class:{active:t.isActive("/console")},on:{click:t.handleConsoleNavigation}},[e("i",{staticClass:"iconfont icon-console"}),t._v("控制台 ")])]),e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",class:{active:t.isActive("/personal")},on:{click:function(e){return t.navigateTo("/personal")}}},[e("i",{staticClass:"iconfont icon-profile"}),t._v("个人中心 ")])]),e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",on:{click:t.logout}},[e("i",{staticClass:"iconfont icon-logout"}),t._v("退出登录 ")])])]:[e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",class:{active:t.isActive("/help")},on:{click:function(e){return t.navigateTo("/help")}}},[e("i",{staticClass:"iconfont icon-docs"}),t._v("帮助文档 ")])]),e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",class:{active:t.isActive("/login")},on:{click:function(e){return t.navigateTo("/login")}}},[e("i",{staticClass:"iconfont icon-user"}),t._v("登录 ")])]),e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",class:{active:t.isActive("/register")},on:{click:function(e){return t.navigateTo("/register")}}},[e("i",{staticClass:"iconfont icon-edit"}),t._v("注册 ")])]),e("li",{staticClass:"mobile-nav-item"},[e("a",{staticClass:"mobile-nav-link",class:{active:t.isActive("/console")},on:{click:function(e){return t.navigateTo("/login")}}},[e("i",{staticClass:"iconfont icon-console"}),t._v("控制台 ")])])]],2)])]),t.showUserMenu?e("div",{staticClass:"mobile-user-menu"},[e("div",{staticClass:"mobile-user-info"},[e("div",{staticClass:"mobile-username"},[t._v(t._s(t.userName))]),e("div",{staticClass:"mobile-user-phone"},[t._v(t._s(t.userPhone))])]),e("div",{staticClass:"mobile-menu-options"},[e("a",{staticClass:"mobile-menu-item",class:{active:t.isActive("/personal")},on:{click:function(e){return t.navigateTo("/personal")}}},[e("i",{staticClass:"iconfont icon-profile"}),t._v("个人中心 ")]),e("a",{staticClass:"mobile-menu-item",class:{active:t.isActive("/userorder")},on:{click:function(e){return t.navigateTo("/userorder")}}},[e("i",{staticClass:"iconfont icon-order"}),t._v("费用中心 ")]),e("a",{staticClass:"mobile-menu-item",class:{active:!1},on:{click:t.navigateToRecharge}},[e("i",{staticClass:"iconfont icon-recharge"}),t._v("充值 ")]),e("a",{staticClass:"mobile-menu-item logout",on:{click:t.logout}},[e("i",{staticClass:"iconfont icon-logout"}),t._v("退出登录 ")])])]):t._e()]):e("div",{staticClass:"nav-container desktop-nav"},[e("div",{staticClass:"logo-area"},[e("a",{staticClass:"logo-link",on:{click:function(e){return t.navigateTo("/")}}},[e("img",{attrs:{src:"images/logo-tiangong.png",alt:"算力租赁",loading:"eager"}})])]),e("div",{staticClass:"nav-menu"},[e("ul",{staticClass:"nav-list"},[e("li",{staticClass:"nav-item"},[e("a",{staticClass:"nav-link",class:{active:t.isActive("/")},on:{click:function(e){return t.navigateTo("/")}}},[t._v("首页")])]),e("li",{staticClass:"nav-item dropdown"},[e("a",{staticClass:"nav-link",class:{active:t.isActive("/product")},on:{click:function(e){return t.navigateTo("/product")}}},[t._v(" 算力市场"),e("i",{staticClass:"iconfont icon-arrow-down"})])]),e("li",{staticClass:"nav-item dropdown"},[e("a",{staticClass:"nav-link",class:{active:!1},on:{click:t.triggerComingSoon}},[t._v("算法社区"),e("i",{staticClass:"iconfont icon-arrow-down"})])]),e("li",{staticClass:"nav-item"},[e("a",{staticClass:"nav-link",class:{active:!1},on:{click:t.triggerComingSoon}},[t._v("私有云")])]),e("li",{staticClass:"nav-item"},[e("a",{staticClass:"nav-link",class:{active:!1},on:{click:t.triggerComingSoon}},[t._v("关于我们")])])])]),e("div",{staticClass:"user-actions"},[t.isLoggedIn?t._e():e("div",{staticClass:"auth-buttons"},[e("a",{staticClass:"btn btn-login",class:{active:t.isActive("/help")},on:{click:function(e){return t.navigateTo("/help")}}},[t._v("帮助文档")]),e("a",{staticClass:"btn btn-login",class:{active:t.isActive("/login")},on:{click:function(e){return t.navigateTo("/login")}}},[t._v("控制台")]),e("a",{staticClass:"btn btn-login",class:{active:t.isActive("/login")},on:{click:function(e){return t.navigateTo("/login")}}},[t._v("登录")]),e("a",{staticClass:"btn btn-register",class:{active:t.isActive("/register")},on:{click:function(e){return t.navigateTo("/register")}}},[t._v("立即注册")])]),t.isLoggedIn?e("div",{staticClass:"user-profile"},[e("a",{staticClass:"btn btn-login",class:{active:t.isActive("/help")},on:{click:function(e){return t.navigateTo("/help")}}},[t._v("帮助文档")]),e("a",{staticClass:"btn btn-login",class:{active:t.isActive("/console"),disabled:t.isConsoleLoading},attrs:{title:t.isConsoleLoading?"控制台加载中，请稍后...":""},on:{click:t.handleConsoleNavigation}},[t._v("控制台")]),e("div",{staticClass:"user-dropdown"},[e("div",{staticClass:"user-avatar"},[e("div",{staticClass:"avatar-letter"},[t._v(t._s(t.userInitial))])]),e("div",{staticClass:"dropdown-menu"},[e("div",{staticClass:"user-info-section"},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("用户名:")]),e("span",{staticClass:"value"},[t._v(t._s(t.userName))])]),e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("手机号:")]),e("span",{staticClass:"value"},[e("i",{staticClass:"copy-icon"}),t._v(" "+t._s(t.userPhone))])]),e("div",{staticClass:"verification-tag",on:{click:t.gotoPersonal}},[e("span",{staticClass:"check-icon"}),t._v(" 个人认证 "),e("span",{staticClass:"status-text"},[t._v("("+t._s(1===t.isReal?"已认证":"未认证")+")")])]),e("div",{staticClass:"detail-item"},[e("span",{staticClass:"label"},[t._v("可用余额:")]),e("span",{staticClass:"value"},[t._v("￥"+t._s(t.userBalance.toFixed(2)))]),e("div",{staticClass:"verification-tag recharge-btn",on:{click:t.navigateToRecharge}},[t._v(" 充值 ")])])]),e("div",{staticClass:"menu-options"},[e("a",{class:{active:t.isActive("/personal")},on:{click:function(e){return t.navigateTo("/personal")}}},[t._v("个人中心")]),e("a",{class:{active:t.isActive("/userorder")},on:{click:function(e){return t.navigateTo("/userorder")}}},[t._v("费用中心")])]),e("div",{staticClass:"logout-button-container"},[e("button",{staticClass:"logout-button",on:{click:t.logout}},[t._v("退出登录")])])])])]):t._e()])])])])],1)},s=[],a=(i(7658),i(2223)),o=i(1836),c=i(7234),r=i(1955),l={name:"Header",components:{SlideNotification:c.Z},data(){return{userBalance:null,showComingSoon:!1,notificationMessage:"",isLoggedIn:!1,userName:"null",userPhone:"",notificationCount:2,currentPath:"",navHeight:60,scrollThrottleTimer:null,previousActivePath:null,isMobile:!1,mobileMenuOpen:!1,showUserMenu:!1,windowWidth:0,isReal:0,cookieWatcher:null,isConsoleLoading:!1,isConsoleReady:!1,hasTriggeredRefresh:!1}},computed:{userInitial(){return this.userName&&this.userName.length>0?this.userName.charAt(0).toUpperCase():"N"}},watch:{$route(t){this.currentPath=t.path,"/product"===t.path?this.currentPath="/product":"/about"===t.path&&(this.currentPath="/about")}},methods:{isActive(t){return this.currentPath===t||"/"===t&&"/index"===this.currentPath||"/product"===t&&this.currentPath.startsWith("/product")||"/about"===t&&"/about"===this.currentPath||"/help"===t&&this.currentPath.startsWith("/help")},checkScreenSize(){this.windowWidth=window.innerWidth,this.isMobile=this.windowWidth<=992,this.isMobile||(this.mobileMenuOpen=!1,this.showUserMenu=!1)},gotoPersonal(){this.closeAllMenus(),this.$router.push({path:"/personal",query:{activeTab:"verification"}})},navigateToRecharge(){this.closeAllMenus(),this.$router.push({path:"/userorder",query:{activeTab:"recharge"}})},handleConsoleNavigation(){if(this.closeAllMenus(),!this.isConsoleLoading)if(this.isLoggedIn){if(1!==this.isReal)return this.notificationMessage="请先完成实名认证，正在为您跳转到对应页面",this.showComingSoon=!0,void setTimeout((()=>{this.navigateTo("/personal",{activeTab:"verification"})}),2e3);if(!this.isConsoleReady)return this.notificationMessage="控制台初始化中，请稍候...",this.showComingSoon=!0,this.isConsoleLoading=!0,void(this.hasTriggeredRefresh||(this.hasTriggeredRefresh=!0,setTimeout((()=>{this.getUserInfo(),this.startCookieWatcher()}),2e3)));this.isConsoleLoading=!0,this.navigateTo("/console"),this.isConsoleReady=!0,this.isConsoleLoading=!1}else this.navigateTo("/login")},handleScroll(){this.scrollThrottleTimer||(this.scrollThrottleTimer=setTimeout((()=>{this.scrollThrottleTimer=null}),50))},logout(){this.closeAllMenus(),(0,a.fB)("/logout/cilent/logout").then((t=>{200===t.data.code&&(Object.keys(r.Z.get()).forEach((t=>{r.Z.remove(t)})),(0,o.gy)(),this.isLoggedIn=!1,this.$router.push("/login"))})).catch((t=>{}))},triggerComingSoon(){this.showComingSoon||(this.notificationMessage="我们正在努力建设中，敬请期待更多精彩内容！",this.showComingSoon=!0,this.closeAllMenus())},navigateTo(t,e={}){this.currentPath&&this.currentPath!==t?(this.previousActivePath=this.currentPath,this.$nextTick((()=>{const e=document.querySelectorAll(".nav-link, .btn-login, .mobile-nav-link");e.forEach((e=>{(e.classList.contains("active")||"/login"===t&&e.classList.contains("btn-login"))&&!e.classList.contains("active-exit")&&(e.classList.add("active-exit"),setTimeout((()=>{e.classList.remove("active-exit")}),300))})),this.currentPath=t}))):this.currentPath=t,this.closeAllMenus(),this.$route.path===t&&JSON.stringify(this.$route.query)===JSON.stringify(e)||(this.$router.push({path:t,query:e}),window.scrollTo({top:0,behavior:"instant"}))},measureNavHeight(){if(this.$refs.mainNav){const t=this.$refs.mainNav.getBoundingClientRect();t.height>0&&(this.navHeight=t.height)}},handleTokenExpired(){this.isLoggedIn=!1,(0,o.gy)(),Object.keys(r.Z.get()).forEach((t=>{r.Z.remove(t)})),this.notificationMessage="由于长时间未操作，登录状态已失效，请重新登录",this.showComingSoon=!0,setTimeout((()=>{this.$router.push("/login")}),2e3)},handleVisibilityChange(){"visible"===document.visibilityState&&this.isLoggedIn&&this.getUserInfo()},getUserInfo(){(0,a.fB)("/logout/cilent/getInfo").then((t=>{if(200===t.data.code){if(this.userName=t.data.data.nickName||"NCloud-user",r.Z.set("userName",t.data.data.nickName),this.userPhone=t.data.data.username,r.Z.set("userPhone",t.data.data.username),this.userEmail=t.data.data.email||"<EMAIL>",r.Z.set("userEmail",t.data.data.email),this.tenantId=t.data.data.tenantId||"te-default",r.Z.set("tenantId",t.data.data.tenantId),this.userId=t.data.data.id||"ac-default",r.Z.set("userId",t.data.data.id),this.userBalance=t.data.data.balance,r.Z.set("userBalance",t.data.data.balance),this.isReal=t.data.data.isReal,r.Z.set("isReal",this.isReal),this.isLoggedIn=!0,r.Z.get("publicKey-C"))return;(0,a.Zx)("/suanleme/login",{correlationId:r.Z.get("userId"),rsaPubk:r.Z.get("publicKey-B")}).then((t=>{r.Z.set("publicKey-C",t.data.data.rsa_pubk),r.Z.set("suanlemeToken",t.data.data.token)}))}else this.handleTokenExpired()}))},toggleMobileMenu(){this.mobileMenuOpen=!this.mobileMenuOpen,this.mobileMenuOpen&&(this.showUserMenu=!1)},toggleUserMenu(){this.showUserMenu=!this.showUserMenu,this.showUserMenu&&(this.mobileMenuOpen=!1)},closeAllMenus(){this.mobileMenuOpen=!1,this.showUserMenu=!1},startCookieWatcher(){this.cookieWatcher=setInterval((()=>{const t=this.getCookie("suanlemeToken"),e=this.getCookie("publicKey-C");t&&e&&(clearInterval(this.cookieWatcher),this.cookieWatcher=null,this.isConsoleReady=!0,this.isConsoleLoading=!1)}),1e3)},getCookie(t){const e=document.cookie.match(new RegExp("(^| )"+t+"=([^;]+)"));return e?e[2]:null}},created(){window.addEventListener("scroll",this.handleScroll,{passive:!0}),window.addEventListener("resize",this.checkScreenSize);const t=(0,o.LP)();this.isLoggedIn=!!t,t&&(this.userName=r.Z.get("userName")||"null",this.userPhone=r.Z.get("userPhone")||"",this.userBalance=parseFloat(r.Z.get("userBalance"))||0,this.isReal=parseInt(r.Z.get("isReal"))||0,this.getUserInfo()),document.addEventListener("visibilitychange",this.handleVisibilityChange),this.currentPath=this.$route.path,this.checkScreenSize()},mounted(){this.$nextTick((()=>{let t;this.measureNavHeight(),window.addEventListener("resize",(()=>{clearTimeout(t),t=setTimeout((()=>{this.measureNavHeight()}),250)}),{passive:!0}),this.startCookieWatcher()}))},beforeDestroy(){window.removeEventListener("scroll",this.handleScroll),window.removeEventListener("resize",this.measureNavHeight),window.removeEventListener("resize",this.checkScreenSize),document.removeEventListener("visibilitychange",this.handleVisibilityChange),clearTimeout(this.scrollThrottleTimer)}},u=l,d=i(1001),h=(0,d.Z)(u,n,s,!1,null,"954b34cc",null),v=h.exports},7234:function(t,e,i){i.d(e,{Z:function(){return l}});var n=function(){var t=this,e=t._self._c;return e("transition",{attrs:{name:"slide"}},[t.visible?e("div",{class:["notification",`notification-${t.type}`],style:{minHeight:t.minHeight},attrs:{role:"alert"}},[e("div",{staticClass:"notification-content"},["error"===t.type?e("div",{staticClass:"icon-wrapper"},[e("span",{staticClass:"error-icon"},[t._v("×")])]):t._e(),e("span",{staticClass:"message"},[t._v(t._s(t.message))]),t.closable?e("button",{staticClass:"close-btn",on:{click:t.close}},[t._v("×")]):t._e()])]):t._e()])},s=[],a={name:"SlideNotification",props:{message:{type:String,required:!0},type:{type:String,default:"info",validator:t=>["success","warning","error","info"].includes(t)},duration:{type:Number,default:4e3},closable:{type:Boolean,default:!0},minHeight:{type:String,default:"auto"}},data(){return{visible:!1,timer:null}},methods:{show(){this.$nextTick((()=>{this.visible=!0,this.startTimer()}))},close(){this.visible=!1,this.$emit("close")},startTimer(){this.duration>0&&(clearTimeout(this.timer),this.timer=setTimeout((()=>{this.close()}),this.duration))}},mounted(){setTimeout((()=>{this.show()}),100)},beforeUnmount(){clearTimeout(this.timer)}},o=a,c=i(1001),r=(0,c.Z)(o,n,s,!1,null,"80a289c0",null),l=r.exports},2223:function(t,e,i){i.d(e,{P2:function(){return d},SV:function(){return r},Zx:function(){return c},fB:function(){return o},km:function(){return l},xf:function(){return u}});var n=i(8433),s=i(1955);let a="/api";const o=(t,e)=>(0,n.Z)({headers:{authorization:s.Z.get("Admin-Token"),"Content-Type":"application/x-www-form-urlencoded"},method:"post",url:`${a}${t}`,data:e}),c=(t,e)=>(0,n.Z)({headers:{authorization:s.Z.get("Admin-Token"),"Content-Type":"application/json;charset=UTF-8"},method:"post",url:`${a}${t}`,data:e}),r=(t,e)=>(0,n.Z)({headers:{"Content-Type":"application/x-www-form-urlencoded"},method:"post",url:`${a}${t}`,data:e}),l=(t,e)=>(0,n.Z)({headers:{"Content-Type":"application/json;charset=UTF-8"},method:"post",url:`${a}${t}`,data:e}),u=(t,e)=>(0,n.Z)({headers:{"Content-Type":"application/json;charset=UTF-8"},method:"get",url:`${a}${t}`,params:e}),d=(t,e)=>(0,n.Z)({headers:{authorization:s.Z.get("Admin-Token"),"Content-Type":"application/x-www-form-urlencoded"},method:"get",url:`${a}${t}`,params:e})},3969:function(t,e,i){var n=i(144),s=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:"app"}},[t.showLayout?e("Layout",{key:t.headerKey}):t._e(),e("router-view",{key:t.$route.fullPath,on:{"refresh-header":t.refreshHeader,"hiden-layout":t.hidenLayout}})],1)},a=[],o=i(4863),c={components:{Layout:o.Z},data(){return{headerKey:0,showLayout:!0}},methods:{hidenLayout(){this.showLayout=!this.showLayout},refreshHeader(){console.log("刷新了"),this.headerKey+=1}}},r=c,l=i(1001),u=(0,l.Z)(r,s,a,!1,null,null,null),d=u.exports,h=i(8345);n.ZP.use(h.ZP);const v=[{path:"/",redirect:"/index"},{path:"/index",name:"index",component:()=>Promise.all([i.e(891),i.e(964)]).then(i.bind(i,4670))},{path:"/product",name:"product",component:()=>i.e(842).then(i.bind(i,2842))},{path:"/example",name:"example",component:()=>Promise.all([i.e(891),i.e(112)]).then(i.bind(i,442))},{path:"/algorithmcommunity",name:"algorithmcommunity",component:()=>i.e(343).then(i.bind(i,343))},{path:"/news",name:"news",component:()=>Promise.all([i.e(891),i.e(626)]).then(i.bind(i,8218))},{path:"/news/newsId/:newsId",name:"newsDetails",component:()=>Promise.all([i.e(891),i.e(258)]).then(i.bind(i,7013))},{path:"/login",name:"login",component:()=>i.e(358).then(i.bind(i,358))},{path:"/register",name:"register",component:()=>i.e(567).then(i.bind(i,2567))},{path:"/forgetpass",name:"forgetpass",component:()=>i.e(366).then(i.bind(i,4366))},{path:"/about",name:"about",component:()=>i.e(635).then(i.bind(i,635))},{path:"/help",redirect:"/help/summary"},{path:"/help/:doc?",name:"help",component:()=>i.e(24).then(i.bind(i,4024)),props:!0},{path:"/order",name:"order",component:()=>i.e(648).then(i.bind(i,5648))},{path:"/personal",name:"personal",component:()=>i.e(433).then(i.bind(i,6433))},{path:"/userorder",name:"userorder",component:()=>i.e(796).then(i.bind(i,7796))},{path:"/console",name:"userorder",component:()=>i.e(860).then(i.bind(i,9860))}],m=new h.ZP({routes:v});var g=m,f=i(1151),p=i(8433);let b="http://localhost:8087";const C=(t,e)=>(0,p.Z)({method:"get",url:`${b}${t}`,params:e});n.ZP.prototype.getRequest=C,n.ZP.use(f.ZP,{position:"top-center",timeout:3e3,closeOnClick:!0}),n.ZP.config.productionTip=!1,new n.ZP({router:g,render:t=>t(d)}).$mount("#app")},1836:function(t,e,i){i.d(e,{LP:function(){return a},gy:function(){return c},o4:function(){return o}});var n=i(1955);const s="Admin-Token";function a(){return n.Z.get(s)}function o(t){return n.Z.set(s,t)}function c(){return n.Z.remove(s)}}},e={};function i(n){var s=e[n];if(void 0!==s)return s.exports;var a=e[n]={exports:{}};return t[n](a,a.exports,i),a.exports}i.m=t,function(){var t=[];i.O=function(e,n,s,a){if(!n){var o=1/0;for(u=0;u<t.length;u++){n=t[u][0],s=t[u][1],a=t[u][2];for(var c=!0,r=0;r<n.length;r++)(!1&a||o>=a)&&Object.keys(i.O).every((function(t){return i.O[t](n[r])}))?n.splice(r--,1):(c=!1,a<o&&(o=a));if(c){t.splice(u--,1);var l=s();void 0!==l&&(e=l)}}return e}a=a||0;for(var u=t.length;u>0&&t[u-1][2]>a;u--)t[u]=t[u-1];t[u]=[n,s,a]}}(),function(){i.d=function(t,e){for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}}(),function(){i.f={},i.e=function(t){return Promise.all(Object.keys(i.f).reduce((function(e,n){return i.f[n](t,e),e}),[]))}}(),function(){i.u=function(t){return"js/"+({61:"docs3",91:"docs18",166:"docs15",168:"docs1",198:"docs5",202:"docs4",231:"docs16",261:"docs12",370:"docs14",371:"docs19",447:"docs10",469:"docs7",543:"docs20",576:"docs17",594:"docs0",675:"docs9",719:"docs6",802:"docs13",838:"docs11",912:"docs2",956:"docs8",959:"docs22",991:"docs21"}[t]||t)+"."+{24:"1bbfac43",61:"c03537b9",91:"d4b0ab50",112:"843bd72d",166:"b82ea8e0",168:"3c7a8715",198:"c90626d0",202:"1e1da226",231:"74a57f91",258:"937ab62c",261:"0bdcf406",343:"cb406483",358:"ee71c5f5",366:"fbd768a5",370:"7b3f04a8",371:"e5d04027",433:"d707433b",447:"66ac2e7b",469:"77018b48",543:"2d06ed9d",567:"62bf4a79",576:"165ee227",594:"1952cb4e",626:"ba709133",635:"0b14b624",648:"92205354",675:"df2c4998",719:"334f5d4c",796:"5ccc8b27",802:"6bced413",838:"067befee",842:"943c9bb9",860:"00a3a032",891:"148dc965",912:"73e90591",956:"0aa1c993",959:"19c890fd",964:"dd19bcfb",991:"8b4c7882"}[t]+".js"}}(),function(){i.miniCssF=function(t){return"css/"+t+"."+{24:"8aa13f7d",112:"4bd0ef8e",258:"4bd0ef8e",343:"ba4e9447",358:"d9cd4fac",366:"64e7bc8b",433:"3643d4b1",567:"f8185547",626:"4bd0ef8e",635:"85eede0c",648:"19908f09",796:"1de55221",842:"7ab80c07",860:"66d5df10",964:"3e8bbf26"}[t]+".css"}}(),function(){i.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"===typeof window)return window}}()}(),function(){i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),function(){var t={},e="portal-ui:";i.l=function(n,s,a,o){if(t[n])t[n].push(s);else{var c,r;if(void 0!==a)for(var l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var d=l[u];if(d.getAttribute("src")==n||d.getAttribute("data-webpack")==e+a){c=d;break}}c||(r=!0,c=document.createElement("script"),c.charset="utf-8",c.timeout=120,i.nc&&c.setAttribute("nonce",i.nc),c.setAttribute("data-webpack",e+a),c.src=n),t[n]=[s];var h=function(e,i){c.onerror=c.onload=null,clearTimeout(v);var s=t[n];if(delete t[n],c.parentNode&&c.parentNode.removeChild(c),s&&s.forEach((function(t){return t(i)})),e)return e(i)},v=setTimeout(h.bind(null,void 0,{type:"timeout",target:c}),12e4);c.onerror=h.bind(null,c.onerror),c.onload=h.bind(null,c.onload),r&&document.head.appendChild(c)}}}(),function(){i.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}}(),function(){i.p="/portal/"}(),function(){if("undefined"!==typeof document){var t=function(t,e,i,n,s){var a=document.createElement("link");a.rel="stylesheet",a.type="text/css";var o=function(i){if(a.onerror=a.onload=null,"load"===i.type)n();else{var o=i&&("load"===i.type?"missing":i.type),c=i&&i.target&&i.target.href||e,r=new Error("Loading CSS chunk "+t+" failed.\n("+c+")");r.code="CSS_CHUNK_LOAD_FAILED",r.type=o,r.request=c,a.parentNode&&a.parentNode.removeChild(a),s(r)}};return a.onerror=a.onload=o,a.href=e,i?i.parentNode.insertBefore(a,i.nextSibling):document.head.appendChild(a),a},e=function(t,e){for(var i=document.getElementsByTagName("link"),n=0;n<i.length;n++){var s=i[n],a=s.getAttribute("data-href")||s.getAttribute("href");if("stylesheet"===s.rel&&(a===t||a===e))return s}var o=document.getElementsByTagName("style");for(n=0;n<o.length;n++){s=o[n],a=s.getAttribute("data-href");if(a===t||a===e)return s}},n=function(n){return new Promise((function(s,a){var o=i.miniCssF(n),c=i.p+o;if(e(o,c))return s();t(n,c,null,s,a)}))},s={826:0};i.f.miniCss=function(t,e){var i={24:1,112:1,258:1,343:1,358:1,366:1,433:1,567:1,626:1,635:1,648:1,796:1,842:1,860:1,964:1};s[t]?e.push(s[t]):0!==s[t]&&i[t]&&e.push(s[t]=n(t).then((function(){s[t]=0}),(function(e){throw delete s[t],e})))}}}(),function(){i.b=document.baseURI||self.location.href;var t={826:0};i.f.j=function(e,n){var s=i.o(t,e)?t[e]:void 0;if(0!==s)if(s)n.push(s[2]);else{var a=new Promise((function(i,n){s=t[e]=[i,n]}));n.push(s[2]=a);var o=i.p+i.u(e),c=new Error,r=function(n){if(i.o(t,e)&&(s=t[e],0!==s&&(t[e]=void 0),s)){var a=n&&("load"===n.type?"missing":n.type),o=n&&n.target&&n.target.src;c.message="Loading chunk "+e+" failed.\n("+a+": "+o+")",c.name="ChunkLoadError",c.type=a,c.request=o,s[1](c)}};i.l(o,r,"chunk-"+e,e)}},i.O.j=function(e){return 0===t[e]};var e=function(e,n){var s,a,o=n[0],c=n[1],r=n[2],l=0;if(o.some((function(e){return 0!==t[e]}))){for(s in c)i.o(c,s)&&(i.m[s]=c[s]);if(r)var u=r(i)}for(e&&e(n);l<o.length;l++)a=o[l],i.o(t,a)&&t[a]&&t[a][0](),t[a]=0;return i.O(u)},n=self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[];n.forEach(e.bind(null,0)),n.push=e.bind(null,n.push.bind(n))}();var n=i.O(void 0,[998],(function(){return i(3969)}));n=i.O(n)})();
//# sourceMappingURL=index.d16b6763.js.map