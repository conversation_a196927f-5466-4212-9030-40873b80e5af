{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    attrs: {\n      \"id\": \"app\"\n    }\n  }, [_vm.showLayout ? _c('Layout', {\n    key: _vm.headerKey\n  }) : _vm._e(), _c('router-view', {\n    key: _vm.$route.fullPath,\n    on: {\n      \"refresh-header\": _vm.refreshHeader,\n      \"hiden-layout\": _vm.hidenLayout\n    }\n  })], 1);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "showLayout", "key", "<PERSON><PERSON><PERSON>", "_e", "$route", "fullPath", "on", "refreshHeader", "hidenLayout", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/App.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[(_vm.showLayout)?_c('Layout',{key:_vm.headerKey}):_vm._e(),_c('router-view',{key:_vm.$route.fullPath,on:{\"refresh-header\":_vm.refreshHeader,\"hiden-layout\":_vm.hidenLayout}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACE,KAAK,EAAC;MAAC,IAAI,EAAC;IAAK;EAAC,CAAC,EAAC,CAAEH,GAAG,CAACI,UAAU,GAAEH,EAAE,CAAC,QAAQ,EAAC;IAACI,GAAG,EAACL,GAAG,CAACM;EAAS,CAAC,CAAC,GAACN,GAAG,CAACO,EAAE,EAAE,EAACN,EAAE,CAAC,aAAa,EAAC;IAACI,GAAG,EAACL,GAAG,CAACQ,MAAM,CAACC,QAAQ;IAACC,EAAE,EAAC;MAAC,gBAAgB,EAACV,GAAG,CAACW,aAAa;MAAC,cAAc,EAACX,GAAG,CAACY;IAAW;EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;AACnR,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AAExB,SAASd,MAAM,EAAEc,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}