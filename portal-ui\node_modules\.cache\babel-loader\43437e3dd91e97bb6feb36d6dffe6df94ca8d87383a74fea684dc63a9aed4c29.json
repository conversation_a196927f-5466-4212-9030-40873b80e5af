{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('Layout', [_c('div', {\n    staticClass: \"layout-container\",\n    staticStyle: {\n      \"width\": \"100%\"\n    }\n  }, [_c('div', {\n    staticClass: \"page-header\"\n  }, [_c('div', {\n    staticClass: \"am-container\"\n  }, [_c('h1', {\n    staticClass: \"page-header-title\"\n  }, [_vm._v(\"公司动态\")])])]), _c('div', {\n    staticClass: \"breadcrumb-box\"\n  }, [_c('div', {\n    staticClass: \"am-container\"\n  }, [_c('ol', {\n    staticClass: \"am-breadcrumb\"\n  }, [_c('li', [_c('router-link', {\n    attrs: {\n      \"to\": \"/\"\n    }\n  }, [_vm._v(\"首页\")])], 1), _c('li', {\n    staticClass: \"am-active\"\n  }, [_vm._v(\"公司动态\")])])])])]), _c('div', {\n    staticClass: \"section\"\n  }, [_c('div', {\n    staticClass: \"container\",\n    staticStyle: {\n      \"max-width\": \"1160px\"\n    }\n  }, [_c('div', {\n    staticClass: \"section--header\"\n  }, [_c('h2', {\n    staticClass: \"section--title\"\n  }, [_vm._v(\"最近新闻\")]), _c('p', {\n    staticClass: \"section--description\"\n  }, [_vm._v(\" 云适配与中建材信息技术股份有限公司（以下简称“中建信息”）联合举办的“战略 \"), _c('br'), _vm._v(\"合作签约仪式暨全国跨屏行动启动大会”在北京成功举办。 \")])]), _c('div', {\n    staticClass: \"news-contaier\"\n  }, [_c('div', {\n    staticClass: \"blog\"\n  }, [_c('div', {\n    staticClass: \"am-g\"\n  }, _vm._l(_vm.articles.records, function (article, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"am-u-lg-4 am-u-md-6 am-u-end\"\n    }, [_c('div', {\n      staticClass: \"article\"\n    }, [_c('div', {\n      staticClass: \"article-img\"\n    }, [_c('img', {\n      attrs: {\n        \"src\": article.cover,\n        \"alt\": \"\"\n      }\n    })]), _c('div', {\n      staticClass: \"article-header\"\n    }, [_c('h2', [_c('router-link', {\n      attrs: {\n        \"to\": {\n          name: 'newsDetails',\n          params: {\n            newsId: article.articleId\n          }\n        },\n        \"rel\": \"\"\n      }\n    }, [_vm._v(_vm._s(article.title))])], 1), _c('ul', {\n      staticClass: \"article--meta\"\n    }, [_c('li', {\n      staticClass: \"article--meta_item -date\"\n    }, [_vm._v(_vm._s(article.createTime))])])]), _c('div', {\n      staticClass: \"article--content\"\n    }, [_c('p', [_vm._v(_vm._s(article.introduction))])]), _c('div', {\n      staticClass: \"article--footer\"\n    }, [_c('router-link', {\n      staticClass: \"link\",\n      attrs: {\n        \"to\": {\n          name: 'newsDetails',\n          params: {\n            newsId: article.articleId\n          }\n        }\n      }\n    }, [_vm._v(\"查看更多\")])], 1)])]);\n  }), 0), _c('ul', {\n    staticClass: \"am-pagination\",\n    staticStyle: {\n      \"text-align\": \"center\"\n    }\n  }, [_c('li', {\n    class: _vm.pageIndex === 1 ? 'am-disabled' : '',\n    on: {\n      \"click\": function ($event) {\n        return _vm.changeIndex(_vm.pageIndex - 1);\n      }\n    }\n  }, [_c('a', {\n    attrs: {\n      \"href\": \"#\"\n    }\n  }, [_vm._v(\"«\")])]), _vm._l(_vm.articles.pages, function (p, index) {\n    return _c('li', {\n      key: index,\n      class: _vm.pageIndex === p ? 'am-active' : '',\n      on: {\n        \"click\": function ($event) {\n          return _vm.changeIndex(p);\n        }\n      }\n    }, [_c('a', {\n      attrs: {\n        \"href\": \"#\"\n      }\n    }, [_vm._v(_vm._s(p))])]);\n  }), _c('li', {\n    class: _vm.pageIndex === _vm.articles.pages ? 'am-disabled' : '',\n    on: {\n      \"click\": function ($event) {\n        return _vm.changeIndex(_vm.pageIndex + 1);\n      }\n    }\n  }, [_c('a', {\n    attrs: {\n      \"href\": \"#\"\n    }\n  }, [_vm._v(\"»\")])])], 2)])])])])]);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "_v", "attrs", "_l", "articles", "records", "article", "index", "key", "cover", "name", "params", "newsId", "articleId", "_s", "title", "createTime", "introduction", "class", "pageIndex", "on", "click", "$event", "changeIndex", "pages", "p", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/views/NewsView.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('Layout',[_c('div',{staticClass:\"layout-container\",staticStyle:{\"width\":\"100%\"}},[_c('div',{staticClass:\"page-header\"},[_c('div',{staticClass:\"am-container\"},[_c('h1',{staticClass:\"page-header-title\"},[_vm._v(\"公司动态\")])])]),_c('div',{staticClass:\"breadcrumb-box\"},[_c('div',{staticClass:\"am-container\"},[_c('ol',{staticClass:\"am-breadcrumb\"},[_c('li',[_c('router-link',{attrs:{\"to\":\"/\"}},[_vm._v(\"首页\")])],1),_c('li',{staticClass:\"am-active\"},[_vm._v(\"公司动态\")])])])])]),_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"container\",staticStyle:{\"max-width\":\"1160px\"}},[_c('div',{staticClass:\"section--header\"},[_c('h2',{staticClass:\"section--title\"},[_vm._v(\"最近新闻\")]),_c('p',{staticClass:\"section--description\"},[_vm._v(\" 云适配与中建材信息技术股份有限公司（以下简称“中建信息”）联合举办的“战略 \"),_c('br'),_vm._v(\"合作签约仪式暨全国跨屏行动启动大会”在北京成功举办。 \")])]),_c('div',{staticClass:\"news-contaier\"},[_c('div',{staticClass:\"blog\"},[_c('div',{staticClass:\"am-g\"},_vm._l((_vm.articles.records),function(article,index){return _c('div',{key:index,staticClass:\"am-u-lg-4 am-u-md-6 am-u-end\"},[_c('div',{staticClass:\"article\"},[_c('div',{staticClass:\"article-img\"},[_c('img',{attrs:{\"src\":article.cover,\"alt\":\"\"}})]),_c('div',{staticClass:\"article-header\"},[_c('h2',[_c('router-link',{attrs:{\"to\":{name:'newsDetails',params:{newsId:article.articleId}},\"rel\":\"\"}},[_vm._v(_vm._s(article.title))])],1),_c('ul',{staticClass:\"article--meta\"},[_c('li',{staticClass:\"article--meta_item -date\"},[_vm._v(_vm._s(article.createTime))])])]),_c('div',{staticClass:\"article--content\"},[_c('p',[_vm._v(_vm._s(article.introduction))])]),_c('div',{staticClass:\"article--footer\"},[_c('router-link',{staticClass:\"link\",attrs:{\"to\":{name:'newsDetails',params:{newsId:article.articleId}}}},[_vm._v(\"查看更多\")])],1)])])}),0),_c('ul',{staticClass:\"am-pagination\",staticStyle:{\"text-align\":\"center\"}},[_c('li',{class:_vm.pageIndex === 1 ? 'am-disabled':'',on:{\"click\":function($event){return _vm.changeIndex(_vm.pageIndex - 1)}}},[_c('a',{attrs:{\"href\":\"#\"}},[_vm._v(\"«\")])]),_vm._l((_vm.articles.pages),function(p,index){return _c('li',{key:index,class:_vm.pageIndex === p ? 'am-active':'',on:{\"click\":function($event){return _vm.changeIndex(p)}}},[_c('a',{attrs:{\"href\":\"#\"}},[_vm._v(_vm._s(p))])])}),_c('li',{class:_vm.pageIndex === _vm.articles.pages ? 'am-disabled':'',on:{\"click\":function($event){return _vm.changeIndex(_vm.pageIndex + 1)}}},[_c('a',{attrs:{\"href\":\"#\"}},[_vm._v(\"»\")])])],2)])])])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,QAAQ,EAAC,CAACA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,kBAAkB;IAACC,WAAW,EAAC;MAAC,OAAO,EAAC;IAAM;EAAC,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAmB,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,aAAa,EAAC;IAACK,KAAK,EAAC;MAAC,IAAI,EAAC;IAAG;EAAC,CAAC,EAAC,CAACN,GAAG,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAS,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,WAAW;IAACC,WAAW,EAAC;MAAC,WAAW,EAAC;IAAQ;EAAC,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAsB,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,yCAAyC,CAAC,EAACJ,EAAE,CAAC,IAAI,CAAC,EAACD,GAAG,CAACK,EAAE,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAM,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAM,CAAC,EAACH,GAAG,CAACO,EAAE,CAAEP,GAAG,CAACQ,QAAQ,CAACC,OAAO,EAAE,UAASC,OAAO,EAACC,KAAK,EAAC;IAAC,OAAOV,EAAE,CAAC,KAAK,EAAC;MAACW,GAAG,EAACD,KAAK;MAACR,WAAW,EAAC;IAA8B,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAS,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAa,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;MAACK,KAAK,EAAC;QAAC,KAAK,EAACI,OAAO,CAACG,KAAK;QAAC,KAAK,EAAC;MAAE;IAAC,CAAC,CAAC,CAAC,CAAC,EAACZ,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAgB,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,aAAa,EAAC;MAACK,KAAK,EAAC;QAAC,IAAI,EAAC;UAACQ,IAAI,EAAC,aAAa;UAACC,MAAM,EAAC;YAACC,MAAM,EAACN,OAAO,CAACO;UAAS;QAAC,CAAC;QAAC,KAAK,EAAC;MAAE;IAAC,CAAC,EAAC,CAACjB,GAAG,CAACK,EAAE,CAACL,GAAG,CAACkB,EAAE,CAACR,OAAO,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAClB,EAAE,CAAC,IAAI,EAAC;MAACE,WAAW,EAAC;IAAe,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;MAACE,WAAW,EAAC;IAA0B,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAACL,GAAG,CAACkB,EAAE,CAACR,OAAO,CAACU,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAkB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC,CAACD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACkB,EAAE,CAACR,OAAO,CAACW,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACpB,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,aAAa,EAAC;MAACE,WAAW,EAAC,MAAM;MAACG,KAAK,EAAC;QAAC,IAAI,EAAC;UAACQ,IAAI,EAAC,aAAa;UAACC,MAAM,EAAC;YAACC,MAAM,EAACN,OAAO,CAACO;UAAS;QAAC;MAAC;IAAC,CAAC,EAAC,CAACjB,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC,eAAe;IAACC,WAAW,EAAC;MAAC,YAAY,EAAC;IAAQ;EAAC,CAAC,EAAC,CAACH,EAAE,CAAC,IAAI,EAAC;IAACqB,KAAK,EAACtB,GAAG,CAACuB,SAAS,KAAK,CAAC,GAAG,aAAa,GAAC,EAAE;IAACC,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;QAAC,OAAO1B,GAAG,CAAC2B,WAAW,CAAC3B,GAAG,CAACuB,SAAS,GAAG,CAAC,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACtB,EAAE,CAAC,GAAG,EAAC;IAACK,KAAK,EAAC;MAAC,MAAM,EAAC;IAAG;EAAC,CAAC,EAAC,CAACN,GAAG,CAACK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAACL,GAAG,CAACO,EAAE,CAAEP,GAAG,CAACQ,QAAQ,CAACoB,KAAK,EAAE,UAASC,CAAC,EAAClB,KAAK,EAAC;IAAC,OAAOV,EAAE,CAAC,IAAI,EAAC;MAACW,GAAG,EAACD,KAAK;MAACW,KAAK,EAACtB,GAAG,CAACuB,SAAS,KAAKM,CAAC,GAAG,WAAW,GAAC,EAAE;MAACL,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;UAAC,OAAO1B,GAAG,CAAC2B,WAAW,CAACE,CAAC,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAAC5B,EAAE,CAAC,GAAG,EAAC;MAACK,KAAK,EAAC;QAAC,MAAM,EAAC;MAAG;IAAC,CAAC,EAAC,CAACN,GAAG,CAACK,EAAE,CAACL,GAAG,CAACkB,EAAE,CAACW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC5B,EAAE,CAAC,IAAI,EAAC;IAACqB,KAAK,EAACtB,GAAG,CAACuB,SAAS,KAAKvB,GAAG,CAACQ,QAAQ,CAACoB,KAAK,GAAG,aAAa,GAAC,EAAE;IAACJ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;QAAC,OAAO1B,GAAG,CAAC2B,WAAW,CAAC3B,GAAG,CAACuB,SAAS,GAAG,CAAC,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACtB,EAAE,CAAC,GAAG,EAAC;IAACK,KAAK,EAAC;MAAC,MAAM,EAAC;IAAG;EAAC,CAAC,EAAC,CAACN,GAAG,CAACK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACr8E,CAAC;AACD,IAAIyB,eAAe,GAAG,EAAE;AAExB,SAAS/B,MAAM,EAAE+B,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}