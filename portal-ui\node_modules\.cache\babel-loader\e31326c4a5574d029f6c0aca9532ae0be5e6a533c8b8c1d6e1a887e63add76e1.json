{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport Layout from \"@/components/common/Layout\";\nexport default {\n  name: \"ExampleView\",\n  components: {\n    Layout\n  },\n  data() {\n    return {\n      tabIndex: 0,\n      tabList: [{\n        id: 1,\n        name: '主要案例',\n        icon: 'am-icon-map-o'\n      }, {\n        id: 1,\n        name: '客户案例一',\n        icon: 'am-icon-scribd'\n      }, {\n        id: 1,\n        name: '客户案例二',\n        icon: 'am-icon-odnoklassniki'\n      }, {\n        id: 1,\n        name: '客户案例三',\n        icon: 'am-icon-building-o'\n      }, {\n        id: 1,\n        name: '客户案例四',\n        icon: 'am-icon-hand-scissors-o'\n      }, {\n        id: 1,\n        name: '客户案例五',\n        icon: 'am-icon-camera'\n      }],\n      list: []\n    };\n  },\n  mounted() {\n    this.findExampleByExampleType(0);\n  },\n  methods: {\n    changeTab(index) {\n      this.tabIndex = index;\n      //console.log(index)\n      this.findExampleByExampleType(index);\n    },\n    findExampleByExampleType(exampleType) {\n      this.getRequest(`/findExampleByExampleType/${exampleType}`).then(resp => {\n        if (resp) {\n          const temp = resp.data.data;\n          //console.log(temp)\n          const list = [];\n          for (let i = 1; i < 17; i++) {\n            // 判断是否为空\n            if (temp['image' + i] != null) {\n              list.push(temp['image' + i]);\n            }\n          }\n          this.list = list;\n          //console.log(this.list)\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "name", "components", "data", "tabIndex", "tabList", "id", "icon", "list", "mounted", "findExampleByExampleType", "methods", "changeTab", "index", "exampleType", "getRequest", "then", "resp", "temp", "i", "push"], "sources": ["src/views/ExampleView.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t\t<div class=\"layout-container\" style=\"width: 100%\">\r\n\t\t\t<div class=\"page-header\">\r\n\t\t\t\t<div class=\"am-container\">\r\n\t\t\t\t\t<h1 class=\"page-header-title\">客户案例</h1>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"breadcrumb-box\">\r\n\t\t\t\t<div class=\"am-container\">\r\n\t\t\t\t\t<ol class=\"am-breadcrumb\">\r\n\t\t\t\t\t\t<li><router-link to=\"/\">首页</router-link></li>\r\n\t\t\t\t\t\t<li class=\"am-active\">客户案例</li>\r\n\t\t\t\t\t</ol>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<div class=\"section example\">\r\n\t\t\t<div class=\"container\" style=\"max-width: 1160px; margin: 0 auto\">\r\n\t\t\t\t<div class=\"section--header\">\r\n\t\t\t\t\t<h2 class=\"section--title\">全球首创 自主创新</h2>\r\n\t\t\t\t\t<p class=\"section--description\">\r\n\t\t\t\t\t\tEnterplorer Studio是一套面向企业级移动信息化建设的开发平台。集聚开发、测试、\r\n\t\t\t\t\t\t<br>打包、发布于一体的移动化开发综合平台。\r\n\t\t\t\t\t</p>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div class=\"example-container\">\r\n\t\t\t\t\t<div class=\"am-tabs\">\r\n\t\t\t\t\t\t<ul class=\"am-tabs-nav am-nav am-nav-tabs am-g\">\r\n\t\t\t\t\t\t\t<li class=\"am-u-md-2\"\r\n\t\t\t\t\t\t\t\tv-for=\"(tab,index) in tabList\"\r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t\***************=\"changeTab(index)\"\r\n\t\t\t\t\t\t\t\t:class=\"tabIndex === index ? 'am-active':''\">\r\n\t\t\t\t\t\t\t\t<a href=\"#\"><i :class=\"tab.icon\"></i>{{tab.name}}</a>\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t</ul>\r\n\r\n\t\t\t\t\t\t<div class=\"tabs\">\r\n\t\t\t\t\t\t\t<div class=\"tab\" v-for=\"(image,index) in list\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<img :src=\"image\" alt=\"\">\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nexport default {\r\n\tname: \"ExampleView\",\r\n\tcomponents: {Layout},\r\n\tdata(){\r\n\t\treturn{\r\n\t\t\ttabIndex: 0,\r\n\t\t\ttabList:[\r\n\t\t\t\t{id:1,name:'主要案例',icon:'am-icon-map-o'},\r\n\t\t\t\t{id:1,name:'客户案例一',icon:'am-icon-scribd'},\r\n\t\t\t\t{id:1,name:'客户案例二',icon:'am-icon-odnoklassniki'},\r\n\t\t\t\t{id:1,name:'客户案例三',icon:'am-icon-building-o'},\r\n\t\t\t\t{id:1,name:'客户案例四',icon:'am-icon-hand-scissors-o'},\r\n\t\t\t\t{id:1,name:'客户案例五',icon:'am-icon-camera'},\r\n\t\t\t],\r\n\t\t\tlist:[],\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.findExampleByExampleType(0)\r\n\t},\r\n\tmethods:{\r\n\t\tchangeTab(index){\r\n\t\t\tthis.tabIndex = index\r\n\t\t\t//console.log(index)\r\n\t\t\tthis.findExampleByExampleType(index)\r\n\t\t},\r\n\t\tfindExampleByExampleType(exampleType){\r\n\t\t\tthis.getRequest(`/findExampleByExampleType/${exampleType}`).then(resp =>{\r\n\t\t\t\tif (resp){\r\n\t\t\t\t\tconst temp = resp.data.data\r\n\t\t\t\t\t//console.log(temp)\r\n\t\t\t\t\tconst list = [];\r\n\t\t\t\t\tfor (let i = 1; i < 17; i++) {\r\n\t\t\t\t\t\t// 判断是否为空\r\n\t\t\t\t\t\tif (temp['image'+ i] != null){\r\n\t\t\t\t\t\t\tlist.push(temp['image'+ i])\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.list = list\r\n\t\t\t\t\t//console.log(this.list)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";AAqDA,OAAAA,MAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;EACAG,KAAA;IACA;MACAC,QAAA;MACAC,OAAA,GACA;QAAAC,EAAA;QAAAL,IAAA;QAAAM,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAL,IAAA;QAAAM,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAL,IAAA;QAAAM,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAL,IAAA;QAAAM,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAL,IAAA;QAAAM,IAAA;MAAA,GACA;QAAAD,EAAA;QAAAL,IAAA;QAAAM,IAAA;MAAA,EACA;MACAC,IAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,wBAAA;EACA;EACAC,OAAA;IACAC,UAAAC,KAAA;MACA,KAAAT,QAAA,GAAAS,KAAA;MACA;MACA,KAAAH,wBAAA,CAAAG,KAAA;IACA;IACAH,yBAAAI,WAAA;MACA,KAAAC,UAAA,8BAAAD,WAAA,IAAAE,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA;UACA,MAAAC,IAAA,GAAAD,IAAA,CAAAd,IAAA,CAAAA,IAAA;UACA;UACA,MAAAK,IAAA;UACA,SAAAW,CAAA,MAAAA,CAAA,OAAAA,CAAA;YACA;YACA,IAAAD,IAAA,WAAAC,CAAA;cACAX,IAAA,CAAAY,IAAA,CAAAF,IAAA,WAAAC,CAAA;YACA;UACA;UACA,KAAAX,IAAA,GAAAA,IAAA;UACA;QACA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}