"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[796],{7796:function(t,e,a){a.r(e),a.d(e,{default:function(){return S}});var s=function(){var t=this,e=t._self._c;return e("div",[t.showNotification?e("SlideNotification",{attrs:{message:t.notificationMessage,type:t.notificationType,duration:3e3,minHeight:t.minHeight},on:{close:function(e){t.showNotification=!1}}}):t._e(),e("div",{staticStyle:{width:"100%"}},[e("div",{staticClass:"fee-center-container"},[e("div",{staticClass:"navigation-sidebar"},[e("h2",{staticClass:"nav-title"},[t._v("费用中心")]),e("ul",{staticClass:"nav-list"},[e("li",{staticClass:"nav-item",class:{active:"transactions"===t.currentSection}},[e("a",{attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.changeSection("transactions")}}},[e("i",{staticClass:"el-icon-money"}),e("span",[t._v("收支明细")])])]),e("li",{staticClass:"nav-item",class:{active:"recharge"===t.currentSection}},[e("a",{attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.changeSection("recharge")}}},[e("i",{staticClass:"el-icon-wallet"}),e("span",[t._v("充值")])])])])]),e("div",{staticClass:"main-content"},["orders"===t.currentSection?e("div",[e("div",{directives:[{name:"show",rawName:"v-show",value:!t.showOrderDetails,expression:"!showOrderDetails"}],staticClass:"tab-content"},[e("div",{staticClass:"search-section"},[e("div",{staticClass:"search-bar"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.orderSearchQuery,expression:"orderSearchQuery"}],attrs:{type:"text",placeholder:"搜索订单号"},domProps:{value:t.orderSearchQuery},on:{input:function(e){e.target.composing||(t.orderSearchQuery=e.target.value)}}}),e("button",{staticClass:"search-button",on:{click:t.searchOrders}},[e("i",{staticClass:"el-icon-search"})]),t.orderSearchQuery?e("button",{staticClass:"clear-button",on:{click:t.clearOrderSearch}},[e("i",{staticClass:"el-icon-close"})]):t._e()]),e("div",{staticClass:"currency-display"},[t._v(" 金额单位: ¥ "),e("span",{staticClass:"flow-count"},[t._v("订单总数: "+t._s(t.currentOrderTotal))])])]),e("div",{staticClass:"table-container"},[t.orderLoading?e("div",{staticClass:"loading-state"},[e("i",{staticClass:"el-icon-loading"}),e("span",[t._v("正在加载订单数据...")])]):t._e(),t.orderError?e("div",{staticClass:"error-state"},[e("i",{staticClass:"el-icon-error"}),e("span",[t._v(t._s(t.orderError))]),e("button",{on:{click:t.fetchOrders}},[t._v("重试")])]):t._e(),e("table",{staticClass:"data-table"},[e("thead",[e("tr",[e("th",[t._v("订单号 "),e("i",{staticClass:"el-icon-sort",on:{click:function(e){return t.sortBy("order_number")}}})]),e("th",[t._v("订单创建时间 "),e("i",{staticClass:"el-icon-sort",on:{click:function(e){return t.sortBy("created_at")}}})]),e("th",[t._v("支付状态 "),e("i",{staticClass:"el-icon-sort",on:{click:function(e){return t.sortBy("payment_status")}}})]),e("th",[t._v("单价 "),e("i",{staticClass:"el-icon-sort",on:{click:function(e){return t.sortBy("unit_price")}}})]),e("th",[t._v("时长 "),e("i",{staticClass:"el-icon-sort",on:{click:function(e){return t.sortBy("duration")}}})]),e("th",[t._v("付款方式 "),e("i",{staticClass:"el-icon-sort",on:{click:function(e){return t.sortBy("payment_method")}}})]),e("th",[t._v("合计")]),e("th",[t._v("操作")])])]),e("tbody",[t._l(t.paginatedOrders,(function(a,s){return e("tr",{key:"order-"+s},[e("td",[t._v(t._s(a.order_number))]),e("td",[t._v(t._s(t.formatDateTime(a.created_at)))]),e("td",[e("span",{staticClass:"status-tag",class:t.getPaymentStatusClass(a.payment_status)},[t._v(" "+t._s(t.getPaymentStatusText(a.payment_status))+" ")])]),e("td",[t._v(t._s(t.formatPrice(a.unit_price)))]),e("td",[t._v(t._s(a.duration))]),e("td",[e("span",{staticClass:"payment-method-tag",class:t.getPaymentMethodClass(a.payment_method)},[t._v(" "+t._s(t.getPaymentMethodText(a.payment_method))+" ")])]),e("td",[t._v(t._s(t.formatPrice(a.total_price)))]),e("td",[e("span",{staticClass:"operation-link",on:{click:function(e){return t.viewOrderDetails(a)}}},[t._v("查看详情")])])])})),0!==t.filteredOrderData.length||t.orderLoading?t._e():e("tr",[t._m(0)])],2)])]),e("common-pagination",{attrs:{"current-page":t.currentPage,total:t.filteredOrderData.length,"page-size":t.pageSize},on:{"change-page":t.goToPage,"change-page-size":t.handlePageSizeChange}})],1),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showOrderDetails,expression:"showOrderDetails"}],staticClass:"order-details"},[e("div",{staticClass:"detail-card"},[e("div",{staticClass:"detail-header"},[e("h2",{staticClass:"detail-title"},[t._v("订单详情")]),e("button",{staticClass:"back-button",on:{click:t.showOrderList}},[e("i",{staticClass:"el-icon-back"}),t._v(" 返回列表 ")])]),e("div",{staticClass:"detail-content"},[e("h3",{staticClass:"detail-subtitle"},[t._v("订单概况")]),e("div",{staticClass:"detail-section"},[e("div",{staticClass:"detail-row"},[e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("订单号:")]),e("div",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.order_number))])]),e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("订单状态:")]),e("div",{staticClass:"detail-value"},[e("span",{staticClass:"status-tag",class:t.getPaymentStatusClass(t.selectedOrder.payment_status)},[t._v(" "+t._s(t.getPaymentStatusText(t.selectedOrder.payment_status))+" ")])])])]),e("div",{staticClass:"detail-row"},[e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("支付方式:")]),e("div",{staticClass:"detail-value"},[e("span",{staticClass:"payment-method-tag",class:t.getPaymentMethodClass(t.selectedOrder.payment_method)},[t._v(" "+t._s(t.getPaymentMethodText(t.selectedOrder.payment_method))+" ")])])]),e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("单价:")]),e("div",{staticClass:"detail-value"},[t._v("¥ "+t._s(t.formatPrice(t.selectedOrder.unit_price)))])])]),e("div",{staticClass:"detail-row"},[e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("订单创建时间:")]),e("div",{staticClass:"detail-value"},[t._v(t._s(t.formatDateTime(t.selectedOrder.created_at)))])]),e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("订单金额:")]),e("div",{staticClass:"detail-value"},[t._v("¥ "+t._s(t.formatPrice(t.selectedOrder.total_price)))])])])]),e("h3",{staticClass:"detail-subtitle"},[t._v("GPU信息")]),e("div",{staticClass:"detail-section"},[e("div",{staticClass:"detail-row"},[e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("型号:")]),e("div",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.gpu_model))])]),e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("地区:")]),e("div",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.region))])])]),e("div",{staticClass:"detail-row"},[e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("显卡数量:")]),e("div",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.gpu_count)+" 个")])]),e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("显存:")]),e("div",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.video_memory)+" GB")])])]),e("div",{staticClass:"detail-row"},[e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("VCPU核数:")]),e("div",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.cpu_cores)+" 核")])]),e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("系统盘:")]),e("div",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.system_disk)+" SSD")])])]),e("div",{staticClass:"detail-row"},[e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("云盘:")]),e("div",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.cloud_disk)+" GB")])]),e("div",{staticClass:"detail-item"},[e("div",{staticClass:"detail-label"},[t._v("内存:")]),e("div",{staticClass:"detail-value"},[t._v(t._s(t.selectedOrder.memory)+" GB")])])])])])])])]):t._e(),"transactions"===t.currentSection?e("div",[e("div",{staticClass:"tab-content"},[e("div",{staticClass:"search-section"},[e("div",{staticClass:"date-range-picker"},[e("select",{directives:[{name:"model",rawName:"v-model",value:t.transactionDateRange,expression:"transactionDateRange"}],on:{change:function(e){var a=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.transactionDateRange=e.target.multiple?a:a[0]}}},[e("option",{attrs:{value:"7"}},[t._v("最近7天")]),e("option",{attrs:{value:"30"}},[t._v("最近一个月")]),e("option",{attrs:{value:"90"}},[t._v("最近三个月")]),e("option",{attrs:{value:"custom"}},[t._v("自定义时间段")])]),"custom"===t.transactionDateRange?e("div",{staticClass:"custom-date-range"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.customDateStart,expression:"customDateStart"}],attrs:{type:"date"},domProps:{value:t.customDateStart},on:{input:function(e){e.target.composing||(t.customDateStart=e.target.value)}}}),e("span",[t._v("至")]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.customDateEnd,expression:"customDateEnd"}],attrs:{type:"date"},domProps:{value:t.customDateEnd},on:{input:function(e){e.target.composing||(t.customDateEnd=e.target.value)}}})]):t._e()]),e("div",{staticClass:"search-filters"},[e("select",{directives:[{name:"model",rawName:"v-model",value:t.transactionType,expression:"transactionType"}],on:{change:function(e){var a=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.transactionType=e.target.multiple?a:a[0]}}},[e("option",{attrs:{value:""}},[t._v("全部类型")]),e("option",{attrs:{value:"income"}},[t._v("收入")]),e("option",{attrs:{value:"expense"}},[t._v("支出")])]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.transactionSearchQuery,expression:"transactionSearchQuery"}],attrs:{type:"text",placeholder:"搜索流水号"},domProps:{value:t.transactionSearchQuery},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.searchTransactions.apply(null,arguments)},input:function(e){e.target.composing||(t.transactionSearchQuery=e.target.value)}}}),e("button",{staticClass:"search-button",on:{click:t.searchTransactions}},[e("i",{staticClass:"el-icon-search"})])])]),e("div",{staticClass:"transaction-summary"},[e("div",{staticClass:"summary-card"},[e("div",{staticClass:"summary-title"},[t._v("总充值")]),e("div",{staticClass:"summary-value income"},[t._v("¥ "+t._s(t.formatPrice(t.summaryData.totalRecharge)))])]),e("div",{staticClass:"summary-card"},[e("div",{staticClass:"summary-title"},[t._v("总消费")]),e("div",{staticClass:"summary-value expense"},[t._v("¥ "+t._s(t.formatPrice(t.summaryData.totalExpense)))])]),e("div",{staticClass:"summary-card"},[e("div",{staticClass:"summary-title"},[t._v("账户余额")]),e("div",{staticClass:"summary-value"},[t._v("¥ "+t._s(t.formatPrice(t.userBalance)))])])]),e("div",{staticClass:"table-container"},[t.transactionLoading?e("div",{staticClass:"loading-state"},[e("i",{staticClass:"el-icon-loading"}),e("span",[t._v("正在加载交易数据...")])]):t._e(),t.transactionError?e("div",{staticClass:"error-state"},[e("i",{staticClass:"el-icon-error"}),e("span",[t._v(t._s(t.transactionError))]),e("button",{on:{click:t.fetchTransactions}},[t._v("重试")])]):t._e(),e("table",{staticClass:"data-table"},[t._m(1),e("tbody",[t._l(t.paginatedTransactions,(function(a,s){return e("tr",{key:"transaction-"+s},[e("td",[t._v(t._s(a.transaction_id))]),e("td",[t._v(t._s(t.formatDateTime(a.created_at)))]),e("td",[e("span",{staticClass:"transaction-type",class:t.getTransactionTypeClass(a.type)},[t._v(" "+t._s(t.getTransactionTypeName(a.type))+" ")])]),e("td",[t._v(t._s(t.getTransactionTypeNamePay(a.pay_type)))]),e("td",{class:"expense"===a.type?"expense-amount":"income-amount"},[t._v(" "+t._s("expense"===a.type?"￥ - ":"￥ + ")+t._s(t.formatPrice(a.amount))+" ")]),e("td",{class:"expense"===a.type?"expense-zhifu":"income-shouru"},[t._v(" "+t._s(t.getPaymentMethodText(a.payment_channel))+" ")]),e("td",[t._v(t._s(a.description))])])})),0!==t.filteredTransactionData.length||t.transactionLoading?t._e():e("tr",[t._m(2)])],2)])]),e("common-pagination",{attrs:{"current-page":t.transactionPage,total:t.filteredTransactionData.length,"page-size":t.pageSize},on:{"change-page":e=>t.transactionPage=e,"change-page-size":e=>{t.pageSize=e,t.transactionPage=1}}})],1)]):t._e(),"usage"===t.currentSection?e("div",[e("div",{staticClass:"tab-content"},[e("div",{staticClass:"search-section"},[e("div",{staticClass:"date-range-picker"},[e("select",{directives:[{name:"model",rawName:"v-model",value:t.usageDateRange,expression:"usageDateRange"}],on:{change:function(e){var a=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.usageDateRange=e.target.multiple?a:a[0]}}},[e("option",{attrs:{value:"7"}},[t._v("最近7天")]),e("option",{attrs:{value:"30"}},[t._v("最近一个月")]),e("option",{attrs:{value:"90"}},[t._v("最近三个月")]),e("option",{attrs:{value:"custom"}},[t._v("自定义时间段")])]),"custom"===t.usageDateRange?e("div",{staticClass:"custom-date-range"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.customUsageDateStart,expression:"customUsageDateStart"}],attrs:{type:"date"},domProps:{value:t.customUsageDateStart},on:{input:function(e){e.target.composing||(t.customUsageDateStart=e.target.value)}}}),e("span",[t._v("至")]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.customUsageDateEnd,expression:"customUsageDateEnd"}],attrs:{type:"date"},domProps:{value:t.customUsageDateEnd},on:{input:function(e){e.target.composing||(t.customUsageDateEnd=e.target.value)}}})]):t._e()]),e("div",{staticClass:"search-filters"},[e("select",{directives:[{name:"model",rawName:"v-model",value:t.usageFilterGpu,expression:"usageFilterGpu"}],on:{change:function(e){var a=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.usageFilterGpu=e.target.multiple?a:a[0]}}},[e("option",{attrs:{value:""}},[t._v("全部GPU型号")]),t._l(t.gpuModels,(function(a){return e("option",{key:a.id,domProps:{value:a.id}},[t._v(" "+t._s(a.name)+" ")])}))],2)])]),e("div",{staticClass:"table-container"},[e("table",{staticClass:"data-table"},[t._m(3),e("tbody",[t._l(t.paginatedUsageRecords,(function(a,s){return e("tr",{key:"usage-"+s},[e("td",[t._v(t._s(a.gpu_model))]),e("td",[e("span",{staticClass:"status-tag",class:t.getUsageStatusClass(a.status)},[t._v(" "+t._s(t.getUsageStatusText(a.status))+" ")])]),e("td",[t._v(t._s(t.formatDateTime(a.start_time)))]),e("td",[t._v(t._s(a.end_time?t.formatDateTime(a.end_time):"--"))]),e("td",[t._v(t._s(t.calculateDuration(a.start_time,a.end_time)))]),e("td",[t._v("¥ "+t._s(t.formatPrice(a.cost/1e4)))]),e("td",[e("span",{staticClass:"operation-link",on:{click:t.navigateToRecharge}},[t._v("续费")]),"scheduled"===a.status?e("span",{staticClass:"operation-link cancel-link",on:{click:function(e){return t.cancelReservation(a)}}},[t._v("取消")]):t._e()])])})),0===t.filteredUsageData.length?e("tr",[t._m(4)]):t._e()],2)])]),e("common-pagination",{attrs:{"current-page":t.usagePage,total:t.filteredUsageData.length,"page-size":t.pageSize},on:{"change-page":e=>t.usagePage=e,"change-page-size":e=>{t.pageSize=e,t.usagePage=1}}})],1)]):t._e(),"recharge"===t.currentSection?e("div",[e("div",{staticClass:"tab-content"},[e("div",{staticClass:"account-balance"},[e("div",{staticClass:"balance-info"},[e("div",{staticClass:"balance-label"},[t._v("当前账户余额")]),e("div",{staticClass:"balance-value"},[t._v("¥ "+t._s(t.formatPrice(t.userBalance)))])])]),e("div",{staticClass:"recharge-options"},[e("h3",{staticClass:"recharge-title"},[t._v("选择充值金额")]),e("div",{staticClass:"amount-options"},[t._l(t.rechargeAmounts,(function(a){return e("div",{key:"amount-"+a,class:["amount-option",{selected:t.rechargeAmount===a}],on:{click:function(e){t.rechargeAmount=a}}},[t._v(" "+t._s(a)+"元 ")])})),e("div",{staticClass:"amount-option custom-amount"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.customRechargeAmount,expression:"customRechargeAmount"}],attrs:{type:"number",placeholder:"其他金额"},domProps:{value:t.customRechargeAmount},on:{focus:function(e){t.rechargeAmount=null},input:[function(e){e.target.composing||(t.customRechargeAmount=e.target.value)},t.handleCustomAmountInput]}})])],2),e("h3",{staticClass:"recharge-title"},[t._v("选择支付方式")]),e("div",{staticClass:"payment-methods"},[e("div",{class:["payment-method",{selected:"alipay"===t.paymentMethod}],on:{click:function(e){t.paymentMethod="alipay"}}},[e("img",{staticClass:"payment-icon",attrs:{src:a(3161),alt:"支付宝"}}),e("span",[t._v("支付宝")])])]),e("div",{staticClass:"recharge-action"},[e("button",{staticClass:"recharge-button",attrs:{disabled:!t.canRecharge},on:{click:t.submitRecharge}},[t._v(" 立即充值 ")])])])])]):t._e()])])])],1)},i=[function(){var t=this,e=t._self._c;return e("td",{staticClass:"empty-state",attrs:{colspan:"8"}},[e("div",{staticClass:"empty-container"},[e("i",{staticClass:"el-icon-document empty-icon"}),e("div",{staticClass:"empty-text"},[t._v("没有找到匹配的订单")])])])},function(){var t=this,e=t._self._c;return e("thead",[e("tr",[e("th",[t._v("流水号")]),e("th",[t._v("交易时间")]),e("th",[t._v("收支类型")]),e("th",[t._v("交易类型")]),e("th",[t._v("金额")]),e("th",[t._v("交易渠道")]),e("th",[t._v("备注")])])])},function(){var t=this,e=t._self._c;return e("td",{staticClass:"empty-state",attrs:{colspan:"8"}},[e("div",{staticClass:"empty-container"},[e("i",{staticClass:"el-icon-money empty-icon"}),e("div",{staticClass:"empty-text"},[t._v("没有找到匹配的交易记录")])])])},function(){var t=this,e=t._self._c;return e("thead",[e("tr",[e("th",[t._v("GPU型号")]),e("th",[t._v("状态")]),e("th",[t._v("开始时间")]),e("th",[t._v("结束时间")]),e("th",[t._v("使用时长")]),e("th",[t._v("计费金额")]),e("th",[t._v("操作")])])])},function(){var t=this,e=t._self._c;return e("td",{staticClass:"empty-state",attrs:{colspan:"7"}},[e("div",{staticClass:"empty-container"},[e("i",{staticClass:"el-icon-time empty-icon"}),e("div",{staticClass:"empty-text"},[t._v("没有找到匹配的使用记录")])])])}],r=function(){var t=this,e=t._self._c;return e("main",{staticClass:"page-wrapper"},[e("Header"),e("div",{staticClass:"main-content"},[t._t("default")],2),e("Footer")],1)},n=[],c=a(2503),o={name:"Layout",components:{Header:c.Z}},l=o,u=a(1001),d=(0,u.Z)(l,r,n,!1,null,"3be2ba12",null),m=d.exports,h=function(){var t=this,e=t._self._c;return e("div",{staticClass:"custom-pagination"},[e("span",{staticClass:"pagination-total"},[t._v("共 "+t._s(t.total)+" 条")]),e("button",{staticClass:"pagination-prev",attrs:{disabled:1===t.currentPage},on:{click:function(e){return t.changePage(t.currentPage-1)}}},[t._v(" < ")]),e("span",{staticClass:"pagination-current"},[t._v(t._s(t.currentPage))]),e("button",{staticClass:"pagination-next",attrs:{disabled:t.currentPage===t.totalPages},on:{click:function(e){return t.changePage(t.currentPage+1)}}},[t._v(" > ")]),e("span",{staticClass:"pagination-size"},[t._v(t._s(t.pageSize)+"条 / 页")]),e("div",{staticClass:"pagination-jump"},[e("span",[t._v("前往")]),e("input",{directives:[{name:"model",rawName:"v-model.number",value:t.inputPage,expression:"inputPage",modifiers:{number:!0}}],attrs:{type:"text",min:"1",max:t.totalPages},domProps:{value:t.inputPage},on:{blur:[t.handleJump,function(e){return t.$forceUpdate()}],keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleJump.apply(null,arguments)},input:function(e){e.target.composing||(t.inputPage=t._n(e.target.value))}}}),e("span",[t._v("页")])])])},g=[],p={name:"CommonPagination",props:{currentPage:{type:Number,required:!0},total:{type:Number,required:!0},pageSize:{type:Number,required:!0}},data(){return{inputPage:this.currentPage}},watch:{currentPage(t){this.inputPage=t}},computed:{totalPages(){return Math.ceil(this.total/this.pageSize)}},methods:{changePage(t){t>=1&&t<=this.totalPages&&this.$emit("change-page",t)},handleJump(){this.inputPage&&this.inputPage>=1&&this.inputPage<=this.totalPages?this.inputPage!==this.currentPage&&this.changePage(this.inputPage):this.inputPage=this.currentPage}}},v=p,_=(0,u.Z)(v,h,g,!1,null,"6343bdf2",null),y=_.exports,f=a(7234),C=a(2223),D=(a(1836),{name:"OrderView",components:{SlideNotification:f.Z,Layout:m,CommonPagination:y},data(){return{currentSection:"transactions",pageSize:6,userBalance:0,showNotification:!1,notificationMessage:"",notificationType:"success",orderData:[],orderSearchQuery:"",currentPage:1,showOrderDetails:!1,selectedOrder:{},orderLoading:!1,orderError:null,transactionData:[],transactionSearchQuery:"",transactionDateRange:"30",transactionType:"",transactionPage:1,customDateStart:"",customDateEnd:"",transactionLoading:!1,transactionError:null,summaryData:{totalRecharge:0,totalExpense:0,balance:0},customUsageDateStart:"",customUsageDateEnd:"",usageData:[],usageDateRange:"30",usageFilterGpu:"",usageLoading:!1,usageError:null,usagePage:1,gpuModels:[{id:"a100",name:"NVIDIA A100"},{id:"v100",name:"NVIDIA V100"},{id:"rtx3090",name:"NVIDIA RTX 3090"}],rechargeAmounts:[100,200,500,1e3,2e3],rechargeAmount:100,customRechargeAmount:null,paymentMethod:"alipay",rechargeLoading:!1}},computed:{filteredOrderData(){if(!this.orderSearchQuery)return this.orderData;const t=this.orderSearchQuery.toLowerCase();return this.orderData.filter((e=>e.order_number.toLowerCase().includes(t)))},paginatedOrders(){const t=(this.currentPage-1)*this.pageSize,e=t+this.pageSize;return this.filteredOrderData.slice(t,e)},currentOrderTotal(){return this.filteredOrderData.length},filteredTransactionData(){let t=[...this.transactionData];if("custom"!==this.transactionDateRange){const e=parseInt(this.transactionDateRange),a=new Date;a.setDate(a.getDate()-e),t=t.filter((t=>{const e=new Date(t.created_at);return e>=a}))}else if(this.customDateStart&&this.customDateEnd){const e=new Date(this.customDateStart),a=new Date(this.customDateEnd);a.setHours(23,59,59,999),t=t.filter((t=>{const s=new Date(t.created_at);return s>=e&&s<=a}))}if(this.transactionType&&(t=t.filter((t=>t.type===this.transactionType))),this.transactionSearchQuery){const e=this.transactionSearchQuery.toLowerCase();t=t.filter((t=>t.transaction_id.toLowerCase().includes(e)))}return t},paginatedTransactions(){const t=(this.transactionPage-1)*this.pageSize,e=t+this.pageSize;return this.filteredTransactionData.slice(t,e)},filteredUsageData(){if(!this.usageData||0===this.usageData.length)return[];let t=[...this.usageData];if("custom"!==this.usageDateRange){const e=parseInt(this.usageDateRange),a=new Date;a.setDate(a.getDate()-e),t=t.filter((t=>{const e=new Date(t.start_time||t.created_at);return e>=a}))}else if(this.customUsageDateStart&&this.customUsageDateEnd){const e=new Date(this.customUsageDateStart),a=new Date(this.customUsageDateEnd);a.setHours(23,59,59,999),t=t.filter((t=>{const s=new Date(t.start_time||t.created_at);return s>=e&&s<=a}))}return this.usageFilterGpu&&(t=t.filter((t=>t.gpu_model.toLowerCase().includes(this.usageFilterGpu.toLowerCase())))),t},paginatedUsageRecords(){const t=(this.usagePage-1)*this.pageSize,e=t+this.pageSize;return this.filteredUsageData.slice(t,e)},canRecharge(){const t=this.getRechargeAmount();return t&&t>0&&this.paymentMethod&&!this.rechargeLoading}},watch:{$route(t){const e=t.query.activeTab;e&&["orders","transactions","usage","recharge"].includes(e)&&(this.currentSection=e)},currentSection(t){"transactions"===t?(this.fetchTransactions(),this.fetchSummaryData()):"orders"===t?this.fetchOrders():"recharge"===t?this.fetchUserBalance():"transactions"===t&&(this.fetchTransactions(),this.loadSectionData("transactions"))}},created(){this.fetchUserBalance(),this.fetchTransactions();const t=this.$route.query.activeTab||"transactions";this.currentSection=t,this.loadSectionData(t),this.$watch((()=>this.$route.query),(t=>{t.activeTab&&["orders","transactions","usage","recharge"].includes(t.activeTab)&&(this.currentSection=t.activeTab)}),{immediate:!0}),this.fetchOrders().then((()=>{this.sortBy("created_at");const t=this.$route.query.activeTab;t&&["orders","transactions","usage","recharge"].includes(t)&&(this.currentSection=t)})),this.fetchSummaryData()},beforeDestroy(){this.$emit("refresh-header")},methods:{showNotificationMessage(t,e="info"){this.notificationMessage=t,this.notificationType=e,this.showNotification=!0},changeSection(t){this.currentSection=t,this.$router.replace({query:{...this.$route.query,activeTab:t}}),"orders"===t?this.fetchOrders():"transactions"===t?(this.fetchTransactions(),this.fetchSummaryData()):"usage"===t?this.fetchUsageData():"recharge"===t&&this.fetchUserBalance()},loadSectionData(t){switch(t){case"orders":this.fetchOrders();break;case"transactions":this.fetchTransactions(),this.fetchSummaryData();break;case"usage":this.fetchUsageData();break;case"recharge":this.fetchUserBalance();break}},async fetchUserBalance(){try{const t=await(0,C.fB)("/logout/cilent/getInfo");200===t.data.code&&(this.userBalance=t.data.data.balance||0,this.$emit("refresh-header"))}catch(t){this.$message.error("获取用户余额失败")}},applyUsageCustomDateRange(){if(!this.customUsageDateStart||!this.customUsageDateEnd)return void this.$message.error("请选择开始和结束日期");const t=new Date(this.customUsageDateStart),e=new Date(this.customUsageDateEnd);t>e?this.$message.error("开始日期不能晚于结束日期"):(this.usagePage=1,this.fetchUsageData())},formatDateTimeusage(t){if(!t)return"--";try{const e=new Date(t);return e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")}catch(e){return"--"}},calculateDuration(t,e){if(!t)return"0分钟";try{const a=new Date(t),s=e?new Date(e):new Date;if(isNaN(a.getTime()))return"时间无效";if(e&&isNaN(s.getTime()))return"时间无效";if(a>new Date)return"未开始";if(!e||s>new Date){const t=new Date-a,e=Math.floor(t/36e5),s=Math.floor(t%36e5/6e4);return`${e}小时${s}分钟 `}const i=s-a,r=Math.floor(i/36e5),n=Math.floor(i%36e5/6e4);return r>0?`${r}小时${n}分钟`:`${n}分钟`}catch(a){return"--"}},navigateToRecharge(){this.$router.replace({path:"/userorder",query:{activeTab:"recharge",timestamp:Date.now()}})},async fetchOrders(){this.orderLoading=!0,this.orderError=null;try{const t=await(0,C.fB)("/system/order/getOrderAndProduct");this.orderData=t.data.data.map((t=>({id:t.name,order_number:t.order_name,payment_status:this.mapOrderStatus(t.order_staus),payment_method:t.order_payment_method||"余额支付",total_price:t.order_price,unit_price:this.calculateUnitPrice(t),duration:t.order_buy_time,created_at:t.create_time,gpu_model:t.name||"未知型号",region:t.Region||"未知地区",gpu_count:t.graphics_card_number||0,video_memory:t.video_memory||"未知",cpu_cores:t.gpu_nuclear_number||0,system_disk:t.system_disk||"未知",cloud_disk:t.data_disk||"未知",memory:t.internal_memory||"未知",rawData:t}))).sort(((t,e)=>new Date(e.created_at)-new Date(t.created_at)))}catch(t){this.orderError="获取订单数据失败，请稍后重试"}finally{this.orderLoading=!1}},async fetchTransactions(){this.transactionLoading=!0,this.transactionError=null;try{const t=await(0,C.fB)("/system/order/getConsumptionOrder"),e=t.data.data,a=new Map;e.forEach((t=>{const e=t.topup_id||t.gpu_order_id;a.has(e)||a.set(e,{transaction_id:t.topup_order_id||t.gpu_order_name,type:"收入"===t.source_table?"income":"expense",pay_type:this.mapPayType(t.source_table),amount:t.topup_topup||t.gpu_order_price,created_at:t.create_time,payment_channel:t.payment_method||"未知",status:this.mapTransactionStatus(t.order_staus),description:t.description||"无"})})),this.transactionData=Array.from(a.values())}catch(t){this.transactionLoading="暂无记录"}finally{this.transactionLoading=!1}},async fetchUsageData(){this.usageLoading=!0,this.usageError=null;try{let t={};if("custom"===this.usageDateRange&&this.customUsageDateStart&&this.customUsageDateEnd)t.start_date=this.customUsageDateStart,t.end_date=this.customUsageDateEnd;else if("custom"!==this.usageDateRange){const e=parseInt(this.usageDateRange),a=new Date;a.setDate(a.getDate()-e),t.start_date=a.toISOString().split("T")[0]}this.usageFilterGpu&&(t.gpu_model=this.usageFilterGpu);await(0,C.fB)("/system/order/getProductResumption");const e=[];return this.usageData=e,this.usageData}catch(t){return this.usageError="加载使用记录失败，请稍后重试",[]}finally{this.usageLoading=!1}},async fetchSummaryData(){try{const t=await(0,C.fB)("/system/order/getConsumptionOrder"),e=t.data.data||[],a=new Map;e.forEach((t=>{const e=t.topup_id||t.gpu_order_id;a.has(e)||a.set(e,t)}));const s=Array.from(a.values()),i=s.filter((t=>"收入"===t.source_table)).reduce(((t,e)=>t+(parseFloat(e.topup_topup)||0)),0),r=s.filter((t=>"支出"===t.source_table)).reduce(((t,e)=>t+(parseFloat(e.gpu_order_price)||0)),0);this.summaryData={totalRecharge:i,totalExpense:r,balance:this.userBalance}}catch(t){this.$message.error("获取账户汇总数据失败")}},mapOrderStatus(t){const e={1:"paid",2:"failed",3:"unpaid"};return e[t]||t},mapPayType(t){return"收入"===t?"充值":"消费"},mapTransactionStatus(t){const e={1:"success",2:"failed",3:"processing"};return e[t]||t},calculateUnitPrice(t){if(!t.computing)return t.order_price;switch(t.order_unit){case"hour":return t.computing.price_bour;case"day":return t.computing.price_day;case"month":return t.computing.price_mouth;case"year":return t.computing.price_year;default:return t.order_price}},formatPrice(t){return isNaN(t)?"0.00":parseFloat(t).toFixed(2)},formatDate(t){return new Date(t).toLocaleDateString()},formatDateTime(t){if(!t)return"";const e=new Date(t);return e.toLocaleString()},getPaymentStatusClass(t){return{paid:"status-success",unpaid:"status-pending",refunded:"status-info",failed:"status-error",cancelled:"status-warning"}[t]||""},getPaymentStatusText(t){return{paid:"已支付",unpaid:"未支付",refunded:"已退款",failed:"支付失败",cancelled:"已取消"}[t]||t},getPaymentMethodClass(t){return{"支付宝":"payment-alipay","微信支付":"payment-wechat","银行卡":"payment-bank","余额支付":"payment-balance","未支付":"payment-warning"}[t]||""},getPaymentMethodText(t){return{alipay:"支付宝",wechat:"微信支付",bank:"银行卡",balance:"余额"}[t]||t},searchOrders(){this.currentPage=1},clearOrderSearch(){this.orderSearchQuery="",this.currentPage=1},sortBy(t){"created_at"===t?this.orderData.sort(((e,a)=>new Date(a[t])-new Date(e[t]))):this.orderData.sort(((e,a)=>e[t]>a[t]?1:-1))},viewOrderDetails(t){this.selectedOrder=t,this.showOrderDetails=!0,this.$nextTick((()=>{document.querySelector(".order-details").scrollIntoView({behavior:"smooth"})}))},showOrderList(){this.showOrderDetails=!1,window.scrollTo({top:0,behavior:"smooth"})},goToPage(t){this.currentPage=t},handlePageSizeChange(t){this.pageSize=t,this.currentPage=1},searchTransactions(){this.transactionPage=1},applyCustomDateRange(){this.customDateStart&&this.customDateEnd&&(this.transactionPage=1)},getTransactionTypeClass(t){return{income:"income-type",expense:"expense-type",refund:"refund-type"}[t]||""},getTransactionTypeName(t){return{income:"收入",expense:"支出",refund:"退款"}[t]||t},getTransactionTypeNamePay(t){return{income:"收入",expense:"支出"}[t]||t},getTransactionStatusClass(t){return{success:"status-success",pending:"status-pending",failed:"status-error",processing:"status-info"}[t]||""},getTransactionStatusName(t){return{success:"成功",pending:"处理中",failed:"失败",processing:"处理中"}[t]||t},getUsageStatusClass(t){return{active:"status-success",running:"status-running",about_to_expire:"status-warning",expired:"status-error",completed:"status-complete",paused:"status-info"}[t]||""},getUsageStatusText(t){return{active:"可用",running:"使用中",about_to_expire:"即将到期",completed:"已结束",paused:"已暂停"}[t]||t},renewService(t){this.$message.success(`正在为 ${t.gpu_model} 续费`)},getRechargeAmount(){return this.rechargeAmount||this.customRechargeAmount},handleCustomAmountInput(){this.rechargeAmount=null},async submitRecharge(){const t=this.getRechargeAmount();if(!t||t<=0)this.showNotificationMessage("请输入有效的充值金额","error");else{this.rechargeLoading=!0;try{const e=await(0,C.P2)("/yun/scanPay",{amount:t});if(e.data.includes("充值金额不足"))return void this.showNotificationMessage(e.data,"error");const a=document.createElement("div");a.innerHTML=e.data,document.body.appendChild(a);const s=a.querySelector("form");s?(this.showNotificationMessage("正在跳转到支付页面...","success"),s.target="_blank",s.submit(),setTimeout((()=>{this.fetchUserBalance(),this.fetchTransactions()}),3e3)):this.showNotificationMessage("支付表单生成失败","error")}catch(e){this.showNotificationMessage("充值请求失败，请稍后重试","error")}finally{this.rechargeLoading=!1}}}}}),b=D,w=(0,u.Z)(b,s,i,!1,null,"2f8bfd3e",null),S=w.exports},3161:function(t,e,a){t.exports=a.p+"img/alipay.3d55ca53.svg"}}]);
//# sourceMappingURL=796.5ccc8b27.js.map