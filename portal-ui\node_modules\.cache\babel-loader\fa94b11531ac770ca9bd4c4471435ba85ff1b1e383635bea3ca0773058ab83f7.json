{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"header-wrapper\"\n  }, [_vm.showComingSoon ? _c('SlideNotification', {\n    attrs: {\n      \"message\": _vm.notificationMessage,\n      \"type\": \"warning\",\n      \"duration\": 2000\n    },\n    on: {\n      \"close\": function ($event) {\n        _vm.showComingSoon = false;\n      }\n    }\n  }) : _vm._e(), _c('div', {\n    staticClass: \"nav-placeholder\",\n    style: {\n      height: _vm.navHeight + 'px'\n    }\n  }), _c('div', {\n    ref: \"mainNav\",\n    staticClass: \"main-nav\"\n  }, [_c('div', {\n    staticClass: \"container\"\n  }, [!_vm.isMobile ? _c('div', {\n    staticClass: \"nav-container desktop-nav\"\n  }, [_c('div', {\n    staticClass: \"logo-area\"\n  }, [_c('a', {\n    staticClass: \"logo-link\",\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/');\n      }\n    }\n  }, [_c('img', {\n    attrs: {\n      \"src\": \"images/logo-tiangong.png\",\n      \"alt\": \"算力租赁\",\n      \"loading\": \"eager\"\n    }\n  })])]), _c('div', {\n    staticClass: \"nav-menu\"\n  }, [_c('ul', {\n    staticClass: \"nav-list\"\n  }, [_c('li', {\n    staticClass: \"nav-item\"\n  }, [_c('a', {\n    staticClass: \"nav-link\",\n    class: {\n      'active': _vm.isActive('/')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/');\n      }\n    }\n  }, [_vm._v(\"首页\")])]), _c('li', {\n    staticClass: \"nav-item dropdown\"\n  }, [_c('a', {\n    staticClass: \"nav-link\",\n    class: {\n      'active': _vm.isActive('/product')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/product');\n      }\n    }\n  }, [_vm._v(\" 算力市场\"), _c('i', {\n    staticClass: \"iconfont icon-arrow-down\"\n  })])]), _c('li', {\n    staticClass: \"nav-item dropdown\"\n  }, [_c('a', {\n    staticClass: \"nav-link\",\n    class: {\n      'active': false\n    },\n    on: {\n      \"click\": _vm.triggerComingSoon\n    }\n  }, [_vm._v(\"算法社区\"), _c('i', {\n    staticClass: \"iconfont icon-arrow-down\"\n  })])]), _c('li', {\n    staticClass: \"nav-item\"\n  }, [_c('a', {\n    staticClass: \"nav-link\",\n    class: {\n      'active': false\n    },\n    on: {\n      \"click\": _vm.triggerComingSoon\n    }\n  }, [_vm._v(\"私有云\")])]), _c('li', {\n    staticClass: \"nav-item\"\n  }, [_c('a', {\n    staticClass: \"nav-link\",\n    class: {\n      'active': false\n    },\n    on: {\n      \"click\": _vm.triggerComingSoon\n    }\n  }, [_vm._v(\"关于我们\")])])])]), _c('div', {\n    staticClass: \"user-actions\"\n  }, [!_vm.isLoggedIn ? _c('div', {\n    staticClass: \"auth-buttons\"\n  }, [_c('a', {\n    staticClass: \"btn btn-login\",\n    class: {\n      'active': _vm.isActive('/help')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/help');\n      }\n    }\n  }, [_vm._v(\"帮助文档\")]), _c('a', {\n    staticClass: \"btn btn-login\",\n    class: {\n      'active': _vm.isActive('/login')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/login');\n      }\n    }\n  }, [_vm._v(\"控制台\")]), _c('a', {\n    staticClass: \"btn btn-login\",\n    class: {\n      'active': _vm.isActive('/login')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/login');\n      }\n    }\n  }, [_vm._v(\"登录\")]), _c('a', {\n    staticClass: \"btn btn-register\",\n    class: {\n      'active': _vm.isActive('/register')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/register');\n      }\n    }\n  }, [_vm._v(\"立即注册\")])]) : _vm._e(), _vm.isLoggedIn ? _c('div', {\n    staticClass: \"user-profile\"\n  }, [_c('a', {\n    staticClass: \"btn btn-login\",\n    class: {\n      'active': _vm.isActive('/help')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/help');\n      }\n    }\n  }, [_vm._v(\"帮助文档\")]), _c('a', {\n    staticClass: \"btn btn-login\",\n    class: {\n      'active': _vm.isActive('/console'),\n      'disabled': _vm.isConsoleLoading\n    },\n    attrs: {\n      \"title\": _vm.isConsoleLoading ? '控制台加载中，请稍后...' : ''\n    },\n    on: {\n      \"click\": _vm.handleConsoleNavigation\n    }\n  }, [_vm._v(\"控制台\")]), _c('div', {\n    staticClass: \"user-dropdown\"\n  }, [_c('div', {\n    staticClass: \"user-avatar\"\n  }, [_c('div', {\n    staticClass: \"avatar-letter\"\n  }, [_vm._v(_vm._s(_vm.userInitial))])]), _c('div', {\n    staticClass: \"dropdown-menu\"\n  }, [_c('div', {\n    staticClass: \"user-info-section\"\n  }, [_c('div', {\n    staticClass: \"detail-item\"\n  }, [_c('span', {\n    staticClass: \"label\"\n  }, [_vm._v(\"用户名:\")]), _c('span', {\n    staticClass: \"value\"\n  }, [_vm._v(_vm._s(_vm.userName))])]), _c('div', {\n    staticClass: \"detail-item\"\n  }, [_c('span', {\n    staticClass: \"label\"\n  }, [_vm._v(\"手机号:\")]), _c('span', {\n    staticClass: \"value\"\n  }, [_c('i', {\n    staticClass: \"copy-icon\"\n  }), _vm._v(\" \" + _vm._s(_vm.userPhone))])]), _c('div', {\n    staticClass: \"verification-tag\",\n    on: {\n      \"click\": _vm.gotoPersonal\n    }\n  }, [_c('span', {\n    staticClass: \"check-icon\"\n  }), _vm._v(\" 个人认证 \"), _c('span', {\n    staticClass: \"status-text\"\n  }, [_vm._v(\"(\" + _vm._s(_vm.isReal === 1 ? '已认证' : '未认证') + \")\")])]), _c('div', {\n    staticClass: \"detail-item\"\n  }, [_c('span', {\n    staticClass: \"label\"\n  }, [_vm._v(\"可用余额:\")]), _c('span', {\n    staticClass: \"value\"\n  }, [_vm._v(\"￥\" + _vm._s(_vm.userBalance.toFixed(2)))]), _c('div', {\n    staticClass: \"verification-tag recharge-btn\",\n    on: {\n      \"click\": _vm.navigateToRecharge\n    }\n  }, [_vm._v(\" 充值 \")])])]), _c('div', {\n    staticClass: \"menu-options\"\n  }, [_c('a', {\n    class: {\n      'active': _vm.isActive('/personal')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/personal');\n      }\n    }\n  }, [_vm._v(\"个人中心\")]), _c('a', {\n    class: {\n      'active': _vm.isActive('/userorder')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/userorder');\n      }\n    }\n  }, [_vm._v(\"费用中心\")])]), _c('div', {\n    staticClass: \"logout-button-container\"\n  }, [_c('button', {\n    staticClass: \"logout-button\",\n    on: {\n      \"click\": _vm.logout\n    }\n  }, [_vm._v(\"退出登录\")])])])])]) : _vm._e()])]) : _c('div', {\n    staticClass: \"mobile-nav\"\n  }, [_c('div', {\n    staticClass: \"mobile-nav-container\"\n  }, [_c('button', {\n    staticClass: \"hamburger-btn\",\n    on: {\n      \"click\": _vm.toggleMobileMenu\n    }\n  }, [_c('span', {\n    staticClass: \"hamburger-line\",\n    class: {\n      'line-1': _vm.mobileMenuOpen\n    }\n  }), _c('span', {\n    staticClass: \"hamburger-line\",\n    class: {\n      'line-2': _vm.mobileMenuOpen\n    }\n  }), _c('span', {\n    staticClass: \"hamburger-line\",\n    class: {\n      'line-3': _vm.mobileMenuOpen\n    }\n  })]), _c('div', {\n    staticClass: \"mobile-logo-area\"\n  }, [_c('a', {\n    staticClass: \"logo-link\",\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/');\n      }\n    }\n  }, [_c('img', {\n    attrs: {\n      \"src\": \"images/logo_tiangong.png\",\n      \"alt\": \"算力租赁\"\n    }\n  })])]), _c('div', {\n    staticClass: \"mobile-user-actions\"\n  }, [!_vm.isLoggedIn ? [_c('a', {\n    staticClass: \"mobile-login-btn\",\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/login');\n      }\n    }\n  }, [_vm._v(\" 登录 \")]), _c('a', {\n    staticClass: \"mobile-register-btn\",\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/register');\n      }\n    }\n  }, [_vm._v(\" 注册 \")])] : [_c('a', {\n    staticClass: \"mobile-console-btn\",\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/help');\n      }\n    }\n  }, [_vm._v(\" 帮助文档 \")]), _c('a', {\n    staticClass: \"mobile-console-btn\",\n    on: {\n      \"click\": _vm.handleConsoleNavigation\n    }\n  }, [_vm._v(\" 控制台 \")]), _c('div', {\n    staticClass: \"mobile-user-profile\",\n    on: {\n      \"click\": _vm.toggleUserMenu\n    }\n  }, [_c('div', {\n    staticClass: \"mobile-user-avatar\"\n  }, [_c('div', {\n    staticClass: \"avatar-letter\"\n  }, [_vm._v(_vm._s(_vm.userInitial))])])])]], 2)]), _c('div', {\n    staticClass: \"mobile-menu\",\n    class: {\n      'open': _vm.mobileMenuOpen\n    }\n  }, [_c('div', {\n    staticClass: \"mobile-menu-content\"\n  }, [_c('ul', {\n    staticClass: \"mobile-nav-list\"\n  }, [_c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      'active': _vm.isActive('/')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/');\n      }\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-home\"\n  }), _vm._v(\"首页 \")])]), _c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      'active': _vm.isActive('/product')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/product');\n      }\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-server\"\n  }), _vm._v(\"算力市场 \")])]), _c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    on: {\n      \"click\": _vm.triggerComingSoon\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-community\"\n  }), _vm._v(\"算法社区 \")])]), _c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    on: {\n      \"click\": _vm.triggerComingSoon\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-cloud\"\n  }), _vm._v(\"私有云 \")])]), _c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      'active': _vm.isActive('/about')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/about');\n      }\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-info\"\n  }), _vm._v(\"关于我们 \")])]), !_vm.isLoggedIn ? [_c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      'active': _vm.isActive('/help')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/help');\n      }\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-docs\"\n  }), _vm._v(\"帮助文档 \")])]), _c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      'active': _vm.isActive('/login')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/login');\n      }\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-user\"\n  }), _vm._v(\"登录 \")])]), _c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      'active': _vm.isActive('/register')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/register');\n      }\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-edit\"\n  }), _vm._v(\"注册 \")])]), _c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      'active': _vm.isActive('/console')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/login');\n      }\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-console\"\n  }), _vm._v(\"控制台 \")])])] : [_c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      'active': _vm.isActive('/help')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/help');\n      }\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-docs\"\n  }), _vm._v(\"帮助文档 \")])]), _c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      'active': _vm.isActive('/console')\n    },\n    on: {\n      \"click\": _vm.handleConsoleNavigation\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-console\"\n  }), _vm._v(\"控制台 \")])]), _c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    class: {\n      'active': _vm.isActive('/personal')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/personal');\n      }\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-profile\"\n  }), _vm._v(\"个人中心 \")])]), _c('li', {\n    staticClass: \"mobile-nav-item\"\n  }, [_c('a', {\n    staticClass: \"mobile-nav-link\",\n    on: {\n      \"click\": _vm.logout\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-logout\"\n  }), _vm._v(\"退出登录 \")])])]], 2)])]), _vm.showUserMenu ? _c('div', {\n    staticClass: \"mobile-user-menu\"\n  }, [_c('div', {\n    staticClass: \"mobile-user-info\"\n  }, [_c('div', {\n    staticClass: \"mobile-username\"\n  }, [_vm._v(_vm._s(_vm.userName))]), _c('div', {\n    staticClass: \"mobile-user-phone\"\n  }, [_vm._v(_vm._s(_vm.userPhone))])]), _c('div', {\n    staticClass: \"mobile-menu-options\"\n  }, [_c('a', {\n    staticClass: \"mobile-menu-item\",\n    class: {\n      'active': _vm.isActive('/personal')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/personal');\n      }\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-profile\"\n  }), _vm._v(\"个人中心 \")]), _c('a', {\n    staticClass: \"mobile-menu-item\",\n    class: {\n      'active': _vm.isActive('/userorder')\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.navigateTo('/userorder');\n      }\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-order\"\n  }), _vm._v(\"费用中心 \")]), _c('a', {\n    staticClass: \"mobile-menu-item\",\n    class: {\n      'active': false\n    },\n    on: {\n      \"click\": _vm.navigateToRecharge\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-recharge\"\n  }), _vm._v(\"充值 \")]), _c('a', {\n    staticClass: \"mobile-menu-item logout\",\n    on: {\n      \"click\": _vm.logout\n    }\n  }, [_c('i', {\n    staticClass: \"iconfont icon-logout\"\n  }), _vm._v(\"退出登录 \")])])]) : _vm._e()])])])], 1);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "showComingSoon", "attrs", "notificationMessage", "on", "close", "$event", "_e", "style", "height", "navHeight", "ref", "isMobile", "click", "navigateTo", "class", "isActive", "_v", "triggerComingSoon", "isLoggedIn", "isConsoleLoading", "handleConsoleNavigation", "_s", "userInitial", "userName", "userPhone", "gotoPersonal", "isReal", "userBalance", "toFixed", "navigateToRecharge", "logout", "toggleMobileMenu", "mobileMenuOpen", "toggleUserMenu", "showUserMenu", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/components/common/header/Header.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"header-wrapper\"},[(_vm.showComingSoon)?_c('SlideNotification',{attrs:{\"message\":_vm.notificationMessage,\"type\":\"warning\",\"duration\":2000},on:{\"close\":function($event){_vm.showComingSoon = false}}}):_vm._e(),_c('div',{staticClass:\"nav-placeholder\",style:({ height: _vm.navHeight + 'px' })}),_c('div',{ref:\"mainNav\",staticClass:\"main-nav\"},[_c('div',{staticClass:\"container\"},[(!_vm.isMobile)?_c('div',{staticClass:\"nav-container desktop-nav\"},[_c('div',{staticClass:\"logo-area\"},[_c('a',{staticClass:\"logo-link\",on:{\"click\":function($event){return _vm.navigateTo('/')}}},[_c('img',{attrs:{\"src\":\"images/logo-tiangong.png\",\"alt\":\"算力租赁\",\"loading\":\"eager\"}})])]),_c('div',{staticClass:\"nav-menu\"},[_c('ul',{staticClass:\"nav-list\"},[_c('li',{staticClass:\"nav-item\"},[_c('a',{staticClass:\"nav-link\",class:{'active': _vm.isActive('/')},on:{\"click\":function($event){return _vm.navigateTo('/')}}},[_vm._v(\"首页\")])]),_c('li',{staticClass:\"nav-item dropdown\"},[_c('a',{staticClass:\"nav-link\",class:{'active': _vm.isActive('/product')},on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_vm._v(\" 算力市场\"),_c('i',{staticClass:\"iconfont icon-arrow-down\"})])]),_c('li',{staticClass:\"nav-item dropdown\"},[_c('a',{staticClass:\"nav-link\",class:{'active': false},on:{\"click\":_vm.triggerComingSoon}},[_vm._v(\"算法社区\"),_c('i',{staticClass:\"iconfont icon-arrow-down\"})])]),_c('li',{staticClass:\"nav-item\"},[_c('a',{staticClass:\"nav-link\",class:{'active': false},on:{\"click\":_vm.triggerComingSoon}},[_vm._v(\"私有云\")])]),_c('li',{staticClass:\"nav-item\"},[_c('a',{staticClass:\"nav-link\",class:{'active': false},on:{\"click\":_vm.triggerComingSoon}},[_vm._v(\"关于我们\")])])])]),_c('div',{staticClass:\"user-actions\"},[(!_vm.isLoggedIn)?_c('div',{staticClass:\"auth-buttons\"},[_c('a',{staticClass:\"btn btn-login\",class:{'active': _vm.isActive('/help')},on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_vm._v(\"帮助文档\")]),_c('a',{staticClass:\"btn btn-login\",class:{'active': _vm.isActive('/login')},on:{\"click\":function($event){return _vm.navigateTo('/login')}}},[_vm._v(\"控制台\")]),_c('a',{staticClass:\"btn btn-login\",class:{'active': _vm.isActive('/login')},on:{\"click\":function($event){return _vm.navigateTo('/login')}}},[_vm._v(\"登录\")]),_c('a',{staticClass:\"btn btn-register\",class:{'active': _vm.isActive('/register')},on:{\"click\":function($event){return _vm.navigateTo('/register')}}},[_vm._v(\"立即注册\")])]):_vm._e(),(_vm.isLoggedIn)?_c('div',{staticClass:\"user-profile\"},[_c('a',{staticClass:\"btn btn-login\",class:{'active': _vm.isActive('/help')},on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_vm._v(\"帮助文档\")]),_c('a',{staticClass:\"btn btn-login\",class:{'active': _vm.isActive('/console'),'disabled': _vm.isConsoleLoading },attrs:{\"title\":_vm.isConsoleLoading ? '控制台加载中，请稍后...' : ''},on:{\"click\":_vm.handleConsoleNavigation}},[_vm._v(\"控制台\")]),_c('div',{staticClass:\"user-dropdown\"},[_c('div',{staticClass:\"user-avatar\"},[_c('div',{staticClass:\"avatar-letter\"},[_vm._v(_vm._s(_vm.userInitial))])]),_c('div',{staticClass:\"dropdown-menu\"},[_c('div',{staticClass:\"user-info-section\"},[_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"用户名:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.userName))])]),_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"手机号:\")]),_c('span',{staticClass:\"value\"},[_c('i',{staticClass:\"copy-icon\"}),_vm._v(\" \"+_vm._s(_vm.userPhone))])]),_c('div',{staticClass:\"verification-tag\",on:{\"click\":_vm.gotoPersonal}},[_c('span',{staticClass:\"check-icon\"}),_vm._v(\" 个人认证 \"),_c('span',{staticClass:\"status-text\"},[_vm._v(\"(\"+_vm._s(_vm.isReal === 1 ? '已认证' : '未认证')+\")\")])]),_c('div',{staticClass:\"detail-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"可用余额:\")]),_c('span',{staticClass:\"value\"},[_vm._v(\"￥\"+_vm._s(_vm.userBalance.toFixed(2)))]),_c('div',{staticClass:\"verification-tag recharge-btn\",on:{\"click\":_vm.navigateToRecharge}},[_vm._v(\" 充值 \")])])]),_c('div',{staticClass:\"menu-options\"},[_c('a',{class:{'active': _vm.isActive('/personal')},on:{\"click\":function($event){return _vm.navigateTo('/personal')}}},[_vm._v(\"个人中心\")]),_c('a',{class:{'active': _vm.isActive('/userorder')},on:{\"click\":function($event){return _vm.navigateTo('/userorder')}}},[_vm._v(\"费用中心\")])]),_c('div',{staticClass:\"logout-button-container\"},[_c('button',{staticClass:\"logout-button\",on:{\"click\":_vm.logout}},[_vm._v(\"退出登录\")])])])])]):_vm._e()])]):_c('div',{staticClass:\"mobile-nav\"},[_c('div',{staticClass:\"mobile-nav-container\"},[_c('button',{staticClass:\"hamburger-btn\",on:{\"click\":_vm.toggleMobileMenu}},[_c('span',{staticClass:\"hamburger-line\",class:{'line-1': _vm.mobileMenuOpen}}),_c('span',{staticClass:\"hamburger-line\",class:{'line-2': _vm.mobileMenuOpen}}),_c('span',{staticClass:\"hamburger-line\",class:{'line-3': _vm.mobileMenuOpen}})]),_c('div',{staticClass:\"mobile-logo-area\"},[_c('a',{staticClass:\"logo-link\",on:{\"click\":function($event){return _vm.navigateTo('/')}}},[_c('img',{attrs:{\"src\":\"images/logo_tiangong.png\",\"alt\":\"算力租赁\"}})])]),_c('div',{staticClass:\"mobile-user-actions\"},[(!_vm.isLoggedIn)?[_c('a',{staticClass:\"mobile-login-btn\",on:{\"click\":function($event){return _vm.navigateTo('/login')}}},[_vm._v(\" 登录 \")]),_c('a',{staticClass:\"mobile-register-btn\",on:{\"click\":function($event){return _vm.navigateTo('/register')}}},[_vm._v(\" 注册 \")])]:[_c('a',{staticClass:\"mobile-console-btn\",on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_vm._v(\" 帮助文档 \")]),_c('a',{staticClass:\"mobile-console-btn\",on:{\"click\":_vm.handleConsoleNavigation}},[_vm._v(\" 控制台 \")]),_c('div',{staticClass:\"mobile-user-profile\",on:{\"click\":_vm.toggleUserMenu}},[_c('div',{staticClass:\"mobile-user-avatar\"},[_c('div',{staticClass:\"avatar-letter\"},[_vm._v(_vm._s(_vm.userInitial))])])])]],2)]),_c('div',{staticClass:\"mobile-menu\",class:{'open': _vm.mobileMenuOpen}},[_c('div',{staticClass:\"mobile-menu-content\"},[_c('ul',{staticClass:\"mobile-nav-list\"},[_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/')},on:{\"click\":function($event){return _vm.navigateTo('/')}}},[_c('i',{staticClass:\"iconfont icon-home\"}),_vm._v(\"首页 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/product')},on:{\"click\":function($event){return _vm.navigateTo('/product')}}},[_c('i',{staticClass:\"iconfont icon-server\"}),_vm._v(\"算力市场 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",on:{\"click\":_vm.triggerComingSoon}},[_c('i',{staticClass:\"iconfont icon-community\"}),_vm._v(\"算法社区 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",on:{\"click\":_vm.triggerComingSoon}},[_c('i',{staticClass:\"iconfont icon-cloud\"}),_vm._v(\"私有云 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/about')},on:{\"click\":function($event){return _vm.navigateTo('/about')}}},[_c('i',{staticClass:\"iconfont icon-info\"}),_vm._v(\"关于我们 \")])]),(!_vm.isLoggedIn)?[_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/help')},on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_c('i',{staticClass:\"iconfont icon-docs\"}),_vm._v(\"帮助文档 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/login')},on:{\"click\":function($event){return _vm.navigateTo('/login')}}},[_c('i',{staticClass:\"iconfont icon-user\"}),_vm._v(\"登录 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/register')},on:{\"click\":function($event){return _vm.navigateTo('/register')}}},[_c('i',{staticClass:\"iconfont icon-edit\"}),_vm._v(\"注册 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/console')},on:{\"click\":function($event){return _vm.navigateTo('/login')}}},[_c('i',{staticClass:\"iconfont icon-console\"}),_vm._v(\"控制台 \")])])]:[_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/help')},on:{\"click\":function($event){return _vm.navigateTo('/help')}}},[_c('i',{staticClass:\"iconfont icon-docs\"}),_vm._v(\"帮助文档 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/console')},on:{\"click\":_vm.handleConsoleNavigation}},[_c('i',{staticClass:\"iconfont icon-console\"}),_vm._v(\"控制台 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",class:{'active': _vm.isActive('/personal')},on:{\"click\":function($event){return _vm.navigateTo('/personal')}}},[_c('i',{staticClass:\"iconfont icon-profile\"}),_vm._v(\"个人中心 \")])]),_c('li',{staticClass:\"mobile-nav-item\"},[_c('a',{staticClass:\"mobile-nav-link\",on:{\"click\":_vm.logout}},[_c('i',{staticClass:\"iconfont icon-logout\"}),_vm._v(\"退出登录 \")])])]],2)])]),(_vm.showUserMenu)?_c('div',{staticClass:\"mobile-user-menu\"},[_c('div',{staticClass:\"mobile-user-info\"},[_c('div',{staticClass:\"mobile-username\"},[_vm._v(_vm._s(_vm.userName))]),_c('div',{staticClass:\"mobile-user-phone\"},[_vm._v(_vm._s(_vm.userPhone))])]),_c('div',{staticClass:\"mobile-menu-options\"},[_c('a',{staticClass:\"mobile-menu-item\",class:{'active': _vm.isActive('/personal')},on:{\"click\":function($event){return _vm.navigateTo('/personal')}}},[_c('i',{staticClass:\"iconfont icon-profile\"}),_vm._v(\"个人中心 \")]),_c('a',{staticClass:\"mobile-menu-item\",class:{'active': _vm.isActive('/userorder')},on:{\"click\":function($event){return _vm.navigateTo('/userorder')}}},[_c('i',{staticClass:\"iconfont icon-order\"}),_vm._v(\"费用中心 \")]),_c('a',{staticClass:\"mobile-menu-item\",class:{'active': false},on:{\"click\":_vm.navigateToRecharge}},[_c('i',{staticClass:\"iconfont icon-recharge\"}),_vm._v(\"充值 \")]),_c('a',{staticClass:\"mobile-menu-item logout\",on:{\"click\":_vm.logout}},[_c('i',{staticClass:\"iconfont icon-logout\"}),_vm._v(\"退出登录 \")])])]):_vm._e()])])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAAEH,GAAG,CAACI,cAAc,GAAEH,EAAE,CAAC,mBAAmB,EAAC;IAACI,KAAK,EAAC;MAAC,SAAS,EAACL,GAAG,CAACM,mBAAmB;MAAC,MAAM,EAAC,SAAS;MAAC,UAAU,EAAC;IAAI,CAAC;IAACC,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;QAACT,GAAG,CAACI,cAAc,GAAG,KAAK;MAAA;IAAC;EAAC,CAAC,CAAC,GAACJ,GAAG,CAACU,EAAE,EAAE,EAACT,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACQ,KAAK,EAAE;MAAEC,MAAM,EAAEZ,GAAG,CAACa,SAAS,GAAG;IAAK;EAAE,CAAC,CAAC,EAACZ,EAAE,CAAC,KAAK,EAAC;IAACa,GAAG,EAAC,SAAS;IAACX,WAAW,EAAC;EAAU,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAAE,CAACH,GAAG,CAACe,QAAQ,GAAEd,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAA2B,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,WAAW;IAACI,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,GAAG,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,KAAK,EAAC;IAACI,KAAK,EAAC;MAAC,KAAK,EAAC,0BAA0B;MAAC,KAAK,EAAC,MAAM;MAAC,SAAS,EAAC;IAAO;EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAU,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAU,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAU,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,UAAU;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,GAAG;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,GAAG,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAmB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,UAAU;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,UAAU;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,UAAU,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAmB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,UAAU;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAE;IAAK,CAAC;IAACX,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACqB;IAAiB;EAAC,CAAC,EAAC,CAACrB,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAU,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,UAAU;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAE;IAAK,CAAC;IAACX,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACqB;IAAiB;EAAC,CAAC,EAAC,CAACrB,GAAG,CAACoB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAU,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,UAAU;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAE;IAAK,CAAC;IAACX,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACqB;IAAiB;EAAC,CAAC,EAAC,CAACrB,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAAE,CAACH,GAAG,CAACsB,UAAU,GAAErB,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,eAAe;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,OAAO;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,OAAO,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,eAAe;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,QAAQ;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,QAAQ,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,eAAe;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,QAAQ;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,QAAQ,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,kBAAkB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,WAAW;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,WAAW,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAACpB,GAAG,CAACU,EAAE,EAAE,EAAEV,GAAG,CAACsB,UAAU,GAAErB,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,eAAe;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,OAAO;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,OAAO,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,eAAe;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,UAAU,CAAC;MAAC,UAAU,EAAEnB,GAAG,CAACuB;IAAiB,CAAC;IAAClB,KAAK,EAAC;MAAC,OAAO,EAACL,GAAG,CAACuB,gBAAgB,GAAG,eAAe,GAAG;IAAE,CAAC;IAAChB,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACwB;IAAuB;EAAC,CAAC,EAAC,CAACxB,GAAG,CAACoB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACH,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACzB,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAmB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAACF,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC;EAAO,CAAC,EAAC,CAACH,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC;EAAO,CAAC,EAAC,CAACH,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC2B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC1B,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAACF,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC;EAAO,CAAC,EAAC,CAACH,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC;EAAO,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,GAAG,GAACpB,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC4B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC3B,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,kBAAkB;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAAC6B;IAAY;EAAC,CAAC,EAAC,CAAC5B,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC;EAAY,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,QAAQ,CAAC,EAACnB,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAACH,GAAG,CAACoB,EAAE,CAAC,GAAG,GAACpB,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC8B,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,GAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC7B,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAACF,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC;EAAO,CAAC,EAAC,CAACH,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC;EAAO,CAAC,EAAC,CAACH,GAAG,CAACoB,EAAE,CAAC,GAAG,GAACpB,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC+B,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC/B,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,+BAA+B;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACiC;IAAkB;EAAC,CAAC,EAAC,CAACjC,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACiB,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,WAAW;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,WAAW,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACiB,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,YAAY;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,YAAY,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAyB,CAAC,EAAC,CAACF,EAAE,CAAC,QAAQ,EAAC;IAACE,WAAW,EAAC,eAAe;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACkC;IAAM;EAAC,CAAC,EAAC,CAAClC,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACpB,GAAG,CAACU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAACT,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAY,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAsB,CAAC,EAAC,CAACF,EAAE,CAAC,QAAQ,EAAC;IAACE,WAAW,EAAC,eAAe;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACmC;IAAgB;EAAC,CAAC,EAAC,CAAClC,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC,gBAAgB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACoC;IAAc;EAAC,CAAC,CAAC,EAACnC,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC,gBAAgB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACoC;IAAc;EAAC,CAAC,CAAC,EAACnC,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC,gBAAgB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACoC;IAAc;EAAC,CAAC,CAAC,CAAC,CAAC,EAACnC,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAkB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,WAAW;IAACI,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,GAAG,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,KAAK,EAAC;IAACI,KAAK,EAAC;MAAC,KAAK,EAAC,0BAA0B;MAAC,KAAK,EAAC;IAAM;EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAqB,CAAC,EAAC,CAAE,CAACH,GAAG,CAACsB,UAAU,GAAE,CAACrB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,kBAAkB;IAACI,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,QAAQ,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,qBAAqB;IAACI,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,WAAW,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAC,CAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,oBAAoB;IAACI,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,OAAO,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACoB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,oBAAoB;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACwB;IAAuB;EAAC,CAAC,EAAC,CAACxB,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,qBAAqB;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACqC;IAAc;EAAC,CAAC,EAAC,CAACpC,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAoB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACH,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAACzB,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,aAAa;IAACe,KAAK,EAAC;MAAC,MAAM,EAAElB,GAAG,CAACoC;IAAc;EAAC,CAAC,EAAC,CAACnC,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAqB,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,GAAG;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,GAAG,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAoB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,UAAU;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,UAAU,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAsB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACqB;IAAiB;EAAC,CAAC,EAAC,CAACpB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAyB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACqB;IAAiB;EAAC,CAAC,EAAC,CAACpB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAqB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,QAAQ;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,QAAQ,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAoB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACpB,GAAG,CAACsB,UAAU,GAAE,CAACrB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,OAAO;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,OAAO,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAoB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,QAAQ;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,QAAQ,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAoB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,WAAW;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,WAAW,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAoB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,UAAU;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,QAAQ,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAuB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,CAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,OAAO;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,OAAO,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAoB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,UAAU;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACwB;IAAuB;EAAC,CAAC,EAAC,CAACvB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAuB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,WAAW;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,WAAW,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAuB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,iBAAiB;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACkC;IAAM;EAAC,CAAC,EAAC,CAACjC,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAsB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEpB,GAAG,CAACsC,YAAY,GAAErC,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAkB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAkB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACH,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC2B,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAC1B,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAmB,CAAC,EAAC,CAACH,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC4B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC3B,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAqB,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,kBAAkB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,WAAW;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,WAAW,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAuB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,kBAAkB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAElB,GAAG,CAACmB,QAAQ,CAAC,YAAY;IAAC,CAAC;IAACZ,EAAE,EAAC;MAAC,OAAO,EAAC,SAAAS,CAASP,MAAM,EAAC;QAAC,OAAOT,GAAG,CAACiB,UAAU,CAAC,YAAY,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAAChB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAqB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,kBAAkB;IAACe,KAAK,EAAC;MAAC,QAAQ,EAAE;IAAK,CAAC;IAACX,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACiC;IAAkB;EAAC,CAAC,EAAC,CAAChC,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAwB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC,yBAAyB;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACkC;IAAM;EAAC,CAAC,EAAC,CAACjC,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAsB,CAAC,CAAC,EAACH,GAAG,CAACoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACpB,GAAG,CAACU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;AACv3T,CAAC;AACD,IAAI6B,eAAe,GAAG,EAAE;AAExB,SAASxC,MAAM,EAAEwC,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}