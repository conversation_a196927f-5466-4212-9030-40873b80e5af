{"version": 3, "file": "js/626.ba709133.js", "mappings": "8JAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACA,EAAG,MAAM,CAACE,YAAY,mBAAmBC,YAAY,CAAC,MAAQ,SAAS,CAACH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACE,YAAY,qBAAqB,CAACJ,EAAIM,GAAG,cAAcJ,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACA,EAAG,cAAc,CAACK,MAAM,CAAC,GAAK,MAAM,CAACP,EAAIM,GAAG,SAAS,GAAGJ,EAAG,KAAK,CAACE,YAAY,aAAa,CAACJ,EAAIM,GAAG,kBAAkBJ,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYC,YAAY,CAAC,YAAY,WAAW,CAACH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACE,YAAY,kBAAkB,CAACJ,EAAIM,GAAG,UAAUJ,EAAG,IAAI,CAACE,YAAY,wBAAwB,CAACJ,EAAIM,GAAG,2CAA2CJ,EAAG,MAAMF,EAAIM,GAAG,mCAAmCJ,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQJ,EAAIQ,GAAIR,EAAIS,SAASC,SAAS,SAASC,EAAQC,GAAO,OAAOV,EAAG,MAAM,CAACW,IAAID,EAAMR,YAAY,gCAAgC,CAACF,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACK,MAAM,CAAC,IAAMI,EAAQG,MAAM,IAAM,QAAQZ,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACA,EAAG,cAAc,CAACK,MAAM,CAAC,GAAK,CAACQ,KAAK,cAAcC,OAAO,CAACC,OAAON,EAAQO,YAAY,IAAM,KAAK,CAAClB,EAAIM,GAAGN,EAAImB,GAAGR,EAAQS,WAAW,GAAGlB,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACE,YAAY,4BAA4B,CAACJ,EAAIM,GAAGN,EAAImB,GAAGR,EAAQU,mBAAmBnB,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,IAAI,CAACF,EAAIM,GAAGN,EAAImB,GAAGR,EAAQW,mBAAmBpB,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,cAAc,CAACE,YAAY,OAAOG,MAAM,CAAC,GAAK,CAACQ,KAAK,cAAcC,OAAO,CAACC,OAAON,EAAQO,cAAc,CAAClB,EAAIM,GAAG,WAAW,MAAM,IAAG,GAAGJ,EAAG,KAAK,CAACE,YAAY,gBAAgBC,YAAY,CAAC,aAAa,WAAW,CAACH,EAAG,KAAK,CAACqB,MAAwB,IAAlBvB,EAAIwB,UAAkB,cAAc,GAAGC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO1B,EAAI2B,YAAY3B,EAAIwB,UAAY,EAAE,IAAI,CAACtB,EAAG,IAAI,CAACK,MAAM,CAAC,KAAO,MAAM,CAACP,EAAIM,GAAG,SAASN,EAAIQ,GAAIR,EAAIS,SAASmB,OAAO,SAASC,EAAEjB,GAAO,OAAOV,EAAG,KAAK,CAACW,IAAID,EAAMW,MAAMvB,EAAIwB,YAAcK,EAAI,YAAY,GAAGJ,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO1B,EAAI2B,YAAYE,EAAE,IAAI,CAAC3B,EAAG,IAAI,CAACK,MAAM,CAAC,KAAO,MAAM,CAACP,EAAIM,GAAGN,EAAImB,GAAGU,OAAO,IAAG3B,EAAG,KAAK,CAACqB,MAAMvB,EAAIwB,YAAcxB,EAAIS,SAASmB,MAAQ,cAAc,GAAGH,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO1B,EAAI2B,YAAY3B,EAAIwB,UAAY,EAAE,IAAI,CAACtB,EAAG,IAAI,CAACK,MAAM,CAAC,KAAO,MAAM,CAACP,EAAIM,GAAG,UAAU,YACz7E,EACIwB,EAAkB,G,UCgFtB,GACAf,KAAA,WACAgB,WAAA,CAAAC,OAAAA,EAAAA,GACAC,OACA,OACAxB,SAAA,GACAe,UAAA,EAEA,EACAU,UACA,KAAAC,WAAA,EACA,EACAC,QAAA,CACAD,WAAAX,GACA,KAAAa,WAAA,iBAAAb,KAAAc,MAAAC,IACAA,IACA,KAAA9B,SAAA8B,EAAAN,KAAAA,KACAO,QAAAC,IAAA,KAAAhC,UAEA,GAEA,EACAkB,YAAAE,GACA,IAAAA,EACA,KAAAL,UAAA,EACAK,IAAA,KAAApB,SAAAmB,MAAA,EACA,KAAAJ,UAAA,KAAAf,SAAAmB,OAEA,KAAAJ,UAAAK,EACA,KAAAM,WAAAN,GAEA,ICjHwP,I,UCOpPa,GAAY,OACd,EACA3C,EACA+B,GACA,EACA,KACA,WACA,MAIF,EAAeY,EAAiB,O", "sources": ["webpack://portal-ui/./src/views/NewsView.vue", "webpack://portal-ui/src/views/NewsView.vue", "webpack://portal-ui/./src/views/NewsView.vue?0f71", "webpack://portal-ui/./src/views/NewsView.vue?978c"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('Layout',[_c('div',{staticClass:\"layout-container\",staticStyle:{\"width\":\"100%\"}},[_c('div',{staticClass:\"page-header\"},[_c('div',{staticClass:\"am-container\"},[_c('h1',{staticClass:\"page-header-title\"},[_vm._v(\"公司动态\")])])]),_c('div',{staticClass:\"breadcrumb-box\"},[_c('div',{staticClass:\"am-container\"},[_c('ol',{staticClass:\"am-breadcrumb\"},[_c('li',[_c('router-link',{attrs:{\"to\":\"/\"}},[_vm._v(\"首页\")])],1),_c('li',{staticClass:\"am-active\"},[_vm._v(\"公司动态\")])])])])]),_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"container\",staticStyle:{\"max-width\":\"1160px\"}},[_c('div',{staticClass:\"section--header\"},[_c('h2',{staticClass:\"section--title\"},[_vm._v(\"最近新闻\")]),_c('p',{staticClass:\"section--description\"},[_vm._v(\" 云适配与中建材信息技术股份有限公司（以下简称“中建信息”）联合举办的“战略 \"),_c('br'),_vm._v(\"合作签约仪式暨全国跨屏行动启动大会”在北京成功举办。 \")])]),_c('div',{staticClass:\"news-contaier\"},[_c('div',{staticClass:\"blog\"},[_c('div',{staticClass:\"am-g\"},_vm._l((_vm.articles.records),function(article,index){return _c('div',{key:index,staticClass:\"am-u-lg-4 am-u-md-6 am-u-end\"},[_c('div',{staticClass:\"article\"},[_c('div',{staticClass:\"article-img\"},[_c('img',{attrs:{\"src\":article.cover,\"alt\":\"\"}})]),_c('div',{staticClass:\"article-header\"},[_c('h2',[_c('router-link',{attrs:{\"to\":{name:'newsDetails',params:{newsId:article.articleId}},\"rel\":\"\"}},[_vm._v(_vm._s(article.title))])],1),_c('ul',{staticClass:\"article--meta\"},[_c('li',{staticClass:\"article--meta_item -date\"},[_vm._v(_vm._s(article.createTime))])])]),_c('div',{staticClass:\"article--content\"},[_c('p',[_vm._v(_vm._s(article.introduction))])]),_c('div',{staticClass:\"article--footer\"},[_c('router-link',{staticClass:\"link\",attrs:{\"to\":{name:'newsDetails',params:{newsId:article.articleId}}}},[_vm._v(\"查看更多\")])],1)])])}),0),_c('ul',{staticClass:\"am-pagination\",staticStyle:{\"text-align\":\"center\"}},[_c('li',{class:_vm.pageIndex === 1 ? 'am-disabled':'',on:{\"click\":function($event){return _vm.changeIndex(_vm.pageIndex - 1)}}},[_c('a',{attrs:{\"href\":\"#\"}},[_vm._v(\"«\")])]),_vm._l((_vm.articles.pages),function(p,index){return _c('li',{key:index,class:_vm.pageIndex === p ? 'am-active':'',on:{\"click\":function($event){return _vm.changeIndex(p)}}},[_c('a',{attrs:{\"href\":\"#\"}},[_vm._v(_vm._s(p))])])}),_c('li',{class:_vm.pageIndex === _vm.articles.pages ? 'am-disabled':'',on:{\"click\":function($event){return _vm.changeIndex(_vm.pageIndex + 1)}}},[_c('a',{attrs:{\"href\":\"#\"}},[_vm._v(\"»\")])])],2)])])])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<Layout>\r\n\t\t<div class=\"layout-container\" style=\"width: 100%\">\r\n\t\t\t<div class=\"page-header\">\r\n\t\t\t\t<div class=\"am-container\">\r\n\t\t\t\t\t<h1 class=\"page-header-title\">公司动态</h1>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t<div class=\"breadcrumb-box\">\r\n\t\t\t\t<div class=\"am-container\">\r\n\t\t\t\t\t<ol class=\"am-breadcrumb\">\r\n\t\t\t\t\t\t<li><router-link to=\"/\">首页</router-link></li>\r\n\t\t\t\t\t\t<li class=\"am-active\">公司动态</li>\r\n\t\t\t\t\t</ol>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<div class=\"section\">\r\n\t\t\t<div class=\"container\" style=\"max-width: 1160px\">\r\n\t\t\t\t<div class=\"section--header\">\r\n\t\t\t\t\t<h2 class=\"section--title\">最近新闻</h2>\r\n\t\t\t\t\t<p class=\"section--description\">\r\n\t\t\t\t\t\t云适配与中建材信息技术股份有限公司（以下简称“中建信息”）联合举办的“战略\r\n\t\t\t\t\t\t<br>合作签约仪式暨全国跨屏行动启动大会”在北京成功举办。\r\n\t\t\t\t\t</p>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div class=\"news-contaier\">\r\n\t\t\t\t\t<div class=\"blog\">\r\n\t\t\t\t\t\t<div class=\"am-g\">\r\n\t\t\t\t\t\t\t<div class=\"am-u-lg-4 am-u-md-6 am-u-end\" v-for=\"(article,index) in articles.records\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<div class=\"article\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"article-img\">\r\n\t\t\t\t\t\t\t\t\t\t<img :src=\"article.cover\" alt=\"\" />\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"article-header\">\r\n\t\t\t\t\t\t\t\t\t\t<h2><router-link :to=\"{name:'newsDetails',params:{newsId:article.articleId}}\" rel=\"\">{{article.title}}</router-link></h2>\r\n\t\t\t\t\t\t\t\t\t\t<ul class=\"article--meta\">\r\n\t\t\t\t\t\t\t\t\t\t\t<li class=\"article--meta_item -date\">{{article.createTime}}</li>\r\n\t\t\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"article--content\">\r\n\t\t\t\t\t\t\t\t\t\t<p>{{article.introduction}}</p>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"article--footer\">\r\n\t\t\t\t\t\t\t\t\t\t<router-link :to=\"{name:'newsDetails',params:{newsId:article.articleId}}\" class=\"link\">查看更多</router-link>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<ul class=\"am-pagination\" style=\"text-align: center;\">\r\n\t\t\t\t\t\t\t<li :class=\"pageIndex === 1 ? 'am-disabled':''\"\r\n\t\t\t\t\t\t\t\t@click=\"changeIndex(pageIndex - 1)\">\r\n\t\t\t\t\t\t\t\t<a href=\"#\">&laquo;</a>\r\n\t\t\t\t\t\t\t</li>\r\n\r\n\t\t\t\t\t\t\t<li v-for=\"(p,index) in articles.pages\"\r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t\t@click=\"changeIndex(p)\"\r\n\t\t\t\t\t\t\t\t:class=\"pageIndex === p ? 'am-active':''\">\r\n\t\t\t\t\t\t\t\t<a href=\"#\">{{p}}</a>\r\n\t\t\t\t\t\t\t</li>\r\n\r\n\t\t\t\t\t\t\t<li :class=\"pageIndex === articles.pages ? 'am-disabled':''\"\r\n\t\t\t\t\t\t\t\t@click=\"changeIndex(pageIndex + 1)\">\r\n\t\t\t\t\t\t\t\t<a href=\"#\">&raquo;</a>\r\n\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nexport default {\r\n\tname: \"NewsView\",\r\n\tcomponents: {Layout},\r\n\tdata(){\r\n\t\treturn{\r\n\t\t\tarticles: {},\r\n\t\t\tpageIndex: 1,\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.getArticle(1)\r\n\t},\r\n\tmethods:{\r\n\t\tgetArticle(pageIndex){\r\n\t\t\tthis.getRequest(`/findArticles/${pageIndex}`).then(resp =>{\r\n\t\t\t\tif (resp){\r\n\t\t\t\t\tthis.articles = resp.data.data\r\n\t\t\t\t\tconsole.log(this.articles)\r\n\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tchangeIndex(p){\r\n\t\t\tif (p === 0){\r\n\t\t\t\tthis.pageIndex = 1\r\n\t\t\t}else if (p === this.articles.pages + 1){\r\n\t\t\t\tthis.pageIndex = this.articles.pages\r\n\t\t\t}else{\r\n\t\t\t\tthis.pageIndex = p;\r\n\t\t\t\tthis.getArticle(p)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./NewsView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./NewsView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./NewsView.vue?vue&type=template&id=33f898a4&scoped=true&\"\nimport script from \"./NewsView.vue?vue&type=script&lang=js&\"\nexport * from \"./NewsView.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"33f898a4\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "staticStyle", "_v", "attrs", "_l", "articles", "records", "article", "index", "key", "cover", "name", "params", "newsId", "articleId", "_s", "title", "createTime", "introduction", "class", "pageIndex", "on", "$event", "changeIndex", "pages", "p", "staticRenderFns", "components", "Layout", "data", "mounted", "getArticle", "methods", "getRequest", "then", "resp", "console", "log", "component"], "sourceRoot": ""}