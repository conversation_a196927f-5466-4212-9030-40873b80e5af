{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('Layout', [_c('div', {\n    staticClass: \"layout-container\",\n    staticStyle: {\n      \"width\": \"100%\"\n    }\n  }, [_c('div', {\n    staticClass: \"page-header\",\n    staticStyle: {\n      \"max-height\": \"14px\"\n    }\n  }), _c('div', {\n    staticClass: \"breadcrumb-box\"\n  }, [_c('div', {\n    staticClass: \"am-container\"\n  }, [_c('ol', {\n    staticClass: \"am-breadcrumb\"\n  }, [_c('li', [_c('router-link', {\n    attrs: {\n      \"to\": \"/news\"\n    }\n  }, [_vm._v(\"公司动态\")])], 1), _c('li', {\n    staticClass: \"am-active\"\n  }, [_vm._v(\"文章详情\")])])])])]), _c('div', {\n    staticClass: \"section\"\n  }, [_c('div', {\n    staticClass: \"container\",\n    staticStyle: {\n      \"max-width\": \"1160px\"\n    }\n  }, [_c('div', {\n    staticClass: \"section--header\"\n  }, [_c('h2', {\n    staticClass: \"section--title\"\n  }, [_vm._v(_vm._s(_vm.article.title))]), _c('p', {\n    staticClass: \"section--description\"\n  }, [_vm._v(_vm._s(_vm.article.introduction))])]), _c('div', {\n    staticClass: \"join-container\"\n  }, [_c('div', {\n    staticClass: \"am-g\"\n  }, [_c('div', {\n    staticClass: \"am-u-md-3\"\n  }, [_c('div', {\n    staticClass: \"careers--articles\"\n  }, [_c('div', {\n    staticClass: \"careers_articles\"\n  }, _vm._l(_vm.recentArticles, function (article, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"careers_article\"\n    }, [_c('div', {\n      staticClass: \"image\"\n    }, [_c('img', {\n      staticStyle: {\n        \"height\": \"160px\"\n      },\n      attrs: {\n        \"src\": article.cover,\n        \"alt\": \"\"\n      }\n    })]), _c('h3', {\n      staticClass: \"careers_article--title\"\n    }, [_vm._v(_vm._s(article.title))]), _c('div', {\n      staticClass: \"careers_article--text\"\n    }, [_vm._v(_vm._s(article.introduction))]), _c('div', {\n      staticClass: \"careers_article--footer\"\n    }, [_c('router-link', {\n      staticClass: \"link\",\n      attrs: {\n        \"to\": {\n          name: 'newsDetails',\n          params: {\n            newsId: article.articleId\n          }\n        }\n      }\n    }, [_vm._v(\"查看更多\")])], 1)]);\n  }), 0)])]), _c('div', {\n    staticClass: \"am-u-md-9\"\n  }, [_c('div', {\n    staticClass: \"careers--vacancies\"\n  }, [_c('div', {\n    staticClass: \"am-panel-group\",\n    attrs: {\n      \"id\": \"accordion\"\n    }\n  }, [_c('div', {\n    staticClass: \"am-panel am-panel-default\"\n  }, [_c('div', {\n    staticClass: \"am-panel-hd\"\n  }, [_c('h4', {\n    staticClass: \"am-panel-title\",\n    attrs: {\n      \"data-am-collapse\": \"{parent: '#accordion', target: '#do-not-say-1'}\"\n    }\n  }, [_vm._v(\" 作者：\" + _vm._s(_vm.article.author) + \"      /      发布时间：\" + _vm._s(_vm.article.createTime) + \" \")])]), _c('div', {\n    staticClass: \"am-panel-collapse am-collapse am-in\"\n  }, [_c('div', {\n    staticClass: \"am-panel-bd\"\n  }, [_c('div', {\n    staticClass: \"vacancies--item_content js-accordion--pane_content\",\n    domProps: {\n      \"innerHTML\": _vm._s(_vm.article.contentHtml)\n    }\n  })])])])])])])])])])])]);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "attrs", "_v", "_s", "article", "title", "introduction", "_l", "recentArticles", "index", "key", "cover", "name", "params", "newsId", "articleId", "author", "createTime", "domProps", "contentHtml", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/views/NewsDetailsView.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('Layout',[_c('div',{staticClass:\"layout-container\",staticStyle:{\"width\":\"100%\"}},[_c('div',{staticClass:\"page-header\",staticStyle:{\"max-height\":\"14px\"}}),_c('div',{staticClass:\"breadcrumb-box\"},[_c('div',{staticClass:\"am-container\"},[_c('ol',{staticClass:\"am-breadcrumb\"},[_c('li',[_c('router-link',{attrs:{\"to\":\"/news\"}},[_vm._v(\"公司动态\")])],1),_c('li',{staticClass:\"am-active\"},[_vm._v(\"文章详情\")])])])])]),_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"container\",staticStyle:{\"max-width\":\"1160px\"}},[_c('div',{staticClass:\"section--header\"},[_c('h2',{staticClass:\"section--title\"},[_vm._v(_vm._s(_vm.article.title))]),_c('p',{staticClass:\"section--description\"},[_vm._v(_vm._s(_vm.article.introduction))])]),_c('div',{staticClass:\"join-container\"},[_c('div',{staticClass:\"am-g\"},[_c('div',{staticClass:\"am-u-md-3\"},[_c('div',{staticClass:\"careers--articles\"},[_c('div',{staticClass:\"careers_articles\"},_vm._l((_vm.recentArticles),function(article,index){return _c('div',{key:index,staticClass:\"careers_article\"},[_c('div',{staticClass:\"image\"},[_c('img',{staticStyle:{\"height\":\"160px\"},attrs:{\"src\":article.cover,\"alt\":\"\"}})]),_c('h3',{staticClass:\"careers_article--title\"},[_vm._v(_vm._s(article.title))]),_c('div',{staticClass:\"careers_article--text\"},[_vm._v(_vm._s(article.introduction))]),_c('div',{staticClass:\"careers_article--footer\"},[_c('router-link',{staticClass:\"link\",attrs:{\"to\":{name:'newsDetails',params:{newsId:article.articleId}}}},[_vm._v(\"查看更多\")])],1)])}),0)])]),_c('div',{staticClass:\"am-u-md-9\"},[_c('div',{staticClass:\"careers--vacancies\"},[_c('div',{staticClass:\"am-panel-group\",attrs:{\"id\":\"accordion\"}},[_c('div',{staticClass:\"am-panel am-panel-default\"},[_c('div',{staticClass:\"am-panel-hd\"},[_c('h4',{staticClass:\"am-panel-title\",attrs:{\"data-am-collapse\":\"{parent: '#accordion', target: '#do-not-say-1'}\"}},[_vm._v(\" 作者：\"+_vm._s(_vm.article.author)+\"      /      发布时间：\"+_vm._s(_vm.article.createTime)+\" \")])]),_c('div',{staticClass:\"am-panel-collapse am-collapse am-in\"},[_c('div',{staticClass:\"am-panel-bd\"},[_c('div',{staticClass:\"vacancies--item_content js-accordion--pane_content\",domProps:{\"innerHTML\":_vm._s(_vm.article.contentHtml)}})])])])])])])])])])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,QAAQ,EAAC,CAACA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,kBAAkB;IAACC,WAAW,EAAC;MAAC,OAAO,EAAC;IAAM;EAAC,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,aAAa;IAACC,WAAW,EAAC;MAAC,YAAY,EAAC;IAAM;EAAC,CAAC,CAAC,EAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,aAAa,EAAC;IAACI,KAAK,EAAC;MAAC,IAAI,EAAC;IAAO;EAAC,CAAC,EAAC,CAACL,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACL,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACH,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACL,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAS,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,WAAW;IAACC,WAAW,EAAC;MAAC,WAAW,EAAC;IAAQ;EAAC,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACH,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,EAACR,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAsB,CAAC,EAAC,CAACH,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,OAAO,CAACE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACT,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAM,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAmB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAkB,CAAC,EAACH,GAAG,CAACW,EAAE,CAAEX,GAAG,CAACY,cAAc,EAAE,UAASJ,OAAO,EAACK,KAAK,EAAC;IAAC,OAAOZ,EAAE,CAAC,KAAK,EAAC;MAACa,GAAG,EAACD,KAAK;MAACV,WAAW,EAAC;IAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAO,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;MAACG,WAAW,EAAC;QAAC,QAAQ,EAAC;MAAO,CAAC;MAACC,KAAK,EAAC;QAAC,KAAK,EAACG,OAAO,CAACO,KAAK;QAAC,KAAK,EAAC;MAAE;IAAC,CAAC,CAAC,CAAC,CAAC,EAACd,EAAE,CAAC,IAAI,EAAC;MAACE,WAAW,EAAC;IAAwB,CAAC,EAAC,CAACH,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,EAACR,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAuB,CAAC,EAAC,CAACH,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACC,OAAO,CAACE,YAAY,CAAC,CAAC,CAAC,CAAC,EAACT,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAyB,CAAC,EAAC,CAACF,EAAE,CAAC,aAAa,EAAC;MAACE,WAAW,EAAC,MAAM;MAACE,KAAK,EAAC;QAAC,IAAI,EAAC;UAACW,IAAI,EAAC,aAAa;UAACC,MAAM,EAAC;YAACC,MAAM,EAACV,OAAO,CAACW;UAAS;QAAC;MAAC;IAAC,CAAC,EAAC,CAACnB,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACL,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAoB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,gBAAgB;IAACE,KAAK,EAAC;MAAC,IAAI,EAAC;IAAW;EAAC,CAAC,EAAC,CAACJ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAA2B,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC,gBAAgB;IAACE,KAAK,EAAC;MAAC,kBAAkB,EAAC;IAAiD;EAAC,CAAC,EAAC,CAACL,GAAG,CAACM,EAAE,CAAC,MAAM,GAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,OAAO,CAACY,MAAM,CAAC,GAAC,oBAAoB,GAACpB,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,OAAO,CAACa,UAAU,CAAC,GAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAACpB,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAqC,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,oDAAoD;IAACmB,QAAQ,EAAC;MAAC,WAAW,EAACtB,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,OAAO,CAACe,WAAW;IAAC;EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/tE,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AAExB,SAASzB,MAAM,EAAEyB,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}