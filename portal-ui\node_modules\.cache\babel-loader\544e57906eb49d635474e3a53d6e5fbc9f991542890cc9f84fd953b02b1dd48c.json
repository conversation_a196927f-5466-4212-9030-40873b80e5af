{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('Layout', [_c('div', {\n    staticClass: \"layout-container\",\n    staticStyle: {\n      \"width\": \"100%\"\n    }\n  }, [_c('div', {\n    staticClass: \"page-header\"\n  }, [_c('div', {\n    staticClass: \"am-container\"\n  }, [_c('h1', {\n    staticClass: \"page-header-title\"\n  }, [_vm._v(\"客户案例\")])])]), _c('div', {\n    staticClass: \"breadcrumb-box\"\n  }, [_c('div', {\n    staticClass: \"am-container\"\n  }, [_c('ol', {\n    staticClass: \"am-breadcrumb\"\n  }, [_c('li', [_c('router-link', {\n    attrs: {\n      \"to\": \"/\"\n    }\n  }, [_vm._v(\"首页\")])], 1), _c('li', {\n    staticClass: \"am-active\"\n  }, [_vm._v(\"客户案例\")])])])])]), _c('div', {\n    staticClass: \"section example\"\n  }, [_c('div', {\n    staticClass: \"container\",\n    staticStyle: {\n      \"max-width\": \"1160px\",\n      \"margin\": \"0 auto\"\n    }\n  }, [_c('div', {\n    staticClass: \"section--header\"\n  }, [_c('h2', {\n    staticClass: \"section--title\"\n  }, [_vm._v(\"全球首创 自主创新\")]), _c('p', {\n    staticClass: \"section--description\"\n  }, [_vm._v(\" Enterplorer Studio是一套面向企业级移动信息化建设的开发平台。集聚开发、测试、 \"), _c('br'), _vm._v(\"打包、发布于一体的移动化开发综合平台。 \")])]), _c('div', {\n    staticClass: \"example-container\"\n  }, [_c('div', {\n    staticClass: \"am-tabs\"\n  }, [_c('ul', {\n    staticClass: \"am-tabs-nav am-nav am-nav-tabs am-g\"\n  }, _vm._l(_vm.tabList, function (tab, index) {\n    return _c('li', {\n      key: index,\n      staticClass: \"am-u-md-2\",\n      class: _vm.tabIndex === index ? 'am-active' : '',\n      on: {\n        \"click\": function ($event) {\n          $event.preventDefault();\n          return _vm.changeTab(index);\n        }\n      }\n    }, [_c('a', {\n      attrs: {\n        \"href\": \"#\"\n      }\n    }, [_c('i', {\n      class: tab.icon\n    }), _vm._v(_vm._s(tab.name))])]);\n  }), 0), _c('div', {\n    staticClass: \"tabs\"\n  }, _vm._l(_vm.list, function (image, index) {\n    return _c('div', {\n      key: index,\n      staticClass: \"tab\"\n    }, [_c('img', {\n      attrs: {\n        \"src\": image,\n        \"alt\": \"\"\n      }\n    })]);\n  }), 0)])])])])]);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "_v", "attrs", "_l", "tabList", "tab", "index", "key", "class", "tabIndex", "on", "click", "$event", "preventDefault", "changeTab", "icon", "_s", "name", "list", "image", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/views/ExampleView.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('Layout',[_c('div',{staticClass:\"layout-container\",staticStyle:{\"width\":\"100%\"}},[_c('div',{staticClass:\"page-header\"},[_c('div',{staticClass:\"am-container\"},[_c('h1',{staticClass:\"page-header-title\"},[_vm._v(\"客户案例\")])])]),_c('div',{staticClass:\"breadcrumb-box\"},[_c('div',{staticClass:\"am-container\"},[_c('ol',{staticClass:\"am-breadcrumb\"},[_c('li',[_c('router-link',{attrs:{\"to\":\"/\"}},[_vm._v(\"首页\")])],1),_c('li',{staticClass:\"am-active\"},[_vm._v(\"客户案例\")])])])])]),_c('div',{staticClass:\"section example\"},[_c('div',{staticClass:\"container\",staticStyle:{\"max-width\":\"1160px\",\"margin\":\"0 auto\"}},[_c('div',{staticClass:\"section--header\"},[_c('h2',{staticClass:\"section--title\"},[_vm._v(\"全球首创 自主创新\")]),_c('p',{staticClass:\"section--description\"},[_vm._v(\" Enterplorer Studio是一套面向企业级移动信息化建设的开发平台。集聚开发、测试、 \"),_c('br'),_vm._v(\"打包、发布于一体的移动化开发综合平台。 \")])]),_c('div',{staticClass:\"example-container\"},[_c('div',{staticClass:\"am-tabs\"},[_c('ul',{staticClass:\"am-tabs-nav am-nav am-nav-tabs am-g\"},_vm._l((_vm.tabList),function(tab,index){return _c('li',{key:index,staticClass:\"am-u-md-2\",class:_vm.tabIndex === index ? 'am-active':'',on:{\"click\":function($event){$event.preventDefault();return _vm.changeTab(index)}}},[_c('a',{attrs:{\"href\":\"#\"}},[_c('i',{class:tab.icon}),_vm._v(_vm._s(tab.name))])])}),0),_c('div',{staticClass:\"tabs\"},_vm._l((_vm.list),function(image,index){return _c('div',{key:index,staticClass:\"tab\"},[_c('img',{attrs:{\"src\":image,\"alt\":\"\"}})])}),0)])])])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,QAAQ,EAAC,CAACA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,kBAAkB;IAACC,WAAW,EAAC;MAAC,OAAO,EAAC;IAAM;EAAC,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAmB,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,aAAa,EAAC;IAACK,KAAK,EAAC;MAAC,IAAI,EAAC;IAAG;EAAC,CAAC,EAAC,CAACN,GAAG,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,WAAW;IAACC,WAAW,EAAC;MAAC,WAAW,EAAC,QAAQ;MAAC,QAAQ,EAAC;IAAQ;EAAC,CAAC,EAAC,CAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAsB,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,mDAAmD,CAAC,EAACJ,EAAE,CAAC,IAAI,CAAC,EAACD,GAAG,CAACK,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAmB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAS,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAqC,CAAC,EAACH,GAAG,CAACO,EAAE,CAAEP,GAAG,CAACQ,OAAO,EAAE,UAASC,GAAG,EAACC,KAAK,EAAC;IAAC,OAAOT,EAAE,CAAC,IAAI,EAAC;MAACU,GAAG,EAACD,KAAK;MAACP,WAAW,EAAC,WAAW;MAACS,KAAK,EAACZ,GAAG,CAACa,QAAQ,KAAKH,KAAK,GAAG,WAAW,GAAC,EAAE;MAACI,EAAE,EAAC;QAAC,OAAO,EAAC,SAAAC,CAASC,MAAM,EAAC;UAACA,MAAM,CAACC,cAAc,EAAE;UAAC,OAAOjB,GAAG,CAACkB,SAAS,CAACR,KAAK,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAACT,EAAE,CAAC,GAAG,EAAC;MAACK,KAAK,EAAC;QAAC,MAAM,EAAC;MAAG;IAAC,CAAC,EAAC,CAACL,EAAE,CAAC,GAAG,EAAC;MAACW,KAAK,EAACH,GAAG,CAACU;IAAI,CAAC,CAAC,EAACnB,GAAG,CAACK,EAAE,CAACL,GAAG,CAACoB,EAAE,CAACX,GAAG,CAACY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,EAACpB,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAM,CAAC,EAACH,GAAG,CAACO,EAAE,CAAEP,GAAG,CAACsB,IAAI,EAAE,UAASC,KAAK,EAACb,KAAK,EAAC;IAAC,OAAOT,EAAE,CAAC,KAAK,EAAC;MAACU,GAAG,EAACD,KAAK;MAACP,WAAW,EAAC;IAAK,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;MAACK,KAAK,EAAC;QAAC,KAAK,EAACiB,KAAK;QAAC,KAAK,EAAC;MAAE;IAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvgD,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AAExB,SAASzB,MAAM,EAAEyB,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}