{"ast": null, "code": "import Cookies from 'js-cookie';\nexport default {\n  name: \"Console\",\n  mounted() {\n    const token = this.getCookie('Admin-Token');\n    const isReal = this.getCookie('isReal');\n    if (!token || isReal !== '1') {\n      console.warn('[ConsolePage] 鉴权失败：未登录 / 未实名 跳转首页');\n      window.location.hash = '#/index';\n    }\n\n    // 监听iframe回传的消息\n    // window.addEventListener('message', (event) => {\n    //     console.log('=== 收到iframe回传消息 ===');\n    //     console.log('消息来源:', event.origin);\n    //     console.log('消息数据:', event.data);\n    //     console.log('消息类型:', typeof event.data);\n    //     console.log('时间戳:', new Date().toLocaleTimeString());\n    //     console.log('========================');\n    // }\n    // );\n\n    this.$nextTick(() => {\n      const iframe = this.$refs.iframe;\n      if (iframe) {\n        iframe.addEventListener('load', () => {\n          setTimeout(() => {\n            this.handleIframeLoad();\n          }, 500);\n        }, {\n          once: true\n        });\n      }\n    });\n  },\n  data() {\n    return {\n      error: null,\n      keyPair: null,\n      // 生产环境替换为 https://suanli.cn?type=other\n      iframeSrc: null,\n      config: {},\n      iframeKey: 0,\n      hasPostedMessage: false\n    };\n  },\n  created() {\n    this.refreshIframe();\n  },\n  methods: {\n    // 强制禁用浏览器缓存\n    refreshIframe() {\n      Object.keys(localStorage).filter(key => key.startsWith('Hm_')).forEach(key => localStorage.removeItem(key));\n      // 同时更新 key 和 src\n      this.iframeKey += 1;\n      this.hasPostedMessage = false; // 重置发送标志\n      this.iframeSrc = `https://console.suanli.cn/serverless/idc?type=other&isHiddenPrice=true`;\n      if (this.config.token) {\n        this.config = {\n          \"token\": Cookies.get('suanlemeToken'),\n          \"rsa_pubk\": Cookies.get('publicKey-C'),\n          \"rsa_prik\": Cookies.get('privateKey-B')\n        };\n      }\n      // 确保 config 同步更新\n      this.config = {\n        token: Cookies.get('suanlemeToken'),\n        rsa_pubk: Cookies.get('publicKey-C'),\n        rsa_prik: Cookies.get('privateKey-B')\n      };\n    },\n    handleIframeLoad() {\n      const iframe = this.$refs.iframe;\n      if (iframe && iframe.contentWindow) {\n        // 防止重复发送\n        if (this.hasPostedMessage) {\n          // console.log('已发送过认证数据，跳过重复发送');\n          return;\n        }\n\n        // 校验三个关键参数\n        const token = this.config.token;\n        const publicKey = this.config.rsa_pubk;\n        const privateKey = this.config.rsa_prik;\n\n        // console.log('=== iframe数据校验 ===');\n        // console.log('Token:', token ? '存在' : '缺失',token);\n        // console.log('公钥C:', publicKey ? '存在' : '缺失',publicKey);\n        // console.log('私钥B:', privateKey ? '存在' : '缺失',privateKey);\n\n        // 三个参数都存在才发送\n        if (token && publicKey && privateKey) {\n          // console.log('数据完整，发送给iframe');\n          // console.log('=== 发送数据详情 ===');\n          // console.log('发送时间:', new Date().toLocaleTimeString());\n          // console.log('iframe URL:', iframe.src);\n\n          // 转换为纯JavaScript对象，避免Vue响应式包装\n          const pureData = {\n            token: token,\n            rsa_pubk: publicKey,\n            rsa_prik: privateKey\n          };\n\n          // console.log('发送的完整数据:', pureData);\n          // console.log('JSON序列化测试:', JSON.stringify(pureData));\n          // console.log('数据类型检查:', typeof pureData);\n          // console.log('是否为纯对象:', pureData.constructor === Object);\n          // console.log('==================');\n\n          iframe.contentWindow.postMessage(pureData, '*');\n          this.hasPostedMessage = true; // 标记已发送\n\n          console.log(' postMessage已发送');\n\n          // 手动触发按钮状态更新\n          if (this.$parent && this.$parent.$refs && this.$parent.$refs.header) {\n            this.$parent.$refs.header.isConsoleReady = true;\n            this.$parent.$refs.header.isConsoleLoading = false;\n          }\n        } else {\n          // console.log('认证数据不完整，延迟重试...');\n          // 数据不完整时，延迟重试\n          setTimeout(() => {\n            // console.log('重试获取认证数据...');\n            // 重新获取Cookie数据\n            this.config = {\n              token: Cookies.get('suanlemeToken'),\n              rsa_pubk: Cookies.get('publicKey-C'),\n              rsa_prik: Cookies.get('privateKey-B')\n            };\n            // 递归重试\n            this.handleIframeLoad();\n          }, 1000);\n        }\n      }\n    },\n    getCookie(name) {\n      const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));\n      return match ? match[2] : null;\n    }\n  },\n  beforeDestroy() {}\n};", "map": {"version": 3, "names": ["Cookies", "name", "mounted", "token", "<PERSON><PERSON><PERSON><PERSON>", "isReal", "console", "warn", "window", "location", "hash", "$nextTick", "iframe", "$refs", "addEventListener", "setTimeout", "handleIframeLoad", "once", "data", "error", "keyPair", "iframeSrc", "config", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasPostedMessage", "created", "refreshIframe", "methods", "Object", "keys", "localStorage", "filter", "key", "startsWith", "for<PERSON>ach", "removeItem", "get", "rsa_pubk", "rsa_prik", "contentWindow", "public<PERSON>ey", "privateKey", "pureData", "postMessage", "log", "$parent", "header", "isConsoleReady", "isConsoleLoading", "match", "document", "cookie", "RegExp", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/Console.vue"], "sourcesContent": ["<template>\r\n  <div class=\"iframe-page\">\r\n    <div class=\"iframe-container\">\r\n      <iframe\r\n          :key=\"iframeKey\"\r\n          ref=\"iframe\"\r\n          :src=\"iframeSrc\"\r\n      ></iframe>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Cookies from 'js-cookie'\r\nexport default {\r\n  name: \"Console\",\r\n\r\n  mounted() {\r\n    const token = this.getCookie('Admin-Token');\r\n    const isReal = this.getCookie('isReal');\r\n\r\n    if (!token || isReal !== '1' ) {\r\n      console.warn('[ConsolePage] 鉴权失败：未登录 / 未实名 跳转首页');\r\n      window.location.hash = '#/index';\r\n    }\r\n\r\n    // 监听iframe回传的消息\r\n    // window.addEventListener('message', (event) => {\r\n    //     console.log('=== 收到iframe回传消息 ===');\r\n    //     console.log('消息来源:', event.origin);\r\n    //     console.log('消息数据:', event.data);\r\n    //     console.log('消息类型:', typeof event.data);\r\n    //     console.log('时间戳:', new Date().toLocaleTimeString());\r\n    //     console.log('========================');\r\n    // }\r\n    // );\r\n\r\n    this.$nextTick(() => {\r\n      const iframe = this.$refs.iframe;\r\n      if (iframe) {\r\n        iframe.addEventListener('load', () => {\r\n          setTimeout(() => {\r\n            this.handleIframeLoad();\r\n          }, 500);\r\n        }, { once: true });\r\n      }\r\n    });\r\n  },\r\n  data(){\r\n    return{\r\n      error:null,\r\n      keyPair:null,\r\n      // 生产环境替换为 https://suanli.cn?type=other\r\n      iframeSrc: null,\r\n      config: {\r\n      },\r\n      iframeKey: 0,\r\n      hasPostedMessage: false,\r\n    }\r\n  },\r\n  created() {\r\n    this.refreshIframe()\r\n  },\r\n  methods:{\r\n    // 强制禁用浏览器缓存\r\n    refreshIframe() {\r\n      Object.keys(localStorage)\r\n          .filter(key => key.startsWith('Hm_'))\r\n          .forEach(key => localStorage.removeItem(key));\r\n      // 同时更新 key 和 src\r\n      this.iframeKey += 1;\r\n      this.hasPostedMessage = false; // 重置发送标志\r\n      this.iframeSrc = `https://console.suanli.cn/serverless/idc?type=other&isHiddenPrice=true`;\r\n      if (this.config.token){\r\n        this.config={\r\n          \"token\":Cookies.get('suanlemeToken'),\r\n          \"rsa_pubk\":Cookies.get('publicKey-C'),\r\n          \"rsa_prik\":Cookies.get('privateKey-B'),\r\n        }\r\n\r\n      }\r\n      // 确保 config 同步更新\r\n      this.config = {\r\n        token: Cookies.get('suanlemeToken'),\r\n        rsa_pubk: Cookies.get('publicKey-C'),\r\n        rsa_prik: Cookies.get('privateKey-B'),\r\n      };\r\n\r\n    },\r\n    handleIframeLoad() {\r\n      const iframe = this.$refs.iframe\r\n      if (iframe && iframe.contentWindow) {\r\n        // 防止重复发送\r\n        if (this.hasPostedMessage) {\r\n          // console.log('已发送过认证数据，跳过重复发送');\r\n          return;\r\n        }\r\n\r\n        // 校验三个关键参数\r\n        const token = this.config.token;\r\n        const publicKey = this.config.rsa_pubk;\r\n        const privateKey = this.config.rsa_prik;\r\n\r\n        // console.log('=== iframe数据校验 ===');\r\n        // console.log('Token:', token ? '存在' : '缺失',token);\r\n        // console.log('公钥C:', publicKey ? '存在' : '缺失',publicKey);\r\n        // console.log('私钥B:', privateKey ? '存在' : '缺失',privateKey);\r\n\r\n        // 三个参数都存在才发送\r\n        if (token && publicKey && privateKey) {\r\n          // console.log('数据完整，发送给iframe');\r\n          // console.log('=== 发送数据详情 ===');\r\n          // console.log('发送时间:', new Date().toLocaleTimeString());\r\n          // console.log('iframe URL:', iframe.src);\r\n\r\n          // 转换为纯JavaScript对象，避免Vue响应式包装\r\n          const pureData = {\r\n            token: token,\r\n            rsa_pubk: publicKey,\r\n            rsa_prik: privateKey\r\n          };\r\n\r\n          // console.log('发送的完整数据:', pureData);\r\n          // console.log('JSON序列化测试:', JSON.stringify(pureData));\r\n          // console.log('数据类型检查:', typeof pureData);\r\n          // console.log('是否为纯对象:', pureData.constructor === Object);\r\n          // console.log('==================');\r\n\r\n          iframe.contentWindow.postMessage(pureData, '*');\r\n          this.hasPostedMessage = true; // 标记已发送\r\n\r\n          console.log(' postMessage已发送');\r\n\r\n          // 手动触发按钮状态更新\r\n          if (this.$parent && this.$parent.$refs && this.$parent.$refs.header) {\r\n            this.$parent.$refs.header.isConsoleReady = true;\r\n            this.$parent.$refs.header.isConsoleLoading = false;\r\n          }\r\n        } else {\r\n          // console.log('认证数据不完整，延迟重试...');\r\n          // 数据不完整时，延迟重试\r\n          setTimeout(() => {\r\n            // console.log('重试获取认证数据...');\r\n            // 重新获取Cookie数据\r\n            this.config = {\r\n              token: Cookies.get('suanlemeToken'),\r\n              rsa_pubk: Cookies.get('publicKey-C'),\r\n              rsa_prik: Cookies.get('privateKey-B'),\r\n            };\r\n            // 递归重试\r\n            this.handleIframeLoad();\r\n          }, 1000);\r\n        }\r\n\r\n      }\r\n    },\r\n    getCookie(name) {\r\n      const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));\r\n      return match ? match[2] : null;\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.iframe-page {\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n.header {\r\n  width: 100%;\r\n  height: 60px;\r\n  background-color: #1a73e8;\r\n  color: #ffffff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.content {\r\n  flex: 1;\r\n  display: flex;\r\n}\r\n\r\n.sidebar {\r\n  width: 200px;\r\n  background-color: #000000;\r\n  color: #ffffff;\r\n  padding: 20px;\r\n}\r\n\r\n.iframe-container {\r\n  flex: 1;\r\n}\r\n\r\niframe {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n}\r\n</style>\r\n"], "mappings": "AAaA,OAAAA,OAAA;AACA;EACAC,IAAA;EAEAC,QAAA;IACA,MAAAC,KAAA,QAAAC,SAAA;IACA,MAAAC,MAAA,QAAAD,SAAA;IAEA,KAAAD,KAAA,IAAAE,MAAA;MACAC,OAAA,CAAAC,IAAA;MACAC,MAAA,CAAAC,QAAA,CAAAC,IAAA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,KAAAC,SAAA;MACA,MAAAC,MAAA,QAAAC,KAAA,CAAAD,MAAA;MACA,IAAAA,MAAA;QACAA,MAAA,CAAAE,gBAAA;UACAC,UAAA;YACA,KAAAC,gBAAA;UACA;QACA;UAAAC,IAAA;QAAA;MACA;IACA;EACA;EACAC,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACA;MACAC,SAAA;MACAC,MAAA,GACA;MACAC,SAAA;MACAC,gBAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA;IACAD,cAAA;MACAE,MAAA,CAAAC,IAAA,CAAAC,YAAA,EACAC,MAAA,CAAAC,GAAA,IAAAA,GAAA,CAAAC,UAAA,SACAC,OAAA,CAAAF,GAAA,IAAAF,YAAA,CAAAK,UAAA,CAAAH,GAAA;MACA;MACA,KAAAT,SAAA;MACA,KAAAC,gBAAA;MACA,KAAAH,SAAA;MACA,SAAAC,MAAA,CAAAnB,KAAA;QACA,KAAAmB,MAAA;UACA,SAAAtB,OAAA,CAAAoC,GAAA;UACA,YAAApC,OAAA,CAAAoC,GAAA;UACA,YAAApC,OAAA,CAAAoC,GAAA;QACA;MAEA;MACA;MACA,KAAAd,MAAA;QACAnB,KAAA,EAAAH,OAAA,CAAAoC,GAAA;QACAC,QAAA,EAAArC,OAAA,CAAAoC,GAAA;QACAE,QAAA,EAAAtC,OAAA,CAAAoC,GAAA;MACA;IAEA;IACApB,iBAAA;MACA,MAAAJ,MAAA,QAAAC,KAAA,CAAAD,MAAA;MACA,IAAAA,MAAA,IAAAA,MAAA,CAAA2B,aAAA;QACA;QACA,SAAAf,gBAAA;UACA;UACA;QACA;;QAEA;QACA,MAAArB,KAAA,QAAAmB,MAAA,CAAAnB,KAAA;QACA,MAAAqC,SAAA,QAAAlB,MAAA,CAAAe,QAAA;QACA,MAAAI,UAAA,QAAAnB,MAAA,CAAAgB,QAAA;;QAEA;QACA;QACA;QACA;;QAEA;QACA,IAAAnC,KAAA,IAAAqC,SAAA,IAAAC,UAAA;UACA;UACA;UACA;UACA;;UAEA;UACA,MAAAC,QAAA;YACAvC,KAAA,EAAAA,KAAA;YACAkC,QAAA,EAAAG,SAAA;YACAF,QAAA,EAAAG;UACA;;UAEA;UACA;UACA;UACA;UACA;;UAEA7B,MAAA,CAAA2B,aAAA,CAAAI,WAAA,CAAAD,QAAA;UACA,KAAAlB,gBAAA;;UAEAlB,OAAA,CAAAsC,GAAA;;UAEA;UACA,SAAAC,OAAA,SAAAA,OAAA,CAAAhC,KAAA,SAAAgC,OAAA,CAAAhC,KAAA,CAAAiC,MAAA;YACA,KAAAD,OAAA,CAAAhC,KAAA,CAAAiC,MAAA,CAAAC,cAAA;YACA,KAAAF,OAAA,CAAAhC,KAAA,CAAAiC,MAAA,CAAAE,gBAAA;UACA;QACA;UACA;UACA;UACAjC,UAAA;YACA;YACA;YACA,KAAAO,MAAA;cACAnB,KAAA,EAAAH,OAAA,CAAAoC,GAAA;cACAC,QAAA,EAAArC,OAAA,CAAAoC,GAAA;cACAE,QAAA,EAAAtC,OAAA,CAAAoC,GAAA;YACA;YACA;YACA,KAAApB,gBAAA;UACA;QACA;MAEA;IACA;IACAZ,UAAAH,IAAA;MACA,MAAAgD,KAAA,GAAAC,QAAA,CAAAC,MAAA,CAAAF,KAAA,KAAAG,MAAA,WAAAnD,IAAA;MACA,OAAAgD,KAAA,GAAAA,KAAA;IACA;EACA;EACAI,cAAA,GAEA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}