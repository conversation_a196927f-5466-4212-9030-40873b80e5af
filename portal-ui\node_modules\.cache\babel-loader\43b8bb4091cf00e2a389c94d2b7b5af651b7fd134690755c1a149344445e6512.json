{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('section', {\n    staticClass: \"section gpu-comparison-section\"\n  }, [_c('div', {\n    staticClass: \"container\"\n  }, [_vm._m(0), _c('div', {\n    staticClass: \"gpu-comparison-table\"\n  }, [_c('table', [_c('thead', [_c('tr', [_c('th', [_vm._v(\"GPU型号\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('th', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.name))]);\n  })], 2)]), _c('tbody', [_c('tr', [_c('td', [_vm._v(\"架构\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.architecture))]);\n  })], 2), _c('tr', [_c('td', [_vm._v(\"FP16性能\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.fp16Performance))]);\n  })], 2), _c('tr', [_c('td', [_vm._v(\"FP32性能\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.fp32Performance))]);\n  })], 2), _c('tr', [_c('td', [_vm._v(\"显存\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.memory))]);\n  })], 2), _c('tr', [_c('td', [_vm._v(\"显存类型\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.memoryType))]);\n  })], 2), _c('tr', [_c('td', [_vm._v(\"带宽\")]), _vm._l(_vm.comparisonGpus, function (gpu) {\n    return _c('td', {\n      key: gpu.name\n    }, [_vm._v(_vm._s(gpu.bandwidth))]);\n  })], 2)])])])])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"section-header\"\n  }, [_c('h2', {\n    staticClass: \"section-title\"\n  }, [_vm._v(\"GPU性能对比\")]), _c('p', {\n    staticClass: \"section-description\"\n  }, [_vm._v(\" 专业GPU性能详细对比，助您选择最适合的计算资源 \")])]);\n}];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_m", "_v", "_l", "comparisonGpus", "gpu", "key", "name", "_s", "architecture", "fp16Performance", "fp32Performance", "memory", "memoryType", "bandwidth", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/views/AboutView.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('section',{staticClass:\"section gpu-comparison-section\"},[_c('div',{staticClass:\"container\"},[_vm._m(0),_c('div',{staticClass:\"gpu-comparison-table\"},[_c('table',[_c('thead',[_c('tr',[_c('th',[_vm._v(\"GPU型号\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('th',{key:gpu.name},[_vm._v(_vm._s(gpu.name))])})],2)]),_c('tbody',[_c('tr',[_c('td',[_vm._v(\"架构\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.architecture))])})],2),_c('tr',[_c('td',[_vm._v(\"FP16性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp16Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"FP32性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp32Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"显存\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memory))])})],2),_c('tr',[_c('td',[_vm._v(\"显存类型\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memoryType))])})],2),_c('tr',[_c('td',[_vm._v(\"带宽\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.bandwidth))])})],2)])])])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"GPU性能对比\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" 专业GPU性能详细对比，助您选择最适合的计算资源 \")])])\n}]\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,SAAS,EAAC;IAACE,WAAW,EAAC;EAAgC,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAsB,CAAC,EAAC,CAACF,EAAE,CAAC,OAAO,EAAC,CAACA,EAAE,CAAC,OAAO,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACK,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAACL,GAAG,CAACM,EAAE,CAAEN,GAAG,CAACO,cAAc,EAAE,UAASC,GAAG,EAAC;IAAC,OAAOP,EAAE,CAAC,IAAI,EAAC;MAACQ,GAAG,EAACD,GAAG,CAACE;IAAI,CAAC,EAAC,CAACV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAACT,EAAE,CAAC,OAAO,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAACL,GAAG,CAACM,EAAE,CAAEN,GAAG,CAACO,cAAc,EAAE,UAASC,GAAG,EAAC;IAAC,OAAOP,EAAE,CAAC,IAAI,EAAC;MAACQ,GAAG,EAACD,GAAG,CAACE;IAAI,CAAC,EAAC,CAACV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACI,YAAY,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACX,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAACL,GAAG,CAACM,EAAE,CAAEN,GAAG,CAACO,cAAc,EAAE,UAASC,GAAG,EAAC;IAAC,OAAOP,EAAE,CAAC,IAAI,EAAC;MAACQ,GAAG,EAACD,GAAG,CAACE;IAAI,CAAC,EAAC,CAACV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACK,eAAe,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACZ,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAACL,GAAG,CAACM,EAAE,CAAEN,GAAG,CAACO,cAAc,EAAE,UAASC,GAAG,EAAC;IAAC,OAAOP,EAAE,CAAC,IAAI,EAAC;MAACQ,GAAG,EAACD,GAAG,CAACE;IAAI,CAAC,EAAC,CAACV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACM,eAAe,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACb,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAACL,GAAG,CAACM,EAAE,CAAEN,GAAG,CAACO,cAAc,EAAE,UAASC,GAAG,EAAC;IAAC,OAAOP,EAAE,CAAC,IAAI,EAAC;MAACQ,GAAG,EAACD,GAAG,CAACE;IAAI,CAAC,EAAC,CAACV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACO,MAAM,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACd,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACL,GAAG,CAACM,EAAE,CAAEN,GAAG,CAACO,cAAc,EAAE,UAASC,GAAG,EAAC;IAAC,OAAOP,EAAE,CAAC,IAAI,EAAC;MAACQ,GAAG,EAACD,GAAG,CAACE;IAAI,CAAC,EAAC,CAACV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACQ,UAAU,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACf,EAAE,CAAC,IAAI,EAAC,CAACA,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAACL,GAAG,CAACM,EAAE,CAAEN,GAAG,CAACO,cAAc,EAAE,UAASC,GAAG,EAAC;IAAC,OAAOP,EAAE,CAAC,IAAI,EAAC;MAACQ,GAAG,EAACD,GAAG,CAACE;IAAI,CAAC,EAAC,CAACV,GAAG,CAACK,EAAE,CAACL,GAAG,CAACW,EAAE,CAACH,GAAG,CAACS,SAAS,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjxC,CAAC;AACD,IAAIC,eAAe,GAAG,CAAC,YAAW;EAAC,IAAIlB,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAACJ,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAqB,CAAC,EAAC,CAACH,GAAG,CAACK,EAAE,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/P,CAAC,CAAC;AAEF,SAASN,MAAM,EAAEmB,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}