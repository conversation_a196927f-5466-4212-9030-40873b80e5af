"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[543],{1782:function(o,t,l){l.r(t);var n='<h1 id="平台概要"><font style="color:#020817">平台概要</font></h1> <h2 id="1产品概述"><strong><font style="color:#020817">1.产品概述</font></strong></h2> <p><font style="color:#020817">天工开物专业算力服务是一款面向企业级用户的高性能计算解决方案，基于全国分布式算力网络，为用户提供专业级算力支持和企业级服务保障。</font></p> <h2 id="2产品特性--价值优势"><strong><font style="color:#020817">2.产品特性 &amp; 价值优势</font></strong></h2> <h3 id="容器化部署"><strong><font style="color:#020817">容器化部署</font></strong></h3> <ul> <li><font style="color:#020817">标准化环境：完整兼容 Docker 生态，支持一键导入现有容器</font></li> <li><font style="color:#020817">全托管服务：无需关心基础设施，专注核心业务开发</font></li> </ul> <h3 id="专业级硬件资源"><strong><font style="color:#020817">专业级硬件资源</font></strong></h3> <ul> <li><font style="color:#020817">企业级可靠性：提供 99.9% 可用性保障，确保业务稳定运行</font></li> <li><font style="color:#020817">大尺寸镜像支持：支持大尺寸镜像快速冷启动，满足复杂业务场景需求</font></li> </ul> <h3 id="智能调度系统"><strong><font style="color:#020817">智能调度系统</font></strong></h3> <ul> <li><font style="color:#020817">实时负载监控：系统实时监控业务负载，智能预测资源需求</font></li> <li><font style="color:#020817">自动扩缩容：根据业务负载自动调整算力资源，确保性能最优</font></li> <li><font style="color:#020817">成本优化：通过智能算法优化资源分配，实现性能与成本的最佳平衡</font></li> </ul> <h3 id="企业级保障"><strong><font style="color:#020817">企业级保障</font></strong></h3> <ul> <li><font style="color:#020817">专业运维支持：提供全天候技术支持服务</font></li> <li><font style="color:#020817">实时监控：全方位监控系统状态，快速响应异常</font></li> <li><font style="color:#020817">故障自动转移：多重容灾机制，确保业务连续性</font></li> </ul> <h2 id="3相关术语解释"><strong><font style="color:#020817">3.相关术语解释</font></strong></h2> <h3 id="服务部署"><strong><font style="color:#020817">服务部署</font></strong></h3> <ul> <li><strong><font style="color:#020817">打个比方：</font></strong><font style="color:#020817"> </font><font style="color:#020817">就像你举办了一场演唱会，搭建好了舞台（服务器环境），调试好了音响设备（运行环境和依赖），把歌手（应用程序）请到现场，观众入场后正式开始表演（对外提供服务）。</font></li> <li><strong><font style="color:#020817">简而言之：</font></strong><font style="color:#020817"> </font><font style="color:#020817">是指将开发完成的应用程序、服务或代码，配置并运行在服务器（物理或虚拟）上，使其能够正常工作并对外提供访问或计算能力的过程。这包括了准备运行环境、上传代码、配置依赖、启动服务等一系列操作。</font></li> </ul> <h3 id="serverless-无服务器"><strong><font style="color:#020817">Serverless (无服务器)</font></strong></h3> <ul> <li><strong><font style="color:#020817">打个比方：</font></strong><font style="color:#020817"> </font><font style="color:#020817">就像你去自助咖啡机买咖啡，你只负责点单和付款（运行代码），不需要关心咖啡机的水、电、原料补充、设备维修（服务器管理）。</font></li> <li><strong><font style="color:#020817">简而言之：</font></strong><font style="color:#020817"> </font><font style="color:#020817">是一种云计算的执行模型，允许您无需管理底层服务器基础设施（规划容量、配置、维护、扩缩容等），只需关注编写和部署代码。云服务提供商会按需自动分配计算资源，通常根据代码的实际执行时间或请求次数来计费。</font></li> </ul> <h3 id="镜像与容器"><strong><font style="color:#020817">镜像与容器</font></strong></h3> <ul> <li><strong><font style="color:#020817">打个比方：</font></strong><ul> <li><strong><font style="color:#020817">镜像 (Image)</font></strong><font style="color:#020817"> </font><font style="color:#020817">就像是一家中央厨房里固定标准的菜谱，里面详细记录了所需食材（代码、依赖、配置）、烹饪步骤（运行流程）以及成品规格，是一个不能直接食用的制作指南。</font></li> <li><strong><font style="color:#020817">容器 (Container)</font></strong><font style="color:#020817"> </font><font style="color:#020817">就像是厨师按照这份菜谱（镜像），真实烹饪出来的一份菜，并且被装在一个独立的打包餐盒里送给顾客，餐盒里包含了这道菜所需的一切，餐盒之间互不影响，吃完这份可以马上用同样的菜谱做出新的。</font></li> </ul> </li> <li><strong><font style="color:#020817">简而言之：</font></strong><font style="color:#020817"> </font><strong><font style="color:#020817">镜像</font></strong><font style="color:#020817">是一个包含了应用程序及其运行所需全部依赖的静态、可执行的软件包。</font><strong><font style="color:#020817">容器</font></strong><font style="color:#020817">是镜像在运行时的实例，它提供了一个轻量级、隔离的运行环境，确保应用程序在任何地方都能以相同的方式运行。</font></li> </ul> <h3 id="镜像仓库"><strong><font style="color:#020817">镜像仓库</font></strong></h3> <ul> <li><strong><font style="color:#020817">打个比方：</font></strong><font style="color:#020817"> </font><font style="color:#020817">就像一家冰淇淋工厂仓库，里面存放着各种冰淇淋制作说明书（容器镜像），你可以随时从这里取用，或者上传自己的新配方。</font></li> <li><strong><font style="color:#020817">简而言之：</font></strong><font style="color:#020817"> </font><font style="color:#020817">是一个用于存储、管理、分享和分发容器镜像的集中式服务。它允许用户方便地上传自己构建的镜像，或者下载其他人或官方提供的镜像。</font></li> </ul> <h3 id="端口"><strong><font style="color:#020817">端口</font></strong></h3> <ul> <li><strong><font style="color:#020817">打个比方：</font></strong><font style="color:#020817"> </font><font style="color:#020817">就像是一栋大楼的各个房间号，房间号不同，代表里面住着不同公司（服务）。要找哪个公司，必须拨打对应房间的门铃（访问对应端口）。</font></li> <li><strong><font style="color:#020817">简而言之：</font></strong><font style="color:#020817"> </font><font style="color:#020817">在计算机网络中，端口是服务器上用来区分不同应用程序或服务的逻辑地址。通过指定 IP 地址和端口号，网络通信可以被导向到目标服务器上正在监听特定端口的正确应用程序。</font></li> </ul> <h3 id="环境变量"><strong><font style="color:#020817">环境变量</font></strong></h3> <ul> <li><strong><font style="color:#020817">打个比方：</font></strong><font style="color:#020817"> </font><font style="color:#020817">就像你给厨师（应用程序）发的小纸条，上面写着今天的菜单（配置信息），比如用什么食材（数据库地址）、盐放多少（API 密钥）。厨师不会把这些写在脑子里（不会写进代码），而是随时看纸条决定怎么做。</font></li> <li><strong><font style="color:#020817">简而言之：</font></strong><font style="color:#020817"> </font><font style="color:#020817">是在操作系统或进程外部设置的动态命名值，应用程序在运行时可以读取这些值来获取配置信息。它们常用于存储不应硬编码在代码中的信息，如数据库连接字符串、API 密钥、日志级别等，便于在不同环境中灵活部署和配置应用。</font></li> </ul> <h3 id="无状态服务"><strong><font style="color:#020817">无状态服务</font></strong></h3> <ul> <li><strong><font style="color:#020817">打个比方：</font></strong><font style="color:#020817"> </font><font style="color:#020817">就像你打电话到外卖客服，每次打电话都需要重新报电话号码和地址（每次请求是独立的），客服不会记得你上次点过什么。</font></li> <li><strong><font style="color:#020817">简而言之：</font></strong><font style="color:#020817"> </font><font style="color:#020817">指的是不存储任何客户端会话状态或历史数据的服务。每一次来自客户端的请求都被视为独立的，服务不会因为之前的请求而保留任何状态信息。这种设计让服务更容易实现水平扩展和负载均衡，因为任何请求都可以由任何一个服务实例处理。</font></li> </ul> <h3 id="弹性伸缩"><strong><font style="color:#020817">弹性伸缩</font></strong></h3> <ul> <li><strong><font style="color:#020817">打个比方：</font></strong><font style="color:#020817"> </font><font style="color:#020817">就像超市结账的收银通道，顾客多的时候，会自动开启更多收银员（增加服务实例），顾客少的时候，收银员会减少（降低服务实例），保证服务流畅。</font></li> <li><strong><font style="color:#020817">简而言之：</font></strong><font style="color:#020817"> </font><font style="color:#020817">是指计算资源（如虚拟机数量、容器实例、数据库容量）能够根据实时的负载变化或预设的策略，自动地增加（Scale Out/Up）或减少（Scale In/Down）的过程。目标是在保证性能的同时，优化资源使用和成本。</font></li> </ul> <h3 id="分布式算力网络"><strong><font style="color:#020817">分布式算力网络</font></strong></h3> <ul> <li><strong><font style="color:#020817">打个比方：</font></strong><font style="color:#020817"> </font><font style="color:#020817">就像全国各地有很多快递分拨中心，大家同时运转，协同把包裹快速送达目的地，而不是依靠一个总部仓库单打独斗。</font></li> <li><strong><font style="color:#020817">简而言之：</font></strong><font style="color:#020817"> </font><font style="color:#020817">是由多个通过网络连接的计算节点（计算机、服务器等）组成的系统。这些节点可以位于不同的地理位置，它们协同工作来处理计算任务，汇聚分散的计算资源，以提供强大的整体计算能力、提高效率和系统可用性。</font></li> </ul> <h3 id="共享算力"><strong><font style="color:#020817">共享算力</font></strong></h3> <ul> <li><strong><font style="color:#020817">打个比方：</font></strong><font style="color:#020817"> </font><font style="color:#020817">就像你去共享办公室，很多人（不同的用户或任务）可以一起用同一张大桌子，每个人只占用自己需要的空间，互不影响，但资源是共享的。</font></li> <li><strong><font style="color:#020817">简而言之：</font></strong><font style="color:#020817"> </font><font style="color:#020817">指的是将物理计算资源（如 CPU、GPU、内存、带宽等）通过虚拟化或资源调度技术，分配给多个不同的用户或任务共同使用。这与独享资源相对，能够更高效地利用硬件资源，通常也是云计算平台提供服务的核心模式之一。</font></li> </ul> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/10 14:02</font></p> ';t["default"]=n}}]);
//# sourceMappingURL=docs20.2d06ed9d.js.map