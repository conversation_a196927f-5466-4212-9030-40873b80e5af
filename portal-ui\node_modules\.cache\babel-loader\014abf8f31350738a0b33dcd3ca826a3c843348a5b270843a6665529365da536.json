{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"help-layout\"\n  }, [_vm.isMobile ? _c(\"div\", {\n    staticClass: \"mobile-controls\"\n  }, [_c(\"button\", {\n    staticClass: \"sidebar-toggle\",\n    class: {\n      active: _vm.sidebarVisible\n    },\n    on: {\n      click: _vm.toggleSidebar\n    }\n  }, [_c(\"i\", {\n    staticClass: \"icon-menu\"\n  }), _c(\"span\", [_vm._v(\"菜单\")])]), _c(\"button\", {\n    staticClass: \"toc-toggle\",\n    class: {\n      active: _vm.tocVisible\n    },\n    on: {\n      click: _vm.toggleToc\n    }\n  }, [_c(\"i\", {\n    staticClass: \"icon-list\"\n  }), _c(\"span\", [_vm._v(\"目录\")])])]) : _vm._e(), _vm.isMobile && (_vm.sidebarVisible || _vm.tocVisible) ? _c(\"div\", {\n    staticClass: \"overlay\",\n    on: {\n      click: _vm.closeAllPanels\n    }\n  }) : _vm._e(), _c(\"aside\", {\n    ref: \"sidebar\",\n    staticClass: \"sidebar\",\n    class: {\n      \"sidebar-hidden\": !_vm.sidebarVisible && _vm.isMobile,\n      \"sidebar-visible\": _vm.sidebarVisible || !_vm.isMobile\n    }\n  }, [_vm.isMobile ? _c(\"div\", {\n    staticClass: \"sidebar-header\"\n  }, [_c(\"span\", {\n    staticClass: \"sidebar-title\"\n  }, [_vm._v(\"帮助文档\")]), _c(\"button\", {\n    staticClass: \"close-btn\",\n    on: {\n      click: _vm.closeSidebar\n    }\n  }, [_c(\"i\", {\n    staticClass: \"icon-close\"\n  }, [_vm._v(\"×\")])])]) : _vm._e(), _c(\"div\", {\n    staticClass: \"sidebar-menu\"\n  }, _vm._l(_vm.menu, function (category) {\n    return _c(\"div\", {\n      key: category.title,\n      staticClass: \"menu-category\"\n    }, [_c(\"div\", {\n      staticClass: \"category-title\"\n    }, [_vm._v(_vm._s(category.title))]), _c(\"ul\", {\n      staticClass: \"menu-list\"\n    }, _vm._l(category.items, function (item) {\n      return _c(\"li\", {\n        key: item.path,\n        staticClass: \"menu-item\"\n      }, [_c(\"router-link\", {\n        staticClass: \"menu-link\",\n        class: {\n          \"menu-link-active\": _vm.isMenuActive(item.path)\n        },\n        attrs: {\n          to: item.path\n        },\n        on: {\n          click: _vm.onMenuItemClick\n        }\n      }, [_vm._v(\" \" + _vm._s(item.name) + \" \")])], 1);\n    }), 0)]);\n  }), 0)]), _c(\"main\", {\n    ref: \"mainContent\",\n    staticClass: \"main-content\",\n    class: {\n      \"content-expanded\": (!_vm.sidebarVisible || !_vm.tocVisible) && _vm.isMobile,\n      \"content-full\": !_vm.sidebarVisible && !_vm.tocVisible && _vm.isMobile\n    }\n  }, [_c(\"HelpContent\", {\n    attrs: {\n      doc: _vm.currentDoc,\n      \"prev-page\": _vm.getPrevPage(),\n      \"next-page\": _vm.getNextPage()\n    },\n    on: {\n      \"content-loaded\": _vm.buildToc\n    }\n  })], 1), _c(\"aside\", {\n    staticClass: \"toc\",\n    class: {\n      \"toc-hidden\": !_vm.tocVisible && _vm.isMobile,\n      \"toc-visible\": _vm.tocVisible || !_vm.isMobile\n    }\n  }, [_vm.isMobile ? _c(\"div\", {\n    staticClass: \"toc-header\"\n  }, [_c(\"span\", {\n    staticClass: \"toc-title\"\n  }, [_vm._v(\"文章导航\")]), _c(\"button\", {\n    staticClass: \"close-btn\",\n    on: {\n      click: _vm.closeToc\n    }\n  }, [_c(\"i\", {\n    staticClass: \"icon-close\"\n  }, [_vm._v(\"×\")])])]) : _vm._e(), !_vm.isMobile ? _c(\"div\", {\n    staticClass: \"toc-title\"\n  }, [_vm._v(\"文章导航\")]) : _vm._e(), _c(\"ul\", {\n    staticClass: \"toc-list\"\n  }, _vm._l(_vm.toc, function (item) {\n    return _c(\"li\", {\n      key: item.id,\n      staticClass: \"toc-item\",\n      class: {\n        \"toc-item-h3\": item.level === 3,\n        active: item.id === _vm.activeTocId\n      }\n    }, [_c(\"a\", {\n      staticClass: \"toc-link\",\n      attrs: {\n        href: \"#\" + item.id\n      },\n      on: {\n        click: function ($event) {\n          $event.preventDefault();\n          return _vm.scrollToAnchor(item.id);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.text) + \" \")])]);\n  }), 0)]), _c(\"Mider\"), _c(\"chatAi\")], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "isMobile", "class", "active", "sidebarVisible", "on", "click", "toggleSidebar", "_v", "tocVisible", "toggleToc", "_e", "closeAllPanels", "ref", "closeSidebar", "_l", "menu", "category", "key", "title", "_s", "items", "item", "path", "isMenuActive", "attrs", "to", "onMenuItemClick", "name", "doc", "currentDoc", "getPrevPage", "getNextPage", "buildToc", "closeToc", "toc", "id", "level", "activeTocId", "href", "$event", "preventDefault", "scrollToAnchor", "text", "staticRenderFns", "_withStripped"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/views/HelpView.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"help-layout\" },\n    [\n      _vm.isMobile\n        ? _c(\"div\", { staticClass: \"mobile-controls\" }, [\n            _c(\n              \"button\",\n              {\n                staticClass: \"sidebar-toggle\",\n                class: { active: _vm.sidebarVisible },\n                on: { click: _vm.toggleSidebar },\n              },\n              [\n                _c(\"i\", { staticClass: \"icon-menu\" }),\n                _c(\"span\", [_vm._v(\"菜单\")]),\n              ]\n            ),\n            _c(\n              \"button\",\n              {\n                staticClass: \"toc-toggle\",\n                class: { active: _vm.tocVisible },\n                on: { click: _vm.toggleToc },\n              },\n              [\n                _c(\"i\", { staticClass: \"icon-list\" }),\n                _c(\"span\", [_vm._v(\"目录\")]),\n              ]\n            ),\n          ])\n        : _vm._e(),\n      _vm.isMobile && (_vm.sidebarVisible || _vm.tocVisible)\n        ? _c(\"div\", {\n            staticClass: \"overlay\",\n            on: { click: _vm.closeAllPanels },\n          })\n        : _vm._e(),\n      _c(\n        \"aside\",\n        {\n          ref: \"sidebar\",\n          staticClass: \"sidebar\",\n          class: {\n            \"sidebar-hidden\": !_vm.sidebarVisible && _vm.isMobile,\n            \"sidebar-visible\": _vm.sidebarVisible || !_vm.isMobile,\n          },\n        },\n        [\n          _vm.isMobile\n            ? _c(\"div\", { staticClass: \"sidebar-header\" }, [\n                _c(\"span\", { staticClass: \"sidebar-title\" }, [\n                  _vm._v(\"帮助文档\"),\n                ]),\n                _c(\n                  \"button\",\n                  { staticClass: \"close-btn\", on: { click: _vm.closeSidebar } },\n                  [_c(\"i\", { staticClass: \"icon-close\" }, [_vm._v(\"×\")])]\n                ),\n              ])\n            : _vm._e(),\n          _c(\n            \"div\",\n            { staticClass: \"sidebar-menu\" },\n            _vm._l(_vm.menu, function (category) {\n              return _c(\n                \"div\",\n                { key: category.title, staticClass: \"menu-category\" },\n                [\n                  _c(\"div\", { staticClass: \"category-title\" }, [\n                    _vm._v(_vm._s(category.title)),\n                  ]),\n                  _c(\n                    \"ul\",\n                    { staticClass: \"menu-list\" },\n                    _vm._l(category.items, function (item) {\n                      return _c(\n                        \"li\",\n                        { key: item.path, staticClass: \"menu-item\" },\n                        [\n                          _c(\n                            \"router-link\",\n                            {\n                              staticClass: \"menu-link\",\n                              class: {\n                                \"menu-link-active\": _vm.isMenuActive(item.path),\n                              },\n                              attrs: { to: item.path },\n                              on: { click: _vm.onMenuItemClick },\n                            },\n                            [_vm._v(\" \" + _vm._s(item.name) + \" \")]\n                          ),\n                        ],\n                        1\n                      )\n                    }),\n                    0\n                  ),\n                ]\n              )\n            }),\n            0\n          ),\n        ]\n      ),\n      _c(\n        \"main\",\n        {\n          ref: \"mainContent\",\n          staticClass: \"main-content\",\n          class: {\n            \"content-expanded\":\n              (!_vm.sidebarVisible || !_vm.tocVisible) && _vm.isMobile,\n            \"content-full\":\n              !_vm.sidebarVisible && !_vm.tocVisible && _vm.isMobile,\n          },\n        },\n        [\n          _c(\"HelpContent\", {\n            attrs: {\n              doc: _vm.currentDoc,\n              \"prev-page\": _vm.getPrevPage(),\n              \"next-page\": _vm.getNextPage(),\n            },\n            on: { \"content-loaded\": _vm.buildToc },\n          }),\n        ],\n        1\n      ),\n      _c(\n        \"aside\",\n        {\n          staticClass: \"toc\",\n          class: {\n            \"toc-hidden\": !_vm.tocVisible && _vm.isMobile,\n            \"toc-visible\": _vm.tocVisible || !_vm.isMobile,\n          },\n        },\n        [\n          _vm.isMobile\n            ? _c(\"div\", { staticClass: \"toc-header\" }, [\n                _c(\"span\", { staticClass: \"toc-title\" }, [_vm._v(\"文章导航\")]),\n                _c(\n                  \"button\",\n                  { staticClass: \"close-btn\", on: { click: _vm.closeToc } },\n                  [_c(\"i\", { staticClass: \"icon-close\" }, [_vm._v(\"×\")])]\n                ),\n              ])\n            : _vm._e(),\n          !_vm.isMobile\n            ? _c(\"div\", { staticClass: \"toc-title\" }, [_vm._v(\"文章导航\")])\n            : _vm._e(),\n          _c(\n            \"ul\",\n            { staticClass: \"toc-list\" },\n            _vm._l(_vm.toc, function (item) {\n              return _c(\n                \"li\",\n                {\n                  key: item.id,\n                  staticClass: \"toc-item\",\n                  class: {\n                    \"toc-item-h3\": item.level === 3,\n                    active: item.id === _vm.activeTocId,\n                  },\n                },\n                [\n                  _c(\n                    \"a\",\n                    {\n                      staticClass: \"toc-link\",\n                      attrs: { href: \"#\" + item.id },\n                      on: {\n                        click: function ($event) {\n                          $event.preventDefault()\n                          return _vm.scrollToAnchor(item.id)\n                        },\n                      },\n                    },\n                    [_vm._v(\" \" + _vm._s(item.text) + \" \")]\n                  ),\n                ]\n              )\n            }),\n            0\n          ),\n        ]\n      ),\n      _c(\"Mider\"),\n      _c(\"chatAi\"),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEH,GAAG,CAACI,QAAQ,GACRH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,gBAAgB;IAC7BE,KAAK,EAAE;MAAEC,MAAM,EAAEN,GAAG,CAACO;IAAe,CAAC;IACrCC,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACU;IAAc;EACjC,CAAC,EACD,CACET,EAAE,CAAC,GAAG,EAAE;IAA<PERSON>,WAAW,EAAE;EAAY,CAAC,CAAC,EACrCF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAC3B,CACF,EACDV,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,YAAY;IACzBE,KAAK,EAAE;MAAEC,MAAM,EAAEN,GAAG,CAACY;IAAW,CAAC;IACjCJ,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACa;IAAU;EAC7B,CAAC,EACD,CACEZ,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,CAAC,EACrCF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAC3B,CACF,CACF,CAAC,GACFX,GAAG,CAACc,EAAE,EAAE,EACZd,GAAG,CAACI,QAAQ,KAAKJ,GAAG,CAACO,cAAc,IAAIP,GAAG,CAACY,UAAU,CAAC,GAClDX,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,SAAS;IACtBK,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACe;IAAe;EAClC,CAAC,CAAC,GACFf,GAAG,CAACc,EAAE,EAAE,EACZb,EAAE,CACA,OAAO,EACP;IACEe,GAAG,EAAE,SAAS;IACdb,WAAW,EAAE,SAAS;IACtBE,KAAK,EAAE;MACL,gBAAgB,EAAE,CAACL,GAAG,CAACO,cAAc,IAAIP,GAAG,CAACI,QAAQ;MACrD,iBAAiB,EAAEJ,GAAG,CAACO,cAAc,IAAI,CAACP,GAAG,CAACI;IAChD;EACF,CAAC,EACD,CACEJ,GAAG,CAACI,QAAQ,GACRH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFV,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,WAAW;IAAEK,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACiB;IAAa;EAAE,CAAC,EAC7D,CAAChB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACxD,CACF,CAAC,GACFX,GAAG,CAACc,EAAE,EAAE,EACZb,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/BH,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,IAAI,EAAE,UAAUC,QAAQ,EAAE;IACnC,OAAOnB,EAAE,CACP,KAAK,EACL;MAAEoB,GAAG,EAAED,QAAQ,CAACE,KAAK;MAAEnB,WAAW,EAAE;IAAgB,CAAC,EACrD,CACEF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAiB,CAAC,EAAE,CAC3CH,GAAG,CAACW,EAAE,CAACX,GAAG,CAACuB,EAAE,CAACH,QAAQ,CAACE,KAAK,CAAC,CAAC,CAC/B,CAAC,EACFrB,EAAE,CACA,IAAI,EACJ;MAAEE,WAAW,EAAE;IAAY,CAAC,EAC5BH,GAAG,CAACkB,EAAE,CAACE,QAAQ,CAACI,KAAK,EAAE,UAAUC,IAAI,EAAE;MACrC,OAAOxB,EAAE,CACP,IAAI,EACJ;QAAEoB,GAAG,EAAEI,IAAI,CAACC,IAAI;QAAEvB,WAAW,EAAE;MAAY,CAAC,EAC5C,CACEF,EAAE,CACA,aAAa,EACb;QACEE,WAAW,EAAE,WAAW;QACxBE,KAAK,EAAE;UACL,kBAAkB,EAAEL,GAAG,CAAC2B,YAAY,CAACF,IAAI,CAACC,IAAI;QAChD,CAAC;QACDE,KAAK,EAAE;UAAEC,EAAE,EAAEJ,IAAI,CAACC;QAAK,CAAC;QACxBlB,EAAE,EAAE;UAAEC,KAAK,EAAET,GAAG,CAAC8B;QAAgB;MACnC,CAAC,EACD,CAAC9B,GAAG,CAACW,EAAE,CAAC,GAAG,GAAGX,GAAG,CAACuB,EAAE,CAACE,IAAI,CAACM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CACxC,CACF,EACD,CAAC,CACF;IACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CACF,EACD9B,EAAE,CACA,MAAM,EACN;IACEe,GAAG,EAAE,aAAa;IAClBb,WAAW,EAAE,cAAc;IAC3BE,KAAK,EAAE;MACL,kBAAkB,EAChB,CAAC,CAACL,GAAG,CAACO,cAAc,IAAI,CAACP,GAAG,CAACY,UAAU,KAAKZ,GAAG,CAACI,QAAQ;MAC1D,cAAc,EACZ,CAACJ,GAAG,CAACO,cAAc,IAAI,CAACP,GAAG,CAACY,UAAU,IAAIZ,GAAG,CAACI;IAClD;EACF,CAAC,EACD,CACEH,EAAE,CAAC,aAAa,EAAE;IAChB2B,KAAK,EAAE;MACLI,GAAG,EAAEhC,GAAG,CAACiC,UAAU;MACnB,WAAW,EAAEjC,GAAG,CAACkC,WAAW,EAAE;MAC9B,WAAW,EAAElC,GAAG,CAACmC,WAAW;IAC9B,CAAC;IACD3B,EAAE,EAAE;MAAE,gBAAgB,EAAER,GAAG,CAACoC;IAAS;EACvC,CAAC,CAAC,CACH,EACD,CAAC,CACF,EACDnC,EAAE,CACA,OAAO,EACP;IACEE,WAAW,EAAE,KAAK;IAClBE,KAAK,EAAE;MACL,YAAY,EAAE,CAACL,GAAG,CAACY,UAAU,IAAIZ,GAAG,CAACI,QAAQ;MAC7C,aAAa,EAAEJ,GAAG,CAACY,UAAU,IAAI,CAACZ,GAAG,CAACI;IACxC;EACF,CAAC,EACD,CACEJ,GAAG,CAACI,QAAQ,GACRH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1DV,EAAE,CACA,QAAQ,EACR;IAAEE,WAAW,EAAE,WAAW;IAAEK,EAAE,EAAE;MAAEC,KAAK,EAAET,GAAG,CAACqC;IAAS;EAAE,CAAC,EACzD,CAACpC,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACxD,CACF,CAAC,GACFX,GAAG,CAACc,EAAE,EAAE,EACZ,CAACd,GAAG,CAACI,QAAQ,GACTH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CAACH,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,GACzDX,GAAG,CAACc,EAAE,EAAE,EACZb,EAAE,CACA,IAAI,EACJ;IAAEE,WAAW,EAAE;EAAW,CAAC,EAC3BH,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACsC,GAAG,EAAE,UAAUb,IAAI,EAAE;IAC9B,OAAOxB,EAAE,CACP,IAAI,EACJ;MACEoB,GAAG,EAAEI,IAAI,CAACc,EAAE;MACZpC,WAAW,EAAE,UAAU;MACvBE,KAAK,EAAE;QACL,aAAa,EAAEoB,IAAI,CAACe,KAAK,KAAK,CAAC;QAC/BlC,MAAM,EAAEmB,IAAI,CAACc,EAAE,KAAKvC,GAAG,CAACyC;MAC1B;IACF,CAAC,EACD,CACExC,EAAE,CACA,GAAG,EACH;MACEE,WAAW,EAAE,UAAU;MACvByB,KAAK,EAAE;QAAEc,IAAI,EAAE,GAAG,GAAGjB,IAAI,CAACc;MAAG,CAAC;MAC9B/B,EAAE,EAAE;QACFC,KAAK,EAAE,SAAAA,CAAUkC,MAAM,EAAE;UACvBA,MAAM,CAACC,cAAc,EAAE;UACvB,OAAO5C,GAAG,CAAC6C,cAAc,CAACpB,IAAI,CAACc,EAAE,CAAC;QACpC;MACF;IACF,CAAC,EACD,CAACvC,GAAG,CAACW,EAAE,CAAC,GAAG,GAAGX,GAAG,CAACuB,EAAE,CAACE,IAAI,CAACqB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CACxC,CACF,CACF;EACH,CAAC,CAAC,EACF,CAAC,CACF,CACF,CACF,EACD7C,EAAE,CAAC,OAAO,CAAC,EACXA,EAAE,CAAC,QAAQ,CAAC,CACb,EACD,CAAC,CACF;AACH,CAAC;AACD,IAAI8C,eAAe,GAAG,EAAE;AACxBhD,MAAM,CAACiD,aAAa,GAAG,IAAI;AAE3B,SAASjD,MAAM,EAAEgD,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}