{"ast": null, "code": "import \"core-js/modules/esnext.typed-array.to-reversed.js\";\nimport \"core-js/modules/esnext.typed-array.to-sorted.js\";\nimport \"core-js/modules/esnext.typed-array.with.js\";\nimport \"core-js/modules/web.dom-exception.stack.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport { postAnyData, getAnyData, postLogin, postJsonData } from \"@/api/login\";\nimport { getToken, setToken, removeToken } from '@/utils/auth';\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\nimport Cookies from 'js-cookie';\nimport backgroundlogin from '@/views/Login/backgroundlogin.vue';\nexport default {\n  name: \"login\",\n  components: {\n    SlideNotification,\n    backgroundlogin\n  },\n  data() {\n    return {\n      activeTab: 'phone',\n      // 默认选中手机号登录\n      phoneForm: {\n        phone: '',\n        code: ''\n      },\n      accountForm: {\n        username: '',\n        password: ''\n      },\n      passwordVisible: false,\n      errors: {\n        phone: '',\n        code: '',\n        username: '',\n        password: ''\n      },\n      codeSent: false,\n      countdown: 60,\n      timer: null,\n      showNotification: false,\n      notificationMessage: '',\n      notificationType: 'success',\n      minHeight: '50px'\n    };\n  },\n  created() {\n    // 页面创建时检查本地存储中的计时器状态\n    this.checkCountdownState();\n    this.$emit('hiden-layout');\n  },\n  activated() {\n    this.$emit('hiden-layout', true); // 从缓存返回时再次隐藏\n  },\n\n  methods: {\n    showNotificationMessage(message, type = 'info') {\n      this.notificationMessage = message;\n      this.notificationType = type;\n      this.showNotification = true;\n    },\n    //生成公私钥对\n    async generateKeyPair() {\n      try {\n        this.error = null;\n        // 生成密钥对\n        const keyPair = await crypto.subtle.generateKey({\n          name: 'RSA-OAEP',\n          modulusLength: 2048,\n          publicExponent: new Uint8Array([1, 0, 1]),\n          hash: {\n            name: 'SHA-256'\n          }\n        }, true, ['encrypt', 'decrypt']);\n\n        // 导出公钥\n        const exportedPublicKey = await crypto.subtle.exportKey('spki', keyPair.publicKey);\n        const publicKeyPem = btoa(String.fromCharCode(...new Uint8Array(exportedPublicKey)));\n\n        // 导出私钥\n        const exportedPrivateKey = await crypto.subtle.exportKey('pkcs8', keyPair.privateKey);\n        const privateKeyPem = btoa(String.fromCharCode(...new Uint8Array(exportedPrivateKey)));\n        const key = {\n          publicKeyPem,\n          privateKeyPem\n        };\n        Cookies.set('publicKey-B', publicKeyPem);\n        Cookies.set('privateKey-B', privateKeyPem);\n      } catch (err) {\n        this.error = '密钥对生成失败，请确保在安全上下文（HTTPS）中运行';\n      } finally {\n        this.isLoading = false;\n      }\n    },\n    checkCountdownState() {\n      // 从localStorage获取倒计时信息\n      const storedPhone = localStorage.getItem('verificationPhone');\n      const expireTime = localStorage.getItem('verificationExpireTime');\n      if (storedPhone && expireTime) {\n        const now = new Date().getTime();\n        const timeLeft = Math.ceil((parseInt(expireTime) - now) / 1000);\n\n        // 如果倒计时还没结束\n        if (timeLeft > 0) {\n          this.codeSent = true;\n          this.countdown = timeLeft;\n          this.startCountdown();\n\n          // 如果当前输入的手机号与存储的手机号一致，应用倒计时限制\n          if (this.phoneForm.phone === storedPhone) {\n            this.codeSent = true;\n          }\n        } else {\n          // 倒计时已结束，清除存储\n          this.clearCountdownStorage();\n        }\n      }\n    },\n    clearCountdownStorage() {\n      localStorage.removeItem('verificationPhone');\n      localStorage.removeItem('verificationExpireTime');\n      this.codeSent = false;\n      this.countdown = 60;\n      if (this.timer) {\n        clearInterval(this.timer);\n        this.timer = null;\n      }\n    },\n    validatePhone() {\n      const phoneRegex = /^1[3-9]\\d{9}$/;\n      if (!this.phoneForm.phone) {\n        this.errors.phone = '请输入手机号';\n      } else if (!phoneRegex.test(this.phoneForm.phone)) {\n        this.errors.phone = '请输入有效的手机号';\n      } else {\n        this.errors.phone = '';\n\n        // 检查该手机号是否处于冷却期\n        const storedPhone = localStorage.getItem('verificationPhone');\n        const expireTime = localStorage.getItem('verificationExpireTime');\n        if (storedPhone === this.phoneForm.phone && expireTime) {\n          const now = new Date().getTime();\n          const timeLeft = Math.ceil((parseInt(expireTime) - now) / 1000);\n          if (timeLeft > 0) {\n            this.codeSent = true;\n            this.countdown = timeLeft;\n            // 如果没有定时器，则重新启动\n            if (!this.timer) {\n              this.startCountdown();\n            }\n          }\n        }\n      }\n    },\n    async validateCodegeshi() {\n      // 清空错误\n      this.errors.code = '';\n\n      // 前端基础验证（保持原有长度判断）\n      if (!this.phoneForm.code) {\n        this.errors.code = '请输入验证码';\n        return false;\n      }\n      if (this.phoneForm.code.length !== 4 || !/^\\d+$/.test(this.phoneForm.code)) {\n        this.errors.code = '验证码必须为4位数字';\n        return false;\n      }\n    },\n    startCountdown() {\n      // 清除可能存在的旧定时器\n      if (this.timer) {\n        clearInterval(this.timer);\n      }\n\n      // 使用固定的时间间隔\n      this.timer = setInterval(() => {\n        if (this.countdown <= 1) {\n          clearInterval(this.timer);\n          this.timer = null;\n          this.codeSent = false;\n          this.countdown = 60;\n          // 清除localStorage中的记录\n          this.clearCountdownStorage();\n        } else {\n          this.countdown--;\n          // 更新localStorage中的过期时间（可选，保持同步）\n          const expireTime = new Date().getTime() + this.countdown * 1000;\n          localStorage.setItem('verificationExpireTime', expireTime.toString());\n        }\n      }, 1000);\n    },\n    navigateTo(path) {\n      // 如果当前路径与目标路径相同，则重新加载页面\n      if (this.$route.path === path) {\n        // 先跳转到一个临时路由（如果有的话）或者重新加载页面\n        this.$nextTick(() => {\n          window.scrollTo({\n            top: 0,\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\n          });\n\n          this.$router.go(0); // 刷新当前页面\n        });\n      } else {\n        // 不同路径，正常导航并滚动到顶部\n        this.$router.push(path);\n        window.scrollTo({\n          top: 0,\n          behavior: 'instant'\n        });\n      }\n      this.currentPath = path;\n    },\n    validateUsername() {\n      const phoneRegex = /^1[3-9]\\d{9}$/;\n      if (!this.accountForm.username) {\n        this.errors.username = '请输入登录账号';\n      } else if (!phoneRegex.test(this.accountForm.username)) {\n        this.errors.username = '请输入有效的登录账号';\n      } else {\n        this.errors.username = '';\n      }\n    },\n    validatePassword() {\n      if (!this.accountForm.password) {\n        this.errors.password = '请输入登录密码';\n      } else if (this.accountForm.password.length < 8) {\n        this.errors.password = '密码长度至少为8位';\n      } else {\n        this.errors.password = '';\n      }\n    },\n    getVerificationCode() {\n      // 先验证手机号格式\n      this.validatePhone();\n      if (this.errors.phone) return;\n      // 显示发送中状态\n      this.codeSent = true;\n      this.isSendingCode = true;\n      // 调用发送验证码接口\n      postLogin(\"/auth/sendCode\", {\n        phone: this.phoneForm.phone\n      }).then(res => {\n        if (res.data.code === 200) {\n          // 存储验证码发送时间和手机号到localStorage\n          const now = new Date().getTime();\n          const expireTime = now + 60 * 1000; // 当前时间 + 60秒\n\n          localStorage.setItem('verificationPhone', this.phoneForm.phone);\n          localStorage.setItem('verificationExpireTime', expireTime.toString());\n          this.showNotificationMessage('验证码已发送，可能会有延迟，请耐心等待！', 'success');\n          this.startCountdown();\n        } else {\n          this.errors.code = res.data.msg || '验证码发送失败';\n          this.codeSent = false; // 发送失败时可重新发送\n          // this.showNotificationMessage(res.data.msg || '验证码发送失败', 'error');\n        }\n      });\n    },\n\n    validateCode() {\n      // 清空错误\n      this.errors.code = '';\n\n      // 前端基础验证（保持原有长度判断）\n      if (!this.phoneForm.code) {\n        this.errors.code = '请输入验证码';\n        return false;\n      }\n      if (this.phoneForm.code.length !== 4 || !/^\\d+$/.test(this.phoneForm.code)) {\n        this.errors.code = '验证码必须为4位数字';\n        return false;\n      }\n      try {\n        // 新增接口验证（参数与发送接口一致）\n        postAnyData(\"/auth/verifyCode\", {\n          phone: this.phoneForm.phone,\n          // 必须携带手机号\n          code: this.phoneForm.code\n        }).then(res => {\n          if (res.data.code == 200) {\n            postLogin(\"/auth/codeLogin\", {\n              phone: this.phoneForm.phone,\n              // 必须携带手机号\n              code: this.phoneForm.code\n            }).then(res => {\n              if (res.data.code == 200) {\n                setToken(res.data.token);\n                this.$emit(\"refresh-header\");\n                this.$router.push('/index');\n              } else if (res.data.code !== 200) {\n                this.errors.code = res.data.msg || '验证码错误';\n                // this.showNotificationMessage(res.data.msg || '验证码错误', 'error');\n                return false;\n              }\n            });\n            return true;\n          } else if (res.data.code !== 200) {\n            this.errors.code = res.data.msg || '验证码错误';\n            // this.showNotificationMessage(res.data.msg || '验证码错误', 'error');\n            return false;\n          }\n        });\n        return true;\n      } catch (error) {\n        this.errors.code = '网络异常，请稍后重试';\n        // this.showNotificationMessage('网络异常，请稍后重试', 'error');\n        return false;\n      }\n    },\n    phoneLogin() {\n      this.validatePhone();\n      this.validateCode();\n      if (this.errors.phone || this.errors.code) {\n        // 如果有错误，显示提示信息\n        const errorMessage = this.errors.phone || this.errors.code || '请正确填写手机号和验证码';\n        // this.showNotificationMessage(errorMessage, 'error');\n        return; // 如果表单无效，直接返回，不发送请求\n      }\n\n      this.generateKeyPair();\n      this.$emit(\"refresh-header\");\n    },\n    accountLogin() {\n      this.validateUsername();\n      this.validatePassword();\n\n      // 表单验证失败处理\n      if (this.errors.username || this.errors.password) {\n        const errorMessage = this.errors.username || this.errors.password || '请正确填写用户名和密码';\n        // this.showNotificationMessage(errorMessage, 'error');\n        return;\n      }\n\n      // 显示加载状态\n      const loading = this.$loading ? this.$loading({\n        lock: true,\n        text: '登录中...',\n        spinner: 'el-icon-loading'\n      }) : null;\n\n      // 调用登录API\n      postLogin(\"/auth/login\", this.accountForm).then(res => {\n        // 关闭加载状态\n        if (loading) loading.close();\n        if (res.data && res.data.code == 200) {\n          // 登录成功\n          setToken(res.data.token);\n          this.showNotificationMessage('登录成功', 'success');\n          this.$emit(\"refresh-header\");\n          this.$router.push('/index');\n          this.generateKeyPair();\n        } else {\n          // 登录失败，服务器返回了错误码\n          this.errors.password = res.data.msg;\n          // this.showNotificationMessage(res.data.msg || '登录失败，请检查账号和密码', 'error');\n        }\n      }).catch(error => {\n        // 关闭加载状态\n        if (loading) loading.close();\n\n        // 处理网络错误或其他异常\n        this.errors.password = '网络异常，请稍后重试';\n        // this.showNotificationMessage('网络异常，请稍后重试', 'error');\n      });\n    },\n\n    togglePasswordVisibility() {\n      this.passwordVisible = !this.passwordVisible;\n    }\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n    this.$emit('hiden-layout');\n  }\n};", "map": {"version": 3, "names": ["postAnyData", "getAnyData", "postLogin", "postJsonData", "getToken", "setToken", "removeToken", "SlideNotification", "Cookies", "backgroundlogin", "name", "components", "data", "activeTab", "phoneForm", "phone", "code", "accountForm", "username", "password", "passwordVisible", "errors", "codeSent", "countdown", "timer", "showNotification", "notificationMessage", "notificationType", "minHeight", "created", "checkCountdownState", "$emit", "activated", "methods", "showNotificationMessage", "message", "type", "generateKeyPair", "error", "keyPair", "crypto", "subtle", "<PERSON><PERSON>ey", "modulus<PERSON>ength", "publicExponent", "Uint8Array", "hash", "exportedPublicKey", "exportKey", "public<PERSON>ey", "publicKeyPem", "btoa", "String", "fromCharCode", "exportedPrivateKey", "privateKey", "privateKeyPem", "key", "set", "err", "isLoading", "storedPhone", "localStorage", "getItem", "expireTime", "now", "Date", "getTime", "timeLeft", "Math", "ceil", "parseInt", "startCountdown", "clearCountdownStorage", "removeItem", "clearInterval", "validatePhone", "phoneRegex", "test", "validate<PERSON><PERSON><PERSON><PERSON>", "length", "setInterval", "setItem", "toString", "navigateTo", "path", "$route", "$nextTick", "window", "scrollTo", "top", "behavior", "$router", "go", "push", "currentPath", "validateUsername", "validatePassword", "getVerificationCode", "isSendingCode", "then", "res", "msg", "validateCode", "token", "phoneLogin", "errorMessage", "accountLogin", "loading", "$loading", "lock", "text", "spinner", "close", "catch", "togglePasswordVisibility", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/Login/login.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login-page\">\r\n    <SlideNotification\r\n        v-if=\"showNotification\"\r\n        :message=\"notificationMessage\"\r\n        :type=\"notificationType\"\r\n        :duration=\"3000\"\r\n        :minHeight= minHeight\r\n        @close=\"showNotification = false\"\r\n    />\r\n\r\n    <div class=\"left-side\">\r\n      <backgroundlogin />\r\n\r\n<!--      <div class=\"logo-container\">-->\r\n<!--        <div class=\"logo-area\">-->\r\n<!--          <img src=\"../assets/images/logo2.png\" alt=\"算力租赁\">-->\r\n<!--        </div>-->\r\n<!--      </div>-->\r\n\r\n<!--      <div class=\"headline-container\">-->\r\n<!--        <h2 class=\"headline\">天工开物 AI算力云</h2>-->\r\n<!--        <p class=\"subheadline\">-->\r\n<!--          专门面向AI 2.0 时代的创新平台。利用顶尖的技术驱动，为用户的大模型开发、训练、运行、应用提供完整的工具链。-->\r\n<!--        </p>-->\r\n<!--      </div>-->\r\n    </div>\r\n\r\n    <div class=\"right-side\">\r\n      <div class=\"login-form-container\">\r\n        <h3>欢迎来到 天工开物</h3>\r\n\r\n        <!-- Login tabs -->\r\n        <div class=\"login-tabs\">\r\n          <div\r\n              :class=\"['tab-item', activeTab === 'phone' ? 'active' : '']\"\r\n              @click=\"activeTab = 'phone'\"\r\n          >\r\n            手机号登录\r\n          </div>\r\n          <div\r\n              :class=\"['tab-item', activeTab === 'account' ? 'active' : '']\"\r\n              @click=\"activeTab = 'account'\"\r\n          >\r\n            账号登录\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Login form container with fixed height -->\r\n        <div class=\"form-container\">\r\n          <!-- Phone login form -->\r\n          <div v-if=\"activeTab === 'phone'\" class=\"login-form\">\r\n            <p class=\"form-note\">   </p>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.phone }\">\r\n              <input\r\n                  type=\"text\"\r\n                  v-model=\"phoneForm.phone\"\r\n                  placeholder=\"请输入手机号\"\r\n                  @blur=\"validatePhone\"\r\n              />\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.phone\" class=\"error-message\">{{ errors.phone }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group verification-code\" :class=\"{ 'error': errors.code }\">\r\n              <div class=\"code-input-container\">\r\n                <input\r\n                    type=\"text\"\r\n                    v-model=\"phoneForm.code\"\r\n                    placeholder=\"请输入验证码\"\r\n                    @blur=\"validateCodegeshi\"\r\n                />\r\n                <button\r\n                    class=\"get-code-btn-inline\"\r\n                    @click=\"getVerificationCode\"\r\n                    :disabled=\"!phoneForm.phone || errors.phone || codeSent\"\r\n                >\r\n                  {{ codeSent ? `${countdown}秒后重试` : '获取验证码' }}\r\n                </button>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.code\" class=\"error-message\">{{ errors.code }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"agreement-text\">\r\n              登录视为您已阅读并同意天工开物\r\n              <router-link to=\"/help/user-agreement\" >服务条款</router-link> 和<router-link to=\"/help/privacy-policy\" >隐私政策</router-link>\r\n            </div>\r\n\r\n            <button\r\n                class=\"login-btn\"\r\n                @click=\"phoneLogin\"\r\n                :disabled=\"!phoneForm.phone || !phoneForm.code\"\r\n            >\r\n              登录\r\n            </button>\r\n            <div class=\"login-link\">\r\n              <a href=\"#\" @click=\"navigateTo('/register')\">立即注册</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"#\" @click=\"navigateTo('/forgetpass')\">忘记密码</a>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Account login form -->\r\n          <div v-if=\"activeTab === 'account'\" class=\"login-form\">\r\n            <p class=\"form-note\">手机号即为登录账号</p>\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.username }\">\r\n              <input\r\n                  type=\"text\"\r\n                  v-model=\"accountForm.username\"\r\n                  placeholder=\"请输入登录账号\"\r\n                  @blur=\"validateUsername\"\r\n              />\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.username\" class=\"error-message\">{{ errors.username }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"input-group\" :class=\"{ 'error': errors.password }\">\r\n              <div class=\"password-input-container\">\r\n                <input\r\n                    :type=\"passwordVisible ? 'text' : 'password'\"\r\n                    v-model=\"accountForm.password\"\r\n                    placeholder=\"请输入登录密码\"\r\n                    @blur=\"validatePassword\"\r\n                />\r\n                <span class=\"password-toggle\" @click=\"togglePasswordVisibility\">\r\n                  <i :class=\"['eye-icon', passwordVisible ? 'visible' : '']\"></i>\r\n                </span>\r\n              </div>\r\n              <div class=\"error-container\">\r\n                <div v-if=\"errors.password\" class=\"error-message\">{{ errors.password }}</div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"agreement-text\">\r\n              登录视为您已阅读并同意天工开物 \r\n              <router-link to=\"/help/user-agreement\" >服务条款</router-link> 和<router-link to=\"/help/privacy-policy\" >隐私政策</router-link>\r\n            </div>\r\n\r\n            <button\r\n                class=\"login-btn\"\r\n                @click=\"accountLogin\"\r\n                :disabled=\"!accountForm.username || !accountForm.password\"\r\n            >\r\n              登录\r\n            </button>\r\n            <div class=\"login-link\">\r\n              <a href=\"#\" @click=\"navigateTo('/register')\">立即注册</a>\r\n              <span class=\"divider\">|</span>\r\n              <a href=\"#\" @click=\"navigateTo('/forgetpass')\">忘记密码</a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import {postAnyData, getAnyData, postLogin, postJsonData} from \"@/api/login\";\r\nimport { getToken, setToken, removeToken } from '@/utils/auth'\r\nimport SlideNotification from '@/components/common/header/SlideNotification.vue';\r\nimport Cookies from 'js-cookie'\r\nimport backgroundlogin from '@/views/Login/backgroundlogin.vue';\r\n\r\nexport default {\r\n  name: \"login\",\r\n  components: {\r\n    SlideNotification,\r\n    backgroundlogin\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'phone', // 默认选中手机号登录\r\n      phoneForm: {\r\n        phone: '',\r\n        code: ''\r\n      },\r\n      accountForm: {\r\n        username: '',\r\n        password: ''\r\n      },\r\n      passwordVisible: false,\r\n      errors: {\r\n        phone: '',\r\n        code: '',\r\n        username: '',\r\n        password: ''\r\n      },\r\n      codeSent: false,\r\n      countdown: 60,\r\n      timer: null,\r\n      showNotification: false,\r\n      notificationMessage: '',\r\n      notificationType: 'success',\r\n      minHeight: '50px'\r\n    }\r\n  },\r\n  created() {\r\n    // 页面创建时检查本地存储中的计时器状态\r\n    this.checkCountdownState();\r\n    this.$emit('hiden-layout')\r\n  },\r\n  activated() {\r\n    this.$emit('hiden-layout', true); // 从缓存返回时再次隐藏\r\n  },\r\n  methods: {\r\n    showNotificationMessage(message, type = 'info') {\r\n      this.notificationMessage = message;\r\n      this.notificationType = type;\r\n      this.showNotification = true;\r\n    },\r\n    //生成公私钥对\r\n    async generateKeyPair() {\r\n      try {\r\n        this.error = null;\r\n        // 生成密钥对\r\n        const keyPair = await crypto.subtle.generateKey(\r\n                {\r\n                  name: 'RSA-OAEP',\r\n                  modulusLength: 2048,\r\n                  publicExponent: new Uint8Array([1, 0, 1]),\r\n                  hash: { name: 'SHA-256' }\r\n                },\r\n                true,\r\n                ['encrypt', 'decrypt']\r\n        );\r\n\r\n        // 导出公钥\r\n        const exportedPublicKey = await crypto.subtle.exportKey('spki', keyPair.publicKey);\r\n        const publicKeyPem = btoa(String.fromCharCode(...new Uint8Array(exportedPublicKey)));\r\n\r\n        // 导出私钥\r\n        const exportedPrivateKey = await crypto.subtle.exportKey('pkcs8', keyPair.privateKey);\r\n        const privateKeyPem = btoa(String.fromCharCode(...new Uint8Array(exportedPrivateKey)));\r\n\r\n        const key = { publicKeyPem, privateKeyPem };\r\n        Cookies.set('publicKey-B',publicKeyPem)\r\n        Cookies.set('privateKey-B',privateKeyPem)\r\n      } catch (err) {\r\n        this.error = '密钥对生成失败，请确保在安全上下文（HTTPS）中运行';\r\n      } finally {\r\n        this.isLoading = false;\r\n      }\r\n    },\r\n\r\n    checkCountdownState() {\r\n      // 从localStorage获取倒计时信息\r\n      const storedPhone = localStorage.getItem('verificationPhone');\r\n      const expireTime = localStorage.getItem('verificationExpireTime');\r\n\r\n      if (storedPhone && expireTime) {\r\n        const now = new Date().getTime();\r\n        const timeLeft = Math.ceil((parseInt(expireTime) - now) / 1000);\r\n\r\n        // 如果倒计时还没结束\r\n        if (timeLeft > 0) {\r\n          this.codeSent = true;\r\n          this.countdown = timeLeft;\r\n          this.startCountdown();\r\n\r\n          // 如果当前输入的手机号与存储的手机号一致，应用倒计时限制\r\n          if (this.phoneForm.phone === storedPhone) {\r\n            this.codeSent = true;\r\n          }\r\n        } else {\r\n          // 倒计时已结束，清除存储\r\n          this.clearCountdownStorage();\r\n        }\r\n      }\r\n    },\r\n\r\n    clearCountdownStorage() {\r\n      localStorage.removeItem('verificationPhone');\r\n      localStorage.removeItem('verificationExpireTime');\r\n      this.codeSent = false;\r\n      this.countdown = 60;\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n        this.timer = null;\r\n      }\r\n    },\r\n\r\n    validatePhone() {\r\n      const phoneRegex = /^1[3-9]\\d{9}$/;\r\n      if (!this.phoneForm.phone) {\r\n        this.errors.phone = '请输入手机号';\r\n      } else if (!phoneRegex.test(this.phoneForm.phone)) {\r\n        this.errors.phone = '请输入有效的手机号';\r\n      } else {\r\n        this.errors.phone = '';\r\n\r\n        // 检查该手机号是否处于冷却期\r\n        const storedPhone = localStorage.getItem('verificationPhone');\r\n        const expireTime = localStorage.getItem('verificationExpireTime');\r\n\r\n        if (storedPhone === this.phoneForm.phone && expireTime) {\r\n          const now = new Date().getTime();\r\n          const timeLeft = Math.ceil((parseInt(expireTime) - now) / 1000);\r\n\r\n          if (timeLeft > 0) {\r\n            this.codeSent = true;\r\n            this.countdown = timeLeft;\r\n            // 如果没有定时器，则重新启动\r\n            if (!this.timer) {\r\n              this.startCountdown();\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    async validateCodegeshi() {\r\n      // 清空错误\r\n      this.errors.code = '';\r\n\r\n      // 前端基础验证（保持原有长度判断）\r\n      if (!this.phoneForm.code) {\r\n        this.errors.code = '请输入验证码';\r\n        return false;\r\n      }\r\n      if (this.phoneForm.code.length !== 4 || !/^\\d+$/.test(this.phoneForm.code)) {\r\n        this.errors.code = '验证码必须为4位数字';\r\n        return false;\r\n      }\r\n    },\r\n    startCountdown() {\r\n      // 清除可能存在的旧定时器\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n      }\r\n\r\n      // 使用固定的时间间隔\r\n      this.timer = setInterval(() => {\r\n        if (this.countdown <= 1) {\r\n          clearInterval(this.timer);\r\n          this.timer = null;\r\n          this.codeSent = false;\r\n          this.countdown = 60;\r\n          // 清除localStorage中的记录\r\n          this.clearCountdownStorage();\r\n        } else {\r\n          this.countdown--;\r\n          // 更新localStorage中的过期时间（可选，保持同步）\r\n          const expireTime = new Date().getTime() + (this.countdown * 1000);\r\n          localStorage.setItem('verificationExpireTime', expireTime.toString());\r\n        }\r\n      }, 1000);\r\n    },\r\n    navigateTo(path) {\r\n      // 如果当前路径与目标路径相同，则重新加载页面\r\n      if (this.$route.path === path) {\r\n        // 先跳转到一个临时路由（如果有的话）或者重新加载页面\r\n        this.$nextTick(() => {\r\n          window.scrollTo({\r\n            top: 0,\r\n            behavior: 'instant' // 使用即时滚动而不是平滑滚动\r\n          });\r\n          this.$router.go(0); // 刷新当前页面\r\n        });\r\n      } else {\r\n        // 不同路径，正常导航并滚动到顶部\r\n        this.$router.push(path);\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: 'instant'\r\n        });\r\n      }\r\n      this.currentPath = path;\r\n    },\r\n\r\n    validateUsername() {\r\n      const phoneRegex = /^1[3-9]\\d{9}$/;\r\n      if (!this.accountForm.username) {\r\n        this.errors.username = '请输入登录账号';\r\n      } else if (!phoneRegex.test(this.accountForm.username)) {\r\n        this.errors.username = '请输入有效的登录账号';\r\n      } else {\r\n        this.errors.username = '';\r\n      }\r\n    },\r\n\r\n    validatePassword() {\r\n      if (!this.accountForm.password) {\r\n        this.errors.password = '请输入登录密码';\r\n      } else if (this.accountForm.password.length < 8) {\r\n        this.errors.password = '密码长度至少为8位';\r\n      } else {\r\n        this.errors.password = '';\r\n      }\r\n    },\r\n\r\n\r\n    getVerificationCode() {\r\n      // 先验证手机号格式\r\n      this.validatePhone();\r\n      if (this.errors.phone) return;\r\n      // 显示发送中状态\r\n      this.codeSent = true;\r\n      this.isSendingCode = true;\r\n      // 调用发送验证码接口\r\n      postLogin(\"/auth/sendCode\", { phone: this.phoneForm.phone}).then(res => {\r\n        if (res.data.code === 200) {\r\n          // 存储验证码发送时间和手机号到localStorage\r\n          const now = new Date().getTime();\r\n          const expireTime = now + (60 * 1000); // 当前时间 + 60秒\r\n\r\n          localStorage.setItem('verificationPhone', this.phoneForm.phone);\r\n          localStorage.setItem('verificationExpireTime', expireTime.toString());\r\n\r\n          this.showNotificationMessage('验证码已发送，可能会有延迟，请耐心等待！', 'success');\r\n          this.startCountdown();\r\n        } else {\r\n          this.errors.code = res.data.msg || '验证码发送失败';\r\n          this.codeSent = false; // 发送失败时可重新发送\r\n          // this.showNotificationMessage(res.data.msg || '验证码发送失败', 'error');\r\n        }\r\n      })\r\n    },\r\n    validateCode() {\r\n      // 清空错误\r\n      this.errors.code = '';\r\n\r\n      // 前端基础验证（保持原有长度判断）\r\n      if (!this.phoneForm.code) {\r\n        this.errors.code = '请输入验证码';\r\n        return false;\r\n      }\r\n      if (this.phoneForm.code.length !== 4 || !/^\\d+$/.test(this.phoneForm.code)) {\r\n        this.errors.code = '验证码必须为4位数字';\r\n        return false;\r\n      }\r\n\r\n      try {\r\n        // 新增接口验证（参数与发送接口一致）\r\n        postAnyData(\"/auth/verifyCode\", {\r\n          phone: this.phoneForm.phone, // 必须携带手机号\r\n          code: this.phoneForm.code\r\n        }).then(res =>{\r\n          if (res.data.code == 200) {\r\n            postLogin(\"/auth/codeLogin\", {\r\n              phone: this.phoneForm.phone, // 必须携带手机号\r\n              code: this.phoneForm.code\r\n            }).then(res =>{\r\n              if (res.data.code == 200) {\r\n                setToken(res.data.token);\r\n                this.$emit(\"refresh-header\")\r\n                this.$router.push('/index');\r\n              } else if (res.data.code !== 200) {\r\n                this.errors.code = res.data.msg || '验证码错误';\r\n                // this.showNotificationMessage(res.data.msg || '验证码错误', 'error');\r\n                return false;\r\n              }\r\n            })\r\n            return true;\r\n          } else if (res.data.code !== 200) {\r\n            this.errors.code = res.data.msg || '验证码错误';\r\n            // this.showNotificationMessage(res.data.msg || '验证码错误', 'error');\r\n            return false;\r\n          }\r\n        })\r\n        return true;\r\n      } catch (error) {\r\n        this.errors.code = '网络异常，请稍后重试';\r\n        // this.showNotificationMessage('网络异常，请稍后重试', 'error');\r\n        return false;\r\n      }\r\n    },\r\n\r\n    phoneLogin() {\r\n      this.validatePhone();\r\n      this.validateCode();\r\n      if (this.errors.phone || this.errors.code) {\r\n        // 如果有错误，显示提示信息\r\n        const errorMessage = this.errors.phone || this.errors.code || '请正确填写手机号和验证码';\r\n        // this.showNotificationMessage(errorMessage, 'error');\r\n        return; // 如果表单无效，直接返回，不发送请求\r\n      }\r\n      this.generateKeyPair()\r\n      this.$emit(\"refresh-header\")\r\n    },\r\n    accountLogin() {\r\n      this.validateUsername();\r\n      this.validatePassword();\r\n\r\n      // 表单验证失败处理\r\n      if (this.errors.username || this.errors.password) {\r\n        const errorMessage = this.errors.username || this.errors.password || '请正确填写用户名和密码';\r\n        // this.showNotificationMessage(errorMessage, 'error');\r\n        return;\r\n      }\r\n\r\n      // 显示加载状态\r\n      const loading = this.$loading ? this.$loading({\r\n        lock: true,\r\n        text: '登录中...',\r\n        spinner: 'el-icon-loading',\r\n      }) : null;\r\n\r\n      // 调用登录API\r\n      postLogin(\"/auth/login\", this.accountForm)\r\n          .then(res => {\r\n            // 关闭加载状态\r\n            if (loading) loading.close();\r\n\r\n            if (res.data && res.data.code == 200) {\r\n              // 登录成功\r\n              setToken(res.data.token);\r\n              this.showNotificationMessage('登录成功', 'success');\r\n              this.$emit(\"refresh-header\")\r\n              this.$router.push('/index');\r\n              this.generateKeyPair()\r\n            } else {\r\n              // 登录失败，服务器返回了错误码\r\n              this.errors.password = res.data.msg;\r\n              // this.showNotificationMessage(res.data.msg || '登录失败，请检查账号和密码', 'error');\r\n            }\r\n          })\r\n          .catch(error => {\r\n            // 关闭加载状态\r\n            if (loading) loading.close();\r\n\r\n            // 处理网络错误或其他异常\r\n            this.errors.password = '网络异常，请稍后重试';\r\n            // this.showNotificationMessage('网络异常，请稍后重试', 'error');\r\n          });\r\n    },\r\n\r\n    togglePasswordVisibility() {\r\n      this.passwordVisible = !this.passwordVisible;\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    if (this.timer) {\r\n      clearInterval(this.timer);\r\n    }\r\n    this.$emit('hiden-layout')\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* Full page layout */\r\n.login-page {\r\n  display: flex;\r\n  min-height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Left side styling */\r\n.left-side {\r\n  flex: 1;\r\n  position: relative;\r\n  background: linear-gradient(135deg, #cdb3e5, #5127d5);\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 0rem;\r\n  color: #030303;\r\n}\r\n\r\n.logo-container {\r\n  z-index: 2;\r\n  padding: 1rem 0;\r\n}\r\n\r\n.logo {\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  color: white;\r\n}\r\n\r\n.logo-subtitle {\r\n  font-size: 14px;\r\n  margin: 0;\r\n}\r\n\r\n.headline-container {\r\n  margin-top: 10px;\r\n  text-align: left;\r\n  z-index: 2;\r\n  max-width: 80%;\r\n  margin-left: 100px;\r\n}\r\n\r\n.headline {\r\n  font-size: 25px;\r\n  font-weight: 500;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.subheadline {\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n  opacity: 0.9;\r\n  margin-bottom: 1rem\r\n}\r\n\r\n/* Animated background */\r\n.animated-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  overflow: hidden;\r\n  opacity: 0.4;\r\n}\r\n\r\n.hex-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.hex {\r\n  position: absolute;\r\n  width: 100px;\r\n  height: 110px;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);\r\n}\r\n\r\n.hex1 {\r\n  top: 20%;\r\n  left: 10%;\r\n  transform: scale(1.5);\r\n  animation: float 8s infinite ease-in-out;\r\n}\r\n\r\n.hex2 {\r\n  top: 60%;\r\n  left: 20%;\r\n  transform: scale(1.2);\r\n  animation: float 7s infinite ease-in-out reverse;\r\n}\r\n\r\n.hex3 {\r\n  top: 30%;\r\n  left: 50%;\r\n  transform: scale(1.3);\r\n  animation: float 10s infinite ease-in-out 1s;\r\n}\r\n\r\n.hex4 {\r\n  top: 70%;\r\n  left: 70%;\r\n  transform: scale(1.1);\r\n  animation: float 6s infinite ease-in-out 2s;\r\n}\r\n\r\n.hex5 {\r\n  top: 40%;\r\n  left: 80%;\r\n  transform: scale(1.4);\r\n  animation: float 9s infinite ease-in-out 3s;\r\n}\r\n\r\n.floating-cube {\r\n  position: absolute;\r\n  width: 50px;\r\n  height: 50px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  top: 25%;\r\n  left: 30%;\r\n  animation: float 8s infinite ease-in-out, rotate 15s infinite linear;\r\n}\r\n\r\n.floating-sphere {\r\n  position: absolute;\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  top: 50%;\r\n  left: 40%;\r\n  animation: float 10s infinite ease-in-out reverse;\r\n}\r\n\r\n.floating-diamond {\r\n  position: absolute;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: rotate(45deg);\r\n  top: 65%;\r\n  left: 60%;\r\n  animation: float 7s infinite ease-in-out 2s;\r\n}\r\n\r\n.ripple-effect {\r\n  position: absolute;\r\n  width: 200px;\r\n  height: 200px;\r\n  border-radius: 50%;\r\n  border: 3px solid rgba(255, 255, 255, 0.1);\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  animation: ripple 6s infinite linear;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(20px);\r\n  }\r\n}\r\n\r\n@keyframes rotate {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n@keyframes ripple {\r\n  0% {\r\n    width: 0;\r\n    height: 0;\r\n    opacity: 0.8;\r\n  }\r\n  100% {\r\n    width: 300px;\r\n    height: 300px;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* Right side styling */\r\n.right-side {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #f8f9fa;\r\n  padding: 2rem;\r\n}\r\n\r\n.login-form-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  padding: 2rem;\r\n  width: 100%;\r\n  max-width: 400px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.login-form-container h3 {\r\n  font-size: 24px;\r\n  font-weight: 500;\r\n  margin-bottom: 20px;\r\n  text-align: center;\r\n  color: #333;\r\n}\r\n\r\n.login-tabs {\r\n  display: flex;\r\n  border-bottom: 1px solid #e0e0e0;\r\n  margin-bottom: 20px;\r\n  /*height: 70px;*/\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  padding: 10px 0;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  position: relative;\r\n  color: #666;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #4169E1;\r\n  font-weight: 500;\r\n}\r\n\r\n.tab-item.active:after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -1px;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 2px;\r\n  background-color: #4169E1;\r\n}\r\n\r\n/* 表单容器，定义固定高度 */\r\n.form-container {\r\n  min-height: 300px;\r\n  position: relative;\r\n  height: 330px;\r\n}\r\n\r\n.login-form {\r\n  margin-top: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.form-note {\r\n  color: #999;\r\n  font-size: 12px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.input-group {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n}\r\n\r\n.input-group input {\r\n  width: 100%;\r\n  padding: 12px 15px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.input-group input:focus {\r\n  outline: none;\r\n  border-color: #6a26cd;\r\n}\r\n/*错误输入框变红*/\r\n.input-group.error input {\r\n  outline: none;\r\n  border: 2px solid #ff4d4f; /* 红色边框 */\r\n  /*background-color: #fff1f0; !* 淡红色背景 *!*/\r\n}\r\n\r\n\r\n/* 错误信息容器，固定高度 */\r\n.error-container {\r\n  min-height: 10px;\r\n  display: block;\r\n}\r\n\r\n/* 验证码输入框与按钮在同一行 */\r\n.verification-code .code-input-container {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.verification-code input {\r\n  flex: 1;\r\n  margin-right: 0; /* 移除原有的右边距 */\r\n}\r\n\r\n.error-container {\r\n  order: 3; /* 将错误容器放在最下方 */\r\n  width: 100%;\r\n  margin-top: 4px;\r\n}\r\n\r\n.get-code-btn-inline {\r\n  flex-shrink: 0;\r\n  width: 130px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  background-color: #2196f3;\r\n  color: #ffffff;\r\n}\r\n\r\n/* 密码输入容器，确保图标垂直居中 */\r\n.password-input-container {\r\n  position: relative;\r\n}\r\n\r\n.password-toggle {\r\n  position: absolute;\r\n  right: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\ninput[type=\"password\"]::-ms-reveal,\r\ninput[type=\"password\"]::-webkit-credentials-auto-fill-button,\r\ninput[type=\"password\"]::-webkit-clear-button {\r\n  display: none !important;\r\n  pointer-events: none;\r\n}\r\n\r\n.eye-icon {\r\n  display: inline-block;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle></svg>');\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  opacity: 0.5;\r\n}\r\n\r\n.eye-icon.visible {\r\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"></path><circle cx=\"12\" cy=\"12\" r=\"3\"></circle><line x1=\"1\" y1=\"1\" x2=\"23\" y2=\"23\"></line></svg>');\r\n}\r\n\r\n.agreement-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.agreement-text a {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n\r\n.login-btn {\r\n  width: 100%;\r\n  padding: 12px 0;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.login-btn:hover:not(:disabled) {\r\n  background-color: #4169E1;\r\n}\r\n\r\n.login-btn:disabled {\r\n  background-color: #2196f3;\r\n  cursor: not-allowed;\r\n}\r\n/*错误提示词提示*/\r\n.error-message {\r\n  color: #f44336;\r\n  font-size: 12px;\r\n  margin-top: 0px;\r\n  /*margin-left: 5px;*/\r\n  max-height: 10px;\r\n}\r\n\r\n/* 人机验证弹窗样式 */\r\n.captcha-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.captcha-container {\r\n  background-color: white;\r\n  border-radius: 8px;\r\n  width: 350px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.captcha-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.captcha-header h4 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  color: #333;\r\n}\r\n\r\n.close-btn {\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: #999;\r\n}\r\n\r\n.captcha-content {\r\n  padding: 20px;\r\n}\r\n\r\n.captcha-content p {\r\n  margin-top: 0;\r\n  margin-bottom: 20px;\r\n  color: #666;\r\n}\r\n\r\n.logo-area {\r\n  flex: 0 0 auto;\r\n  margin-right: 10px;\r\n}\r\n\r\n.logo-link {\r\n  display: flex;\r\n  align-items: center;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n}\r\n\r\n.logo-area img {\r\n  height: 30px;\r\n  max-width: 100%;\r\n}\r\n\r\n.logo-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-left: 10px;\r\n}\r\n\r\n/* 调整提示文字大小 */\r\n.input-group input::placeholder {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 独立验证码按钮样式 */\r\n.get-code-btn-standalone {\r\n  margin-top: 10px;\r\n  padding: 10px 15px;\r\n  background-color: #2196f3;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.get-code-btn-standalone:hover:not(:disabled) {\r\n  background-color: #5a20b0;\r\n}\r\n\r\n.get-code-btn-standalone:disabled {\r\n  background-color: #9254de;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Login link */\r\n.login-link {\r\n  margin-top: 15px;\r\n  text-align: center;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-link a {\r\n  color: #4169E1;\r\n  text-decoration: none;\r\n}\r\n.divider {\r\n  margin: 0 10px;\r\n  color: #ddd;\r\n}\r\n@media screen and (max-width: 768px) {\r\n  .left-side {\r\n    display: none; /* 在手机端隐藏左侧背景 */\r\n  }\r\n\r\n  .right-side {\r\n    flex: 1 0 100%; /* 让右侧占据全部宽度 */\r\n    padding: 1rem; /* 减少内边距以适应小屏幕 */\r\n  }\r\n\r\n  .login-form-container {\r\n    max-width: 100%; /* 让登录表单占据全部可用宽度 */\r\n    box-shadow: none; /* 移除阴影以节省空间 */\r\n    padding: 1.5rem; /* 调整内边距 */\r\n  }\r\n\r\n}\r\n</style>\r\n"], "mappings": ";;;;;AAmKA,SAAAA,WAAA,EAAAC,UAAA,EAAAC,SAAA,EAAAC,YAAA;AACA,SAAAC,QAAA,EAAAC,QAAA,EAAAC,WAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,OAAA;AACA,OAAAC,eAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAJ,iBAAA;IACAE;EACA;EACAG,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,SAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACAC,WAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,eAAA;MACAC,MAAA;QACAN,KAAA;QACAC,IAAA;QACAE,QAAA;QACAC,QAAA;MACA;MACAG,QAAA;MACAC,SAAA;MACAC,KAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,gBAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,KAAAC,mBAAA;IACA,KAAAC,KAAA;EACA;EACAC,UAAA;IACA,KAAAD,KAAA;EACA;;EACAE,OAAA;IACAC,wBAAAC,OAAA,EAAAC,IAAA;MACA,KAAAV,mBAAA,GAAAS,OAAA;MACA,KAAAR,gBAAA,GAAAS,IAAA;MACA,KAAAX,gBAAA;IACA;IACA;IACA,MAAAY,gBAAA;MACA;QACA,KAAAC,KAAA;QACA;QACA,MAAAC,OAAA,SAAAC,MAAA,CAAAC,MAAA,CAAAC,WAAA,CACA;UACAhC,IAAA;UACAiC,aAAA;UACAC,cAAA,MAAAC,UAAA;UACAC,IAAA;YAAApC,IAAA;UAAA;QACA,GACA,MACA,uBACA;;QAEA;QACA,MAAAqC,iBAAA,SAAAP,MAAA,CAAAC,MAAA,CAAAO,SAAA,SAAAT,OAAA,CAAAU,SAAA;QACA,MAAAC,YAAA,GAAAC,IAAA,CAAAC,MAAA,CAAAC,YAAA,QAAAR,UAAA,CAAAE,iBAAA;;QAEA;QACA,MAAAO,kBAAA,SAAAd,MAAA,CAAAC,MAAA,CAAAO,SAAA,UAAAT,OAAA,CAAAgB,UAAA;QACA,MAAAC,aAAA,GAAAL,IAAA,CAAAC,MAAA,CAAAC,YAAA,QAAAR,UAAA,CAAAS,kBAAA;QAEA,MAAAG,GAAA;UAAAP,YAAA;UAAAM;QAAA;QACAhD,OAAA,CAAAkD,GAAA,gBAAAR,YAAA;QACA1C,OAAA,CAAAkD,GAAA,iBAAAF,aAAA;MACA,SAAAG,GAAA;QACA,KAAArB,KAAA;MACA;QACA,KAAAsB,SAAA;MACA;IACA;IAEA9B,oBAAA;MACA;MACA,MAAA+B,WAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,MAAAC,UAAA,GAAAF,YAAA,CAAAC,OAAA;MAEA,IAAAF,WAAA,IAAAG,UAAA;QACA,MAAAC,GAAA,OAAAC,IAAA,GAAAC,OAAA;QACA,MAAAC,QAAA,GAAAC,IAAA,CAAAC,IAAA,EAAAC,QAAA,CAAAP,UAAA,IAAAC,GAAA;;QAEA;QACA,IAAAG,QAAA;UACA,KAAA9C,QAAA;UACA,KAAAC,SAAA,GAAA6C,QAAA;UACA,KAAAI,cAAA;;UAEA;UACA,SAAA1D,SAAA,CAAAC,KAAA,KAAA8C,WAAA;YACA,KAAAvC,QAAA;UACA;QACA;UACA;UACA,KAAAmD,qBAAA;QACA;MACA;IACA;IAEAA,sBAAA;MACAX,YAAA,CAAAY,UAAA;MACAZ,YAAA,CAAAY,UAAA;MACA,KAAApD,QAAA;MACA,KAAAC,SAAA;MACA,SAAAC,KAAA;QACAmD,aAAA,MAAAnD,KAAA;QACA,KAAAA,KAAA;MACA;IACA;IAEAoD,cAAA;MACA,MAAAC,UAAA;MACA,UAAA/D,SAAA,CAAAC,KAAA;QACA,KAAAM,MAAA,CAAAN,KAAA;MACA,YAAA8D,UAAA,CAAAC,IAAA,MAAAhE,SAAA,CAAAC,KAAA;QACA,KAAAM,MAAA,CAAAN,KAAA;MACA;QACA,KAAAM,MAAA,CAAAN,KAAA;;QAEA;QACA,MAAA8C,WAAA,GAAAC,YAAA,CAAAC,OAAA;QACA,MAAAC,UAAA,GAAAF,YAAA,CAAAC,OAAA;QAEA,IAAAF,WAAA,UAAA/C,SAAA,CAAAC,KAAA,IAAAiD,UAAA;UACA,MAAAC,GAAA,OAAAC,IAAA,GAAAC,OAAA;UACA,MAAAC,QAAA,GAAAC,IAAA,CAAAC,IAAA,EAAAC,QAAA,CAAAP,UAAA,IAAAC,GAAA;UAEA,IAAAG,QAAA;YACA,KAAA9C,QAAA;YACA,KAAAC,SAAA,GAAA6C,QAAA;YACA;YACA,UAAA5C,KAAA;cACA,KAAAgD,cAAA;YACA;UACA;QACA;MACA;IACA;IAEA,MAAAO,kBAAA;MACA;MACA,KAAA1D,MAAA,CAAAL,IAAA;;MAEA;MACA,UAAAF,SAAA,CAAAE,IAAA;QACA,KAAAK,MAAA,CAAAL,IAAA;QACA;MACA;MACA,SAAAF,SAAA,CAAAE,IAAA,CAAAgE,MAAA,mBAAAF,IAAA,MAAAhE,SAAA,CAAAE,IAAA;QACA,KAAAK,MAAA,CAAAL,IAAA;QACA;MACA;IACA;IACAwD,eAAA;MACA;MACA,SAAAhD,KAAA;QACAmD,aAAA,MAAAnD,KAAA;MACA;;MAEA;MACA,KAAAA,KAAA,GAAAyD,WAAA;QACA,SAAA1D,SAAA;UACAoD,aAAA,MAAAnD,KAAA;UACA,KAAAA,KAAA;UACA,KAAAF,QAAA;UACA,KAAAC,SAAA;UACA;UACA,KAAAkD,qBAAA;QACA;UACA,KAAAlD,SAAA;UACA;UACA,MAAAyC,UAAA,OAAAE,IAAA,GAAAC,OAAA,UAAA5C,SAAA;UACAuC,YAAA,CAAAoB,OAAA,2BAAAlB,UAAA,CAAAmB,QAAA;QACA;MACA;IACA;IACAC,WAAAC,IAAA;MACA;MACA,SAAAC,MAAA,CAAAD,IAAA,KAAAA,IAAA;QACA;QACA,KAAAE,SAAA;UACAC,MAAA,CAAAC,QAAA;YACAC,GAAA;YACAC,QAAA;UACA;;UACA,KAAAC,OAAA,CAAAC,EAAA;QACA;MACA;QACA;QACA,KAAAD,OAAA,CAAAE,IAAA,CAAAT,IAAA;QACAG,MAAA,CAAAC,QAAA;UACAC,GAAA;UACAC,QAAA;QACA;MACA;MACA,KAAAI,WAAA,GAAAV,IAAA;IACA;IAEAW,iBAAA;MACA,MAAAnB,UAAA;MACA,UAAA5D,WAAA,CAAAC,QAAA;QACA,KAAAG,MAAA,CAAAH,QAAA;MACA,YAAA2D,UAAA,CAAAC,IAAA,MAAA7D,WAAA,CAAAC,QAAA;QACA,KAAAG,MAAA,CAAAH,QAAA;MACA;QACA,KAAAG,MAAA,CAAAH,QAAA;MACA;IACA;IAEA+E,iBAAA;MACA,UAAAhF,WAAA,CAAAE,QAAA;QACA,KAAAE,MAAA,CAAAF,QAAA;MACA,gBAAAF,WAAA,CAAAE,QAAA,CAAA6D,MAAA;QACA,KAAA3D,MAAA,CAAAF,QAAA;MACA;QACA,KAAAE,MAAA,CAAAF,QAAA;MACA;IACA;IAGA+E,oBAAA;MACA;MACA,KAAAtB,aAAA;MACA,SAAAvD,MAAA,CAAAN,KAAA;MACA;MACA,KAAAO,QAAA;MACA,KAAA6E,aAAA;MACA;MACAjG,SAAA;QAAAa,KAAA,OAAAD,SAAA,CAAAC;MAAA,GAAAqF,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAzF,IAAA,CAAAI,IAAA;UACA;UACA,MAAAiD,GAAA,OAAAC,IAAA,GAAAC,OAAA;UACA,MAAAH,UAAA,GAAAC,GAAA;;UAEAH,YAAA,CAAAoB,OAAA,2BAAApE,SAAA,CAAAC,KAAA;UACA+C,YAAA,CAAAoB,OAAA,2BAAAlB,UAAA,CAAAmB,QAAA;UAEA,KAAAjD,uBAAA;UACA,KAAAsC,cAAA;QACA;UACA,KAAAnD,MAAA,CAAAL,IAAA,GAAAqF,GAAA,CAAAzF,IAAA,CAAA0F,GAAA;UACA,KAAAhF,QAAA;UACA;QACA;MACA;IACA;;IACAiF,aAAA;MACA;MACA,KAAAlF,MAAA,CAAAL,IAAA;;MAEA;MACA,UAAAF,SAAA,CAAAE,IAAA;QACA,KAAAK,MAAA,CAAAL,IAAA;QACA;MACA;MACA,SAAAF,SAAA,CAAAE,IAAA,CAAAgE,MAAA,mBAAAF,IAAA,MAAAhE,SAAA,CAAAE,IAAA;QACA,KAAAK,MAAA,CAAAL,IAAA;QACA;MACA;MAEA;QACA;QACAhB,WAAA;UACAe,KAAA,OAAAD,SAAA,CAAAC,KAAA;UAAA;UACAC,IAAA,OAAAF,SAAA,CAAAE;QACA,GAAAoF,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAzF,IAAA,CAAAI,IAAA;YACAd,SAAA;cACAa,KAAA,OAAAD,SAAA,CAAAC,KAAA;cAAA;cACAC,IAAA,OAAAF,SAAA,CAAAE;YACA,GAAAoF,IAAA,CAAAC,GAAA;cACA,IAAAA,GAAA,CAAAzF,IAAA,CAAAI,IAAA;gBACAX,QAAA,CAAAgG,GAAA,CAAAzF,IAAA,CAAA4F,KAAA;gBACA,KAAAzE,KAAA;gBACA,KAAA6D,OAAA,CAAAE,IAAA;cACA,WAAAO,GAAA,CAAAzF,IAAA,CAAAI,IAAA;gBACA,KAAAK,MAAA,CAAAL,IAAA,GAAAqF,GAAA,CAAAzF,IAAA,CAAA0F,GAAA;gBACA;gBACA;cACA;YACA;YACA;UACA,WAAAD,GAAA,CAAAzF,IAAA,CAAAI,IAAA;YACA,KAAAK,MAAA,CAAAL,IAAA,GAAAqF,GAAA,CAAAzF,IAAA,CAAA0F,GAAA;YACA;YACA;UACA;QACA;QACA;MACA,SAAAhE,KAAA;QACA,KAAAjB,MAAA,CAAAL,IAAA;QACA;QACA;MACA;IACA;IAEAyF,WAAA;MACA,KAAA7B,aAAA;MACA,KAAA2B,YAAA;MACA,SAAAlF,MAAA,CAAAN,KAAA,SAAAM,MAAA,CAAAL,IAAA;QACA;QACA,MAAA0F,YAAA,QAAArF,MAAA,CAAAN,KAAA,SAAAM,MAAA,CAAAL,IAAA;QACA;QACA;MACA;;MACA,KAAAqB,eAAA;MACA,KAAAN,KAAA;IACA;IACA4E,aAAA;MACA,KAAAX,gBAAA;MACA,KAAAC,gBAAA;;MAEA;MACA,SAAA5E,MAAA,CAAAH,QAAA,SAAAG,MAAA,CAAAF,QAAA;QACA,MAAAuF,YAAA,QAAArF,MAAA,CAAAH,QAAA,SAAAG,MAAA,CAAAF,QAAA;QACA;QACA;MACA;;MAEA;MACA,MAAAyF,OAAA,QAAAC,QAAA,QAAAA,QAAA;QACAC,IAAA;QACAC,IAAA;QACAC,OAAA;MACA;;MAEA;MACA9G,SAAA,qBAAAe,WAAA,EACAmF,IAAA,CAAAC,GAAA;QACA;QACA,IAAAO,OAAA,EAAAA,OAAA,CAAAK,KAAA;QAEA,IAAAZ,GAAA,CAAAzF,IAAA,IAAAyF,GAAA,CAAAzF,IAAA,CAAAI,IAAA;UACA;UACAX,QAAA,CAAAgG,GAAA,CAAAzF,IAAA,CAAA4F,KAAA;UACA,KAAAtE,uBAAA;UACA,KAAAH,KAAA;UACA,KAAA6D,OAAA,CAAAE,IAAA;UACA,KAAAzD,eAAA;QACA;UACA;UACA,KAAAhB,MAAA,CAAAF,QAAA,GAAAkF,GAAA,CAAAzF,IAAA,CAAA0F,GAAA;UACA;QACA;MACA,GACAY,KAAA,CAAA5E,KAAA;QACA;QACA,IAAAsE,OAAA,EAAAA,OAAA,CAAAK,KAAA;;QAEA;QACA,KAAA5F,MAAA,CAAAF,QAAA;QACA;MACA;IACA;;IAEAgG,yBAAA;MACA,KAAA/F,eAAA,SAAAA,eAAA;IACA;EACA;EACAgG,cAAA;IACA,SAAA5F,KAAA;MACAmD,aAAA,MAAAnD,KAAA;IACA;IACA,KAAAO,KAAA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}