"use strict";(self["webpackChunkportal_ui"]=self["webpackChunkportal_ui"]||[]).push([[91],{1362:function(o,t,n){n.r(t);var e=new URL(n(1404),n.b),l=new URL(n(4117),n.b),f=new URL(n(8917),n.b),i=new URL(n(3178),n.b),r=new URL(n(4214),n.b),p=new URL(n(301),n.b),s=new URL(n(1524),n.b),c=new URL(n(931),n.b),a='<h1 id="容器化部署-stablediffusion21-webui-应用"><font style="color:#020817">容器化部署 StableDiffusion2.1-WebUI 应用</font></h1> <h2 id="1-部署步骤"><font style="color:#020817">1 部署步骤</font></h2> <p><font style="color:#020817">我们提供了构建完毕的 Stable-Diffusion-WebUI 镜像，您可以直接部署使用。</font></p> <h3 id="11-访问天工开物控制台，点击新增部署。"><font style="color:#020817">1.1 访问</font><a href="https://tiangongkaiwu.top/#/console">天工开物控制台</a><font style="color:#020817">，点击新增部署。</font></h3> <p><img src="'+e+'" alt=""></p> <h3 id="12-基于自身需要进行配置，参考配置为单卡-4090-和-1-个节点（初次使用进行调试）。"><font style="color:#020817">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></h3> <p><img src="'+l+'" alt=""></p> <h3 id="13-选择相应预制镜像"><font style="color:#020817">1.3 选择相应预制镜像</font></h3> <p><img src="'+f+'" alt=""></p> <h3 id="14-点击部署服务，耐心等待节点拉取镜像并启动。"><font style="color:#020817">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</font></h3> <p><img src="'+i+'" alt=""></p> <h3 id="15-节点启动后，你所在任务详情页中看到的内容可能如下："><font style="color:#020817">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font></h3> <p><img src="'+r+'" alt=""></p> <h3 id="16-我们可以点击快捷访问下方7860端口的链接，测试-gradio-运行情况"><font style="color:#020817">1.6 我们可以点击快捷访问下方“7860”端口的链接，测试 Gradio 运行情况</font></h3> <p><font style="color:#020817">接下来填写 prompt，描述我们希望图片的内容。</font></p> <p><img src="'+p+'" alt=""></p> <p><font style="color:#020817">最后点击生成按钮，接下来我们耐心稍等片刻，可以看到图片已经生成。</font></p> <p><img src="'+s+'" alt=""></p> <h2 id="2-构建镜像"><font style="color:#020817">2 构建镜像</font></h2> <p><font style="color:#020817">如果您对如何构建该镜像感兴趣，可以继续查看接下来的教程。</font></p> <h3 id="21-克隆项目"><font style="color:#020817">2.1 克隆项目</font></h3> <p><font style="color:#020817">首先，我们需要在本地磁盘中新建一个文件夹，将 StableDiffusion-WebUI 项目克隆下来。 运行如下命令：</font></p> <pre><code class="language-dockerfile"><NAME_EMAIL>:AUTOMATIC1111/stable-diffusion-webui.git\n</code></pre> <h3 id="22-下载模型"><font style="color:#020817">2.2 下载模型</font></h3> <p><font style="color:#020817">我们需要 sd 的 2.1 版本模型，为此，我们可以选择到 HuggingFace 下载。</font></p> <p><font style="color:#020817">打开下面的 URL，点击页面中模型的下载按钮，耐心等待模型下载完毕。 </font><a href="https://huggingface.co/stabilityai/stable-diffusion-2-1/tree/main"><font style="color:#2f8ef4">https://huggingface.co/stabilityai/stable-diffusion-2-1/tree/main</font></a></p> <p><img src="'+c+'" alt=""></p> <p><font style="color:#020817">下载完毕后，将其放入 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">stable-diffusion-webui\\models\\Stable-diffusion</font><font style="color:#2f8ef4"> </font><font style="color:#020817">目录下。</font></p> <h3 id="23-修改源码"><font style="color:#020817">2.3 修改源码</font></h3> <p><font style="color:#020817">默认该版本 gradio 的 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">server_name</font><font style="color:#020817"> 为 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">127.0.0.1</font><font style="color:#020817"> ，这是一个本机可访问的地址，但无法提供给外界主机访问，因而我们需要修改其配置，而这一步最方便的办法就是直接修改其源码。</font></p> <p><font style="color:#020817">我们在根目录下搜索 </font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">initialize_util.py</font><font style="color:#020817"> 这个文件，将 gradio_server_name 方法中的代码进行覆盖，使其返回内容如下。</font></p> <pre><code class="language-dockerfile">def gradio_server_name():\n    return &quot;0.0.0.0&quot;\n</code></pre> <h3 id="24-编写-dockerfile-文件"><font style="color:#020817">2.4 编写 Dockerfile 文件</font></h3> <p><font style="color:#020817">在</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">stable-diffusion-webui</font><font style="color:#020817">同级目录下新建一个文本文件，命名为</font><font style="color:#2f8ef4;background-color:rgba(142,150,170,.14)">Dockerfile</font><font style="color:#020817">。</font></p> <p><font style="color:#020817">将下列内容粘贴到文件中：</font></p> <pre><code class="language-dockerfile">FROM nvcr.io/nvidia/pytorch:24.08-py3\n\nENV venv_dir=&quot;-&quot;\n\n# 复制代码\nCOPY ./stable-diffusion-webui /app\n\n# 强制卸载冲突 OpenCV 包\nRUN pip uninstall -y opencv-python-headless opencv-contrib-python opencv-python || true\n\nRUN sed -i &#39;s/\\r//g&#39; /app/webui.sh &amp;&amp; \\\n    sed -i &#39;s/\\r//g&#39; /app/webui-user.sh &amp;&amp; \\\n    chmod +x /app/webui.sh\n\nRUN apt-get update &amp;&amp; \\\n    apt-get install -y --no-install-recommends \\\n        libgl1 \\\n        libsm6 \\\n        libxrender1 \\\n        libxext6 \\\n        ffmpeg \\\n        libgl1-mesa-glx &amp;&amp; \\\n    rm -rf /var/lib/apt/lists/*\n\nCOPY CLIP-d50d76daa670286dd6cacf3bcd80b5e4823fc8e1.zip /app/clip.zip\n\nRUN pip install /app/clip.zip --prefer-binary\n\nWORKDIR /app\n\n# 仅安装其他Python依赖\nRUN pip install --no-cache-dir -r requirements.txt\n\nCMD [&quot;/bin/sh&quot;, &quot;-c&quot;, &quot;/app/webui.sh&quot;]\n</code></pre> <h3 id="25-构建镜像"><font style="color:#020817">2.5 构建镜像</font></h3> <p><font style="color:#020817">运行如下命令：</font></p> <pre><code class="language-shell">docker build -t sd-webui:0.1 .\n</code></pre> <p><font style="color:#020817">耐心等待镜像构建完毕即可。如果构建完毕后，运行时发现镜像缺少某些依赖，可下载好后通过命令复制到镜像中，重新执行构建。</font></p> <p><strong><font style="color:#67676c"></font></strong></p> <p><br><br> <font style="color:#b2b2b2">最后更新于: 2025/6/18 14:35</font></p> ';t["default"]=a},1404:function(o,t,n){o.exports=n.p+"img/universal1.9a1b3f4b.png"},4117:function(o,t,n){o.exports=n.p+"img/universal2.4306636e.png"},8917:function(o,t,n){o.exports=n.p+"img/webui2-1v3.ccbbdf6e.png"},3178:function(o,t,n){o.exports=n.p+"img/webui2-1v4.1a79bf43.png"},4214:function(o,t,n){o.exports=n.p+"img/webui2-1v5.185bf80b.png"},301:function(o,t,n){o.exports=n.p+"img/webui2-1v6.c7e32bc9.png"},1524:function(o,t,n){o.exports=n.p+"img/webui2-1v7.a4df661b.png"},931:function(o,t,n){o.exports=n.p+"img/webui2-1v8.414b4e99.png"}}]);
//# sourceMappingURL=docs18.d4b0ab50.js.map