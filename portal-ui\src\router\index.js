import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
    {
        path:'/',
        redirect:'/index'
    },
    {
        path: '/index',
        name: 'index',
        component: () => import('../views/Index/IndexView.vue')
    },

    {
        path: '/product',
        name: 'product',
        component: () => import('../views/Product/ProductView.vue'),
    },
    // {
    //     path: '/product/productId/:productId',
    //     name: 'productDetails',
    //     component: () => import("../views/ProductDetailsView.vue")
    // },
    {
        path: '/example',
        name: 'example',
        component: () => import('../views/ExampleView.vue')
    },
    {
        path: '/algorithmcommunity',
        name: 'algorithmcommunity',
        component: () => import('../views/AlgorithmCommunity.vue')
    },
    {
        path: '/news',
        name: 'news',
        component: () => import('../views/NewsView.vue')
    },
    {
        path: '/news/newsId/:newsId',
        name: 'newsDetails',
        component: () => import('../views/NewsDetailsView.vue')
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('../views/Login/login.vue')
    },
    {
        path: '/register',
        name: 'register',
        component: () => import('../views/Login/register.vue')
    },
    {
        path: '/forgetpass',
        name: 'forgetpass',
        component: () => import('../views/Login/ForgetPassView.vue')

    },
    {
        path: '/about',
        name: 'about',
        component: () => import('../views/AboutView.vue')
    },
    {
        path: '/help',
        redirect: '/help/summary'
    },
    {
        path: '/help/:doc?',
        name: 'help',
        component: () => import('../views/HelpView.vue'),
        props: true
    },
    {
        path: '/order',
        name: 'order',
        component: () => import('../views/Product/OrderDetail.vue')
    },
    {
        path: '/personal',
        name: 'personal',
        component: () => import('../views/Personal/personal.vue')
    },
    {
        path: '/userorder',
        name: 'userorder',
        component: () => import('../views/Ordermange/OrderView.vue')
    },
    {
        path: '/console',
        name: 'userorder',
        component: () => import('../views/Console.vue')
    }
    //
    // {
    //     path: '/topup',
    //     name: 'topup',
    //     component: () => import('../views/TopupView.vue')
    // }
]

const router = new VueRouter({
    routes
})

export default router
