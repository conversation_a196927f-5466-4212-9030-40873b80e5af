{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('transition', {\n    attrs: {\n      \"name\": \"slide\"\n    }\n  }, [_vm.visible ? _c('div', {\n    class: ['notification', `notification-${_vm.type}`],\n    style: {\n      minHeight: _vm.minHeight\n    },\n    attrs: {\n      \"role\": \"alert\"\n    }\n  }, [_c('div', {\n    staticClass: \"notification-content\"\n  }, [_vm.type === 'error' ? _c('div', {\n    staticClass: \"icon-wrapper\"\n  }, [_c('span', {\n    staticClass: \"error-icon\"\n  }, [_vm._v(\"×\")])]) : _vm._e(), _c('span', {\n    staticClass: \"message\"\n  }, [_vm._v(_vm._s(_vm.message))]), _vm.closable ? _c('button', {\n    staticClass: \"close-btn\",\n    on: {\n      \"click\": _vm.close\n    }\n  }, [_vm._v(\"×\")]) : _vm._e()])]) : _vm._e()]);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "visible", "class", "type", "style", "minHeight", "staticClass", "_v", "_e", "_s", "message", "closable", "on", "close", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/components/common/header/SlideNotification.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('transition',{attrs:{\"name\":\"slide\"}},[(_vm.visible)?_c('div',{class:['notification', `notification-${_vm.type}`],style:({ minHeight: _vm.minHeight }),attrs:{\"role\":\"alert\"}},[_c('div',{staticClass:\"notification-content\"},[(_vm.type === 'error')?_c('div',{staticClass:\"icon-wrapper\"},[_c('span',{staticClass:\"error-icon\"},[_vm._v(\"×\")])]):_vm._e(),_c('span',{staticClass:\"message\"},[_vm._v(_vm._s(_vm.message))]),(_vm.closable)?_c('button',{staticClass:\"close-btn\",on:{\"click\":_vm.close}},[_vm._v(\"×\")]):_vm._e()])]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,YAAY,EAAC;IAACE,KAAK,EAAC;MAAC,MAAM,EAAC;IAAO;EAAC,CAAC,EAAC,CAAEH,GAAG,CAACI,OAAO,GAAEH,EAAE,CAAC,KAAK,EAAC;IAACI,KAAK,EAAC,CAAC,cAAc,EAAG,gBAAeL,GAAG,CAACM,IAAK,EAAC,CAAC;IAACC,KAAK,EAAE;MAAEC,SAAS,EAAER,GAAG,CAACQ;IAAU,CAAE;IAACL,KAAK,EAAC;MAAC,MAAM,EAAC;IAAO;EAAC,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACQ,WAAW,EAAC;EAAsB,CAAC,EAAC,CAAET,GAAG,CAACM,IAAI,KAAK,OAAO,GAAEL,EAAE,CAAC,KAAK,EAAC;IAACQ,WAAW,EAAC;EAAc,CAAC,EAAC,CAACR,EAAE,CAAC,MAAM,EAAC;IAACQ,WAAW,EAAC;EAAY,CAAC,EAAC,CAACT,GAAG,CAACU,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAACV,GAAG,CAACW,EAAE,EAAE,EAACV,EAAE,CAAC,MAAM,EAAC;IAACQ,WAAW,EAAC;EAAS,CAAC,EAAC,CAACT,GAAG,CAACU,EAAE,CAACV,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACa,OAAO,CAAC,CAAC,CAAC,CAAC,EAAEb,GAAG,CAACc,QAAQ,GAAEb,EAAE,CAAC,QAAQ,EAAC;IAACQ,WAAW,EAAC,WAAW;IAACM,EAAE,EAAC;MAAC,OAAO,EAACf,GAAG,CAACgB;IAAK;EAAC,CAAC,EAAC,CAAChB,GAAG,CAACU,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAACV,GAAG,CAACW,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAACX,GAAG,CAACW,EAAE,EAAE,CAAC,CAAC;AACrlB,CAAC;AACD,IAAIM,eAAe,GAAG,EAAE;AAExB,SAASlB,MAAM,EAAEkB,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}