{"version": 3, "file": "js/635.0b14b624.js", "mappings": "6JAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACE,YAAY,kCAAkC,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACJ,EAAIK,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,QAAQ,CAACA,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIM,GAAG,WAAWN,EAAIO,GAAIP,EAAIQ,gBAAgB,SAASC,GAAK,OAAOP,EAAG,KAAK,CAACQ,IAAID,EAAIE,MAAM,CAACX,EAAIM,GAAGN,EAAIY,GAAGH,EAAIE,QAAQ,KAAI,KAAKT,EAAG,QAAQ,CAACA,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIM,GAAG,QAAQN,EAAIO,GAAIP,EAAIQ,gBAAgB,SAASC,GAAK,OAAOP,EAAG,KAAK,CAACQ,IAAID,EAAIE,MAAM,CAACX,EAAIM,GAAGN,EAAIY,GAAGH,EAAII,gBAAgB,KAAI,GAAGX,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIM,GAAG,YAAYN,EAAIO,GAAIP,EAAIQ,gBAAgB,SAASC,GAAK,OAAOP,EAAG,KAAK,CAACQ,IAAID,EAAIE,MAAM,CAACX,EAAIM,GAAGN,EAAIY,GAAGH,EAAIK,mBAAmB,KAAI,GAAGZ,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIM,GAAG,YAAYN,EAAIO,GAAIP,EAAIQ,gBAAgB,SAASC,GAAK,OAAOP,EAAG,KAAK,CAACQ,IAAID,EAAIE,MAAM,CAACX,EAAIM,GAAGN,EAAIY,GAAGH,EAAIM,mBAAmB,KAAI,GAAGb,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIM,GAAG,QAAQN,EAAIO,GAAIP,EAAIQ,gBAAgB,SAASC,GAAK,OAAOP,EAAG,KAAK,CAACQ,IAAID,EAAIE,MAAM,CAACX,EAAIM,GAAGN,EAAIY,GAAGH,EAAIO,UAAU,KAAI,GAAGd,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIM,GAAG,UAAUN,EAAIO,GAAIP,EAAIQ,gBAAgB,SAASC,GAAK,OAAOP,EAAG,KAAK,CAACQ,IAAID,EAAIE,MAAM,CAACX,EAAIM,GAAGN,EAAIY,GAAGH,EAAIQ,cAAc,KAAI,GAAGf,EAAG,KAAK,CAACA,EAAG,KAAK,CAACF,EAAIM,GAAG,QAAQN,EAAIO,GAAIP,EAAIQ,gBAAgB,SAASC,GAAK,OAAOP,EAAG,KAAK,CAACQ,IAAID,EAAIE,MAAM,CAACX,EAAIM,GAAGN,EAAIY,GAAGH,EAAIS,aAAa,KAAI,YACrwC,EACIC,EAAkB,CAAC,WAAY,IAAInB,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACJ,EAAIM,GAAG,aAAaJ,EAAG,IAAI,CAACE,YAAY,uBAAuB,CAACJ,EAAIM,GAAG,iCAC9N,GCgDA,GACAK,KAAA,gBACAS,OACA,OACAZ,eAAA,CACA,CACAG,KAAA,OACAE,aAAA,SACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,cAEA,CACAP,KAAA,OACAE,aAAA,QACAC,gBAAA,aACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,OACAC,UAAA,YAEA,CACAP,KAAA,QACAE,aAAA,SACAC,gBAAA,cACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,QACAC,UAAA,YAEA,CACAP,KAAA,QACAE,aAAA,SACAC,gBAAA,cACAC,gBAAA,cACAC,OAAA,QACAC,WAAA,QACAC,UAAA,YAEA,CACAP,KAAA,QACAE,aAAA,SACAC,gBAAA,eACAC,gBAAA,eACAC,OAAA,QACAC,WAAA,QACAC,UAAA,aAIA,GCvGyP,I,UCQrPG,GAAY,OACd,EACAtB,EACAoB,GACA,EACA,KACA,WACA,MAIF,EAAeE,EAAiB,O", "sources": ["webpack://portal-ui/./src/views/AboutView.vue", "webpack://portal-ui/src/views/AboutView.vue", "webpack://portal-ui/./src/views/AboutView.vue?aa2a", "webpack://portal-ui/./src/views/AboutView.vue?296c"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('section',{staticClass:\"section gpu-comparison-section\"},[_c('div',{staticClass:\"container\"},[_vm._m(0),_c('div',{staticClass:\"gpu-comparison-table\"},[_c('table',[_c('thead',[_c('tr',[_c('th',[_vm._v(\"GPU型号\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('th',{key:gpu.name},[_vm._v(_vm._s(gpu.name))])})],2)]),_c('tbody',[_c('tr',[_c('td',[_vm._v(\"架构\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.architecture))])})],2),_c('tr',[_c('td',[_vm._v(\"FP16性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp16Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"FP32性能\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.fp32Performance))])})],2),_c('tr',[_c('td',[_vm._v(\"显存\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memory))])})],2),_c('tr',[_c('td',[_vm._v(\"显存类型\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.memoryType))])})],2),_c('tr',[_c('td',[_vm._v(\"带宽\")]),_vm._l((_vm.comparisonGpus),function(gpu){return _c('td',{key:gpu.name},[_vm._v(_vm._s(gpu.bandwidth))])})],2)])])])])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"section-header\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"GPU性能对比\")]),_c('p',{staticClass:\"section-description\"},[_vm._v(\" 专业GPU性能详细对比，助您选择最适合的计算资源 \")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <section class=\"section gpu-comparison-section\">\r\n    <div class=\"container\">\r\n      <div class=\"section-header\">\r\n        <h2 class=\"section-title\">GPU性能对比</h2>\r\n        <p class=\"section-description\">\r\n          专业GPU性能详细对比，助您选择最适合的计算资源\r\n        </p>\r\n      </div>\r\n\r\n      <div class=\"gpu-comparison-table\">\r\n        <table>\r\n          <thead>\r\n          <tr>\r\n            <th>GPU型号</th>\r\n            <th v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.name }}</th>\r\n          </tr>\r\n          </thead>\r\n          <tbody>\r\n          <tr>\r\n            <td>架构</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.architecture }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>FP16性能</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp16Performance }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>FP32性能</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.fp32Performance }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>显存</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memory }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>显存类型</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.memoryType }}</td>\r\n          </tr>\r\n          <tr>\r\n            <td>带宽</td>\r\n            <td v-for=\"gpu in comparisonGpus\" :key=\"gpu.name\">{{ gpu.bandwidth }}</td>\r\n          </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </div>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'GpuComparison',\r\n  data() {\r\n    return {\r\n      comparisonGpus: [\r\n        {\r\n          name: 'A100',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '312 TFLOPS',\r\n          fp32Performance: '19.5 TFLOPS',\r\n          memory: '80 GB',\r\n          memoryType: 'HBM2',\r\n          bandwidth: '2,039 GB/s'\r\n        },\r\n        {\r\n          name: 'V100',\r\n          architecture: 'Volta',\r\n          fp16Performance: '125 TFLOPS',\r\n          fp32Performance: '15.7 TFLOPS',\r\n          memory: '32 GB',\r\n          memoryType: 'HBM2',\r\n          bandwidth: '900 GB/s'\r\n        },\r\n        {\r\n          name: 'A6000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '77.4 TFLOPS',\r\n          fp32Performance: '38.7 TFLOPS',\r\n          memory: '48 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '768 GB/s'\r\n        },\r\n        {\r\n          name: 'A5000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '54.2 TFLOPS',\r\n          fp32Performance: '27.8 TFLOPS',\r\n          memory: '24 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '768 GB/s'\r\n        },\r\n        {\r\n          name: 'A4000',\r\n          architecture: 'Ampere',\r\n          fp16Performance: '19.17 TFLOPS',\r\n          fp32Performance: '19.17 TFLOPS',\r\n          memory: '16 GB',\r\n          memoryType: 'GDDR6',\r\n          bandwidth: '448 GB/s'\r\n        }\r\n      ]\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.gpu-comparison-section {\r\n  background-color: #f9f9f9;\r\n  width: 100%;\r\n  max-width: 2560px;\r\n  margin: 0 auto;\r\n  padding: 60px 0;\r\n}\r\n\r\n.container {\r\n  width: 95%;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.section-header {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 32px;\r\n  color: #2c3e50;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-description {\r\n  font-size: 18px;\r\n  color: #7f8c8d;\r\n  max-width: 700px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.gpu-comparison-table {\r\n  width: 100%;\r\n  padding: 0;\r\n  border-collapse: collapse;\r\n  margin: 0;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  overflow-x: auto;\r\n}\r\n\r\n.gpu-comparison-table table {\r\n  width: 100%;\r\n  min-width: 1000px;\r\n}\r\n\r\n.gpu-comparison-table thead {\r\n  background-color: #cddfec;\r\n}\r\n\r\n.gpu-comparison-table thead tr th,\r\n.gpu-comparison-table tbody tr td {\r\n  padding: 18px 25px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  color: #2c3e50;\r\n  font-size: 16px;\r\n  min-width: 180px;\r\n  border-bottom: 1px solid #e0e5eb;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.gpu-comparison-table thead tr th {\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.gpu-comparison-table tbody tr td {\r\n  color: #333;\r\n  font-weight: normal;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:nth-child(even) {\r\n  background-color: #f9fafc;\r\n}\r\n\r\n.gpu-comparison-table tbody tr:hover {\r\n  background-color: #f1f6fd;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .container {\r\n    width: 98%;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .section-title {\r\n    font-size: 26px;\r\n  }\r\n\r\n  .section-description {\r\n    font-size: 16px;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./AboutView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./AboutView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./AboutView.vue?vue&type=template&id=37510926&scoped=true&\"\nimport script from \"./AboutView.vue?vue&type=script&lang=js&\"\nexport * from \"./AboutView.vue?vue&type=script&lang=js&\"\nimport style0 from \"./AboutView.vue?vue&type=style&index=0&id=37510926&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"37510926\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "_m", "_v", "_l", "comparisonGpus", "gpu", "key", "name", "_s", "architecture", "fp16Performance", "fp32Performance", "memory", "memoryType", "bandwidth", "staticRenderFns", "data", "component"], "sourceRoot": ""}