{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"help-layout\"\n  }, [_vm.isMobile ? _c('div', {\n    staticClass: \"mobile-controls\"\n  }, [_c('button', {\n    staticClass: \"sidebar-toggle\",\n    class: {\n      'active': _vm.sidebarVisible\n    },\n    on: {\n      \"click\": _vm.toggleSidebar\n    }\n  }, [_c('i', {\n    staticClass: \"icon-menu\"\n  }), _c('span', [_vm._v(\"菜单\")])]), _c('button', {\n    staticClass: \"toc-toggle\",\n    class: {\n      'active': _vm.tocVisible\n    },\n    on: {\n      \"click\": _vm.toggleToc\n    }\n  }, [_c('i', {\n    staticClass: \"icon-list\"\n  }), _c('span', [_vm._v(\"目录\")])])]) : _vm._e(), _vm.isMobile && (_vm.sidebarVisible || _vm.tocVisible) ? _c('div', {\n    staticClass: \"overlay\",\n    on: {\n      \"click\": _vm.closeAllPanels\n    }\n  }) : _vm._e(), _c('aside', {\n    ref: \"sidebar\",\n    staticClass: \"sidebar\",\n    class: {\n      'sidebar-hidden': !_vm.sidebarVisible && _vm.isMobile,\n      'sidebar-visible': _vm.sidebarVisible || !_vm.isMobile\n    }\n  }, [_vm.isMobile ? _c('div', {\n    staticClass: \"sidebar-header\"\n  }, [_c('span', {\n    staticClass: \"sidebar-title\"\n  }, [_vm._v(\"帮助文档\")]), _c('button', {\n    staticClass: \"close-btn\",\n    on: {\n      \"click\": _vm.closeSidebar\n    }\n  }, [_c('i', {\n    staticClass: \"icon-close\"\n  }, [_vm._v(\"×\")])])]) : _vm._e(), _c('div', {\n    staticClass: \"sidebar-menu\"\n  }, _vm._l(_vm.menu, function (category) {\n    return _c('div', {\n      key: category.title,\n      staticClass: \"menu-category\"\n    }, [_c('div', {\n      staticClass: \"category-title\"\n    }, [_vm._v(_vm._s(category.title))]), _c('ul', {\n      staticClass: \"menu-list\"\n    }, _vm._l(category.items, function (item) {\n      return _c('li', {\n        key: item.path,\n        staticClass: \"menu-item\"\n      }, [_c('router-link', {\n        staticClass: \"menu-link\",\n        class: {\n          'menu-link-active': _vm.isMenuActive(item.path)\n        },\n        attrs: {\n          \"to\": item.path\n        },\n        on: {\n          \"click\": _vm.onMenuItemClick\n        }\n      }, [_vm._v(\" \" + _vm._s(item.name) + \" \")])], 1);\n    }), 0)]);\n  }), 0)]), _c('main', {\n    ref: \"mainContent\",\n    staticClass: \"main-content\",\n    class: {\n      'content-expanded': (!_vm.sidebarVisible || !_vm.tocVisible) && _vm.isMobile,\n      'content-full': !_vm.sidebarVisible && !_vm.tocVisible && _vm.isMobile\n    }\n  }, [_c('HelpContent', {\n    attrs: {\n      \"doc\": _vm.currentDoc,\n      \"prev-page\": _vm.getPrevPage(),\n      \"next-page\": _vm.getNextPage()\n    },\n    on: {\n      \"content-loaded\": _vm.buildToc\n    }\n  })], 1), _c('aside', {\n    staticClass: \"toc\",\n    class: {\n      'toc-hidden': !_vm.tocVisible && _vm.isMobile,\n      'toc-visible': _vm.tocVisible || !_vm.isMobile\n    }\n  }, [_vm.isMobile ? _c('div', {\n    staticClass: \"toc-header\"\n  }, [_c('span', {\n    staticClass: \"toc-title\"\n  }, [_vm._v(\"文章导航\")]), _c('button', {\n    staticClass: \"close-btn\",\n    on: {\n      \"click\": _vm.closeToc\n    }\n  }, [_c('i', {\n    staticClass: \"icon-close\"\n  }, [_vm._v(\"×\")])])]) : _vm._e(), !_vm.isMobile ? _c('div', {\n    staticClass: \"toc-title\"\n  }, [_vm._v(\"文章导航\")]) : _vm._e(), _c('ul', {\n    staticClass: \"toc-list\"\n  }, _vm._l(_vm.toc, function (item) {\n    return _c('li', {\n      key: item.id,\n      staticClass: \"toc-item\",\n      class: {\n        'toc-item-h3': item.level === 3,\n        'active': item.id === _vm.activeTocId\n      }\n    }, [_c('a', {\n      staticClass: \"toc-link\",\n      attrs: {\n        \"href\": '#' + item.id\n      },\n      on: {\n        \"click\": function ($event) {\n          $event.preventDefault();\n          return _vm.scrollToAnchor(item.id);\n        }\n      }\n    }, [_vm._v(\" \" + _vm._s(item.text) + \" \")])]);\n  }), 0)]), _c('Mider'), _c('chatAi')], 1);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "isMobile", "class", "sidebarVisible", "on", "toggleSidebar", "_v", "tocVisible", "toggleToc", "_e", "closeAllPanels", "ref", "closeSidebar", "_l", "menu", "category", "key", "title", "_s", "items", "item", "path", "isMenuActive", "attrs", "onMenuItemClick", "name", "currentDoc", "getPrevPage", "getNextPage", "buildToc", "closeToc", "toc", "id", "level", "activeTocId", "click", "$event", "preventDefault", "scrollToAnchor", "text", "staticRenderFns"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/views/HelpView.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"help-layout\"},[(_vm.isMobile)?_c('div',{staticClass:\"mobile-controls\"},[_c('button',{staticClass:\"sidebar-toggle\",class:{ 'active': _vm.sidebarVisible },on:{\"click\":_vm.toggleSidebar}},[_c('i',{staticClass:\"icon-menu\"}),_c('span',[_vm._v(\"菜单\")])]),_c('button',{staticClass:\"toc-toggle\",class:{ 'active': _vm.tocVisible },on:{\"click\":_vm.toggleToc}},[_c('i',{staticClass:\"icon-list\"}),_c('span',[_vm._v(\"目录\")])])]):_vm._e(),(_vm.isMobile && (_vm.sidebarVisible || _vm.tocVisible))?_c('div',{staticClass:\"overlay\",on:{\"click\":_vm.closeAllPanels}}):_vm._e(),_c('aside',{ref:\"sidebar\",staticClass:\"sidebar\",class:{\n      'sidebar-hidden': !_vm.sidebarVisible && _vm.isMobile,\n      'sidebar-visible': _vm.sidebarVisible || !_vm.isMobile\n    }},[(_vm.isMobile)?_c('div',{staticClass:\"sidebar-header\"},[_c('span',{staticClass:\"sidebar-title\"},[_vm._v(\"帮助文档\")]),_c('button',{staticClass:\"close-btn\",on:{\"click\":_vm.closeSidebar}},[_c('i',{staticClass:\"icon-close\"},[_vm._v(\"×\")])])]):_vm._e(),_c('div',{staticClass:\"sidebar-menu\"},_vm._l((_vm.menu),function(category){return _c('div',{key:category.title,staticClass:\"menu-category\"},[_c('div',{staticClass:\"category-title\"},[_vm._v(_vm._s(category.title))]),_c('ul',{staticClass:\"menu-list\"},_vm._l((category.items),function(item){return _c('li',{key:item.path,staticClass:\"menu-item\"},[_c('router-link',{staticClass:\"menu-link\",class:{ 'menu-link-active': _vm.isMenuActive(item.path) },attrs:{\"to\":item.path},on:{\"click\":_vm.onMenuItemClick}},[_vm._v(\" \"+_vm._s(item.name)+\" \")])],1)}),0)])}),0)]),_c('main',{ref:\"mainContent\",staticClass:\"main-content\",class:{\n      'content-expanded': (!_vm.sidebarVisible || !_vm.tocVisible) && _vm.isMobile,\n      'content-full': !_vm.sidebarVisible && !_vm.tocVisible && _vm.isMobile\n    }},[_c('HelpContent',{attrs:{\"doc\":_vm.currentDoc,\"prev-page\":_vm.getPrevPage(),\"next-page\":_vm.getNextPage()},on:{\"content-loaded\":_vm.buildToc}})],1),_c('aside',{staticClass:\"toc\",class:{\n      'toc-hidden': !_vm.tocVisible && _vm.isMobile,\n      'toc-visible': _vm.tocVisible || !_vm.isMobile\n    }},[(_vm.isMobile)?_c('div',{staticClass:\"toc-header\"},[_c('span',{staticClass:\"toc-title\"},[_vm._v(\"文章导航\")]),_c('button',{staticClass:\"close-btn\",on:{\"click\":_vm.closeToc}},[_c('i',{staticClass:\"icon-close\"},[_vm._v(\"×\")])])]):_vm._e(),(!_vm.isMobile)?_c('div',{staticClass:\"toc-title\"},[_vm._v(\"文章导航\")]):_vm._e(),_c('ul',{staticClass:\"toc-list\"},_vm._l((_vm.toc),function(item){return _c('li',{key:item.id,staticClass:\"toc-item\",class:{ 'toc-item-h3': item.level === 3, 'active': item.id === _vm.activeTocId }},[_c('a',{staticClass:\"toc-link\",attrs:{\"href\":'#' + item.id},on:{\"click\":function($event){$event.preventDefault();return _vm.scrollToAnchor(item.id)}}},[_vm._v(\" \"+_vm._s(item.text)+\" \")])])}),0)]),_c('Mider'),_c('chatAi')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAa,CAAC,EAAC,CAAEH,GAAG,CAACI,QAAQ,GAAEH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAiB,CAAC,EAAC,CAACF,EAAE,CAAC,QAAQ,EAAC;IAACE,WAAW,EAAC,gBAAgB;IAACE,KAAK,EAAC;MAAE,QAAQ,EAAEL,GAAG,CAACM;IAAe,CAAC;IAACC,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACQ;IAAa;EAAC,CAAC,EAAC,CAACP,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,CAAC,EAACF,EAAE,CAAC,MAAM,EAAC,CAACD,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAACR,EAAE,CAAC,QAAQ,EAAC;IAACE,WAAW,EAAC,YAAY;IAACE,KAAK,EAAC;MAAE,QAAQ,EAAEL,GAAG,CAACU;IAAW,CAAC;IAACH,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACW;IAAS;EAAC,CAAC,EAAC,CAACV,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,CAAC,EAACF,EAAE,CAAC,MAAM,EAAC,CAACD,GAAG,CAACS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACT,GAAG,CAACY,EAAE,EAAE,EAAEZ,GAAG,CAACI,QAAQ,KAAKJ,GAAG,CAACM,cAAc,IAAIN,GAAG,CAACU,UAAU,CAAC,GAAET,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,SAAS;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACa;IAAc;EAAC,CAAC,CAAC,GAACb,GAAG,CAACY,EAAE,EAAE,EAACX,EAAE,CAAC,OAAO,EAAC;IAACa,GAAG,EAAC,SAAS;IAACX,WAAW,EAAC,SAAS;IAACE,KAAK,EAAC;MACrrB,gBAAgB,EAAE,CAACL,GAAG,CAACM,cAAc,IAAIN,GAAG,CAACI,QAAQ;MACrD,iBAAiB,EAAEJ,GAAG,CAACM,cAAc,IAAI,CAACN,GAAG,CAACI;IAChD;EAAC,CAAC,EAAC,CAAEJ,GAAG,CAACI,QAAQ,GAAEH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAgB,CAAC,EAAC,CAACF,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACR,EAAE,CAAC,QAAQ,EAAC;IAACE,WAAW,EAAC,WAAW;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACe;IAAY;EAAC,CAAC,EAAC,CAACd,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAY,CAAC,EAAC,CAACH,GAAG,CAACS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACT,GAAG,CAACY,EAAE,EAAE,EAACX,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAACH,GAAG,CAACgB,EAAE,CAAEhB,GAAG,CAACiB,IAAI,EAAE,UAASC,QAAQ,EAAC;IAAC,OAAOjB,EAAE,CAAC,KAAK,EAAC;MAACkB,GAAG,EAACD,QAAQ,CAACE,KAAK;MAACjB,WAAW,EAAC;IAAe,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;MAACE,WAAW,EAAC;IAAgB,CAAC,EAAC,CAACH,GAAG,CAACS,EAAE,CAACT,GAAG,CAACqB,EAAE,CAACH,QAAQ,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,EAACnB,EAAE,CAAC,IAAI,EAAC;MAACE,WAAW,EAAC;IAAW,CAAC,EAACH,GAAG,CAACgB,EAAE,CAAEE,QAAQ,CAACI,KAAK,EAAE,UAASC,IAAI,EAAC;MAAC,OAAOtB,EAAE,CAAC,IAAI,EAAC;QAACkB,GAAG,EAACI,IAAI,CAACC,IAAI;QAACrB,WAAW,EAAC;MAAW,CAAC,EAAC,CAACF,EAAE,CAAC,aAAa,EAAC;QAACE,WAAW,EAAC,WAAW;QAACE,KAAK,EAAC;UAAE,kBAAkB,EAAEL,GAAG,CAACyB,YAAY,CAACF,IAAI,CAACC,IAAI;QAAE,CAAC;QAACE,KAAK,EAAC;UAAC,IAAI,EAACH,IAAI,CAACC;QAAI,CAAC;QAACjB,EAAE,EAAC;UAAC,OAAO,EAACP,GAAG,CAAC2B;QAAe;MAAC,CAAC,EAAC,CAAC3B,GAAG,CAACS,EAAE,CAAC,GAAG,GAACT,GAAG,CAACqB,EAAE,CAACE,IAAI,CAACK,IAAI,CAAC,GAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;IAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC3B,EAAE,CAAC,MAAM,EAAC;IAACa,GAAG,EAAC,aAAa;IAACX,WAAW,EAAC,cAAc;IAACE,KAAK,EAAC;MACj2B,kBAAkB,EAAE,CAAC,CAACL,GAAG,CAACM,cAAc,IAAI,CAACN,GAAG,CAACU,UAAU,KAAKV,GAAG,CAACI,QAAQ;MAC5E,cAAc,EAAE,CAACJ,GAAG,CAACM,cAAc,IAAI,CAACN,GAAG,CAACU,UAAU,IAAIV,GAAG,CAACI;IAChE;EAAC,CAAC,EAAC,CAACH,EAAE,CAAC,aAAa,EAAC;IAACyB,KAAK,EAAC;MAAC,KAAK,EAAC1B,GAAG,CAAC6B,UAAU;MAAC,WAAW,EAAC7B,GAAG,CAAC8B,WAAW,EAAE;MAAC,WAAW,EAAC9B,GAAG,CAAC+B,WAAW;IAAE,CAAC;IAACxB,EAAE,EAAC;MAAC,gBAAgB,EAACP,GAAG,CAACgC;IAAQ;EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC/B,EAAE,CAAC,OAAO,EAAC;IAACE,WAAW,EAAC,KAAK;IAACE,KAAK,EAAC;MAC1L,YAAY,EAAE,CAACL,GAAG,CAACU,UAAU,IAAIV,GAAG,CAACI,QAAQ;MAC7C,aAAa,EAAEJ,GAAG,CAACU,UAAU,IAAI,CAACV,GAAG,CAACI;IACxC;EAAC,CAAC,EAAC,CAAEJ,GAAG,CAACI,QAAQ,GAAEH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAY,CAAC,EAAC,CAACF,EAAE,CAAC,MAAM,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAACR,EAAE,CAAC,QAAQ,EAAC;IAACE,WAAW,EAAC,WAAW;IAACI,EAAE,EAAC;MAAC,OAAO,EAACP,GAAG,CAACiC;IAAQ;EAAC,CAAC,EAAC,CAAChC,EAAE,CAAC,GAAG,EAAC;IAACE,WAAW,EAAC;EAAY,CAAC,EAAC,CAACH,GAAG,CAACS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACT,GAAG,CAACY,EAAE,EAAE,EAAE,CAACZ,GAAG,CAACI,QAAQ,GAAEH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAW,CAAC,EAAC,CAACH,GAAG,CAACS,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,GAACT,GAAG,CAACY,EAAE,EAAE,EAACX,EAAE,CAAC,IAAI,EAAC;IAACE,WAAW,EAAC;EAAU,CAAC,EAACH,GAAG,CAACgB,EAAE,CAAEhB,GAAG,CAACkC,GAAG,EAAE,UAASX,IAAI,EAAC;IAAC,OAAOtB,EAAE,CAAC,IAAI,EAAC;MAACkB,GAAG,EAACI,IAAI,CAACY,EAAE;MAAChC,WAAW,EAAC,UAAU;MAACE,KAAK,EAAC;QAAE,aAAa,EAAEkB,IAAI,CAACa,KAAK,KAAK,CAAC;QAAE,QAAQ,EAAEb,IAAI,CAACY,EAAE,KAAKnC,GAAG,CAACqC;MAAY;IAAC,CAAC,EAAC,CAACpC,EAAE,CAAC,GAAG,EAAC;MAACE,WAAW,EAAC,UAAU;MAACuB,KAAK,EAAC;QAAC,MAAM,EAAC,GAAG,GAAGH,IAAI,CAACY;MAAE,CAAC;MAAC5B,EAAE,EAAC;QAAC,OAAO,EAAC,SAAA+B,CAASC,MAAM,EAAC;UAACA,MAAM,CAACC,cAAc,EAAE;UAAC,OAAOxC,GAAG,CAACyC,cAAc,CAAClB,IAAI,CAACY,EAAE,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAACnC,GAAG,CAACS,EAAE,CAAC,GAAG,GAACT,GAAG,CAACqB,EAAE,CAACE,IAAI,CAACmB,IAAI,CAAC,GAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAACzC,EAAE,CAAC,OAAO,CAAC,EAACA,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC;AACvuB,CAAC;AACD,IAAI0C,eAAe,GAAG,EAAE;AAExB,SAAS5C,MAAM,EAAE4C,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}