{"version": 3, "file": "js/docs19.e5d04027.js", "mappings": "wHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,aACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,aACrCO,EAA6B,IAAIP,IAAI,aACrCQ,EAA6B,IAAIR,IAAI,aACrCS,EAA6B,IAAIT,IAAI,aACrCU,EAA8B,IAAIV,IAAI,aACtCW,EAA8B,IAAIX,IAAI,aAEtCY,EAAO,gYAAmZb,EAA6B,4KAAsLE,EAA6B,wGAAkHC,EAA6B,kIAA4IC,EAA6B,4IAAsJC,EAA6B,yZAAuaC,EAA6B,iJAAyJC,EAA6B,0tBAAsuBC,EAA6B,ksBAA4tBC,EAA6B,oNAAgOC,EAA6B,4uBAA0wBC,EAA8B,6VAA6WC,EAA8B,qaAE/pJ,c", "sources": ["webpack://portal-ui/./src/docs/stable-diffusion3.5-large.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/universal1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/universal2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/comfyui3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/comfyui4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/comfyui5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/comfyui6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/comfyui7.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_7___ = new URL(\"./imgs/comfyui8.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_8___ = new URL(\"./imgs/comfyui9.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_9___ = new URL(\"./imgs/comfyui10.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_10___ = new URL(\"./imgs/comfyui11.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_11___ = new URL(\"./imgs/comfyui12.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"容器化部署-stablediffusion-35-large-文生图模型应用\\\"><font style=\\\"color:#020817\\\">容器化部署 StableDiffusion-3.5-large 文生图模型应用</font></h1> <h2 id=\\\"1-部署步骤\\\"><font style=\\\"color:#020817\\\">1 部署步骤</font></h2> <h3 id=\\\"11-访问天工开物控制台，点击新增部署。\\\"><font style=\\\"color:#020817\\\">1.1 访问</font><a href=\\\"https://tiangongkaiwu.top/#/console\\\">天工开物控制台</a><font style=\\\"color:#020817\\\">，点击新增部署。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"12-基于自身需要进行配置，参考配置为单卡-4090-和-1-个节点（初次使用进行调试）。\\\"><font style=\\\"color:#020817\\\">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"13-选择相应预制镜像\\\"><font style=\\\"color:#020817\\\">1.3 选择相应预制镜像</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"14-点击部署服务，耐心等待节点拉取镜像并启动。\\\"><font style=\\\"color:#020817\\\">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"15-节点启动后，你所在任务详情页中看到的内容可能如下：\\\"><font style=\\\"color:#020817\\\">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"16-我们可以点击快速访问下方8188端口的链接，测试-comfyui-部署情况\\\"><font style=\\\"color:#020817\\\">1.6 我们可以点击快速访问下方“8188”端口的链接，测试 comfyui 部署情况</font></h3> <p><font style=\\\"color:#020817\\\">系统会自动分配一个可公网访问的域名，点击 8188 端口的链接。接下来我们即可自由地通过使用工作流在 comfyui 中使用 Flux 模型进行图像生成。</font></p> <p><font style=\\\"color:#020817\\\">我们进入 8188 端口的 web ui 界面，选择左侧的“工作流“菜单，找到名为”sd_text_encoder_exampl.json“的工作流文件，鼠标点击。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">随后，我们可以看到工作流已经被正常载入 ComfyUI 了。接下来，我们找到 prompt（提示词）的填写节点，输入我们想要生成的图像描述文本。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">参考的 prompt 如下（SD 模型对英文支持较好）：</font></p> <p><font style=\\\"color:#67676c\\\">best quality, a cute anime girl, sunset light, warm atmosphere, soft features, dreamy mood, sitting on a swing, solo, looking at viewer, gentle smile, blush, pale skin, full body, long flowing hair, off-shoulder dress with blue and white edges, visible collarbone, barefoot, golden hour lighting, light particles, flower petals floating, orange sky, backlight glow, surrounded by vines and plants, roses and blue flowers around, soft shadows, natural environment, cinematic framing</font></p> <p><font style=\\\"color:#020817\\\">稍等片刻，即可在后方的“保存图像”节点看到基于我们提示词生成的图片。右键点击图片，选择“Save Image”即可保存图片。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_7___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"17-通过-api-的形式来调用-comfyui-进行图像生成\\\"><font style=\\\"color:#020817\\\">1.7 通过 API 的形式来调用 comfyui 进行图像生成</font></h3> <p><font style=\\\"color:#020817\\\">我们不推荐直接使用 comfyui 的默认 API 接口，因为多节点时需自行解决无状态问题。更推荐的做法是通过我们暴露的 3000 端口进行请求，其通过 </font><a href=\\\"https://github.com/SaladTechnologies/comfyui-api?tab=readme-ov-file\\\"><font style=\\\"color:#2f8ef4\\\">comfyui-api</font></a><font style=\\\"color:#020817\\\"> 进行包装，支持默认的同步生图请求和 webhook 实现。</font></p> <p><font style=\\\"color:#020817\\\">以下以 POSTMAN 为例，简要描述如何向 3000 端口发送图片生成的 API 请求：</font></p> <h4 id=\\\"171-保存页面工作流\\\"><font style=\\\"color:#020817\\\">1.7.1 保存页面工作流</font></h4> <p><font style=\\\"color:#020817\\\">点击”导航栏——工作流——导出（API）“菜单，浏览器会自动下载一个 json 文件。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_8___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"172-打开-postman，新建一个-post-请求\\\"><font style=\\\"color:#020817\\\">1.7.2 打开 POSTMAN，新建一个 POST 请求</font></h4> <p><font style=\\\"color:#020817\\\">新建一个 POST 请求，并命名为”prompt“，如下图所示：</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_9___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"173-完善请求信息\\\"><font style=\\\"color:#020817\\\">1.7.3 完善请求信息</font></h4> <p><font style=\\\"color:#020817\\\">需要完善的信息如下：</font></p> <ul> <li><strong><font style=\\\"color:#020817\\\">请求的 URL</font></strong></li> </ul> <p><font style=\\\"color:#020817\\\">在 3000 端口的回传链接后加上“/prompt”的路径，保证其格式类似于</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\"><a href=\\\"https://xxx/prompt\\\">https://xxx/prompt</a></font><font style=\\\"color:#020817\\\">。</font></p> <ul> <li><strong><font style=\\\"color:#020817\\\">将请求体参数格式设置为 raw 和 json</font></strong></li> </ul> <p><font style=\\\"color:#020817\\\">如图。</font></p> <ul> <li><strong><font style=\\\"color:#020817\\\">设置参数内容基本格式</font></strong></li> </ul> <p><font style=\\\"color:#020817\\\">如图。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_10___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"174-将我们下载好的工作流-json-文件粘贴为参数中prompt字段的值\\\"><font style=\\\"color:#020817\\\">1.7.4 将我们下载好的工作流 json 文件粘贴为参数中</font><font style=\\\"color:#020817;background-color:rgba(142,150,170,.14)\\\">prompt</font><font style=\\\"color:#020817\\\">字段的值</font></h4> <p><font style=\\\"color:#020817\\\">如下图所示，我们将鼠标移动至 prompt 字段的冒号后，粘贴工作流的内容。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_11___ + \"\\\" alt=\\\"\\\"></p> <h4 id=\\\"175-发送请求\\\"><font style=\\\"color:#020817\\\">1.7.5 发送请求</font></h4> <p><font style=\\\"color:#020817\\\">返回结果如下所示，</font><font style=\\\"color:#2f8ef4;background-color:rgba(142,150,170,.14)\\\">images</font><font style=\\\"color:#020817\\\">字段包含一个字符数组，其中的元素即为生成图片的 base64 编码。</font></p> <p><strong><font style=\\\"color:#67676c\\\"></font></strong></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/18 16:03</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "___HTML_LOADER_IMPORT_7___", "___HTML_LOADER_IMPORT_8___", "___HTML_LOADER_IMPORT_9___", "___HTML_LOADER_IMPORT_10___", "___HTML_LOADER_IMPORT_11___", "code"], "sourceRoot": ""}