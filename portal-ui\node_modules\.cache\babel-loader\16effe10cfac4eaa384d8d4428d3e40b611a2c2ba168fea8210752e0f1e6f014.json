{"ast": null, "code": "// 引入 axios\nimport axios from \"axios\";\nlet base = 'http://localhost:8087';\n\n//传送json格式的get请求\nexport const getRequest = (url, params) => {\n  return axios({\n    method: 'get',\n    url: `${base}${url}`,\n    params: params\n  });\n};", "map": {"version": 3, "names": ["axios", "base", "getRequest", "url", "params", "method"], "sources": ["D:/workcode/tiangongkaiwu/BusinessWebisite_ui/portal-ui/src/api/api.js"], "sourcesContent": ["// 引入 axios\r\nimport axios from \"axios\";\r\n\r\n\r\n\r\nlet base = 'http://localhost:8087';\r\n\r\n//传送json格式的get请求\r\nexport const getRequest=(url,params)=>{\r\n    return axios({\r\n        method:'get',\r\n        url:`${base}${url}`,\r\n        params: params,\r\n    })\r\n}\r\n"], "mappings": "AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AAIzB,IAAIC,IAAI,GAAG,uBAAuB;;AAElC;AACA,OAAO,MAAMC,UAAU,GAACA,CAACC,GAAG,EAACC,MAAM,KAAG;EAClC,OAAOJ,KAAK,CAAC;IACTK,MAAM,EAAC,KAAK;IACZF,GAAG,EAAE,GAAEF,IAAK,GAAEE,GAAI,EAAC;IACnBC,MAAM,EAAEA;EACZ,CAAC,CAAC;AACN,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}