{"version": 3, "file": "js/258.937ab62c.js", "mappings": "8JAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACA,EAAG,MAAM,CAACE,YAAY,mBAAmBC,YAAY,CAAC,MAAQ,SAAS,CAACH,EAAG,MAAM,CAACE,YAAY,cAAcC,YAAY,CAAC,aAAa,UAAUH,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACA,EAAG,cAAc,CAACI,MAAM,CAAC,GAAK,UAAU,CAACN,EAAIO,GAAG,WAAW,GAAGL,EAAG,KAAK,CAACE,YAAY,aAAa,CAACJ,EAAIO,GAAG,kBAAkBL,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,YAAYC,YAAY,CAAC,YAAY,WAAW,CAACH,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACE,YAAY,kBAAkB,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,QAAQC,UAAUR,EAAG,IAAI,CAACE,YAAY,wBAAwB,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,QAAQE,mBAAmBT,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoBJ,EAAIY,GAAIZ,EAAIa,gBAAgB,SAASJ,EAAQK,GAAO,OAAOZ,EAAG,MAAM,CAACa,IAAID,EAAMV,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,MAAM,CAACG,YAAY,CAAC,OAAS,SAASC,MAAM,CAAC,IAAMG,EAAQO,MAAM,IAAM,QAAQd,EAAG,KAAK,CAACE,YAAY,0BAA0B,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGC,EAAQC,UAAUR,EAAG,MAAM,CAACE,YAAY,yBAAyB,CAACJ,EAAIO,GAAGP,EAAIQ,GAAGC,EAAQE,iBAAiBT,EAAG,MAAM,CAACE,YAAY,2BAA2B,CAACF,EAAG,cAAc,CAACE,YAAY,OAAOE,MAAM,CAAC,GAAK,CAACW,KAAK,cAAcC,OAAO,CAACC,OAAOV,EAAQW,cAAc,CAACpB,EAAIO,GAAG,WAAW,IAAI,IAAG,OAAOL,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,sBAAsB,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiBE,MAAM,CAAC,GAAK,cAAc,CAACJ,EAAG,MAAM,CAACE,YAAY,6BAA6B,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,KAAK,CAACE,YAAY,iBAAiBE,MAAM,CAAC,mBAAmB,oDAAoD,CAACN,EAAIO,GAAG,OAAOP,EAAIQ,GAAGR,EAAIS,QAAQY,QAAQ,qBAAqBrB,EAAIQ,GAAGR,EAAIS,QAAQa,YAAY,SAASpB,EAAG,MAAM,CAACE,YAAY,uCAAuC,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,qDAAqDmB,SAAS,CAAC,UAAYvB,EAAIQ,GAAGR,EAAIS,QAAQe,qCAC1rE,EACIC,EAAkB,G,UCwEtB,GACAR,KAAA,kBACAS,WAAA,CAAAC,OAAAA,EAAAA,GACAC,OACA,OACAnB,QAAA,GACAI,eAAA,GAEA,EACAgB,UACA,KAAAC,sBAAA,KAAAC,OAAAb,OAAAC,OACA,EACAa,QAAA,CACAF,sBAAAV,GACA,KAAAa,WAAA,2BAAAb,KAAAc,MAAAC,IACAA,IACA,KAAA1B,QAAA0B,EAAAP,KAAAA,KACA,KAAAQ,iBAAAhB,GACA,GAEA,EACAgB,iBAAAC,GACA,KAAAJ,WAAA,sBAAAI,KAAAH,MAAAC,IACAA,IACA,KAAAtB,eAAAsB,EAAAP,KAAAA,KACA,GAEA,ICrG+P,I,UCO3PU,GAAY,OACd,EACAvC,EACA0B,GACA,EACA,KACA,WACA,MAIF,EAAea,EAAiB,O", "sources": ["webpack://portal-ui/./src/views/NewsDetailsView.vue", "webpack://portal-ui/src/views/NewsDetailsView.vue", "webpack://portal-ui/./src/views/NewsDetailsView.vue?7645", "webpack://portal-ui/./src/views/NewsDetailsView.vue?39ad"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('Layout',[_c('div',{staticClass:\"layout-container\",staticStyle:{\"width\":\"100%\"}},[_c('div',{staticClass:\"page-header\",staticStyle:{\"max-height\":\"14px\"}}),_c('div',{staticClass:\"breadcrumb-box\"},[_c('div',{staticClass:\"am-container\"},[_c('ol',{staticClass:\"am-breadcrumb\"},[_c('li',[_c('router-link',{attrs:{\"to\":\"/news\"}},[_vm._v(\"公司动态\")])],1),_c('li',{staticClass:\"am-active\"},[_vm._v(\"文章详情\")])])])])]),_c('div',{staticClass:\"section\"},[_c('div',{staticClass:\"container\",staticStyle:{\"max-width\":\"1160px\"}},[_c('div',{staticClass:\"section--header\"},[_c('h2',{staticClass:\"section--title\"},[_vm._v(_vm._s(_vm.article.title))]),_c('p',{staticClass:\"section--description\"},[_vm._v(_vm._s(_vm.article.introduction))])]),_c('div',{staticClass:\"join-container\"},[_c('div',{staticClass:\"am-g\"},[_c('div',{staticClass:\"am-u-md-3\"},[_c('div',{staticClass:\"careers--articles\"},[_c('div',{staticClass:\"careers_articles\"},_vm._l((_vm.recentArticles),function(article,index){return _c('div',{key:index,staticClass:\"careers_article\"},[_c('div',{staticClass:\"image\"},[_c('img',{staticStyle:{\"height\":\"160px\"},attrs:{\"src\":article.cover,\"alt\":\"\"}})]),_c('h3',{staticClass:\"careers_article--title\"},[_vm._v(_vm._s(article.title))]),_c('div',{staticClass:\"careers_article--text\"},[_vm._v(_vm._s(article.introduction))]),_c('div',{staticClass:\"careers_article--footer\"},[_c('router-link',{staticClass:\"link\",attrs:{\"to\":{name:'newsDetails',params:{newsId:article.articleId}}}},[_vm._v(\"查看更多\")])],1)])}),0)])]),_c('div',{staticClass:\"am-u-md-9\"},[_c('div',{staticClass:\"careers--vacancies\"},[_c('div',{staticClass:\"am-panel-group\",attrs:{\"id\":\"accordion\"}},[_c('div',{staticClass:\"am-panel am-panel-default\"},[_c('div',{staticClass:\"am-panel-hd\"},[_c('h4',{staticClass:\"am-panel-title\",attrs:{\"data-am-collapse\":\"{parent: '#accordion', target: '#do-not-say-1'}\"}},[_vm._v(\" 作者：\"+_vm._s(_vm.article.author)+\"      /      发布时间：\"+_vm._s(_vm.article.createTime)+\" \")])]),_c('div',{staticClass:\"am-panel-collapse am-collapse am-in\"},[_c('div',{staticClass:\"am-panel-bd\"},[_c('div',{staticClass:\"vacancies--item_content js-accordion--pane_content\",domProps:{\"innerHTML\":_vm._s(_vm.article.contentHtml)}})])])])])])])])])])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<Layout>\r\n\t\t\r\n\t\t<div class=\"layout-container\" style=\"width: 100%;\">\r\n\t\t\t<div class=\"page-header\" style=\"max-height: 14px;\"></div>\r\n\t\t\t<div class=\"breadcrumb-box\">\r\n\t\t\t\t<div class=\"am-container\">\r\n\t\t\t\t\t<ol class=\"am-breadcrumb\">\r\n\t\t\t\t\t\t<li><router-link to=\"/news\">公司动态</router-link></li>\r\n\t\t\t\t\t\t<li class=\"am-active\">文章详情</li>\r\n\t\t\t\t\t</ol>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t\t<div class=\"section\">\r\n\t\t\t<div class=\"container\" style=\"max-width: 1160px\">\r\n\t\t\t\t<div class=\"section--header\">\r\n\t\t\t\t\t<h2 class=\"section--title\">{{article.title}}</h2>\r\n\t\t\t\t\t<p class=\"section--description\">{{article.introduction}}</p>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div class=\"join-container\">\r\n\t\t\t\t\t<div class=\"am-g\">\r\n\t\t\t\t\t\t<div class=\"am-u-md-3\">\r\n\t\t\t\t\t\t\t<div class=\"careers--articles\">\r\n\t\t\t\t\t\t\t\t<!--<h3 class=\"careers&#45;&#45;subtitle\">文章信息</h3>-->\r\n\t\t\t\t\t\t\t\t<div class=\"careers_articles\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"careers_article\" v-for=\"(article,index) in recentArticles\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"image\">\r\n\t\t\t\t\t\t\t\t\t\t\t<img style=\"height: 160px;\" :src=\"article.cover\" alt=\"\">\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<h3 class=\"careers_article--title\">{{ article.title }}</h3>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"careers_article--text\">{{article.introduction}}</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"careers_article--footer\">\r\n\t\t\t\t\t\t\t\t\t\t\t<router-link :to=\"{name:'newsDetails',params:{newsId:article.articleId}}\" class=\"link\">查看更多</router-link>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<div class=\"am-u-md-9\">\r\n\t\t\t\t\t\t\t<!--<h3 class=\"careers&#45;&#45;subtitle\">文章内容</h3>-->\r\n\t\t\t\t\t\t\t<div class=\"careers--vacancies\">\r\n\t\t\t\t\t\t\t\t<div class=\"am-panel-group\" id=\"accordion\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"am-panel am-panel-default\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"am-panel-hd\">\r\n\t\t\t\t\t\t\t\t\t\t\t<h4 class=\"am-panel-title\" data-am-collapse=\"{parent: '#accordion', target: '#do-not-say-1'}\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t作者：{{article.author}} &nbsp;&nbsp;&nbsp;&nbsp; / &nbsp;&nbsp;&nbsp;&nbsp; 发布时间：{{article.createTime}}\r\n\t\t\t\t\t\t\t\t\t\t\t</h4>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"am-panel-collapse am-collapse am-in\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"am-panel-bd\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"vacancies--item_content js-accordion--pane_content\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-html=\"article.contentHtml\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\n\r\nexport default {\r\n\tname: \"NewsDetailsView\",\r\n\tcomponents: {Layout,},\r\n\tdata(){\r\n\t\treturn{\r\n\t\t\tarticle:{},\r\n\t\t\trecentArticles:[]\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.getArticleByArticleId(this.$route.params.newsId)\r\n\t},\r\n\tmethods:{\r\n\t\tgetArticleByArticleId(articleId){\r\n\t\t\tthis.getRequest(`/findArticleByArticleId/${articleId}`).then(resp =>{\r\n\t\t\t\tif (resp){\r\n\t\t\t\t\tthis.article = resp.data.data\r\n\t\t\t\t\tthis.getRecentArticle(articleId)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetRecentArticle(currentArticleId){\r\n\t\t\tthis.getRequest(`/findRecentArticle/${currentArticleId}`).then(resp =>{\r\n\t\t\t\tif (resp){\r\n\t\t\t\t\tthis.recentArticles = resp.data.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./NewsDetailsView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./NewsDetailsView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./NewsDetailsView.vue?vue&type=template&id=e807f88e&scoped=true&\"\nimport script from \"./NewsDetailsView.vue?vue&type=script&lang=js&\"\nexport * from \"./NewsDetailsView.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e807f88e\",\n  null\n  \n)\n\nexport default component.exports"], "names": ["render", "_vm", "this", "_c", "_self", "staticClass", "staticStyle", "attrs", "_v", "_s", "article", "title", "introduction", "_l", "recentArticles", "index", "key", "cover", "name", "params", "newsId", "articleId", "author", "createTime", "domProps", "contentHtml", "staticRenderFns", "components", "Layout", "data", "mounted", "getArticleByArticleId", "$route", "methods", "getRequest", "then", "resp", "getRecentArticle", "currentArticleId", "component"], "sourceRoot": ""}