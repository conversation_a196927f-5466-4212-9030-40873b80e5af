{"ast": null, "code": "import Layout from \"@/components/common/Layout-header\";\nexport default {\n  name: \"AlgorithmCommunity\",\n  components: {\n    Layout\n  }\n};", "map": {"version": 3, "names": ["Layout", "name", "components"], "sources": ["src/views/AlgorithmCommunity.vue"], "sourcesContent": ["<template>\r\n  <Layout>\r\n    <div class=\"coming-soon-container\">\r\n      <h1 class=\"title\">敬请期待</h1>\r\n      <p class=\"subtitle\">我们正在努力建设中，敬请期待更多精彩内容！</p>\r\n    </div>\r\n  </Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout-header\";\r\n\r\nexport default {\r\n  name: \"AlgorithmCommunity\",\r\n  components: { Layout }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.coming-soon-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 70vh;\r\n  text-align: center;\r\n  background-color: #f9fafb;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  padding: 40px;\r\n}\r\n\r\n.title {\r\n  font-size: 3rem;\r\n  color: #1f2937;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.subtitle {\r\n  font-size: 1.2rem;\r\n  color: #4b5563;\r\n}\r\n</style>\r\n"], "mappings": "AAUA,OAAAA,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}