{"version": 3, "file": "js/docs17.165ee227.js", "mappings": "wHACA,IAAIA,EAA6B,IAAIC,IAAI,aACrCC,EAA6B,IAAID,IAAI,aACrCE,EAA6B,IAAIF,IAAI,aACrCG,EAA6B,IAAIH,IAAI,aACrCI,EAA6B,IAAIJ,IAAI,aACrCK,EAA6B,IAAIL,IAAI,aACrCM,EAA6B,IAAIN,IAAI,aAErCO,EAAO,8cAAmeR,EAA6B,4KAAsLE,EAA6B,wGAAkHC,EAA6B,kIAA4IC,EAA6B,4IAAsJC,EAA6B,sOAAkPC,EAA6B,yGAAiHC,EAA6B,2FAEtmD,c", "sources": ["webpack://portal-ui/./src/docs/stable-diffusion1.5.md"], "sourcesContent": ["// Imports\nvar ___HTML_LOADER_IMPORT_0___ = new URL(\"./imgs/universal1.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_1___ = new URL(\"./imgs/universal2.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_2___ = new URL(\"./imgs/webui1-5v3.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_3___ = new URL(\"./imgs/webui1-5v4.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_4___ = new URL(\"./imgs/webui1-5v5.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_5___ = new URL(\"./imgs/webui1-5v6.png\", import.meta.url);\nvar ___HTML_LOADER_IMPORT_6___ = new URL(\"./imgs/webui1-5v7.png\", import.meta.url);\n// Module\nvar code = \"<h1 id=\\\"容器化部署-stablediffusion15-webui-应用\\\"><font style=\\\"color:#020817\\\">容器化部署 StableDiffusion1.5-WebUI 应用</font></h1> <h2 id=\\\"1-部署步骤\\\"><font style=\\\"color:#020817\\\">1 部署步骤</font></h2> <p><font style=\\\"color:#020817\\\">我们提供了构建完毕的 Stable-Diffusion-WebUI 镜像，您可以直接部署使用。</font></p> <h3 id=\\\"11-访问天工开物控制台，点击新增部署。\\\"><font style=\\\"color:#020817\\\">1.1 访问</font><a href=\\\"https://tiangongkaiwu.top/#/console\\\">天工开物控制台</a><font style=\\\"color:#020817\\\">，点击新增部署。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_0___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"12-基于自身需要进行配置，参考配置为单卡-4090-和-1-个节点（初次使用进行调试）。\\\"><font style=\\\"color:#020817\\\">1.2 基于自身需要进行配置，参考配置为单卡 4090 和 1 个节点（初次使用进行调试）。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_1___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"13-选择相应预制镜像\\\"><font style=\\\"color:#020817\\\">1.3 选择相应预制镜像</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_2___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"14-点击部署服务，耐心等待节点拉取镜像并启动。\\\"><font style=\\\"color:#020817\\\">1.4 点击部署服务，耐心等待节点拉取镜像并启动。</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_3___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"15-节点启动后，你所在任务详情页中看到的内容可能如下：\\\"><font style=\\\"color:#020817\\\">1.5 节点启动后，你所在“任务详情页”中看到的内容可能如下：</font></h3> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_4___ + \"\\\" alt=\\\"\\\"></p> <h3 id=\\\"16-我们可以点击快捷访问下方7860端口的链接，测试-gradio-运行情况\\\"><font style=\\\"color:#020817\\\">1.6 我们可以点击快捷访问下方“7860”端口的链接，测试 Gradio 运行情况</font></h3> <p><font style=\\\"color:#020817\\\">接下来填写 prompt，描述我们希望图片的内容。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_5___ + \"\\\" alt=\\\"\\\"></p> <p><font style=\\\"color:#020817\\\">最后点击生成按钮，接下来我们耐心稍等片刻，可以看到图片已经生成。</font></p> <p><img src=\\\"\" + ___HTML_LOADER_IMPORT_6___ + \"\\\" alt=\\\"\\\"></p> <p><br><br> <font style=\\\"color:#b2b2b2\\\">最后更新于: 2025/6/18 11:29</font></p> \";\n// Exports\nexport default code;"], "names": ["___HTML_LOADER_IMPORT_0___", "URL", "___HTML_LOADER_IMPORT_1___", "___HTML_LOADER_IMPORT_2___", "___HTML_LOADER_IMPORT_3___", "___HTML_LOADER_IMPORT_4___", "___HTML_LOADER_IMPORT_5___", "___HTML_LOADER_IMPORT_6___", "code"], "sourceRoot": ""}