{"ast": null, "code": "import Header from \"@/components/common/header/Header\";\n// import Footer from \"@/components/common/footer/Footer\";\n\nexport default {\n  name: \"Layout\",\n  components: {\n    Header\n  }\n};", "map": {"version": 3, "names": ["Header", "name", "components"], "sources": ["src/components/common/Layout-header.vue"], "sourcesContent": ["<template>\r\n\t<main class=\"page-wrapper\">\r\n\t\t<Header/>\r\n\r\n\t\t<div class=\"main-content\">\r\n\t\t\t<slot></slot>\r\n\t\t</div>\r\n\t\r\n\t\t<Footer/>\r\n\t</main>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"@/components/common/header/Header\";\r\n// import Footer from \"@/components/common/footer/Footer\";\r\n\r\nexport default {\r\n\tname: \"Layout\",\r\n\tcomponents:{Header}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.main-content{\r\n\twidth: 100%;\r\n  max-width: 2560px;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n</style>\r\n"], "mappings": "AAaA,OAAAA,MAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF;EAAA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}